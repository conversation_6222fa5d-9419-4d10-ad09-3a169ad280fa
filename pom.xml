<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.7.18</version>
        <relativePath/> <!-- lookup parent from repository -->
    </parent>

    <groupId>net.snaptag</groupId>
    <artifactId>snaptag</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <name>SnapTag</name>
    <description>SnapTag Application</description>

    <properties>
        <java.version>17</java.version>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>

        <!-- 依赖版本管理 - 使用完全兼容版本 -->
        <mybatis-plus.version>3.4.3</mybatis-plus.version>
        <mysql.version>8.0.28</mysql.version>
        <hutool.version>5.7.22</hutool.version>
        <fastjson.version>1.2.83</fastjson.version>
        <aliyun-sdk.version>4.5.25</aliyun-sdk.version>
        <aliyun-oss.version>3.15.1</aliyun-oss.version>
        <aliyun-sls.version>0.6.77</aliyun-sls.version>
        <jedis.version>3.6.3</jedis.version>
        <commons-lang3.version>3.12.0</commons-lang3.version>
        <commons-io.version>2.11.0</commons-io.version>
        <okhttp.version>4.9.3</okhttp.version>
        <jjwt.version>0.9.1</jjwt.version>

    </properties>


    <dependencies>
        <!-- Spring Boot Starters -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

        <!-- Servlet API -->
        <dependency>
            <groupId>javax.servlet</groupId>
            <artifactId>javax.servlet-api</artifactId>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-jpa</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <!-- Database -->
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <version>8.0.28</version>
        </dependency>

        <!-- MyBatis-Plus -->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
            <version>${mybatis-plus.version}</version>
        </dependency>


        <!-- Redis -->
        <dependency>
            <groupId>redis.clients</groupId>
            <artifactId>jedis</artifactId>
            <version>${jedis.version}</version>
        </dependency>

        <!-- Lombok -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <optional>true</optional>
        </dependency>

        <!-- JSON处理 -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>${fastjson.version}</version>
        </dependency>

        <!-- 工具类 -->
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>${hutool.version}</version>
        </dependency>

        <!-- Hutool 图像处理 -->
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-extra</artifactId>
            <version>${hutool.version}</version>
        </dependency>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
            <version>${commons-lang3.version}</version>
        </dependency>

        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
            <version>${commons-io.version}</version>
        </dependency>

        <!-- Apache Commons Codec -->
        <dependency>
            <groupId>commons-codec</groupId>
            <artifactId>commons-codec</artifactId>
            <version>1.15</version>
        </dependency>

        <!-- HTTP客户端 -->
        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
            <version>${okhttp.version}</version>
        </dependency>

        <!-- JWT -->
        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt</artifactId>
            <version>${jjwt.version}</version>
        </dependency>

        <!-- 阿里云服务 -->
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>aliyun-java-sdk-core</artifactId>
            <version>${aliyun-sdk.version}</version>
        </dependency>

        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>aliyun-java-sdk-dysmsapi</artifactId>
            <version>2.2.1</version>
        </dependency>

        <dependency>
            <groupId>com.aliyun.oss</groupId>
            <artifactId>aliyun-sdk-oss</artifactId>
            <version>${aliyun-oss.version}</version>
        </dependency>

        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>aliyun-java-sdk-cdn</artifactId>
            <version>3.8.1</version>
        </dependency>

        <dependency>
            <groupId>com.aliyun.openservices</groupId>
            <artifactId>aliyun-log</artifactId>
            <version>${aliyun-sls.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.google.protobuf</groupId>
                    <artifactId>protobuf-java</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- 邮件 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-mail</artifactId>
        </dependency>

        <!-- 添加javax.mail支持 -->
        <dependency>
            <groupId>com.sun.mail</groupId>
            <artifactId>javax.mail</artifactId>
            <version>1.6.2</version>
        </dependency>

        <!-- Apache Commons Validator -->
        <dependency>
            <groupId>commons-validator</groupId>
            <artifactId>commons-validator</artifactId>
            <version>1.7</version>
        </dependency>

        <!-- Apache Commons Lang (旧版本) -->
        <dependency>
            <groupId>commons-lang</groupId>
            <artifactId>commons-lang</artifactId>
            <version>2.6</version>
        </dependency>

        <!-- Auth0 JWT -->
        <dependency>
            <groupId>com.auth0</groupId>
            <artifactId>jwks-rsa</artifactId>
            <version>0.22.1</version>
        </dependency>

        <!-- 百度AI -->
        <dependency>
            <groupId>com.baidu.aip</groupId>
            <artifactId>java-sdk</artifactId>
            <version>4.16.19</version>
        </dependency>

        <!-- Feign客户端 -->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
            <version>3.1.8</version>
        </dependency>

        <!--VIDA SLS Starter -->
        <dependency>
            <groupId>com.vida</groupId>
            <artifactId>vida-sls-autoconfiguration</artifactId>
            <version>1.0.3-SNAPSHOT</version>
        </dependency>

        <!-- Spring Boot Actuator - 启用监控功能 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>

        <!-- Nacos Config Spring Boot Starter - 兼容版本 -->
        <dependency>
            <groupId>com.alibaba.boot</groupId>
            <artifactId>nacos-config-spring-boot-starter</artifactId>
            <version>0.2.12</version>
            <exclusions>
                <!-- 排除可能冲突的依赖 -->
                <exclusion>
                    <groupId>com.alibaba.nacos</groupId>
                    <artifactId>nacos-client</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- 手动指定兼容的Nacos客户端版本 -->
        <dependency>
            <groupId>com.alibaba.nacos</groupId>
            <artifactId>nacos-client</artifactId>
            <version>1.4.6</version>
        </dependency>


    </dependencies>

    <build>
        <finalName>snaptag</finalName>
        <plugins>
            <!-- Spring Boot Maven Plugin -->
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <excludes>
                        <exclude>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                        </exclude>
                    </excludes>
                </configuration>
            </plugin>

            <!-- Maven Compiler Plugin -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.11.0</version>
                <configuration>
                    <source>17</source>
                    <target>17</target>
                    <encoding>UTF-8</encoding>
                    <parameters>true</parameters>
                </configuration>
            </plugin>
        </plugins>

        <!-- 资源文件配置 -->
        <resources>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.yml</include>
                    <include>**/*.properties</include>
                    <include>**/*.xml</include>
                </includes>
                <filtering>false</filtering>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <includes>
                    <include>**/*.yml</include>
                    <include>**/*.properties</include>
                    <include>**/*.xml</include>
                </includes>
                <filtering>false</filtering>
            </resource>
        </resources>
        <!--        <pluginManagement>-->
        <!--            <plugins>-->
        <!--                <plugin>-->
        <!--                    <groupId>io.fabric8</groupId>-->
        <!--                    <artifactId>docker-maven-plugin</artifactId>-->
        <!--                    <version>${docker.maven.plugin.version}</version>-->
        <!--                    <executions>-->
        <!--                        &lt;!&ndash;如果想在项目打包时构建镜像添加&ndash;&gt;-->
        <!--                        <execution>-->
        <!--                            <id>build-image</id>-->
        <!--                            <phase>package</phase>-->
        <!--                            <goals>-->
        <!--                                <goal>build</goal>-->
        <!--                                <goal>push</goal>-->
        <!--                            </goals>-->
        <!--                        </execution>-->
        <!--                    </executions>-->
        <!--                    <configuration>-->
        <!--                        <dockerHost>${docker.host}</dockerHost>-->
        <!--                        <certPath>./config/docker-cert</certPath>-->
        <!--                        &lt;!&ndash; Docker 推送镜像仓库地址&ndash;&gt;-->
        <!--                        <pushRegistry>registry-vpc.cn-shenzhen.aliyuncs.com</pushRegistry>-->
        <!--                        &lt;!&ndash;认证配置,用于私有registry认证&ndash;&gt;-->
        <!--                        <authConfig>-->
        <!--                            <username>深圳市微嗒科技有限公司</username>-->
        <!--                            <password>vida@2021</password>-->
        <!--                        </authConfig>-->

        <!--                        <images>-->
        <!--                            <image>-->
        <!--                                &lt;!&ndash;定义镜像名称&ndash;&gt;-->
        <!--                                <name>-->
        <!--                                    registry-vpc.cn-shenzhen.aliyuncs.com/${docker.env}/${project.artifactId}:${project.version}-->
        <!--                                </name>-->
        <!--                                &lt;!&ndash;定义镜像构建行为&ndash;&gt;-->
        <!--                                <build>-->
        <!--                                    &lt;!&ndash;定义基础镜像&ndash;&gt;-->
        <!--                                    <from>java:8</from>-->
        <!--                                    <args>-->
        <!--                                        <JAR_FILE>${project.build.finalName}.jar</JAR_FILE>-->
        <!--                                    </args>-->
        <!--                                    &lt;!&ndash;定义哪些文件拷贝到容器中&ndash;&gt;-->
        <!--                                    <assembly>-->
        <!--                                        &lt;!&ndash;定义拷贝到容器的目录&ndash;&gt;-->
        <!--                                        <targetDir>/</targetDir>-->
        <!--                                        &lt;!&ndash;只拷贝生成的jar包&ndash;&gt;-->
        <!--                                        <descriptorRef>artifact</descriptorRef>-->
        <!--                                    </assembly>-->
        <!--                                    &lt;!&ndash;定义容器启动命令&ndash;&gt;-->
        <!--                                    <entryPoint>-->
        <!--                                        ["sh", "-c", "java $JAVA_OPTS -jar -Dspring.profiles.active=dev-->
        <!--                                        /${project.build.finalName}.jar"]-->
        <!--                                    </entryPoint>-->
        <!--                                    &lt;!&ndash;定义维护者&ndash;&gt;-->
        <!--                                    <maintainer>wangpf</maintainer>-->
        <!--                                </build>-->
        <!--                            </image>-->
        <!--                        </images>-->
        <!--                    </configuration>-->
        <!--                </plugin>-->
        <!--            </plugins>-->
        <!--        </pluginManagement>-->
    </build>
    <repositories>
        <repository>
            <id>aliyun-private</id>  <!-- 这个id必须与settings.xml中的server id一致 -->
            <url>https://packages.aliyun.com/60ab6782b8301d20d58b293b/maven/2105509-snapshot-joji3g</url>
        </repository>
    </repositories>
</project>
