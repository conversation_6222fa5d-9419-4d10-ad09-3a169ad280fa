<?xml version="1.0" encoding="UTF-8"?>
<settings xmlns="http://maven.apache.org/SETTINGS/1.0.0"
          xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
          xsi:schemaLocation="http://maven.apache.org/SETTINGS/1.0.0 
                              http://maven.apache.org/xsd/settings-1.0.0.xsd">

    <!-- CI/CD 专用配置 - 简化版本 -->
    
    <!-- 服务器认证配置 -->
    <servers>
        <!-- 阿里云私有仓库认证 -->
        <server>
            <id>vida-sls</id>
            <username>${env.ALIYUN_USERNAME}</username>
            <password>${env.ALIYUN_PASSWORD}</password>
        </server>
        
        <server>
            <id>aliyun-private</id>
            <username>${env.ALIYUN_USERNAME}</username>
            <password>${env.ALIYUN_PASSWORD}</password>
        </server>
    </servers>

    <!-- 镜像配置 - 使用阿里云镜像加速 -->
    <mirrors>
        <mirror>
            <id>aliyunmaven</id>
            <name>阿里云公共仓库</name>
            <url>https://maven.aliyun.com/repository/public</url>
            <mirrorOf>central</mirrorOf>
        </mirror>
    </mirrors>

    <!-- 配置文件 -->
    <profiles>
        <profile>
            <id>ci</id>
            <repositories>
                <!-- vida-sls 私有仓库 -->
                <repository>
                    <id>vida-sls</id>
                    <name>Vida SLS Private Repository</name>
                    <url>https://packages.aliyun.com/60ab6782b8301d20d58b293b/maven/2105509-snapshot-joji3g</url>
                    <releases>
                        <enabled>true</enabled>
                        <updatePolicy>always</updatePolicy>
                    </releases>
                    <snapshots>
                        <enabled>true</enabled>
                        <updatePolicy>always</updatePolicy>
                    </snapshots>
                </repository>
                
                <!-- 阿里云公共仓库 -->
                <repository>
                    <id>aliyun-public</id>
                    <name>阿里云公共仓库</name>
                    <url>https://maven.aliyun.com/repository/public</url>
                    <releases>
                        <enabled>true</enabled>
                    </releases>
                    <snapshots>
                        <enabled>false</enabled>
                    </snapshots>
                </repository>
            </repositories>
            
            <pluginRepositories>
                <pluginRepository>
                    <id>aliyun-plugin</id>
                    <name>阿里云插件仓库</name>
                    <url>https://maven.aliyun.com/repository/public</url>
                    <releases>
                        <enabled>true</enabled>
                    </releases>
                    <snapshots>
                        <enabled>false</enabled>
                    </snapshots>
                </pluginRepository>
            </pluginRepositories>
            
            <!-- CI环境属性 -->
            <properties>
                <maven.test.skip>true</maven.test.skip>
                <maven.javadoc.skip>true</maven.javadoc.skip>
                <maven.source.skip>false</maven.source.skip>
            </properties>
        </profile>
    </profiles>

    <!-- 激活CI配置 -->
    <activeProfiles>
        <activeProfile>ci</activeProfile>
    </activeProfiles>

</settings>
