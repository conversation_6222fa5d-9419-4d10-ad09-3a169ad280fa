FROM harbor.xpyun.net/base/maven:3.8.4-openjdk-17 as builder
WORKDIR /apps
COPY . .
RUN --mount=type=cache,target=/data/maven_repo \
    cat settings.xml > /usr/share/maven/conf/settings.xml && \
    mvn clean install -q -Dmaven.test.skip=true

FROM harbor.xpyun.net/base/eclipse-temurin:17-jdk
EXPOSE 30001
ARG MOD_NAME
ENV TZ "Asia/Shanghai"
ENV MOD_NAME "${MOD_NAME}"
WORKDIR  /apps
COPY --from=builder /apps/target/${MOD_NAME}.jar /apps/
COPY --from=builder /apps/target/resources /apps/resources
COPY --from=builder /apps/target/lib /apps/lib
ENTRYPOINT ["sh","-c","java -jar -XX:MaxHeapSize=2048M ${MOD_NAME}.jar --spring.profiles.active=dev"]