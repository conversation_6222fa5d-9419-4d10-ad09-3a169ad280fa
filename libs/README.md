# 本地依赖包说明

## vida-sls-starter

这是一个自定义的SLS（Simple Log Service）日志服务启动器，用于集成阿里云日志服务。

### 文件信息
- **文件名**: `vida-sls-starter-1.0.3-SNAPSHOT.jar`
- **版本**: `1.0.3-SNAPSHOT`
- **用途**: 提供SLS日志服务的自动配置和客户端管理

### 使用说明

1. **自动包含**: 该jar包已经配置在项目的pom.xml中，使用`system`作用域
2. **无需安装**: 其他开发者拉取代码后可以直接使用，无需额外安装
3. **版本控制**: 该jar包已提交到Git仓库，确保团队成员使用相同版本

### 配置示例

在Nacos配置中心配置SLS相关参数：

```yaml
snaptag:
  sls:
    endpoint: https://cn-shenzhen.log.aliyuncs.com
    access-key: YOUR_ACCESS_KEY
    access-key-secret: YOUR_ACCESS_KEY_SECRET
    projects:
      business:
        project: your-project-name
        logstore: your-logstore-name
```

### 功能特性

- ✅ 自动配置SLS客户端
- ✅ 支持多项目多日志库配置
- ✅ 提供AOP日志拦截器
- ✅ 集成Spring Boot Actuator健康检查
- ✅ 支持异步日志发送
- ✅ 提供指标收集功能

### 注意事项

- 确保该jar包文件存在于`libs/`目录中
- 如果更新jar包版本，需要同时更新pom.xml中的版本号和文件名
- 该依赖使用`system`作用域，在打包时会自动包含到最终的jar包中
