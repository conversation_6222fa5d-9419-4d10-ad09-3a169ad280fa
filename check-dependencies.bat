@echo off
echo ========================================
echo SnapTag项目依赖检查
echo ========================================

echo.
echo 检查本地依赖包...

if exist "libs\vida-sls-starter-1.0.3-SNAPSHOT.jar" (
    echo ✅ vida-sls-starter-1.0.3-SNAPSHOT.jar 存在
) else (
    echo ❌ vida-sls-starter-1.0.3-SNAPSHOT.jar 不存在
    echo.
    echo 请确保以下文件存在于libs目录中:
    echo   - libs\vida-sls-starter-1.0.3-SNAPSHOT.jar
    echo.
    echo 如果文件缺失，请联系项目维护者获取。
    pause
    exit /b 1
)

echo.
echo 检查Maven配置...
mvn dependency:resolve-sources -q
if %errorlevel% equ 0 (
    echo ✅ Maven依赖解析成功
) else (
    echo ❌ Maven依赖解析失败
    echo 请检查网络连接和Maven配置
)

echo.
echo ========================================
echo 依赖检查完成
echo ========================================
echo.
echo 现在可以运行以下命令启动项目:
echo   mvn spring-boot:run
echo.
pause
