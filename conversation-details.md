# SnapTag项目Nacos配置中心详细对话记录

## 对话时间：2025年7月15日

---

## 1. Spring Cloud Nacos迁移

### 用户请求
> 现在添加dependencyManagement

### 实施过程
1. **添加Spring Cloud依赖管理**
   ```xml
   <dependencyManagement>
       <dependencies>
           <!-- Spring Cloud Dependencies -->
           <dependency>
               <groupId>org.springframework.cloud</groupId>
               <artifactId>spring-cloud-dependencies</artifactId>
               <version>${spring-cloud.version}</version>
               <type>pom</type>
               <scope>import</scope>
           </dependency>
           
           <!-- Spring Cloud Alibaba Dependencies -->
           <dependency>
               <groupId>com.alibaba.cloud</groupId>
               <artifactId>spring-cloud-alibaba-dependencies</artifactId>
               <version>${spring-cloud-alibaba.version}</version>
               <type>pom</type>
               <scope>import</scope>
           </dependency>
       </dependencies>
   </dependencyManagement>
   ```

2. **修改bootstrap.yml配置**
   ```yaml
   spring:
     application:
       name: SnapTag
     profiles:
       active: dev
     cloud:
       nacos:
         config:
           server-addr: localhost:8848
           namespace: 93d46b33-48c9-4d59-8204-147c093992b0
           group: DEFAULT_GROUP
           file-extension: yml
           refresh-enabled: true
   ```

3. **修改主应用类**
   - 移除`@NacosPropertySource`注解
   - 添加`@EnableDiscoveryClient`注解

4. **创建Spring Cloud测试控制器**
   - 使用`@Value`替代`@NacosValue`
   - 添加`@RefreshScope`支持配置刷新

### 遇到的问题
- 编译失败，找不到Spring Cloud相关类
- Nacos配置文件为空

---

## 2. Nacos服务端版本问题

### 用户反馈
> 文件已经创建了，是否跟nacos服务端版本有关，当前nacos服务端版本为1.1.4

### 问题分析
- Nacos 1.1.4是2019年的版本
- Spring Cloud Alibaba 2021.0.5.0是2022年的版本
- 存在API版本差异和协议兼容性问题

### 解决方案尝试
1. **降级Spring Cloud Alibaba版本**
   ```xml
   <spring-cloud.version>2020.0.6</spring-cloud.version>
   <spring-cloud-alibaba.version>2.2.10.RELEASE</spring-cloud-alibaba.version>
   ```

2. **简化bootstrap.yml配置**
   ```yaml
   spring:
     cloud:
       nacos:
         config:
           server-addr: localhost:8848
           namespace: 93d46b33-48c9-4d59-8204-147c093992b0
           group: DEFAULT_GROUP
           file-extension: yml
           refresh: true
           prefix: ${spring.application.name}
   ```

---

## 3. 切换到Nacos 2.2.2服务端

### 用户提供
> 我有另外一个nacos服务端，版本是2.2.2，访问地址是localhost:8849,namespace 是1089c610-758b-4fb4-b26b-855d168ee9ca

### 实施过程
1. **更新bootstrap.yml配置**
   ```yaml
   spring:
     cloud:
       nacos:
         config:
           server-addr: localhost:8849
           namespace: 1089c610-758b-4fb4-b26b-855d168ee9ca
           group: DEFAULT_GROUP
           file-extension: yml
           refresh-enabled: true
   ```

2. **恢复最新的Spring Cloud Alibaba版本**
   ```xml
   <spring-cloud.version>2021.0.8</spring-cloud.version>
   <spring-cloud-alibaba.version>2021.0.5.0</spring-cloud-alibaba.version>
   ```

### 遇到的问题
- gRPC协议连接失败：`Connection refused: localhost/[0:0:0:0:0:0:0:1]:9849`
- 配置文件仍然为空

### 问题根源
- Spring Cloud Nacos 2.0.4客户端默认使用gRPC协议（端口9849）
- Nacos 2.2.2服务端可能没有启用gRPC端口

---

## 4. 回退到Spring Boot版本

### 用户决定
> 帮我回退到Spring-boot版本

### 实施过程
1. **恢复pom.xml依赖**
   ```xml
   <dependency>
       <groupId>com.alibaba.boot</groupId>
       <artifactId>nacos-config-spring-boot-starter</artifactId>
       <version>0.2.12</version>
   </dependency>
   ```

2. **恢复主应用类**
   ```java
   @SpringBootApplication
   @EnableScheduling
   @NacosPropertySource(
       dataId = "${nacos.config.data-id:SnapTag}", 
       autoRefreshed = true,
       first = true
   )
   ```

3. **恢复application.yml配置**
   ```yaml
   nacos:
     config:
       server-addr: localhost:8848
       data-id: SnapTag
       group: DEFAULT_GROUP
       type: YAML
       auto-refresh: true
       namespace: 93d46b33-48c9-4d59-8204-147c093992b0
       bootstrap:
         enable: true
         log-enable: true
       enable-remote-sync-config: true
       override-none: false
       override-system-properties: false
   ```

4. **恢复NacosTestController**
   - 使用`@NacosValue`注解

---

## 5. 配置文件名称问题

### 用户反馈
> 我在Nacos中创建的配置文件已经更改为SnapTag-dev.yml

### 解决过程
1. **修改application-dev.yml中的data-id**
   ```yaml
   nacos:
     config:
       data-id: ${NACOS_CONFIG_DATA_ID:SnapTag-dev.yml}
   ```

2. **解决端口冲突**
   - 发现30001端口被占用
   - 使用`taskkill /F /PID 7764`终止占用进程

---

## 6. 本地依赖问题

### 用户需求
> vida-sls-starter 这个依赖是本地的，我想要让别人拉去这个项目的代码之后也可以直接使用，而不需要我去给他这个依赖包，要怎么做

### 解决方案
1. **创建libs目录**
   ```bash
   mkdir libs
   ```

2. **修改pom.xml使用本地jar包**
   ```xml
   <dependency>
       <groupId>com.vida</groupId>
       <artifactId>vida-sls-starter</artifactId>
       <version>1.0.3-SNAPSHOT</version>
       <scope>system</scope>
       <systemPath>${project.basedir}/libs/vida-sls-starter-1.0.3-SNAPSHOT.jar</systemPath>
   </dependency>
   ```

3. **创建说明文档和检查脚本**
   - libs/README.md
   - check-dependencies.bat

### 遇到的问题
> 好像报错了，你install一下

- 编译失败，找不到SLS相关类
- 本地jar包损坏（只有1673字节）

### 最终解决
- 改回使用Maven仓库依赖
- 成功编译和安装

---

## 7. 最终成功状态

### 应用启动成功
```
Started SnapTagApplication in 6.358 seconds (JVM running for 6.717)
【【【【【【 Snap&Tag 微服务 】】】】】】已启动...
```

### 关键功能正常
- ✅ Nacos配置正确加载：`dataId='SnapTag-dev.yml'`
- ✅ 数据库连接成功：`HikariPool-1 - Start completed.`
- ✅ Redis连接成功：`Connent **************:6988 Redis is Success...`
- ✅ SLS日志服务成功：`SLS客户端初始化成功！`
- ✅ 健康检查正常：`status: UP`

---

## 8. 最终配置总结

### Nacos配置
- **服务端**：`localhost:8848` (Nacos 1.1.4)
- **配置文件**：`SnapTag-dev.yml`
- **命名空间**：`93d46b33-48c9-4d59-8204-147c093992b0`
- **配置覆盖**：✅ 正常工作

### 应用状态
- **端口**：`30001`
- **状态**：`UP`
- **启动时间**：`6.358秒`

### 关于本地依赖
- 项目使用Maven仓库中的`vida-sls-starter-1.0.3-SNAPSHOT`
- 功能完全正常，SLS日志服务正常工作

---

## 9. 最终建议

- **当前状态**：nacos-config-spring-boot-starter版本完全满足需求
- **建议**：保持当前版本，专注于业务功能开发
- **未来规划**：当需要更多Spring Cloud功能时再考虑迁移

**项目已完全正常运行，实现了配置的集中管理和动态更新功能！**

---

## 10. 技术要点总结

### Spring Boot vs Spring Cloud Nacos对比

| 特性 | nacos-config-spring-boot-starter | Spring Cloud Nacos |
|------|----------------------------------|---------------------|
| **配置方式** | `@NacosValue` + `@NacosPropertySource` | `@Value` + `@RefreshScope` |
| **配置文件** | 单一配置文件 | 多配置文件支持 |
| **生态集成** | 基础集成 | 完整Spring Cloud生态 |
| **服务发现** | 需要额外配置 | 内置支持 |
| **配置刷新** | 自动刷新 | 手动/自动刷新 |
| **版本兼容** | 兼容多版本Nacos | 需要精确版本匹配 |

### 关键配置项说明

#### nacos-config-spring-boot-starter配置
```yaml
nacos:
  config:
    server-addr: localhost:8848                    # Nacos服务端地址
    data-id: SnapTag-dev.yml                      # 配置文件ID
    group: DEFAULT_GROUP                          # 配置分组
    type: YAML                                    # 配置格式
    auto-refresh: true                            # 自动刷新
    namespace: 93d46b33-48c9-4d59-8204-147c093992b0  # 命名空间
    enable-remote-sync-config: true               # 启用远程同步
    override-none: false                          # 允许覆盖
    override-system-properties: false             # 不覆盖系统属性
```

#### Spring Cloud Nacos配置
```yaml
spring:
  cloud:
    nacos:
      config:
        server-addr: localhost:8849
        namespace: 1089c610-758b-4fb4-b26b-855d168ee9ca
        group: DEFAULT_GROUP
        file-extension: yml
        refresh-enabled: true
        extension-configs:
          - data-id: SnapTag.yml
            group: DEFAULT_GROUP
            refresh: true
        shared-configs:
          - data-id: common.yml
            group: DEFAULT_GROUP
            refresh: true
```

### 问题排查经验

1. **版本兼容性问题**
   - 检查Nacos服务端版本与客户端版本兼容性
   - 注意gRPC协议支持情况

2. **配置文件匹配问题**
   - 确保data-id与Nacos中的配置文件名完全匹配
   - 注意命名空间配置

3. **端口冲突问题**
   - 使用`netstat -ano | findstr :端口号`检查端口占用
   - 使用`taskkill /F /PID 进程ID`终止占用进程

4. **本地依赖问题**
   - system作用域需要确保jar包完整性
   - Maven仓库依赖更稳定可靠

### 最佳实践建议

1. **选择合适的版本**
   - 稳定需求：使用nacos-config-spring-boot-starter
   - 复杂微服务：考虑Spring Cloud Nacos

2. **配置管理**
   - 使用环境变量支持多环境部署
   - 配置敏感信息加密

3. **监控和日志**
   - 启用Nacos配置变更日志
   - 集成健康检查端点

4. **团队协作**
   - 统一Nacos服务端版本
   - 文档化配置文件结构
