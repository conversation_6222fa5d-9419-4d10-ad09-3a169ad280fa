package net.snaptag.system.business.buservice;

import cn.hutool.core.date.DatePattern;
import net.snaptag.system.account.buservice.UserAccountAndInfoBuService;
import net.snaptag.system.account.buservice.UserInfoBuService;
import net.snaptag.system.account.dto.UserLoginDto;
import net.snaptag.system.business.dao.UserFeedbackDao;
import net.snaptag.system.business.dto.*;
import net.snaptag.system.business.entity.UserFeedback;
import net.snaptag.system.business.enums.FeedBackPrinterTypeEnums;
import net.snaptag.system.business.enums.FeedBackProblemTypeEnums;
import net.snaptag.system.business.enums.MsgColumnTypeEnums;
import net.snaptag.system.business.utils.ToolUtils;
import net.snaptag.system.sadais.core.common.core.ToolsConst;
import net.snaptag.system.sadais.core.common.enums.ExceptionEnums;
import net.snaptag.system.sadais.core.common.exception.ServiceException;
import net.snaptag.system.sadais.mongo.common.Page;
import net.snaptag.system.sadais.util.core.ToolsKit;
import net.snaptag.system.sadais.web.core.I18nUtils;
import net.snaptag.system.sadais.web.enums.MsgTypeEnums;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.*;

@Service
public class UserFeedbackBuService {

    @Autowired
    private MsgCenterBuService msgCenterBuService;
    @Autowired
    private UserAccountAndInfoBuService userAccountAndInfoBuServiced;
    @Autowired
    private I18nUtils i18nUtils;

    @Autowired
    private UserFeedbackDao userFeedbackDao;
    private static final String SOFTWARE_TYPE = "software";
    private static final String HARDWARE_TYPE = "hardware";
    private static SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    public UserFeedbackVo getUserFeedbackList(String userId, int page, int pageSize) {
        page = page - 1;
        if (page < 0) {
            page = 0;
        }
        List<UserFeedback> lists = userFeedbackDao.findUserFeedbackList(userId, page, pageSize);
        UserFeedbackVo feedbackVo = new UserFeedbackVo(new ArrayList<>(), new ArrayList<>());
        if (lists != null) {
            for (UserFeedback item:lists) {
                UserFeedbackItem feedbackItem = new UserFeedbackItem(item.getUserId(), item.getType(), item.getQtype(), item.getContent(), item.getImages(), item.getResult());
                if (item.getCreatetime() != null) {
                    feedbackItem.setCreateTime(sdf.format(item.getCreatetime()));
                }
                feedbackItem.setMobile(item.getMobile());

                if (item.getType().equals(SOFTWARE_TYPE)) {
                    feedbackVo.getSoftwares().add(feedbackItem);
                } else if (item.getType().equals(HARDWARE_TYPE)) {
                    feedbackVo.getHardwares().add(feedbackItem);
                }
            }
        }
        return feedbackVo;
    }

    public List<UserFeedbackItem> getUserFeedbackList2(String userId, int page, int pageSize, Locale locale) {
        page = page - 1;
        if (page < 0) {
            page = 0;
        }
        List<UserFeedback> lists = userFeedbackDao.findUserFeedbackList(userId, page, pageSize);
        List<UserFeedbackItem> result = new ArrayList<>();



        // userId, msgType, Integer.parseInt(pageNo), Integer.parseInt(pageSize), lastId, locale, headInfoDto.getVersion())
        List<MsgCenterDto> msgList = msgCenterBuService.getMsgCenterDtoList(userId, String.valueOf(MsgColumnTypeEnums.FEEDBACK.getType()),1, 99, null, null, "");
        // msgCenterBuService.getMsgListByType(userId, MsgColumnTypeEnums.FEEDBACK.getType(),1, 99);
        Map<String, MsgCenterDto> noReadSsgMap = new HashMap<>();
        if (ToolsKit.isNotEmpty(msgList)){
            msgList.forEach(item -> {
                if (ToolsKit.isNotEmpty(item.getParam()) && ToolsKit.isNotEmpty(item.getParam().getId())){
                    if (noReadSsgMap.get(item.getParam().getId())==null){
                        noReadSsgMap.put(item.getParam().getId(), item);
                    } else {
                        MsgCenterDto temp = noReadSsgMap.get(item.getParam().getId());
                        if (temp.getMsgTime().getTime()<item.getMsgTime().getTime()){
                            noReadSsgMap.put(item.getParam().getId(), item);
                        }
                    }
                }
            });
        }

//        UserFeedbackVo feedbackVo = new UserFeedbackVo(new ArrayList<>(), new ArrayList<>());
        if (lists != null) {
            for (UserFeedback item:lists) {
                UserFeedbackItem feedbackItem = new UserFeedbackItem(item.getUserId(), item.getType(), item.getQtype(), item.getContent(), item.getImages(), item.getResult());
                if (item.getCreatetime() != null) {
                    feedbackItem.setCreateTime(sdf.format(item.getCreatetime()));
                }
                feedbackItem.setMobile(item.getMobile());
                feedbackItem.setPrinterType(item.getPrinterType());
                feedbackItem.setQtypeName(getQtypeName(feedbackItem.getQtype(), locale));

                // 设置是否已读
                if (noReadSsgMap.get(item.getId())!=null){
                    feedbackItem.setIsRead(noReadSsgMap.get(item.getId()).getIsRead());
                    feedbackItem.setMessage(noReadSsgMap.get(item.getId()).getMsgContent());
                    feedbackItem.setMid(noReadSsgMap.get(item.getId()).getId());
                    feedbackItem.setId(item.getId());
                } else {
                    feedbackItem.setIsRead(1);
                }

                result.add(feedbackItem);
            }
        }
        return result;
    }

    private String getQtypeName(String qtype, Locale locale) {
        if (ToolsKit.isEmpty(qtype)){
            return "";
        }
        String [] array = qtype.split("/");
        String [] arrayQtname = new String[array.length];
        StringBuffer sb = new StringBuffer("");
        for (int i=0; i<array.length; i++){
            if (FeedBackProblemTypeEnums.getMap().get(array[i])!=null){
                FeedBackProblemTypeEnums enums = FeedBackProblemTypeEnums.getMap().get(array[i]);
                String descValue = i18nUtils.getKey(enums.getLanguageCode(), locale);
                if (ToolsKit.isEmpty(descValue)){
                    arrayQtname[i] = enums.getDesc();
                } else {
                    arrayQtname[i] = descValue;
                }
            }
        }
        return StringUtils.join(arrayQtname, ",");
    }

    public Map<String, String> addUserFeedback(String userId, String type, String qtype, String content, List<String> images, String errorMsg, String mobile,String printerType, String clientInfo) {
        UserFeedback userFeedback = new UserFeedback();
        ToolsKit.setIdEntityData(userFeedback, userId);
        userFeedback.setUserId(userId);
        userFeedback.setType(type);
        userFeedback.setQtype(qtype);
        userFeedback.setContent(content);
        userFeedback.setImages(images);
        userFeedback.setCreatetime(new Date());
        userFeedback.setErrormsg(errorMsg);
        userFeedback.setMobile(mobile);
        userFeedback.setClientInfo(clientInfo);
        userFeedback.setPrinterType(printerType);
        userFeedback.setCreatetime(new Date());
        UserLoginDto userLoginDto = userAccountAndInfoBuServiced.getLoginInfo(userId);

        if (userLoginDto != null && userLoginDto.getUserInfoDto() != null) {
            userFeedback.setUserNo(userLoginDto.getUserInfoDto().getCodeId());
        }
        userFeedbackDao.saveEntity(userFeedback);

        Map<String, String> map = new HashMap<String, String>();
        map.put("id", userFeedback.getId());
        return map;
    }

    /**
     * 获取用户的反馈分页列表
     *
     * @param pageNo - 当前页码
     * @param pageSize - 每页大小
     * @return
     */
    public Page<UserFeedback> findUserFeedbackPage(String startDate, String endDate, int pageNo, int pageSize) {
        Date start = null;
        Date end = null;
        if (ToolsKit.isNotEmpty(startDate)) {
            start = ToolsKit.Date.parse(startDate + ToolsConst.START_TIME, DatePattern.NORM_DATETIME_PATTERN);
        }
        if (ToolsKit.isNotEmpty(endDate)) {
            end = ToolsKit.Date.parse(endDate + ToolsConst.END_TIME, DatePattern.NORM_DATETIME_PATTERN);
        }
        return userFeedbackDao.findUserFeedbackPage(start, end, pageNo, pageSize);
    }

    /**
     * 根据记录ID获取用户的反馈
     * @param id - 反馈ID
     * @param mid - 消息ID
     * @return
     */
    public UserFeedbackItem getById(String id, String mid) {
        if (ToolsKit.isEmpty(id)) {
            throw new ServiceException("反馈ID不能为空");
        }
        if (ToolsKit.isEmpty(mid)) {
            throw new ServiceException("消息ID不能为空");
        }
        // 标识反馈已读状态
//        msgCenterService.read(mid);
        UserFeedback feedback = userFeedbackDao.getById(id, ToolsConst.DATA_SUCCESS_STATUS);
        UserFeedbackItem feedbackVo = new UserFeedbackItem();
        if (feedback != null) {
            feedbackVo.setMobile(feedback.getMobile());
            feedbackVo.setContent(feedback.getContent());
            feedbackVo.setCreateTime(sdf.format(feedback.getCreatetime()));
            feedbackVo.setImages(feedback.getImages());
            feedbackVo.setQtype(feedback.getQtype());
            feedbackVo.setResult(feedback.getResult());
            feedbackVo.setType(feedback.getType());
            feedbackVo.setUserId(feedback.getUserId());
            feedbackVo.setPrinterType(feedback.getPrinterType());
            feedbackVo.setClientInfo(feedback.getClientInfo());
        }
        return feedbackVo;
    }

    public UserFeedback getById(String id) {
        if (ToolsKit.isEmpty(id)) {
            throw new ServiceException("反馈ID不能为空");
        }
        return userFeedbackDao.getById(id, ToolsConst.DATA_SUCCESS_STATUS);
    }

    /**
     * 保存
     *
     * @param feedback
     */
    public void save(UserFeedback feedback) {
        userFeedbackDao.saveEntity(feedback);
    }

    /**
     * 删除用户的反馈
     * @param id
     */
    public void delById(String id) {
        userFeedbackDao.removeById(id);
    }

    /**
     * 评论用户的反馈
     * @param id - 反馈ID
     * @param remark - 评论信息
     * @throws ServiceException
     */
    public void update(String id, String remark) throws ServiceException {
        if (ToolsKit.isEmpty(id)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("反馈ID不能为空");
        }
        if (ToolsKit.isEmpty(remark)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("评论信息不能为空");
        }

        UserFeedback feedback = getById(id);
        if (feedback != null) {
            feedback.setResult(remark);
            feedback.setUpdatetime(new Date());
            save(feedback);

            // 发送反馈回复消息
            SendMsgDto sendMsgDto = new SendMsgDto();
            sendMsgDto.setMsgContent(ToolUtils.replaceHtml(remark));
            sendMsgDto.setMsgType(MsgColumnTypeEnums.FEEDBACK.getType());
            sendMsgDto.setMsgSubType(MsgTypeEnums.NO_JUMP.getType());
            sendMsgDto.setSenderUserId(feedback.getUserId());
            sendMsgDto.setReceiverUserId(feedback.getUserId());
            MsgCenterParamDto param = new MsgCenterParamDto();
            param.setId(feedback.getId());
            sendMsgDto.setParam(param);
            msgCenterBuService.sendMsg(sendMsgDto);
        }
    }

    public List<Map<String, String>> getPrinterType(Locale locale) {
        List<Map<String, String>> result = new ArrayList<>();
        for (FeedBackPrinterTypeEnums enums: FeedBackPrinterTypeEnums.values()) {
            Map<String, String> temp = new HashMap<>();
            temp.put("picUrl", enums.getPicUrl());
//            temp.put("name", enums.getKey());
            if ("其他".equals(enums.getKey())){
                temp.put("name", i18nUtils.getKey("feedback_problem_other", locale));
            } else {
                temp.put("name", enums.getKey());
            }
            result.add(temp);
        }
        return result;
    }

    public List<Map<String, String>> getProblemType(Locale locale) {
        List<Map<String, String>> result = new ArrayList<>();
//        System.out.println("locale.toString()="+locale);
        for (FeedBackProblemTypeEnums enums: FeedBackProblemTypeEnums.values()) {
            Map<String, String> temp = new HashMap<>();
            temp.put("type", enums.getType());
            temp.put("qtype", enums.getCode());
//            temp.put("desc", enums.getDesc());
            String descValue = i18nUtils.getKey(enums.getLanguageCode(), locale);
            if (ToolsKit.isEmpty(descValue)){
                temp.put("desc", enums.getDesc());
            } else {
                temp.put("desc", descValue);
            }
            result.add(temp);
        }
        return result;
    }
}