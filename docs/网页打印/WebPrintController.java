package net.snaptag.system.business.controller;

import net.snaptag.system.business.vo.WebGroupSortVo;
import net.snaptag.system.business.buservice.WebPrintBuService;
import net.snaptag.system.business.utils.Constant;
import net.snaptag.system.business.vo.WebPagePrintVo;
import net.snaptag.system.sadais.core.common.enums.ExceptionEnums;
import net.snaptag.system.sadais.core.common.exception.ServiceException;
import net.snaptag.system.sadais.util.core.ToolsKit;
import net.snaptag.system.sadais.web.common.JsonKit;
import net.snaptag.system.sadais.web.core.BaseController;
import net.snaptag.system.sadais.web.dto.HeadInfoDto;
import net.snaptag.system.sadais.web.dto.ReturnDto;
import net.snaptag.system.sadais.web.utils.WebTools;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Locale;

/**
 * 网页打印控制器类
 */
@RestController
@RequestMapping(Constant.PRE_MAPPING_URL + "/webprint")
public class WebPrintController extends BaseController {

    @Autowired
    private WebPrintBuService webPrintBuService;
    /**
     * 删除网站
     * @return
     */
    @RequestMapping(value = "/delWebSite")
    public ReturnDto delWebSite() {
        try {
            String userId = this.getValue("userId");
            String groupId = this.getValue("groupId");
            String siteId = this.getValue("siteId");
            if (StringUtils.isBlank(userId) || StringUtils.isBlank(groupId) || StringUtils.isBlank(siteId)) {
                return this.returnSuccessJson(ExceptionEnums.PARAM_ERROR, "参数不能为空");
            }

            webPrintBuService.delWebSite(userId, groupId, Integer.parseInt(siteId));

            return this.returnSuccessJson(ExceptionEnums.SUCCESS, siteId);
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    /**
     * 网站排序
     * @return
     */
    @RequestMapping(value = "/sortWebSites")
    public ReturnDto sortWebSites() {
        try {
            String userId = this.getValue("userId");
            /*String groupId = this.getValue("groupId");
            String siteIds = this.getValue("siteIds");
            if (StringUtils.isBlank(userId) || StringUtils.isBlank(groupId) || StringUtils.isBlank(siteIds)) {
                return this.returnSuccessJson(ExceptionEnums.PARAM_ERROR, "参数不能为空");
            }

            webPrintBuService.sortWebSites(userId, groupId, siteIds);*/

            String strGroupList = this.getValue("groupList");
            if (ToolsKit.isEmpty(strGroupList)) {
                throw new ServiceException("网站分组集合不能为空");
            }
            List<WebGroupSortVo> groupList = JsonKit.jsonParseArray(strGroupList, WebGroupSortVo.class);
            if (groupList != null && groupList.size() > 0) {
                for (WebGroupSortVo sortVo:groupList) {
                    if (StringUtils.isBlank(sortVo.getGroupId()) || StringUtils.isBlank(sortVo.getSiteIds())) {
                        continue;
                    }
                    webPrintBuService.sortWebSites(userId, sortVo.getGroupId(), sortVo.getSiteIds());
                }
            }

            return this.returnSuccessJson(ExceptionEnums.SUCCESS, "");
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    /**
     * 获取所有分组和网站
     * @return
     */
//    @RequestMapping(value = "/getWebSites")
//    public ReturnDto getWebSites() {
//        try {
////            String userId = this.getValue("userId");
////            HeadInfoDto headInfoDto = this.getHeadInfoDto();
////            if (!headInfoDto.getOverseas() && StringUtils.isBlank(userId)) {
////                return this.returnSuccessJson(ExceptionEnums.PARAM_ERROR, "参数不能为空");
////            }
//
////            Locale locale = WebTools.getLocaleByLanguage(headInfoDto.getLanguage());
//            return this.returnSuccessJson(ExceptionEnums.SUCCESS, webPrintBuService.getWebPrintList(userId, headInfoDto.getOverseas(), locale));
//        } catch (ServiceException e) {
//            return this.returnFailJson(e);
//        } catch (Exception e) {
//            e.printStackTrace();
//            return this.returnFailJson(e);
//        }
//    }

//    @RequestMapping(value = "/getDefaultWebSites")
//    public ReturnDto getDefaultWebSites() {
//        try {
//            String userId = this.getValue("userId");
//            HeadInfoDto headInfoDto = this.getHeadInfoDto();
////            if (!headInfoDto.getOverseas() && StringUtils.isBlank(userId)) {
////                return this.returnSuccessJson(ExceptionEnums.PARAM_ERROR, "参数不能为空");
////            }
//
//            Locale locale = WebTools.getLocaleByLanguage(headInfoDto.getLanguage());
//            return this.returnSuccessJson(ExceptionEnums.SUCCESS, webPrintBuService.getWebPrintListA4(userId, true, locale));
//        } catch (ServiceException e) {
//            return this.returnFailJson(e);
//        } catch (Exception e) {
//            e.printStackTrace();
//            return this.returnFailJson(e);
//        }
//    }

    /**
     * 初始化默认分组和网站
     * @return
     */
//    @RequestMapping(value = "/initBasicData")
//    public ReturnDto initBasicData() {
//        try {
//            String userId = this.getValue("userId");
//            String secretKey = this.getValue("secretKey");
//            if (StringUtils.isBlank(userId) || StringUtils.isBlank(secretKey)) {
//                return this.returnSuccessJson(ExceptionEnums.PARAM_ERROR, "参数不能为空");
//            }
//
//            if (secretKey.equals("<EMAIL>")) {
//                return this.returnSuccessJson(ExceptionEnums.SUCCESS, webPrintBuService.initBasicData());
//            }
//            return this.returnSuccessJson(ExceptionEnums.SUCCESS, "Failed");
//        } catch (ServiceException e) {
//            return this.returnFailJson(e);
//        }
//    }

    /**
     * 获取热门网站，还有
     * @return
     */
    @RequestMapping(value = "/getWebSites")
    public ReturnDto getWebSites() {
        try {
            String userId = this.getValue("userid");
            HeadInfoDto headInfoDto = this.getHeadInfoDto();
//            if (StringUtils.isBlank(userId)) {
//                return this.returnSuccessJson(ExceptionEnums.PARAM_ERROR, "参数不能为空");
//            }

            Locale locale = WebTools.getLocaleByLanguage(headInfoDto.getLanguage());
//            return this.returnSuccessJson(ExceptionEnums.SUCCESS, webPrintBuService.getWebPrintList(userId, headInfoDto.getOverseas(), locale));
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, webPrintBuService.getWebPrintListA4(userId, Boolean.TRUE, locale));
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    /**
     * 添加网站
     * @return
     */
    @RequestMapping(value = "/addWebSite")
    public ReturnDto addWebSite() {
        try {
            String userId = this.getValue("userId");
            WebPagePrintVo pagePrintVo = this.getBean(WebPagePrintVo.class);
            if (StringUtils.isBlank(userId) || pagePrintVo == null) {
                return this.returnSuccessJson(ExceptionEnums.PARAM_ERROR, "参数不能为空");
            }
            HeadInfoDto headInfoDto = this.getHeadInfoDto();
            Locale locale = WebTools.getLocaleByLanguage(headInfoDto.getLanguage());
            int codeId = webPrintBuService.addWebSite(pagePrintVo, userId, locale);
            if (codeId <= 0) {
                return this.returnSuccessJson(ExceptionEnums.PARAM_ERROR, "网站分组不存在");
            }

            pagePrintVo.setCodeId(codeId);
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, pagePrintVo);
        } catch (ServiceException e) {
            e.printStackTrace();
            return this.returnFailJson(e);
        } catch (Exception e) {
            e.printStackTrace();
            return this.returnFailJson(e);
        }
    }
}
