package net.snaptag.system.business.buservice;

import net.snaptag.system.business.enums.WebPrintDefaultEnums;
import net.snaptag.system.business.vo.WebPrintGroupVo;
import net.snaptag.system.sadais.core.common.exception.ServiceException;
import net.snaptag.system.sadais.util.core.ToolsKit;
import net.snaptag.system.sadais.web.utils.LocalTools;
import net.snaptag.system.business.dao.WebPrintDao;
import net.snaptag.system.business.entity.WebPrint;
import net.snaptag.system.business.vo.WebPagePrintVo;
import net.snaptag.system.sadais.web.core.I18nUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 网页打印服务类
 */
@Service
public class WebPrintBuService {

    @Autowired
    private WebPrintDao webPrintGroupDao;
    @Autowired
    private I18nUtils i18nUtils;

    public static final int TRUE = 1;
    public static final int FALSE = 0;

    /**
     * 添加分组
     * @param groupName
     * @param userId
     * @return
     */
    public Map<String, String> addGroup(String groupName, String userId) {
        WebPrint printGroup = new WebPrint();
        printGroup.setUserId(userId);
        printGroup.setName(groupName);
        printGroup.setIsDefault(FALSE);
        printGroup.setFromDefault(FALSE);
        printGroup.setPageList(new ArrayList<>());
        printGroup.setCreatetime(new Date());

        webPrintGroupDao.saveEntity(printGroup);
        Map<String, String> map = new HashMap<String, String>();
        map.put("id", printGroup.getId());
        return map;
    }

    /**
     * 删除分组
     * @param groupId
     */
    public void delGroup(String groupId) {
        webPrintGroupDao.removeById(groupId);
    }

    /**
     * 添加网站
     * @param pagePrintVo
     * @param userId
     * @return
     */
    public int addWebSite(WebPagePrintVo pagePrintVo, String userId, Locale locale) {
        // 5位随机数
        int codeId = (int)((Math.random() * 9 + 1) * 10000);
        String groupId = pagePrintVo.getGroupId();
        WebPrint printGroup = webPrintGroupDao.findUserGroup(userId, groupId);
        if (printGroup != null) {

            if ("我的收藏".equals(printGroup.getName())){
                if (checkIsExist(userId, pagePrintVo.getLinkUrl(), locale)){
                    String msg = i18nUtils.getKey(LocalTools.app_websites_tip, locale);
                    if (ToolsKit.isEmpty(msg)){
                        msg = "the current website has been collected, please do not repeat the collection";
                    }
                    throw new ServiceException(msg);
                }
            }

            WebPagePrintVo printVo = new WebPagePrintVo(codeId);
            printVo.setGroupId(groupId);
            printVo.setName(pagePrintVo.getName());
            printVo.setLinkUrl(pagePrintVo.getLinkUrl());
            printVo.setIconUrl(pagePrintVo.getIconUrl());
            printVo.setIsDefault(FALSE);
            printGroup.getPageList().add(printVo);

            webPrintGroupDao.saveEntity(printGroup);
        }
        return codeId;
    }

    /**
     * 删除网站
     * @param userId
     * @param groupId
     * @param siteId
     */
    public void delWebSite(String userId, String groupId, Integer siteId) {
        WebPrint printGroup = webPrintGroupDao.findUserGroup(userId, groupId);
        if (printGroup != null) {
            int deleteIdx = -1;
            int idx = 0;
            for (WebPagePrintVo printVo:printGroup.getPageList()) {
                if (printVo.getCodeId() == siteId) {
                    deleteIdx = idx;
                    break;
                }
                idx++;
            }
            if (deleteIdx >= 0) {
                printGroup.getPageList().remove(deleteIdx);
                webPrintGroupDao.saveEntity(printGroup);
            }
        }
    }

    /**
     * 网站排序
     * @param userId
     * @param groupId
     * @param siteIdList
     */
    public void sortWebSites(String userId, String groupId, String siteIdList) {
        WebPrint printGroup = webPrintGroupDao.findUserGroup(userId, groupId);
        if (printGroup != null && StringUtils.isNotBlank(siteIdList)) {

            List<WebPagePrintVo> printVoList = new ArrayList<>();
            String[] siteIds = siteIdList.trim().split(",");
            if (siteIds != null && siteIds.length > 0) {

                Map<String, WebPagePrintVo> webPageMap = new HashMap<>();
                for (WebPagePrintVo printVo:printGroup.getPageList()) {
                    webPageMap.put(printVo.getCodeId() + "", printVo);
                }

                // 按排序删除取出
                for (String siteId:siteIds) {
                    if (StringUtils.isBlank(siteId)) {
                        continue;
                    }

                    WebPagePrintVo pagePrintVo = webPageMap.remove(siteId);
                    if (pagePrintVo != null) {
                        printVoList.add(pagePrintVo);
                    }
                }

                // 不指定排序的排在最后
                for (WebPagePrintVo pagePrintVo:webPageMap.values()) {
                    printVoList.add(pagePrintVo);
                }

                // 保存
                printGroup.getPageList().clear();
                printGroup.getPageList().addAll(printVoList);
                webPrintGroupDao.saveEntity(printGroup);
            }
        }
    }

    /**
     * 获取指定用户所有分组和网站
     * @param userId
     * @return
     */
    public List<WebPrintGroupVo> getWebPrintList(String userId, Boolean overseas, Locale locale) {
        List<WebPrintGroupVo> printGroupVos = new ArrayList<>();
        if (StringUtils.isNotBlank(userId)) {
            // 用户已登录
            Set<String> defGroupNames = new HashSet<>();
            List<WebPrint> defPrintGroups = webPrintGroupDao.findDefaultGroupList();
            if (defPrintGroups != null) {
                for (WebPrint defGroup:defPrintGroups) {
                    defGroupNames.add(defGroup.getName());
                }
            }

            // --------- 处理海外版本，只保留“推荐网站”的 google ----------
            if (overseas) {
                defGroupNames.clear();
                defGroupNames.add("推荐网站");
                defPrintGroups = new ArrayList<>();
                WebPrint defGroup = new WebPrint();
                defGroup.setName("推荐网站");
                defGroup.setPageList(new ArrayList<>());
                defPrintGroups.add(defGroup);
                // 添加 google 网站
                int codeId = (int)((Math.random() * 9 + 1) * 10000);
                WebPagePrintVo printVo = new WebPagePrintVo(codeId);
                printVo.setGroupId(defGroup.getId());
                printVo.setName("google");
                printVo.setLinkUrl("https://www.google.com");
                printVo.setIconUrl("https://m.snaptag.top/api/img/gam/snapTag/common/webprint/web_ic_google.png");
                printVo.setIsDefault(TRUE);
                defGroup.getPageList().add(printVo);
            }
            // -------------------------------------------------------------

            List<WebPrint> printGroups = webPrintGroupDao.findUserGroupList(userId);
            if (printGroups != null && printGroups.size() > 0) {
                for (WebPrint printGroup:printGroups) {
                    WebPrintGroupVo printGroupVo = new WebPrintGroupVo();
                    printGroupVo.setId(printGroup.getId());
                    printGroupVo.setName(printGroup.getName());

                    if (defGroupNames.contains(printGroup.getName()) && printGroup.getFromDefault() == TRUE) {
                        if (ToolsKit.isNotEmpty(printGroup.getName()) && "阅读".equals(printGroup.getName())){
                            printGroupVo.setIsDefault(FALSE);
                        }
                        printGroupVo.setIsDefault(TRUE);
                    } else {
                        printGroupVo.setIsDefault(FALSE);
                    }
                    printGroupVo.setPageList(new ArrayList<>());
                    printGroupVo.getPageList().addAll(printGroup.getPageList());
                    printGroupVos.add(printGroupVo);
                }
                // 添加没有的默认项（系统自带的栏目不允许删除,以前没有的或需求新增的，添加上）
                for (WebPrint defPrintGroup: defPrintGroups) {
                    boolean needAdd = true;
                    for (WebPrint printGroup:printGroups) {
                        if (printGroup.getName().equals(defPrintGroup.getName())){
                            needAdd = false;
                        }
                    }

                    if (needAdd){
                        WebPrint printGroupAdd = new WebPrint();
                        printGroupAdd.setUserId(userId);
                        printGroupAdd.setName(defPrintGroup.getName());
                        printGroupAdd.setIsDefault(FALSE);
                        printGroupAdd.setFromDefault(TRUE);
                        printGroupAdd.setCreatetime(new Date());
                        printGroupAdd.setPageList(new ArrayList<>());
                        webPrintGroupDao.saveEntity(printGroupAdd);

                        // 添加默认网站
                        if (defPrintGroup.getPageList() != null && defPrintGroup.getPageList().size() > 0) {
                            for (WebPagePrintVo webPageVo:defPrintGroup.getPageList()) {
                                WebPagePrintVo printVo = new WebPagePrintVo(webPageVo.getCodeId());
                                printVo.setGroupId(printGroupAdd.getId());
                                printVo.setName(webPageVo.getName());
                                printVo.setLinkUrl(webPageVo.getLinkUrl());
                                printVo.setIconUrl(webPageVo.getIconUrl());
                                printVo.setIsDefault(TRUE);
                                printGroupAdd.getPageList().add(printVo);
                            }
                        }
                        webPrintGroupDao.saveEntity(printGroupAdd);

                        WebPrintGroupVo printGroupVo = new WebPrintGroupVo();
                        printGroupVo.setId(printGroupAdd.getId());
                        printGroupVo.setName(printGroupAdd.getName());
                        printGroupVo.setIsDefault(TRUE);
                        printGroupVo.setPageList(new ArrayList<>());
                        printGroupVo.getPageList().addAll(printGroupAdd.getPageList());
                        printGroupVos.add(printGroupVo);
                    }
                }
            } else {
                // 第一次访问，给用户添加默认分组和网站
                if (defPrintGroups != null) {
                    printGroupVos.clear();
                    for (WebPrint defGroup:defPrintGroups) {
                        WebPrint printGroupAdd = new WebPrint();
                        printGroupAdd.setUserId(userId);
                        printGroupAdd.setName(defGroup.getName());
                        printGroupAdd.setIsDefault(FALSE);
                        printGroupAdd.setFromDefault(TRUE);
                        printGroupAdd.setCreatetime(new Date());
                        printGroupAdd.setPageList(new ArrayList<>());
                        printGroupAdd.setCreatetime(new Date());
                        webPrintGroupDao.saveEntity(printGroupAdd);

                        // 添加默认网站
                        if (defGroup.getPageList() != null && defGroup.getPageList().size() > 0) {
                            for (WebPagePrintVo webPageVo:defGroup.getPageList()) {
                                WebPagePrintVo printVo = new WebPagePrintVo(webPageVo.getCodeId());
                                printVo.setGroupId(printGroupAdd.getId());
                                printVo.setName(webPageVo.getName());
                                printVo.setLinkUrl(webPageVo.getLinkUrl());
                                printVo.setIconUrl(webPageVo.getIconUrl());
                                printVo.setIsDefault(TRUE);
                                printGroupAdd.getPageList().add(printVo);
                            }
                        }
                        webPrintGroupDao.saveEntity(printGroupAdd);

                        WebPrintGroupVo printGroupVo = new WebPrintGroupVo();
                        printGroupVo.setId(printGroupAdd.getId());
                        printGroupVo.setName(printGroupAdd.getName());
                        printGroupVo.setIsDefault(TRUE);
                        if (ToolsKit.isNotEmpty(printGroupAdd.getName()) && "阅读".equals(printGroupAdd.getName())){
                            printGroupVo.setIsDefault(FALSE);
                        }
                        printGroupVo.setPageList(new ArrayList<>());
                        printGroupVo.getPageList().addAll(printGroupAdd.getPageList());
                        printGroupVos.add(printGroupVo);
                    }
                }
            }
        } else {
            // 用户未登录(只有海外版本)
            WebPrintGroupVo groupVo = new WebPrintGroupVo();
            groupVo.setName("推荐网站");
            groupVo.setId("0");
            groupVo.setIsDefault(TRUE);
            groupVo.setPageList(new ArrayList<>());
            printGroupVos.add(groupVo);

            // 添加 google 网站
            int codeId = (int)((Math.random() * 9 + 1) * 10000);
            WebPagePrintVo printVo = new WebPagePrintVo(codeId);
            printVo.setGroupId(groupVo.getId());
            printVo.setName("google");
            printVo.setLinkUrl("https://www.google.com");
            printVo.setIconUrl("https://m.snaptag.top/app/webprint/icons/print_ic_google.png");
            printVo.setIsDefault(TRUE);
            groupVo.getPageList().add(printVo);
        }

        // 处理海外版本，只保留“推荐网站”的 google
        if (overseas) {
            for (WebPrintGroupVo groupVo:printGroupVos) {
                if (groupVo.getName().equals("推荐网站")) {
                    groupVo.setName(i18nUtils.getKey(LocalTools.app_default_websites, locale));
                }
            }
        }

        return printGroupVos;
    }

    /**
     * 初始化默认分组和网站
     * @return
     */
    public String initBasicData() {
        addGroupSite("推荐网站", "百度@https://www.baidu.com@https://share.yoyin.net/app/webprint/icons/print_ic_webbaidu.png,花瓣@https://huaban.com@https://share.yoyin.net/app/webprint/icons/print_ic_webhuaban.png,知乎@https://www.zhihu.com@https://share.yoyin.net/app/webprint/icons/print_ic_zhihu.png,网易新闻@https://news.163.com@https://share.yoyin.net/app/webprint/icons/print_ic_wangyi.png,搜狐新闻@http://news.sohu.com@https://share.yoyin.net/app/webprint/icons/print_ic_shouhu.png,今日头条@https://www.toutiao.com@https://share.yoyin.net/app/webprint/icons/print_ic_toutiao.png");
        addGroupSite("素材", "花瓣@https://huaban.com@https://share.yoyin.net/app/webprint/icons/print_ic_webhuaban.png,推糖@https://www.duitang.com@https://share.yoyin.net/app/webprint/icons/print_ic_webtangdui.png,快看漫画@https://www.kuaikanmanhua.com@https://share.yoyin.net/app/webprint/icons/print_ic_kuaikan.png,斗图@https://www.52doutu.cn@https://share.yoyin.net/app/webprint/icons/print_ic_doutu.png");
        addGroupSite("学习", "百度汉语@https://hanyu.baidu.com@https://share.yoyin.net/app/webprint/icons/print_ic_baiduhanyu.png,有道词典@http://www.youdao.com@https://share.yoyin.net/app/webprint/icons/print_ic_youdao.png,故事365@https://www.gushi365.com@https://share.yoyin.net/app/webprint/icons/print_ic_gushi.png,美食天下@https://home.meishichina.com@https://share.yoyin.net/app/webprint/icons/print_ic_meishi.png");
        addGroupSite("公众号", "微信公众号@https://share.yoyin.net/app/webprint/icons/print_ic_webwechat.png");
        addGroupSite("阅读", "纵横小说@https://share.yoyin.net/app/webprint/icons/print_ic_zongheng.png,17K@https://share.yoyin.net/app/webprint/icons/print_ic_17K.png,百度小说@https://share.yoyin.net/app/webprint/icons/print_ic_baidu.png");
        return "Ok";
    }

    /**
     * 初始化默认分组和网站
     * @return
     */
    public String initBasicData2() {
        addGroupSite("热门网站", "百度@https://www.baidu.com@https://share.yoyin.net/app/webprint/icons/print_ic_webbaidu.png,花瓣@https://huaban.com@https://share.yoyin.net/app/webprint/icons/print_ic_webhuaban.png,知乎@https://www.zhihu.com@https://share.yoyin.net/app/webprint/icons/print_ic_zhihu.png,网易新闻@https://news.163.com@https://share.yoyin.net/app/webprint/icons/print_ic_wangyi.png,搜狐新闻@http://news.sohu.com@https://share.yoyin.net/app/webprint/icons/print_ic_shouhu.png,今日头条@https://www.toutiao.com@https://share.yoyin.net/app/webprint/icons/print_ic_toutiao.png");
        return "Ok";
    }

    private void addGroupSite(String groupName, String strSites) {
        WebPrint printGroup = new WebPrint();
        printGroup.setUserId("");
        printGroup.setName(groupName);
        printGroup.setIsDefault(TRUE);
        printGroup.setFromDefault(FALSE);
        printGroup.setPageList(new ArrayList<>());
        printGroup.setCreatetime(new Date());
        webPrintGroupDao.saveEntity(printGroup);

        String[] siteArr = strSites.split(",");
        int codeId = 0;
        for (String site:siteArr) {
            if (StringUtils.isBlank(site)) {
                continue;
            }

            codeId++;
            String[] siteItems = site.split("@");

            WebPagePrintVo printVo = new WebPagePrintVo(codeId);
            printVo.setGroupId(printGroup.getId());
            printVo.setName(siteItems[0]);
            if (siteItems.length > 1) {
                printVo.setLinkUrl(siteItems[1]);
            } else {
                printVo.setLinkUrl("");
            }
            if (siteItems.length > 2) {
                printVo.setIconUrl(siteItems[2]);
            } else {
                printVo.setIconUrl("");
            }
            printVo.setIsDefault(TRUE);
            printGroup.getPageList().add(printVo);
        }
        webPrintGroupDao.saveEntity(printGroup);
    }

    public List<WebPrintGroupVo> getWebPrintListA4(String userId, Boolean overseas, Locale locale) {
        String nameHots = i18nUtils.getKey(LocalTools.app_websites_hots, locale);
        String nameFav = i18nUtils.getKey(LocalTools.app_websites_favorite, locale);;
        if (ToolsKit.isEmpty(nameHots)){
            nameHots = "hot";
        }
        if (ToolsKit.isEmpty(nameFav)){
            nameFav = "favorite";
        }
        List<WebPrintGroupVo> printGroupVos = new ArrayList<>();
        printGroupVos.add(WebPrintDefaultEnums.getDefaultWebPrintGroup());

//        if (ToolsKit.isNotEmpty(userId)){
//            List<WebPrint> printGroups = webPrintGroupDao.findUserGroupList(userId);
//            if (ToolsKit.isNotEmpty(printGroups)){
//                for (WebPrint printGroup:printGroups) {
//                    if ("我的收藏".equals(printGroup.getName())){
//                        WebPrintGroupVo printGroupVo = new WebPrintGroupVo();
//                        printGroupVo.setId(printGroup.getId());
//                        printGroupVo.setName(nameFav);
//                        printGroupVo.setIsDefault(FALSE);
//                        printGroupVo.setPageList(new ArrayList<>());
//                        printGroupVo.getPageList().addAll(printGroup.getPageList());
//                        printGroupVos.add(printGroupVo);
//                    }
//                }
//                if (printGroupVos.size()<2){
//                    Map<String, String> newGroup = addGroup("我的收藏", userId);
//                    WebPrintGroupVo userWebPrintVo = ToolsKit.Bean.mapToBean(newGroup, WebPrintGroupVo.class, true );
//                    userWebPrintVo.setName(nameFav);
//                    printGroupVos.add(userWebPrintVo);
//                } else if (printGroupVos.size()>2){
//                    return printGroupVos.subList(0, 2);
//                }
//            } else {
//                // 创建默认的
//                Map<String, String> newGroup = addGroup("我的收藏", userId);
//                WebPrintGroupVo userWebPrintVo = ToolsKit.Bean.mapToBean(newGroup, WebPrintGroupVo.class, true );
//                userWebPrintVo.setName(nameFav);
//                printGroupVos.add(userWebPrintVo);
//            }
//        } else  {
//            WebPrintGroupVo printGroupVo = new WebPrintGroupVo();
//            printGroupVo.setId("");
//            printGroupVo.setName(nameFav);
//            printGroupVo.setIsDefault(FALSE);
//            printGroupVo.setPageList(new ArrayList<>());
//            printGroupVos.add(printGroupVo);
//        }

        return printGroupVos;
    }

    private boolean checkIsExist(String userId, String link, Locale locale){
        if (ToolsKit.isEmpty(userId) || ToolsKit.isEmpty(link) ){
            throw new ServiceException("连接地址为空");
        }

        List<WebPrintGroupVo> groupVoLis = getWebPrintListA4(userId, false, locale);
        for (WebPrintGroupVo groupVo:groupVoLis) {
            if (ToolsKit.isNotEmpty(groupVo.getPageList())){
                for (WebPagePrintVo vo: groupVo.getPageList()) {
                    if (link.equals(vo.getLinkUrl())){
                        return true;
                    }
                }
            }
        }

        return false;
    }

    public Object getDefaultWebSites() {
        return null;
    }
}
