# WebPrintController 修复说明

## 修复概述

WebPrintController 控制器存在多个问题，主要是缺少关键的业务方法和数据结构不匹配。通过参考 docs/网页打印 目录下的旧版本代码，已完成修复。

## 主要问题

1. **缺少关键业务方法**：
   - `delWebSite` - 删除网站
   - `sortWebSites` - 网站排序
   - `getWebSites` - 获取网站列表
   - `addWebSite` - 添加网站

2. **数据结构不匹配**：
   - 旧版本使用 `List<WebPagePrintVo>` 直接存储页面列表
   - 新版本使用 JSON 字符串存储，需要转换

3. **业务逻辑缺失**：
   - 网站重复检查逻辑
   - 海外版本特殊处理
   - 国际化支持

## 修复内容

### WebPrintBuService.java 修复

1. **添加必要的导入和依赖**：
   ```java
   @Autowired
   private I18nUtils i18nUtils;
   
   public static final int TRUE = 1;
   public static final int FALSE = 0;
   ```

2. **添加核心业务方法**：
   - `addWebSite()` - 添加网站到分组
   - `delWebSite()` - 删除指定网站
   - `sortWebSites()` - 网站排序功能
   - `getWebPrintList()` - 完整的网站列表获取
   - `getWebPrintListA4()` - A4版本的网站列表（包含"我的收藏"逻辑）
   - `addGroup()` - 添加分组
   - `delGroup()` - 删除分组（简单版本）
   - `checkIsExist()` - 检查网站是否已存在
   - `initBasicData()` - 初始化默认分组和网站数据
   - `initBasicData2()` - 初始化默认分组和网站数据（版本2）
   - `addGroupSite()` - 添加分组和网站的私有方法
   - `getDefaultWebSites()` - 获取默认网站（兼容方法）

3. **数据转换优化**：
   - 完善 JSON 与 List 的相互转换
   - 兼容旧版本数据结构

### WebPrintController.java 修复

1. **添加必要的导入**：
   ```java
   import net.snaptag.system.sadais.web.common.JsonKit;
   import net.snaptag.system.business.vo.WebGroupSortVo;
   import net.snaptag.system.business.vo.WebPagePrintVo;
   import org.apache.commons.lang3.StringUtils;
   ```

2. **添加缺失的接口方法**：
   - `/delWebSite` - 删除网站接口
   - `/sortWebSites` - 网站排序接口
   - `/getWebSites` - 获取网站列表接口
   - `/addWebSite` - 添加网站接口
   - `/initBasicData` - 初始化基础数据接口
   - `/initBasicData2` - 初始化基础数据接口（版本2）
   - `/getDefaultWebSites` - 获取默认网站接口

3. **完善日志注解**：
   - 为所有新增方法添加 `@SLSLog` 注解
   - 统一日志配置和操作类型

## 核心功能说明

### 1. 网站管理
- **添加网站**：支持添加网站到指定分组，包含重复检查
- **删除网站**：根据 siteId 删除指定网站
- **网站排序**：支持批量排序，按指定顺序重新排列网站

### 2. 分组管理
- **默认分组**：系统预设的分组和网站
- **用户分组**：用户自定义的分组
- **分组复制**：将默认分组复制给用户

### 3. 国际化支持
- 支持多语言环境
- 海外版本特殊处理（仅显示 Google 等国外网站）

### 4. 数据兼容性
- 兼容旧版本数据结构
- 自动转换 JSON 格式数据

### 5. 数据初始化
- **系统默认分组**：推荐网站、素材、学习、公众号、阅读
- **预设网站**：百度、花瓣、知乎、网易新闻、搜狐新闻、今日头条等
- **我的收藏功能**：自动为用户创建收藏分组
- **安全验证**：初始化接口需要密钥验证

## 接口说明

### 新增接口

1. **DELETE /api/business/v1/webprint/delWebSite**
   - 参数：userId, groupId, siteId
   - 功能：删除指定网站

2. **POST /api/business/v1/webprint/sortWebSites**
   - 参数：userId, groupList (JSON)
   - 功能：批量排序网站

3. **GET /api/business/v1/webprint/getWebSites**
   - 参数：userid (可选)
   - 功能：获取网站列表

4. **POST /api/business/v1/webprint/addWebSite**
   - 参数：userId, WebPagePrintVo
   - 功能：添加新网站

5. **POST /api/business/v1/webprint/initBasicData**
   - 参数：userId, secretKey
   - 功能：初始化默认分组和网站数据（需要密钥验证）

6. **POST /api/business/v1/webprint/initBasicData2**
   - 参数：userId, secretKey
   - 功能：初始化默认分组和网站数据（版本2，需要密钥验证）

7. **GET /api/business/v1/webprint/getDefaultWebSites**
   - 参数：userid (可选)
   - 功能：获取默认网站列表

### 现有接口保持不变
- `/getDefaultGroupList` - 获取默认分组列表
- `/getUserGroupList` - 获取用户分组列表
- `/getGroupDetail` - 获取分组详情
- `/saveOrUpdateGroup` - 保存或更新分组
- `/deleteGroup` - 删除分组
- `/findPage` - 分页查询
- `/copyDefaultGroup` - 复制默认分组

## 测试验证

- ✅ 编译通过
- ✅ 所有方法签名正确
- ✅ 导入依赖完整
- ✅ 日志注解配置正确

## 注意事项

1. **数据库兼容性**：确保 `v1_web_print` 表的 `page_list` 字段支持 JSON 格式
2. **权限控制**：所有操作都需要验证用户权限
3. **异常处理**：完善的异常捕获和错误提示
4. **性能优化**：大量数据时考虑分页和缓存

修复完成后，WebPrintController 现在具备完整的网页打印管理功能，兼容旧版本接口，支持国际化和海外版本。
