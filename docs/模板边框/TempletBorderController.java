package com.sandu.xinye.api.v2.templetBorder;

import com.jfinal.aop.Before;
import com.jfinal.aop.Clear;
import com.sandu.xinye.common.controller.AppController;
import com.sandu.xinye.common.interceptor.I18nInterceptor;
import com.sandu.xinye.common.kit.RetKit;

public class TempletBorderController extends AppController {

    @Clear
    public void getBorderList() {
        RetKit ret = TempletBorderService.me.getBorderList();
        renderJson(ret);
    }


    /**
     * @description 获取模板边框类别
     * <AUTHOR>
     * @date 2024/1/29
     */
    @Clear
    @Before({I18nInterceptor.class})
    public void getBorderKindList() {
        Integer language = getParaToInt("language");
        String lang = getPara("lang");
        RetKit ret = TempletBorderService.me.getBorderKindList(language, lang);
        renderJson(ret);
    }


    /**
     * @description 获取模板边框列表分页
     * <AUTHOR>
     * @date 2024/1/29
     */
    @Clear
    public void getBorderPage() {
        int pageNumber = getParaToInt("pageNumber", 1);
        int pageSize = getParaToInt("pageSize", 10);
        int language = getParaToInt("language", 1);
        String kindId = getPara("borderKindId");
        RetKit ret = TempletBorderService.me.getBorderPage(pageNumber, pageSize, kindId, language);
        renderJson(ret);
    }
}
