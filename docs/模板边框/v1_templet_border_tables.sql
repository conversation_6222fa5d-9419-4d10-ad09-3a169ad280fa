-- =============================================
-- 模板边框功能数据库表结构
-- 适配当前项目的数据库规范
-- =============================================

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for v1_templet_border
-- ----------------------------
DROP TABLE IF EXISTS `v1_templet_border`;
CREATE TABLE `v1_templet_border` (
  `borderId` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '边框ID',
  `borderKindId` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '边框分类ID',
  `headerImgUrl` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '边框左图链接',
  `fillImgUrl` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '边框中间图链接',
  `footerImgUrl` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '边框右图链接',
  `thumbUrl` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '缩略图链接',
  `sysUserId` int(11) NOT NULL COMMENT '操作人ID',
  `createTime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`borderId`) USING BTREE,
  KEY `idx_border_kind_id` (`borderKindId`) USING BTREE,
  KEY `idx_create_time` (`createTime`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='模板边框表' ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for v1_templet_border_kind
-- ----------------------------
DROP TABLE IF EXISTS `v1_templet_border_kind`;
CREATE TABLE `v1_templet_border_kind` (
  `borderKindId` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '边框分类ID',
  `borderKindName` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '边框分类名称',
  `englishName` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '英文名称',
  `traditionalName` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '繁体中文名称',
  `koreanName` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '韩文名称',
  `russianName` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '俄语名称',
  `frenchName` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '法语名称',
  `spanishName` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '西班牙语名称',
  `germanyName` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '德语名称',
  `italyName` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '意大利语名称',
  `sysUserId` int(11) NOT NULL COMMENT '操作人ID',
  `createTime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`borderKindId`) USING BTREE,
  KEY `idx_create_time` (`createTime`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='模板边框分类表' ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- 测试数据插入
-- ----------------------------

-- 插入边框分类测试数据
INSERT INTO `v1_templet_border_kind` VALUES 
('kind_001', '简约边框', 'Simple Border', '簡約邊框', '심플 테두리', 'Простая граница', 'Bordure simple', 'Borde simple', 'Einfacher Rahmen', 'Bordo semplice', 1, NOW()),
('kind_002', '装饰边框', 'Decorative Border', '裝飾邊框', '장식 테두리', 'Декоративная граница', 'Bordure décorative', 'Borde decorativo', 'Dekorativer Rahmen', 'Bordo decorativo', 1, NOW()),
('kind_003', '节日边框', 'Holiday Border', '節日邊框', '휴일 테두리', 'Праздничная граница', 'Bordure de vacances', 'Borde de vacaciones', 'Feiertagsrahmen', 'Bordo festivo', 1, NOW()),
('kind_004', '商务边框', 'Business Border', '商務邊框', '비즈니스 테두리', 'Деловая граница', 'Bordure d\'affaires', 'Borde de negocios', 'Geschäftsrahmen', 'Bordo aziendale', 1, NOW()),
('kind_005', '艺术边框', 'Artistic Border', '藝術邊框', '예술 테두리', 'Художественная граница', 'Bordure artistique', 'Borde artístico', 'Künstlerischer Rahmen', 'Bordo artistico', 1, NOW());

-- 插入边框测试数据
INSERT INTO `v1_templet_border` VALUES 
('border_001', 'kind_001', '/images/borders/simple/header_001.png', '/images/borders/simple/fill_001.png', '/images/borders/simple/footer_001.png', '/images/borders/simple/thumb_001.png', 1, NOW()),
('border_002', 'kind_001', '/images/borders/simple/header_002.png', '/images/borders/simple/fill_002.png', '/images/borders/simple/footer_002.png', '/images/borders/simple/thumb_002.png', 1, NOW()),
('border_003', 'kind_002', '/images/borders/decorative/header_001.png', '/images/borders/decorative/fill_001.png', '/images/borders/decorative/footer_001.png', '/images/borders/decorative/thumb_001.png', 1, NOW()),
('border_004', 'kind_002', '/images/borders/decorative/header_002.png', '/images/borders/decorative/fill_002.png', '/images/borders/decorative/footer_002.png', '/images/borders/decorative/thumb_002.png', 1, NOW()),
('border_005', 'kind_003', '/images/borders/holiday/header_001.png', '/images/borders/holiday/fill_001.png', '/images/borders/holiday/footer_001.png', '/images/borders/holiday/thumb_001.png', 1, NOW()),
('border_006', 'kind_003', '/images/borders/holiday/header_002.png', '/images/borders/holiday/fill_002.png', '/images/borders/holiday/footer_002.png', '/images/borders/holiday/thumb_002.png', 1, NOW()),
('border_007', 'kind_004', '/images/borders/business/header_001.png', '/images/borders/business/fill_001.png', '/images/borders/business/footer_001.png', '/images/borders/business/thumb_001.png', 1, NOW()),
('border_008', 'kind_004', '/images/borders/business/header_002.png', '/images/borders/business/fill_002.png', '/images/borders/business/footer_002.png', '/images/borders/business/thumb_002.png', 1, NOW()),
('border_009', 'kind_005', '/images/borders/artistic/header_001.png', '/images/borders/artistic/fill_001.png', '/images/borders/artistic/footer_001.png', '/images/borders/artistic/thumb_001.png', 1, NOW()),
('border_010', 'kind_005', '/images/borders/artistic/header_002.png', '/images/borders/artistic/fill_002.png', '/images/borders/artistic/footer_002.png', '/images/borders/artistic/thumb_002.png', 1, NOW());

-- ----------------------------
-- 查询验证数据
-- ----------------------------

-- 验证边框分类数据
SELECT '=== 边框分类数据 ===' as info;
SELECT borderKindId, borderKindName, englishName, traditionalName, createTime FROM v1_templet_border_kind;

-- 验证边框数据
SELECT '=== 边框数据 ===' as info;
SELECT borderId, borderKindId, thumbUrl, createTime FROM v1_templet_border;

-- 验证关联查询
SELECT '=== 边框与分类关联查询 ===' as info;
SELECT 
    b.borderId,
    b.thumbUrl,
    k.borderKindName,
    k.englishName,
    b.createTime
FROM v1_templet_border b
LEFT JOIN v1_templet_border_kind k ON b.borderKindId = k.borderKindId
ORDER BY b.createTime DESC;

SET FOREIGN_KEY_CHECKS = 1;
