/*
 Navicat Premium Dump SQL

 Source Server         : vida
 Source Server Type    : MySQL
 Source Server Version : 50742 (5.7.42-log)
 Source Host           : localhost:3302
 Source Schema         : barcode4_test

 Target Server Type    : MySQL
 Target Server Version : 50742 (5.7.42-log)
 File Encoding         : 65001

 Date: 20/08/2025 14:57:09
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for v1_templet_border
-- ----------------------------
DROP TABLE IF EXISTS `v1_templet_border`;
CREATE TABLE `v1_templet_border`  (
  `borderId` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL  COMMENT '边框Id',
  `borderKindId` int(11) NOT NULL COMMENT '边框分类Id',
  `headerImgUrl` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '边框左图链接',
  `fillImgUrl` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '边框中间图链接',
  `footerImgUrl` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '边框右图链接',
  `thumbUrl` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '缩略图链接',
  `sysUserId` int(11) NOT NULL COMMENT '操作人id',
  `createTime` datetime NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`borderId`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '模板边框表' ROW_FORMAT = DYNAMIC;

SET FOREIGN_KEY_CHECKS = 1;
