# 模板边框功能 - API测试文档

## 1. 测试环境准备

### 1.1 数据库初始化
```sql
-- 执行表结构创建脚本
source docs/模板边框/v1_templet_border_tables.sql;
```

### 1.2 验证数据
```sql
-- 验证边框分类数据
SELECT * FROM v1_templet_border_kind;

-- 验证边框数据  
SELECT * FROM v1_templet_border;

-- 验证关联查询
SELECT b.borderId, b.thumbUrl, k.borderKindName, k.englishName 
FROM v1_templet_border b 
LEFT JOIN v1_templet_border_kind k ON b.borderKindId = k.borderKindId;
```

## 2. 核心接口测试

### 2.1 获取边框分类列表

#### 测试用例1：默认中文分类列表
```
GET /api/business/v1/templetBorder/getBorderKindList
```

#### 测试用例2：英文分类列表
```
GET /api/business/v1/templetBorder/getBorderKindList?language=2
```

#### 测试用例3：使用lang参数
```
GET /api/business/v1/templetBorder/getBorderKindList?lang=english
```

#### 期望返回格式：
```json
{
    "data": [
        {
            "borderKindId": "kind_001",
            "borderKindName": "Simple Border",
            "displayName": "Simple Border"
        }
    ],
    "head": {
        "ret": 0,
        "msg": "成功"
    }
}
```

### 2.2 获取所有边框列表

#### 测试用例1：获取所有边框
```
GET /api/business/v1/templetBorder/getBorderList
```

#### 期望返回格式：
```json
{
    "data": [
        {
            "borderId": "border_001",
            "headerImgUrl": "/images/borders/simple/header_001.png",
            "fillImgUrl": "/images/borders/simple/fill_001.png",
            "footerImgUrl": "/images/borders/simple/footer_001.png",
            "thumbUrl": "/images/borders/simple/thumb_001.png",
            "groupName": "简约边框"
        }
    ],
    "head": {
        "ret": 0,
        "msg": "成功"
    }
}
```

### 2.3 分页查询边框列表

#### 测试用例1：基础分页查询
```
GET /api/business/v1/templetBorder/getBorderPage?pageNumber=1&pageSize=5
```

#### 测试用例2：按分类过滤
```
GET /api/business/v1/templetBorder/getBorderPage?pageNumber=1&pageSize=5&borderKindId=kind_001
```

#### 测试用例3：多语言分页查询
```
GET /api/business/v1/templetBorder/getBorderPage?pageNumber=1&pageSize=5&language=2
```

#### 期望返回格式：
```json
{
    "data": {
        "records": [...],
        "total": 10,
        "size": 5,
        "current": 1,
        "pages": 2
    },
    "head": {
        "ret": 0,
        "msg": "成功"
    }
}
```

### 2.4 增强分页查询

#### 测试用例1：包含多语言信息
```
GET /api/business/v1/templetBorder/getBorderPageEnhanced?pageNumber=1&pageSize=5&includeMultiLang=true&lang=english
```

#### 测试用例2：高级分页查询
```
GET /api/business/v1/templetBorder/getBorderPageAdvanced?pageNumber=1&pageSize=5&orderBy=createTime&orderDirection=ASC
```

### 2.5 根据分类查询边框

#### 测试用例1：查询简约边框
```
GET /api/business/v1/templetBorder/getBordersByKind?borderKindId=kind_001
```

### 2.6 获取边框详情

#### 测试用例1：基础详情查询
```
GET /api/business/v1/templetBorder/getBorderDetail?borderId=border_001
```

#### 测试用例2：包含多语言分类信息
```
GET /api/business/v1/templetBorder/getBorderDetailWithLanguage?borderId=border_001&lang=english
```

### 2.7 统计信息查询

#### 测试用例1：获取统计信息
```
GET /api/business/v1/templetBorder/getStatistics
```

#### 测试用例2：分页统计信息
```
GET /api/business/v1/templetBorder/getBorderPageStatistics?borderKindId=kind_001
```

## 3. 错误处理测试

### 3.1 参数验证测试

#### 测试用例1：无效的边框ID
```
GET /api/business/v1/templetBorder/getBorderDetail?borderId=invalid_id
```
期望：返回"边框不存在"错误

#### 测试用例2：无效的语言参数
```
GET /api/business/v1/templetBorder/getBorderKindList?lang=invalid_lang
```
期望：返回语言参数错误

#### 测试用例3：无效的分页参数
```
GET /api/business/v1/templetBorder/getBorderPage?pageNumber=0&pageSize=-1
```
期望：自动修正为默认值

### 3.2 边界值测试

#### 测试用例1：大页面大小
```
GET /api/business/v1/templetBorder/getBorderPage?pageSize=1000
```
期望：限制为最大值100

#### 测试用例2：空分类ID
```
GET /api/business/v1/templetBorder/getBordersByKind?borderKindId=
```
期望：返回参数错误

## 4. 性能测试

### 4.1 并发测试
- 同时发起100个分页查询请求
- 验证响应时间和系统稳定性

### 4.2 大数据量测试
- 插入1000条边框数据
- 测试分页查询性能

## 5. 兼容性测试

### 5.1 原有接口兼容性
验证新实现的接口与原有JFinal版本的兼容性：

#### 原接口格式测试
```
GET /api/business/v1/templetBorder/getBorderKindList?language=2
GET /api/business/v1/templetBorder/getBorderList
GET /api/business/v1/templetBorder/getBorderPage?pageNumber=1&pageSize=10&borderKindId=kind_001&language=2
```

### 5.2 多语言兼容性
测试所有8种语言的支持：
- chinese (中文)
- english (英文)
- traditional (繁体中文)
- korean (韩文)
- russian (俄语)
- french (法语)
- spanish (西班牙语)
- germany (德语)
- italy (意大利语)

## 6. 自动化测试脚本

### 6.1 基础功能测试脚本
```bash
#!/bin/bash
BASE_URL="http://localhost:8080/api/business/v1/templetBorder"

echo "测试获取分类列表..."
curl -X GET "$BASE_URL/getBorderKindList"

echo "测试获取边框列表..."
curl -X GET "$BASE_URL/getBorderList"

echo "测试分页查询..."
curl -X GET "$BASE_URL/getBorderPage?pageNumber=1&pageSize=5"

echo "测试统计信息..."
curl -X GET "$BASE_URL/getStatistics"
```

### 6.2 多语言测试脚本
```bash
#!/bin/bash
BASE_URL="http://localhost:8080/api/business/v1/templetBorder"

languages=("english" "traditional" "korean" "russian" "french" "spanish" "germany" "italy")

for lang in "${languages[@]}"; do
    echo "测试语言: $lang"
    curl -X GET "$BASE_URL/getBorderKindList?lang=$lang"
done
```

## 7. 测试检查清单

- [ ] 数据库表结构创建成功
- [ ] 测试数据插入成功
- [ ] 所有核心接口返回正确格式
- [ ] 多语言功能正常工作
- [ ] 分页查询功能正常
- [ ] 错误处理机制正常
- [ ] 参数验证功能正常
- [ ] 性能满足要求
- [ ] 与原有接口兼容
