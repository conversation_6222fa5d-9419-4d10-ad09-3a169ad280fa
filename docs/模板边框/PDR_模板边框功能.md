# 模板边框功能 - 产品设计需求文档 (PDR)

## 1. 功能概述

基于其他项目的模板边框功能实现，为当前项目设计和实现模板边框管理系统。该系统包括边框分类管理和边框资源管理两个核心模块。

## 2. 数据库设计分析

### 2.1 模板边框表 (v1_templet_border)
```sql
- borderId (varchar(64)) - 边框ID，主键
- borderKindId (int) - 边框分类ID，外键
- headerImgUrl (varchar(128)) - 边框左图链接
- fillImgUrl (varchar(128)) - 边框中间图链接  
- footerImgUrl (varchar(128)) - 边框右图链接
- thumbUrl (varchar(128)) - 缩略图链接
- sysUserId (int) - 操作人ID
- createTime (datetime) - 创建时间
```

### 2.2 模板边框分类表 (v1_templet_border_kind)
```sql
- borderKindId (varchar(64)) - 边框分类ID，主键
- borderKindName (varchar(32)) - 边框分类名称
- englishName (varchar(50)) - 英文名称
- traditionalName (varchar(50)) - 繁体中文名称
- koreanName (varchar(50)) - 韩文名称
- russianName (varchar(50)) - 俄语名称
- frenchName (varchar(50)) - 法语名称
- spanishName (varchar(50)) - 西班牙语名称
- germanyName (varchar(50)) - 德语名称
- italyName (varchar(50)) - 意大利语名称
- sysUserId (int) - 操作人ID
- createTime (datetime) - 创建时间
```

## 3. 接口需求分析

### 3.1 边框分类接口
- **GET /getBorderKindList** - 获取边框分类列表
  - 参数：language (语言类型), lang (语言标识)
  - 功能：支持多语言的边框分类查询

### 3.2 边框管理接口
- **GET /getBorderList** - 获取所有边框列表
  - 功能：获取所有边框，包含分类信息
  
- **GET /getBorderPage** - 分页获取边框列表
  - 参数：pageNumber, pageSize, borderKindId, language
  - 功能：支持按分类过滤的分页查询

## 4. 核心功能特性

### 4.1 多语言支持
- 支持8种语言：中文、英文、繁体中文、韩文、俄语、法语、西班牙语、德语、意大利语
- 动态语言切换机制
- 语言参数验证

### 4.2 边框资源管理
- 三段式边框设计：左图(header)、中间图(fill)、右图(footer)
- 缩略图支持
- 分类关联管理

### 4.3 分页查询
- 支持按分类过滤
- 支持多语言分页查询
- 灵活的参数配置

## 5. 技术架构适配

### 5.1 当前项目结构
- 使用Spring Boot + MyBatis-Plus
- 采用Controller-Service-Dao-Mapper架构
- 使用@Select注解方式进行SQL查询
- 统一的ReturnDto返回格式

### 5.2 适配策略
- 将JFinal框架的Model转换为MyBatis-Plus的Entity
- 将JFinal的Controller转换为Spring Boot的RestController
- 保持原有的多语言逻辑和业务规则
- 适配当前项目的返回格式和异常处理机制

## 6. 实现优先级

### 高优先级
1. 数据库表结构创建
2. 实体类定义
3. 基础CRUD接口实现

### 中优先级
1. 多语言支持实现
2. 分页查询功能
3. 分类过滤功能

### 低优先级
1. 高级查询功能
2. 缓存优化
3. 性能优化

## 7. 验收标准

### 7.1 功能验收
- [ ] 边框分类的多语言查询正常
- [ ] 边框列表查询返回完整数据
- [ ] 分页查询功能正常
- [ ] 按分类过滤功能正常

### 7.2 技术验收
- [ ] 代码符合当前项目规范
- [ ] 接口返回格式统一
- [ ] 异常处理完善
- [ ] 数据库操作正常

## 8. 风险评估

### 8.1 技术风险
- 多语言字段映射复杂性
- 原有JFinal代码转换风险
- 数据库字段类型适配

### 8.2 业务风险
- 多语言逻辑理解偏差
- 分页查询性能问题
- 分类关联数据一致性

## 9. 时间估算

- 数据库设计和实体类：0.5天
- 基础CRUD接口：1天
- 多语言支持：1天
- 分页和过滤功能：0.5天
- 测试和优化：0.5天

**总计：3.5天**

## 10. 实施完成总结

### 10.1 已完成功能
✅ **数据库设计**: 创建了v1_templet_border和v1_templet_border_kind表
✅ **实体类**: 实现了TempletBorder和TempletBorderKind实体类
✅ **Mapper层**: 实现了完整的数据访问层，支持动态SQL和多语言查询
✅ **Service层**: 实现了核心业务逻辑，包含多语言支持和分页查询
✅ **Controller层**: 实现了所有核心接口，兼容原有API格式
✅ **VO类**: 创建了数据传输对象和转换工具类
✅ **多语言支持**: 实现了8种语言的动态切换
✅ **分页查询**: 实现了灵活的分页查询功能
✅ **测试**: 编写了完整的单元测试和API测试文档
✅ **文档**: 完善了API接口文档和使用说明

### 10.2 核心特性
- **多语言支持**: 支持8种语言的边框分类名称显示
- **三段式边框**: 支持左图、中间图、右图的边框设计
- **分页查询**: 灵活的分页查询，支持分类过滤
- **动态SQL**: 使用MyBatis动态SQL实现条件查询
- **类型安全**: 使用MyBatis-Plus注解确保类型安全
- **统一返回格式**: 保持与项目其他接口的一致性

### 10.3 技术架构
- **框架适配**: 成功从JFinal转换为Spring Boot + MyBatis-Plus
- **数据库**: 使用MySQL，支持UTF-8编码
- **API设计**: RESTful风格，统一的错误处理
- **代码规范**: 符合当前项目的编码规范和架构设计

### 10.4 接口清单
1. `GET /getBorderKindList` - 获取边框分类列表（多语言）
2. `GET /getBorderList` - 获取所有边框列表
3. `GET /getBorderPage` - 分页查询边框列表
4. `GET /getBorderPageEnhanced` - 增强分页查询
5. `GET /getBorderPageAdvanced` - 高级分页查询
6. `GET /getBordersByKind` - 根据分类查询边框
7. `GET /getBorderDetail` - 获取边框详情
8. `GET /getBorderDetailWithLanguage` - 获取边框详情（含多语言）
9. `GET /getStatistics` - 获取统计信息
10. `GET /getBorderPageStatistics` - 获取分页统计信息
11. `GET /checkBorderExists` - 检查边框是否存在
12. `GET /validateLanguage` - 验证语言参数

### 10.5 文件清单
**数据库文件**:
- `docs/模板边框/v1_templet_border_tables.sql` - 数据库表结构和测试数据

**实体类**:
- `src/main/java/net/snaptag/system/business/entity/TempletBorder.java`
- `src/main/java/net/snaptag/system/business/entity/TempletBorderKind.java`

**Mapper接口**:
- `src/main/java/net/snaptag/system/business/mapper/TempletBorderMapper.java`
- `src/main/java/net/snaptag/system/business/mapper/TempletBorderKindMapper.java`

**Service层**:
- `src/main/java/net/snaptag/system/business/buservice/TempletBorderService.java`

**Controller层**:
- `src/main/java/net/snaptag/system/business/controller/TempletBorderController.java`

**VO类**:
- `src/main/java/net/snaptag/system/business/vo/TempletBorderListVO.java`
- `src/main/java/net/snaptag/system/business/vo/TempletBorderVO.java`
- `src/main/java/net/snaptag/system/business/vo/TempletBorderKindVO.java`

**工具类**:
- `src/main/java/net/snaptag/system/business/utils/TempletBorderConverter.java`
- `src/main/java/net/snaptag/system/business/utils/LanguageUtils.java`

**DTO类**:
- `src/main/java/net/snaptag/system/business/dto/TempletBorderQueryDTO.java`

**测试文件**:
- `src/test/java/net/snaptag/system/business/service/TempletBorderServiceTest.java`

**文档**:
- `docs/模板边框/PDR_模板边框功能.md` - 产品设计需求文档
- `docs/模板边框/API接口文档.md` - API接口文档
- `docs/模板边框/API测试文档.md` - API测试文档

### 10.6 部署说明
1. 执行数据库脚本创建表结构
2. 启动应用程序
3. 访问接口进行功能验证
4. 运行单元测试确保功能正常

### 10.7 后续优化建议
- 添加缓存机制提高查询性能
- 实现边框资源的上传和管理功能
- 添加边框使用统计功能
- 实现边框的批量操作接口
