# 边框分页多语言功能测试

## 问题描述
边框分页接口传入`language=2`（英文），但返回的分类名称仍然是中文，没有根据语言参数返回对应的英文名称。

## 修复内容

### 1. Service层修改
在`TempletBorderService.getBorderPage`方法中添加了多语言处理逻辑：

```java
// 根据language参数获取对应语言的分类名称
if (border.getBorderKindId() != null && !border.getBorderKindId().trim().isEmpty()) {
    TempletBorderKind kind = borderKindMapper.selectFullKindById(border.getBorderKindId());
    if (kind != null) {
        String localizedName = getLocalizedKindName(kind, language);
        border.setGroupName(localizedName);
        border.setGroupDisplayName(localizedName);
    }
}
```

### 2. 新增辅助方法
添加了`getLocalizedKindName`方法来根据语言类型获取对应的分类名称：

```java
private String getLocalizedKindName(TempletBorderKind kind, Integer language) {
    switch (language) {
        case 2: // 英文
            return StringUtils.hasText(kind.getEnglishName()) ? kind.getEnglishName() : kind.getBorderKindName();
        case 3: // 繁体中文
            return StringUtils.hasText(kind.getTraditionalName()) ? kind.getTraditionalName() : kind.getBorderKindName();
        case 4: // 韩文
            return StringUtils.hasText(kind.getKoreanName()) ? kind.getKoreanName() : kind.getBorderKindName();
        // ... 其他语言
        default:
            return kind.getBorderKindName();
    }
}
```

## 测试用例

### 测试1：中文分页（默认）
```bash
curl -X GET "http://localhost:8080/api/business/v1/templetBorder/getBorderPage?pageNumber=1&pageSize=5"
```

期望返回：
```json
{
    "msg": "操作成功",
    "code": "200",
    "success": true,
    "page": {
        "list": [
            {
                "borderId": "1",
                "borderKindName": "简约边框",
                "headerImgUrl": "...",
                "fillImgUrl": "...",
                "footerImgUrl": "...",
                "thumbUrl": "..."
            }
        ]
    }
}
```

### 测试2：英文分页
```bash
curl -X GET "http://localhost:8080/api/business/v1/templetBorder/getBorderPage?pageNumber=1&pageSize=5&language=2"
```

期望返回：
```json
{
    "msg": "操作成功",
    "code": "200",
    "success": true,
    "page": {
        "list": [
            {
                "borderId": "1",
                "borderKindName": "Simple Border",
                "headerImgUrl": "...",
                "fillImgUrl": "...",
                "footerImgUrl": "...",
                "thumbUrl": "..."
            }
        ]
    }
}
```

### 测试3：韩文分页
```bash
curl -X GET "http://localhost:8080/api/business/v1/templetBorder/getBorderPage?pageNumber=1&pageSize=5&language=4"
```

期望返回：
```json
{
    "msg": "操作成功",
    "code": "200",
    "success": true,
    "page": {
        "list": [
            {
                "borderId": "1",
                "borderKindName": "심플한",
                "headerImgUrl": "...",
                "fillImgUrl": "...",
                "footerImgUrl": "...",
                "thumbUrl": "..."
            }
        ]
    }
}
```

### 测试4：繁体中文分页
```bash
curl -X GET "http://localhost:8080/api/business/v1/templetBorder/getBorderPage?pageNumber=1&pageSize=5&language=3"
```

期望返回：
```json
{
    "msg": "操作成功",
    "code": "200",
    "success": true,
    "page": {
        "list": [
            {
                "borderId": "1",
                "borderKindName": "簡約邊框",
                "headerImgUrl": "...",
                "fillImgUrl": "...",
                "footerImgUrl": "...",
                "thumbUrl": "..."
            }
        ]
    }
}
```

### 测试5：按分类过滤 + 多语言
```bash
curl -X GET "http://localhost:8080/api/business/v1/templetBorder/getBorderPage?pageNumber=1&pageSize=5&borderKindId=kind_001&language=2"
```

期望返回：
```json
{
    "msg": "操作成功",
    "code": "200",
    "success": true,
    "page": {
        "list": [
            {
                "borderId": "1",
                "borderKindName": "Simple Border",
                "headerImgUrl": "...",
                "fillImgUrl": "...",
                "footerImgUrl": "...",
                "thumbUrl": "..."
            }
        ]
    }
}
```

## 语言类型对照表

| language值 | 语言 | 对应字段 |
|------------|------|----------|
| 1 | 中文（默认） | borderKindName |
| 2 | 英文 | englishName |
| 3 | 繁体中文 | traditionalName |
| 4 | 韩文 | koreanName |
| 5 | 俄语 | russianName |
| 6 | 法语 | frenchName |
| 7 | 西班牙语 | spanishName |
| 8 | 德语 | germanyName |
| 9 | 意大利语 | italyName |

## 验证清单

- [ ] 中文分页返回中文分类名称
- [ ] 英文分页返回英文分类名称
- [ ] 韩文分页返回韩文分类名称
- [ ] 繁体中文分页返回繁体分类名称
- [ ] 俄语分页返回俄语分类名称
- [ ] 法语分页返回法语分类名称
- [ ] 西班牙语分页返回西班牙语分类名称
- [ ] 德语分页返回德语分类名称
- [ ] 意大利语分页返回意大利语分类名称
- [ ] 按分类过滤 + 多语言功能正常
- [ ] 无效language值时返回默认中文名称
- [ ] 多语言字段为空时回退到默认中文名称

## 注意事项

1. 如果指定语言的字段为空，会自动回退到默认的中文名称
2. 如果language参数无效或为空，使用默认的中文名称
3. 确保数据库中的多语言字段有正确的数据
4. 分页信息（totalRow, pageNumber等）不受语言参数影响
