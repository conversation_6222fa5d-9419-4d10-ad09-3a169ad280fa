package com.sandu.xinye.common.model.base;

import com.jfinal.plugin.activerecord.Model;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by JF<PERSON>, do not modify this file.
 */
@SuppressWarnings({"serial", "unchecked"})
public abstract class BaseTempletBorder<M extends BaseTempletBorder<M>> extends Model<M> implements IBean {

	public M setBorderId(java.lang.Integer borderId) {
		set("borderId", borderId);
		return (M)this;
	}
	
	public java.lang.Integer getBorderId() {
		return getInt("borderId");
	}

	public M setBorderKindId(java.lang.Integer borderKindId) {
		set("borderKindId", borderKindId);
		return (M)this;
	}
	
	public java.lang.Integer getBorderKindId() {
		return getInt("borderKindId");
	}

	public M setHeaderImgUrl(java.lang.String headerImgUrl) {
		set("headerImgUrl", headerImgUrl);
		return (M)this;
	}
	
	public java.lang.String getHeaderImgUrl() {
		return getStr("headerImgUrl");
	}

	public M setFillImgUrl(java.lang.String fillImgUrl) {
		set("fillImgUrl", fillImgUrl);
		return (M)this;
	}
	
	public java.lang.String getFillImgUrl() {
		return getStr("fillImgUrl");
	}

	public M setFooterImgUrl(java.lang.String footerImgUrl) {
		set("footerImgUrl", footerImgUrl);
		return (M)this;
	}
	
	public java.lang.String getFooterImgUrl() {
		return getStr("footerImgUrl");
	}

	public M setThumbUrl(java.lang.String thumbUrl) {
		set("thumbUrl", thumbUrl);
		return (M)this;
	}
	
	public java.lang.String getThumbUrl() {
		return getStr("thumbUrl");
	}

	public M setSysUserId(java.lang.Integer sysUserId) {
		set("sysUserId", sysUserId);
		return (M)this;
	}
	
	public java.lang.Integer getSysUserId() {
		return getInt("sysUserId");
	}

	public M setCreateTime(java.util.Date createTime) {
		set("createTime", createTime);
		return (M)this;
	}
	
	public java.util.Date getCreateTime() {
		return get("createTime");
	}

}
