package com.sandu.xinye.api.v2.templetBorder;

import com.jfinal.kit.Kv;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.SqlPara;
import com.sandu.xinye.common.kit.RetKit;
import com.sandu.xinye.common.model.*;
import com.xiaoleilu.hutool.util.CollectionUtil;

import java.util.List;

public class TempletBorderService {

    public static final TempletBorderService me = new TempletBorderService();

    /*
     * 语言类型
     */
    private static final int LANGUAGE_SYSTEM = 0;
    private static final int LANGUAGE_CHINESE = 1;
    private static final int LANGUAGE_ENGLISH = 2;
    private static final int LANGUAGE_TRADITION = 3;
    private static final int LANGUAGE_KOREAN = 4;
    private static final int LANGUAGE_RUSSIAN = 5;

    public RetKit getBorderList() {
        List<TempletBorder> list = TempletBorder.dao.find("select * from templet_border");
        for (TempletBorder border : list) {
            TempletBorderKind kind = TempletBorderKind.dao.findById(border.getBorderKindId());
            String groupName = kind.getBorderKindName();
            border.put("groupName", groupName);
            border.remove("borderKindId", "sysUserId", "createTime");
        }
        return RetKit.ok("items", list);
    }


    /**
     * 获取模板边框类别列表
     *
     * @param language 显示的语言类型 depressed
     * @param lang     显示的语言类型
     * @return
     */
    public RetKit getBorderKindList(Integer language, String lang) {
        String queryKindName = "borderKindName";
        if (language == null) {
            language = LANGUAGE_CHINESE;
        }

        if (StrKit.notBlank(lang)) {
            queryKindName = String.format("%sName", lang);
            if (!isColumnExistInBorderKind(queryKindName)) {
                return RetKit.fail(String.format("参数lang:%s错误，语言不存在！", lang));
            }
        } else {
            if (language == LANGUAGE_TRADITION) {
                queryKindName = "traditionalName";
            } else if (language == LANGUAGE_ENGLISH) {
                queryKindName = "englishName";
            } else if (language == LANGUAGE_KOREAN) {
                queryKindName = "koreanName";
            } else if (language == LANGUAGE_RUSSIAN) {
                queryKindName = "russianName";
            }
        }

        List<TempletBorderKind> list = TempletBorderKind.dao.find(String.format("SELECT borderKindId, %s, %1$s as borderKindName from templet_border_kind order by createTime asc", queryKindName));

        return RetKit.ok("items", list);
    }

    private Boolean isColumnExistInBorderKind(String columnName) {
        List<TempletBorderKind> list = TempletBorderKind.dao.find(String.format("select * from information_schema.columns where table_name='templet_border_kind' and column_name='%s'", columnName));
        return CollectionUtil.isNotEmpty(list);
    }

    /**
     * @param pageNumber 分页
     * @param pageSize   分页
     * @param kindId     类别ID
     * @param language
     * @return
     */
    public RetKit getBorderPage(int pageNumber, int pageSize, String kindId, Integer language) {
        Kv kvParams = Kv.by("kindId", kindId).set("lang", language);

        try {
            SqlPara sqlPara = Db.getSqlPara("app.templet-border.paginate", kvParams);
            Page<TempletBorder> page = TempletBorder.dao.paginate(pageNumber, pageSize, sqlPara);
            return RetKit.ok("page", page);
        } catch (Exception e) {
            return RetKit.fail(e.getMessage());
        }
    }
}
