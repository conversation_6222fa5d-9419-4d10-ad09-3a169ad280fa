# 模板边框功能 - API接口文档

## 1. 概述

模板边框功能提供边框资源管理和多语言分类支持，包括边框列表查询、分页查询、分类管理等核心功能。

### 1.1 基础信息
- **基础路径**: `/api/business/v1/templetBorder`
- **支持格式**: JSON
- **字符编码**: UTF-8
- **支持语言**: 中文、英文、繁体中文、韩文、俄语、法语、西班牙语、德语、意大利语

### 1.2 返回格式说明

**边框分类接口返回格式**:
```json
{
    "msg": "操作成功",
    "code": "200",
    "success": true,
    "items": [...]
}
```

**边框分页接口返回格式**:
```json
{
    "msg": "操作成功",
    "code": "200",
    "success": true,
    "page": {
        "totalRow": 20,
        "pageNumber": 1,
        "pageSize": 10,
        "totalPage": 2,
        "firstPage": true,
        "lastPage": false,
        "list": [...]
    }
}
```

**其他接口返回格式**:
```json
{
    "data": {},
    "head": {
        "method": "GET",
        "msg": "成功",
        "ret": 0,
        "timestamp": "2025-08-20 12:00:00",
        "uri": "/api/business/v1/templetBorder/xxx",
        "requestId": "xxx"
    }
}
```

## 2. 边框分类接口

### 2.1 获取边框分类列表

**接口地址**: `GET /getBorderKindList`

**功能描述**: 获取边框分类列表，支持多语言显示

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 | 示例 |
|--------|------|------|------|------|
| language | Integer | 否 | 语言类型(1:中文,2:英文,3:繁体,4:韩文,5:俄语) | 2 |
| lang | String | 否 | 语言标识(english,traditional,korean,russian,french,spanish,germany,italy) | english |

**注意**: `lang`参数优先级高于`language`参数

**响应示例**:

**韩文分类列表** (`?lang=korean`):
```json
{
    "msg": "操作成功",
    "code": "200",
    "success": true,
    "items": [
        {
            "borderKindId": "kind_001",
            "borderKindName": "심플한",
            "koreanName": "심플한"
        },
        {
            "borderKindId": "kind_002",
            "borderKindName": "장식적인",
            "koreanName": "장식적인"
        }
    ]
}
```

**英文分类列表** (`?lang=english`):
```json
{
    "msg": "操作成功",
    "code": "200",
    "success": true,
    "items": [
        {
            "borderKindId": "kind_001",
            "borderKindName": "Simple Border",
            "englishName": "Simple Border"
        },
        {
            "borderKindId": "kind_002",
            "borderKindName": "Decorative Border",
            "englishName": "Decorative Border"
        }
    ]
}
```

## 3. 边框查询接口

### 3.1 获取所有边框列表

**接口地址**: `GET /getBorderList`

**功能描述**: 获取所有边框列表，包含基本信息和分类名称

**请求参数**: 无

**响应示例**:
```json
{
    "data": [
        {
            "borderId": "border_001",
            "headerImgUrl": "/images/borders/simple/header_001.png",
            "fillImgUrl": "/images/borders/simple/fill_001.png", 
            "footerImgUrl": "/images/borders/simple/footer_001.png",
            "thumbUrl": "/images/borders/simple/thumb_001.png",
            "groupName": "简约边框",
            "createTime": "2025-08-20 10:00:00"
        }
    ],
    "head": {
        "ret": 0,
        "msg": "成功"
    }
}
```

### 3.2 分页查询边框列表

**接口地址**: `GET /getBorderPage`

**功能描述**: 分页查询边框列表，支持按分类过滤

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 | 示例 |
|--------|------|------|------|------|
| pageNumber | Integer | 否 | 页码，默认1 | 1 |
| pageSize | Integer | 否 | 页大小，默认10，最大100 | 10 |
| borderKindId | String | 否 | 边框分类ID | kind_001 |
| language | Integer | 否 | 语言类型，默认1 | 2 |

**响应示例**:
```json
{
    "msg": "操作成功",
    "code": "200",
    "success": true,
    "page": {
        "totalRow": 20,
        "pageNumber": 1,
        "pageSize": 10,
        "totalPage": 2,
        "firstPage": true,
        "lastPage": false,
        "list": [
            {
                "borderId": "1",
                "borderKindName": "简单",
                "headerImgUrl": "https://img.ycjqb.com/img/templet-border/01/header.png",
                "fillImgUrl": "http://img.ycjqb.com/img/templet-border/01/fill.png",
                "footerImgUrl": "https://img.ycjqb.com/img/templet-border/01/footer.png",
                "thumbUrl": "https://img.ycjqb.com/img/templet-border/01/thumb.png"
            },
            {
                "borderId": "2",
                "borderKindName": "简单",
                "headerImgUrl": "https://img.ycjqb.com/img/templet-border/02/header.png",
                "fillImgUrl": "http://img.ycjqb.com/img/templet-border/02/fill.png",
                "footerImgUrl": "https://img.ycjqb.com/img/templet-border/02/footer.png",
                "thumbUrl": "https://img.ycjqb.com/img/templet-border/02/thumb.png"
            }
        ]
    }
}
```

### 3.3 增强分页查询

**接口地址**: `GET /getBorderPageEnhanced`

**功能描述**: 增强的分页查询，支持更多参数和多语言信息

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 | 示例 |
|--------|------|------|------|------|
| pageNumber | Integer | 否 | 页码，默认1 | 1 |
| pageSize | Integer | 否 | 页大小，默认10 | 10 |
| borderKindId | String | 否 | 边框分类ID | kind_001 |
| language | Integer | 否 | 语言类型 | 2 |
| lang | String | 否 | 语言标识 | english |
| includeMultiLang | Boolean | 否 | 是否包含多语言信息，默认false | true |
| includeKindInfo | Boolean | 否 | 是否包含分类信息，默认true | true |

### 3.4 根据分类查询边框

**接口地址**: `GET /getBordersByKind`

**功能描述**: 根据分类ID查询该分类下的所有边框

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 | 示例 |
|--------|------|------|------|------|
| borderKindId | String | 是 | 边框分类ID | kind_001 |

## 4. 边框详情接口

### 4.1 获取边框详情

**接口地址**: `GET /getBorderDetail`

**功能描述**: 根据边框ID获取详细信息

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 | 示例 |
|--------|------|------|------|------|
| borderId | String | 是 | 边框ID | border_001 |

**响应示例**:
```json
{
    "data": {
        "borderId": "border_001",
        "borderKindId": "kind_001",
        "headerImgUrl": "/images/borders/simple/header_001.png",
        "fillImgUrl": "/images/borders/simple/fill_001.png",
        "footerImgUrl": "/images/borders/simple/footer_001.png",
        "thumbUrl": "/images/borders/simple/thumb_001.png",
        "groupName": "简约边框",
        "createTime": "2025-08-20 10:00:00"
    },
    "head": {
        "ret": 0,
        "msg": "成功"
    }
}
```

### 4.2 获取边框详情（含多语言）

**接口地址**: `GET /getBorderDetailWithLanguage`

**功能描述**: 获取边框详情，包含多语言分类信息

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 | 示例 |
|--------|------|------|------|------|
| borderId | String | 是 | 边框ID | border_001 |
| language | Integer | 否 | 语言类型 | 2 |
| lang | String | 否 | 语言标识 | english |

## 5. 统计信息接口

### 5.1 获取统计信息

**接口地址**: `GET /getStatistics`

**功能描述**: 获取边框和分类的统计信息

**请求参数**: 无

**响应示例**:
```json
{
    "data": {
        "borderCount": 10,
        "kindCount": 5
    },
    "head": {
        "ret": 0,
        "msg": "成功"
    }
}
```

### 5.2 获取分页统计信息

**接口地址**: `GET /getBorderPageStatistics`

**功能描述**: 获取分页查询的统计信息

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 | 示例 |
|--------|------|------|------|------|
| borderKindId | String | 否 | 边框分类ID | kind_001 |

## 6. 工具接口

### 6.1 检查边框是否存在

**接口地址**: `GET /checkBorderExists`

**功能描述**: 检查指定边框ID是否存在

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 | 示例 |
|--------|------|------|------|------|
| borderId | String | 是 | 边框ID | border_001 |

### 6.2 验证语言参数

**接口地址**: `GET /validateLanguage`

**功能描述**: 验证语言标识是否有效

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 | 示例 |
|--------|------|------|------|------|
| lang | String | 是 | 语言标识 | english |

## 7. 错误码说明

| 错误码 | 说明 | 解决方案 |
|--------|------|----------|
| 0 | 成功 | - |
| 1 | 系统错误 | 检查系统状态 |
| 400 | 参数错误 | 检查请求参数 |
| 404 | 资源不存在 | 检查资源ID |
| 500 | 服务器内部错误 | 联系技术支持 |

## 8. 多语言支持

### 8.1 支持的语言类型
- **1**: 中文（默认）
- **2**: 英文
- **3**: 繁体中文
- **4**: 韩文
- **5**: 俄语

### 8.2 支持的语言标识
- **chinese**: 中文
- **english**: 英文
- **traditional**: 繁体中文
- **korean**: 韩文
- **russian**: 俄语
- **french**: 法语
- **spanish**: 西班牙语
- **germany**: 德语
- **italy**: 意大利语

## 9. 使用示例

### 9.1 获取英文分类列表
```bash
curl -X GET "http://localhost:8080/api/business/v1/templetBorder/getBorderKindList?lang=english"
```

### 9.2 分页查询简约边框
```bash
curl -X GET "http://localhost:8080/api/business/v1/templetBorder/getBorderPage?pageNumber=1&pageSize=5&borderKindId=kind_001"
```

### 9.3 获取边框详情
```bash
curl -X GET "http://localhost:8080/api/business/v1/templetBorder/getBorderDetail?borderId=border_001"
```
