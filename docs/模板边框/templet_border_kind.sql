/*
 Navicat Premium Dump SQL

 Source Server         : vida
 Source Server Type    : MySQL
 Source Server Version : 50742 (5.7.42-log)
 Source Host           : localhost:3302
 Source Schema         : barcode4_test

 Target Server Type    : MySQL
 Target Server Version : 50742 (5.7.42-log)
 File Encoding         : 65001

 Date: 20/08/2025 14:57:16
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for v1_templet_border_kind
-- ----------------------------
DROP TABLE IF EXISTS `v1_templet_border_kind`;
CREATE TABLE `v1_templet_border_kind`  (
  `borderKindId` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '边框分类Id',
  `borderKindName` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '边框分类名称',
  `englishName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '英文',
  `traditionalName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '繁体',
  `koreanName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '韩文',
  `russianName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '俄语',
  `sysUserId` int(11) NOT NULL COMMENT '操作人id',
  `createTime` datetime NOT NULL COMMENT '创建日期',
  `frenchName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '法语',
  `spanishName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '西班牙语',
  `germanyName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '德语',
  `italyName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '意大利语',
  PRIMARY KEY (`borderKindId`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '模板边框父类表' ROW_FORMAT = DYNAMIC;

SET FOREIGN_KEY_CHECKS = 1;
