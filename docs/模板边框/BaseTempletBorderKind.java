package com.sandu.xinye.common.model.base;

import com.jfinal.plugin.activerecord.Model;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by JFinal, do not modify this file.
 */
@SuppressWarnings({"serial", "unchecked"})
public abstract class BaseTempletBorderKind<M extends BaseTempletBorderKind<M>> extends Model<M> implements IBean {

	public M setBorderKindId(java.lang.Integer borderKindId) {
		set("borderKindId", borderKindId);
		return (M)this;
	}
	
	public java.lang.Integer getBorderKindId() {
		return getInt("borderKindId");
	}

	public M setBorderKindName(java.lang.String borderKindName) {
		set("borderKindName", borderKindName);
		return (M)this;
	}
	
	public java.lang.String getBorderKindName() {
		return getStr("borderKindName");
	}

	public M setEnglishName(java.lang.String englishName) {
		set("englishName", englishName);
		return (M)this;
	}
	
	public java.lang.String getEnglishName() {
		return getStr("englishName");
	}

	public M setTraditionalName(java.lang.String traditionalName) {
		set("traditionalName", traditionalName);
		return (M)this;
	}
	
	public java.lang.String getTraditionalName() {
		return getStr("traditionalName");
	}

	public M setKoreanName(java.lang.String koreanName) {
		set("koreanName", koreanName);
		return (M)this;
	}
	
	public java.lang.String getKoreanName() {
		return getStr("koreanName");
	}

	public M setRussianName(java.lang.String russianName) {
		set("russianName", russianName);
		return (M)this;
	}
	
	public java.lang.String getRussianName() {
		return getStr("russianName");
	}

	public M setSysUserId(java.lang.Integer sysUserId) {
		set("sysUserId", sysUserId);
		return (M)this;
	}
	
	public java.lang.Integer getSysUserId() {
		return getInt("sysUserId");
	}

	public M setCreateTime(java.util.Date createTime) {
		set("createTime", createTime);
		return (M)this;
	}
	
	public java.util.Date getCreateTime() {
		return get("createTime");
	}

}
