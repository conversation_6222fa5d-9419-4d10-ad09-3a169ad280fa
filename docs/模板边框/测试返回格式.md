# 边框分类接口返回格式测试

## 修改说明

根据用户反馈，边框分类接口的返回格式需要调整为：

### 期望格式
```json
{
    "msg": "操作成功",
    "code": "200",
    "success": true,
    "items": [
        {
            "borderKindId": "1",
            "borderKindName": "심플한",
            "koreanName": "심플한"
        }
    ]
}
```

### 修改内容

1. **返回格式调整**：
   - 从 `{data: [...], head: {...}}` 改为 `{msg, code, success, items: [...]}`
   - 只有边框分类接口使用新格式，其他接口保持原格式

2. **数据字段优化**：
   - 只返回必要字段：`borderKindId`, `borderKindName`, `{language}Name`
   - 移除不必要的字段：`createTime`, `sysUserId`, 其他语言字段等

3. **多语言字段处理**：
   - 根据`lang`参数动态添加对应的语言字段
   - `lang=korean` → 添加 `koreanName` 字段
   - `lang=english` → 添加 `englishName` 字段
   - 以此类推

## 测试用例

### 测试1：韩文分类列表
```bash
curl -X GET "http://localhost:8080/api/business/v1/templetBorder/getBorderKindList?lang=korean"
```

期望返回：
```json
{
    "msg": "操作成功",
    "code": "200",
    "success": true,
    "items": [
        {
            "borderKindId": "kind_001",
            "borderKindName": "심플한",
            "koreanName": "심플한"
        }
    ]
}
```

### 测试2：英文分类列表
```bash
curl -X GET "http://localhost:8080/api/business/v1/templetBorder/getBorderKindList?lang=english"
```

期望返回：
```json
{
    "msg": "操作成功",
    "code": "200",
    "success": true,
    "items": [
        {
            "borderKindId": "kind_001",
            "borderKindName": "Simple Border",
            "englishName": "Simple Border"
        }
    ]
}
```

### 测试3：中文分类列表（默认）
```bash
curl -X GET "http://localhost:8080/api/business/v1/templetBorder/getBorderKindList"
```

期望返回：
```json
{
    "msg": "操作成功",
    "code": "200",
    "success": true,
    "items": [
        {
            "borderKindId": "kind_001",
            "borderKindName": "简约边框"
        }
    ]
}
```

### 测试4：错误情况
```bash
curl -X GET "http://localhost:8080/api/business/v1/templetBorder/getBorderKindList?lang=invalid"
```

期望返回：
```json
{
    "msg": "参数lang:invalid错误，语言不存在！",
    "code": "500",
    "success": false,
    "items": null
}
```

## 验证清单

- [ ] 韩文分类列表返回正确格式
- [ ] 英文分类列表返回正确格式
- [ ] 中文分类列表返回正确格式
- [ ] 繁体中文分类列表返回正确格式
- [ ] 俄语分类列表返回正确格式
- [ ] 法语分类列表返回正确格式
- [ ] 西班牙语分类列表返回正确格式
- [ ] 德语分类列表返回正确格式
- [ ] 意大利语分类列表返回正确格式
- [ ] 错误参数处理正确
- [ ] 其他接口格式未受影响

## 边框分页接口返回格式修改

### 期望格式
```json
{
    "msg": "操作成功",
    "code": "200",
    "success": true,
    "page": {
        "totalRow": 20,
        "pageNumber": 1,
        "pageSize": 10,
        "totalPage": 2,
        "firstPage": true,
        "lastPage": false,
        "list": [
            {
                "borderId": "1",
                "borderKindName": "简单",
                "headerImgUrl": "https://img.ycjqb.com/img/templet-border/01/header.png",
                "fillImgUrl": "http://img.ycjqb.com/img/templet-border/01/fill.png",
                "footerImgUrl": "https://img.ycjqb.com/img/templet-border/01/footer.png",
                "thumbUrl": "https://img.ycjqb.com/img/templet-border/01/thumb.png"
            }
        ]
    }
}
```

### 边框分页接口测试用例

#### 测试1：基础分页查询
```bash
curl -X GET "http://localhost:8080/api/business/v1/templetBorder/getBorderPage?pageNumber=1&pageSize=10"
```

#### 测试2：按分类过滤分页
```bash
curl -X GET "http://localhost:8080/api/business/v1/templetBorder/getBorderPage?pageNumber=1&pageSize=10&borderKindId=kind_001"
```

#### 测试3：多语言分页查询
```bash
curl -X GET "http://localhost:8080/api/business/v1/templetBorder/getBorderPage?pageNumber=1&pageSize=10&language=2"
```

## 注意事项

1. **边框分类接口**使用 `{msg, code, success, items: [...]}` 格式
2. **边框分页接口**使用 `{msg, code, success, page: {...}}` 格式
3. **其他接口**（如getBorderList等）仍使用原有的`{data, head}`格式
4. **语言字段**根据请求的`lang`参数动态添加
5. **错误处理**也使用对应的新格式
