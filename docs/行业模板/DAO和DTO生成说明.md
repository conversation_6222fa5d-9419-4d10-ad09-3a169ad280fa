# TemplateDomestic DAO和DTO生成说明

## 概述

根据现有项目的DAO和DTO文件结构，为TemplateDomestic实体类生成了完整的数据访问层和数据传输对象。

## 生成的文件

### 1. Mapper接口
**文件**: `src/main/java/net/snaptag/system/business/mapper/TemplateDomesticMapper.java`

- 继承`BaseMapper<TemplateDomestic>`
- 提供基础的CRUD操作
- 包含自定义查询方法

### 2. DAO数据访问层
**文件**: `src/main/java/net/snaptag/system/business/dao/TemplateDomesticDao.java`

- 继承`ServiceImpl<TemplateDomesticMapper, TemplateDomestic>`
- 提供业务层数据访问方法
- 支持复杂查询和分页

### 3. DTO数据传输对象
**文件**: `src/main/java/net/snaptag/system/business/dto/TemplateDomesticDto.java`

- 实现`Serializable`接口
- 包含所有业务字段
- 提供完整的getter/setter方法

## Mapper接口功能

### 基础查询方法
```java
// 分页查询
IPage<TemplateDomestic> findPage(Page<TemplateDomestic> page, String status);

// 查询所有列表
List<TemplateDomestic> findAllList(String status);
```

### 条件查询方法
```java
// 根据用户ID查询
List<TemplateDomestic> findByUserId(String status, Integer userId);

// 根据模板类型查询
List<TemplateDomestic> findByType(String status, Integer type);

// 根据机器类型查询
List<TemplateDomestic> findByMachineType(String status, Integer machineType);

// 根据纸张类型查询
List<TemplateDomestic> findByPaperType(String status, Integer paperType);

// 根据分组ID查询
List<TemplateDomestic> findByGroupId(String status, Integer groupId);

// 根据名称模糊查询
List<TemplateDomestic> findByNameLike(String status, String name);
```

### 统计方法
```java
// 统计用户模板数量
Long countByUserId(String status, Integer userId);

// 统计指定类型模板数量
Long countByType(String status, Integer type);
```

## DAO层功能

### 复合查询方法
```java
public List<TemplateDomestic> findTemplateDomesticList(
    Integer userId, Integer type, Integer machineType, 
    Integer paperType, Integer groupId, String name, 
    int pageNo, int pageSize
)
```

**支持的查询条件**：
- `userId` - 用户ID
- `type` - 模板类型
- `machineType` - 机器类型
- `paperType` - 纸张类型
- `groupId` - 分组ID
- `name` - 模板名称（模糊查询）
- 分页参数

### 分页查询方法
```java
public IPage<TemplateDomestic> findTemplateDomesticPage(
    Integer userId, Integer type, Integer machineType, 
    Integer paperType, Integer groupId, String name, 
    int pageNo, int pageSize
)
```

### 单条件查询方法
```java
// 根据用户ID查询
List<TemplateDomestic> findByUserId(Integer userId);

// 根据模板类型查询
List<TemplateDomestic> findByType(Integer type);

// 根据机器类型查询
List<TemplateDomestic> findByMachineType(Integer machineType);

// 根据纸张类型查询
List<TemplateDomestic> findByPaperType(Integer paperType);
```

### 统计方法
```java
// 统计用户模板数量
long countByUserId(Integer userId);

// 统计指定类型模板数量
long countByType(Integer type);

// 根据多个条件统计
long countByConditions(Integer userId, Integer type, Integer machineType, Integer paperType);
```

## DTO字段说明

### 基础信息字段
- `id` - 主键ID
- `userId` - 用户ID
- `groupId` - 模板分组ID
- `name` - 模板名称
- `cover` - 封面图片地址
- `data` - 标签内容数据

### 尺寸和布局字段
- `gap` - 间隙
- `height` - 高度
- `width` - 宽度
- `labelNum` - 标签数量
- `labelGap` - 多排标签间距

### 打印设置字段
- `paperType` - 纸张类型
- `printDirection` - 打印方向
- `machineType` - 机器类型
- `cutAfterPrint` - 打印后切纸
- `printType` - 打印类型

### 多语言字段
- `nameEn` - 英语名称
- `nameKor` - 韩语名称
- `nameJP` - 日语名称
- `nameHK` - 繁体中文名称
- `nameRU` - 俄语名称

### 其他字段
- `type` - 模板类型
- `updateTime` - 更新时间
- `createTime` - 创建时间
- `status` - 状态

## 使用示例

### 基础CRUD操作
```java
@Autowired
private TemplateDomesticDao templateDomesticDao;

// 查询单个模板
TemplateDomestic template = templateDomesticDao.getById("1755587374743970137");

// 新增模板
TemplateDomestic newTemplate = new TemplateDomestic();
newTemplate.setName("新模板");
newTemplate.setUserId(201733);
templateDomesticDao.save(newTemplate);

// 更新模板
template.setName("更新后的名称");
templateDomesticDao.updateById(template);

// 删除模板
templateDomesticDao.removeById("1755587374743970137");
```

### 条件查询
```java
// 查询用户的所有模板
List<TemplateDomestic> userTemplates = templateDomesticDao.findByUserId(201733);

// 查询指定类型的模板
List<TemplateDomestic> industryTemplates = templateDomesticDao.findByType(1);

// 复合条件查询
List<TemplateDomestic> templates = templateDomesticDao.findTemplateDomesticList(
    201733,  // userId
    1,       // type
    1,       // machineType
    1,       // paperType
    null,    // groupId
    "标签",   // name
    1,       // pageNo
    10       // pageSize
);
```

### 分页查询
```java
IPage<TemplateDomestic> page = templateDomesticDao.findTemplateDomesticPage(
    null,    // userId - 查询所有用户
    1,       // type - 行业模板
    1,       // machineType - 标签机器
    null,    // paperType - 所有纸张类型
    null,    // groupId - 所有分组
    null,    // name - 不限制名称
    1,       // pageNo - 第1页
    20       // pageSize - 每页20条
);

List<TemplateDomestic> records = page.getRecords();
long total = page.getTotal();
```

### 统计查询
```java
// 统计用户模板数量
long userTemplateCount = templateDomesticDao.countByUserId(201733);

// 统计行业模板数量
long industryTemplateCount = templateDomesticDao.countByType(1);

// 复合条件统计
long count = templateDomesticDao.countByConditions(201733, 1, 1, 1);
```

### DTO转换
```java
// Entity转DTO
TemplateDomestic entity = templateDomesticDao.getById("1755587374743970137");
TemplateDomesticDto dto = new TemplateDomesticDto();

// 手动属性复制
dto.setId(entity.getId());
dto.setName(entity.getName());
dto.setUserId(entity.getUserId());
// ... 其他属性

// 或使用工具类
BeanUtils.copyProperties(entity, dto);
```

## 技术特点

### 1. 标准化设计
- 遵循项目现有的DAO/DTO设计模式
- 使用MyBatis-Plus提供的基础功能
- 统一的命名规范和代码风格

### 2. 灵活查询
- 支持单条件和多条件组合查询
- 提供分页和非分页两种查询方式
- 支持模糊查询和精确查询

### 3. 性能优化
- 使用QueryWrapper构建动态查询条件
- 避免不必要的字段查询
- 支持索引优化的查询方式

### 4. 扩展性
- 易于添加新的查询方法
- 支持复杂的业务查询需求
- 便于后续功能扩展

## 注意事项

### 1. 数据类型
- 统计方法返回`long`类型而不是`Long`
- BigDecimal用于精度要求高的数值字段
- 时间字段使用Date类型

### 2. 查询条件
- 所有查询都包含status条件过滤
- 支持null值参数，表示不限制该条件
- 默认按创建时间倒序排列

### 3. 分页参数
- pageNo从1开始计数
- pageSize为0表示不分页查询
- 返回IPage对象包含总数和记录列表

## 总结

✅ **完整性**: 提供了完整的数据访问层实现
✅ **标准化**: 符合项目现有的代码规范
✅ **功能性**: 支持各种业务查询需求
✅ **扩展性**: 易于维护和功能扩展
✅ **性能**: 优化的查询条件和分页支持

现在可以使用这些DAO和DTO类来实现TemplateDomestic相关的业务功能了！
