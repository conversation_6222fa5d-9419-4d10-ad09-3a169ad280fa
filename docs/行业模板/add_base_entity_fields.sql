-- 为v1_templet_domestic表添加BaseEntity需要的字段

-- 添加createuserid字段
ALTER TABLE `v1_templet_domestic` 
ADD COLUMN `createuserid` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '创建人ID' AFTER `createTime`;

-- 添加updateuserid字段  
ALTER TABLE `v1_templet_domestic`
ADD COLUMN `updateuserid` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '更新人ID' AFTER `updateTime`;

-- 添加status字段（逻辑删除）
ALTER TABLE `v1_templet_domestic`
ADD COLUMN `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '审核通过' COMMENT '数据状态' AFTER `printType`;

-- 添加source字段
ALTER TABLE `v1_templet_domestic`
ADD COLUMN `source` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '数据来源' AFTER `status`;

-- 添加app_id字段
ALTER TABLE `v1_templet_domestic`
ADD COLUMN `app_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '应用渠道ID' AFTER `source`;

-- 修改createTime字段名为createtime（与BaseEntity保持一致）
ALTER TABLE `v1_templet_domestic`
CHANGE COLUMN `createTime` `createtime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';

-- 修改updateTime字段名为updatetime（与BaseEntity保持一致）
ALTER TABLE `v1_templet_domestic`
CHANGE COLUMN `updateTime` `updatetime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';

-- 创建索引
CREATE INDEX `idx_status` ON `v1_templet_domestic`(`status`);
CREATE INDEX `idx_createuserid` ON `v1_templet_domestic`(`createuserid`);
CREATE INDEX `idx_app_id` ON `v1_templet_domestic`(`app_id`);
