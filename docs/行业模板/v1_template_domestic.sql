/*
 Navicat Premium Dump SQL

 Source Server         : vida
 Source Server Type    : MySQL
 Source Server Version : 50742 (5.7.42-log)
 Source Host           : localhost:3302
 Source Schema         : barcode4_test

 Target Server Type    : MySQL
 Target Server Version : 50742 (5.7.42-log)
 File Encoding         : 65001

 Date: 19/08/2025 14:30:54
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for templet
-- ----------------------------
DROP TABLE IF EXISTS `v1_templet_domestic`;
CREATE TABLE `v1_templet_domestic`  (
                                        `id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '主键ID',
                                        `userId` int(11) NOT NULL,
                                        `groupId` int(11) NOT NULL DEFAULT -1 COMMENT '模板分组id',
                                        `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
                                        `cover` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
                                        `gap` float(12, 2) NOT NULL DEFAULT 2.00,
  `height` float(20, 16) NOT NULL,
  `width` float(20, 16) NOT NULL,
  `paperType` tinyint(2) NOT NULL COMMENT '纸张类型  1-间隙纸  2-连续纸   3-黑标纸  4-定孔纸',
  `printDirection` int(11) NOT NULL,
  `data` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '标签内容数据',
  `blackLabelGap` float(12, 2) NOT NULL DEFAULT 0.00,
  `blackLabelOffset` float(12, 2) NOT NULL DEFAULT 0.00,
  `createTime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `type` tinyint(4) NULL DEFAULT 0 COMMENT '0 - 1.0老版本数据  1 - 行业模板  2 - 2.0新数据',
  `nameEn` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '模板英语名称（行业模板）',
  `nameKor` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '模板韩语名称（行业模板）',
  `updateTime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `machineType` tinyint(4) NULL DEFAULT NULL COMMENT '机器类型: 1-标签 2-票据 3-76针打',
  `cutAfterPrint` tinyint(4) NOT NULL DEFAULT 0 COMMENT '打印后切纸 0-否 1-是',
  `labelNum` int(11) NOT NULL DEFAULT 1 COMMENT '标签数量，默认为 1，大于1为多排标签',
  `labelGap` float(12, 2) NOT NULL DEFAULT 0.00 COMMENT '多排标签间距',
  `nameJP` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '模板日语名称（行业模板）',
  `nameHK` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '模板HK繁体名称（行业模板）',
  `multiLabelType` tinyint(4) NOT NULL DEFAULT 0 COMMENT '多排类型 0 - 不复制  1 - 仅复制首排标签',
  `paperTearType` int(1) NOT NULL DEFAULT 1 COMMENT '撕纸类型  1-撕离  2 -剥离',
  `shareUser` int(11) NULL DEFAULT NULL,
  `labelType` int(1) NULL DEFAULT NULL COMMENT '标签类型 labelType字段整型（标签形状）   1.矩形 2.圆角矩形 3.圆   ',
  `ticketMachineType` tinyint(4) NULL DEFAULT NULL COMMENT '票据模板机器子类型: 1-58票据 2-80票据 3-76针打',
  `paperFeedCount` int(11) NULL DEFAULT NULL COMMENT '打印后走纸长度',
  `mirrorImage` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否开启镜像： 0 - 否 1 - 是',
  `nameRU` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '模板俄语名称（行业模板）',
  `printType` int(1) NOT NULL DEFAULT 0 COMMENT '打印类型 label：标签打印（0） pdf：PDF 打印（1） image：图片打印（2）',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_type_groupId`(`type`, `groupId`) USING BTREE,
  INDEX `idx_userId`(`userId`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '家用行业模板' ROW_FORMAT = DYNAMIC;

SET FOREIGN_KEY_CHECKS = 1;
