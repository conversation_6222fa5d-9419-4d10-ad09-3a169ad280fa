# getDomesticTemplateList方法实现说明

## 概述

参考JFinal框架的实现思路，使用MyBatis-Plus框架完成了`getDomesticTemplateList`方法的开发。该方法支持分页查询、条件过滤和尺寸范围查询。

## 实现文件

### 1. 服务层
**文件**: `src/main/java/net/snaptag/system/business/buservice/TemplateDomesticService.java`

### 2. 数据访问层扩展
**文件**: `src/main/java/net/snaptag/system/business/dao/TemplateDomesticDao.java`
- 新增了支持尺寸范围查询的方法

### 3. 控制器层
**文件**: `src/main/java/net/snaptag/system/business/controller/TemplateDomesticController.java`

## 核心实现

### JFinal参考代码分析
```java
// JFinal原始实现思路
if (StrKit.notBlank(name)) {
    name = "%" + name + "%";  // 模糊查询处理
}
Kv kvParams = Kv.by("groupId", groupId).set("name", name);
if (StrKit.notBlank(indexRange)) {
    String[] indexRangeArr = indexRange.split(",");
    kvParams.set("widthBegin", indexRangeArr[0]);
    if (indexRangeArr.length > 1) {
        kvParams.set("widthEnd", indexRangeArr[1]);
    }
}
SqlPara sqlPara = Db.getSqlPara("app.templet.busi.paginate", kvParams);
Page<Templet> page = Templet.dao.paginate(pageNumber, pageSize, sqlPara);
return RetKit.ok("page", page);
```

### MyBatis-Plus实现
```java
public List<TemplateDomesticDto> getDomesticTemplateList(int pageNumber, int pageSize, 
                                                        String name, String groupId, String indexRange) {
    // 1. 参数处理（参考JFinal思路）
    Integer groupIdInt = null;
    if (ToolsKit.String.isNotBlank(groupId)) {
        groupIdInt = Integer.parseInt(groupId);
    }

    String namePattern = null;
    if (ToolsKit.String.isNotBlank(name)) {
        namePattern = name; // DAO层处理LIKE查询
    }

    // 2. 尺寸范围处理（参考JFinal的indexRange处理）
    BigDecimal widthBegin = null;
    BigDecimal widthEnd = null;
    if (ToolsKit.String.isNotBlank(indexRange)) {
        String[] indexRangeArr = indexRange.split(",");
        if (indexRangeArr.length > 0) {
            widthBegin = new BigDecimal(indexRangeArr[0]);
        }
        if (indexRangeArr.length > 1) {
            widthEnd = new BigDecimal(indexRangeArr[1]);
        }
    }

    // 3. 调用DAO查询
    List<TemplateDomestic> list = templateDomesticDao.findTemplateDomesticListWithRange(
        groupIdInt, namePattern, widthBegin, widthEnd, pageNumber, pageSize
    );

    // 4. 转换为DTO
    return convertToDto(list);
}
```

## DAO层扩展

### 新增方法
```java
/**
 * 根据条件查询模板列表（支持尺寸范围查询）
 * 参考JFinal的实现思路
 */
public List<TemplateDomestic> findTemplateDomesticListWithRange(
    Integer groupId, String name, BigDecimal widthBegin, BigDecimal widthEnd, 
    int pageNo, int pageSize
) {
    QueryWrapper<TemplateDomestic> queryWrapper = new QueryWrapper<>();
    queryWrapper.eq("status", DataConst.DATA_SUCCESS_STATUS);

    // 分组ID条件
    if (groupId != null) {
        queryWrapper.eq("groupId", groupId);
    }

    // 名称模糊查询（参考JFinal的name处理）
    if (ToolsKit.isNotEmpty(name)) {
        queryWrapper.like("name", name);
    }

    // 尺寸范围查询（参考JFinal的indexRange处理）
    if (widthBegin != null) {
        queryWrapper.ge("width", widthBegin);
    }
    if (widthEnd != null) {
        queryWrapper.le("width", widthEnd);
    }

    queryWrapper.orderByDesc("createtime");
    // 分页处理...
}
```

## 控制器接口

### 主要接口
```java
/**
 * 获取家用行业模板列表
 * 参考JFinal实现思路
 */
@RequestMapping(value = "/getlist")
public ReturnDto getDomesticTemplateList() {
    // 获取参数
    int pageNumber = ToolsKit.isEmpty(pageNumberStr) ? 1 : Integer.parseInt(pageNumberStr);
    int pageSize = ToolsKit.isEmpty(pageSizeStr) ? 20 : Integer.parseInt(pageSizeStr);
    String name = this.getValue("name");
    String groupId = this.getValue("groupId");
    String indexRange = this.getValue("indexRange");

    // 调用服务
    List<TemplateDomesticDto> list = templateDomesticService.getDomesticTemplateList(
        pageNumber, pageSize, name, groupId, indexRange
    );

    // 构造返回结果（参考JFinal的RetKit.ok格式）
    Map<String, Object> result = new HashMap<>();
    result.put("list", list);
    result.put("pageNumber", pageNumber);
    result.put("pageSize", pageSize);
    result.put("total", list.size());

    return this.returnSuccessJson(ExceptionEnums.SUCCESS, result);
}
```

## 参数说明

### 输入参数
- **pageNumber**: 页码（默认1）
- **pageSize**: 页大小（默认20）
- **name**: 模板名称（支持模糊查询）
- **groupId**: 分组ID（精确匹配）
- **indexRange**: 尺寸范围（格式：`宽度开始,宽度结束`）

### 参数处理逻辑
1. **name处理**: 直接传递给DAO，由QueryWrapper的like方法处理模糊查询
2. **groupId处理**: 字符串转Integer，支持null值
3. **indexRange处理**: 按逗号分割，转换为BigDecimal类型的宽度范围

## 接口示例

### 基础查询
```
GET /api/business/v1/templatedomestic/getlist?pageNumber=1&pageSize=10
```

### 条件查询
```
GET /api/business/v1/templatedomestic/getlist?pageNumber=1&pageSize=10&name=标签&groupId=285
```

### 尺寸范围查询
```
GET /api/business/v1/templatedomestic/getlist?pageNumber=1&pageSize=10&indexRange=10,50
```

### 组合查询
```
GET /api/business/v1/templatedomestic/getlist?pageNumber=1&pageSize=10&name=标签&groupId=285&indexRange=10,50
```

## 返回格式

### 成功响应
```json
{
  "head": {
    "ret": 0,
    "msg": "成功"
  },
  "data": {
    "list": [
      {
        "id": "1755587374743970137",
        "name": "模板名称",
        "groupId": 285,
        "width": 25.0,
        "height": 15.0,
        "cover": "封面图片URL",
        // ... 其他字段
      }
    ],
    "pageNumber": 1,
    "pageSize": 10,
    "total": 1
  }
}
```

## 其他接口

### 分页查询接口
```
GET /api/business/v1/templatedomestic/getpagelist
```
返回标准的MyBatis-Plus分页对象。

### 条件查询接口
```
GET /api/business/v1/templatedomestic/findlist?userId=201733&type=1&machineType=1
```

### 模板详情接口
```
GET /api/business/v1/templatedomestic/getdetail?id=1755587374743970137
```

### 统计接口
```
GET /api/business/v1/templatedomestic/count?userId=201733&type=1
```

### 调试接口
```
GET /api/business/v1/templatedomestic/debug?name=标签&groupId=285&indexRange=10,50
```

## 技术特点

### 1. 参考JFinal思路
- 参数处理逻辑与JFinal保持一致
- 支持相同的查询条件和格式
- 返回结果结构类似

### 2. MyBatis-Plus优势
- 类型安全的查询构建
- 自动分页支持
- 实体映射和缓存支持

### 3. 扩展性
- 易于添加新的查询条件
- 支持复杂的业务逻辑
- 便于性能优化

## 测试建议

### 1. 基础功能测试
- 测试分页功能
- 测试各种查询条件
- 测试参数边界情况

### 2. 性能测试
- 大数据量分页查询
- 复杂条件组合查询
- 并发访问测试

### 3. 兼容性测试
- 与原JFinal接口对比
- 参数格式兼容性
- 返回结果一致性

## 总结

✅ **实现完成**: 成功参考JFinal思路实现了getDomesticTemplateList方法
✅ **功能完整**: 支持分页、条件查询、尺寸范围查询
✅ **框架适配**: 完美适配MyBatis-Plus框架
✅ **接口丰富**: 提供了多种查询和管理接口
✅ **编译通过**: 所有代码编译成功，无语法错误

现在可以使用这些接口来查询和管理家用行业模板数据了！
