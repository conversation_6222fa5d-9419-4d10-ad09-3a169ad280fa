# TemplateDomestic实体类说明

## 概述

根据`docs/行业模板/v1_template_domestic.sql`表结构生成的家用行业模板实体类，参考了`Goods.java`的代码风格和注解规范。

## 文件位置

```
src/main/java/net/snaptag/system/business/entity/TemplateDomestic.java
```

## 表映射

- **数据库表**: `v1_templet_domestic`
- **实体类**: `TemplateDomestic`
- **继承**: `BaseEntity`（包含id、createtime、createuserid等基础字段）

## 字段映射

### 基础信息字段

| 字段名 | 数据类型 | 说明 |
|--------|----------|------|
| userId | Integer | 用户ID |
| groupId | Integer | 模板分组ID（默认-1） |
| name | String | 模板名称 |
| cover | String | 封面图片地址 |

### 尺寸和布局字段

| 字段名 | 数据类型 | 说明 |
|--------|----------|------|
| gap | BigDecimal | 间隙（默认2.00） |
| height | BigDecimal | 高度 |
| width | BigDecimal | 宽度 |
| labelNum | Integer | 标签数量（默认1，大于1为多排标签） |
| labelGap | BigDecimal | 多排标签间距 |

### 纸张和打印设置

| 字段名 | 数据类型 | 说明 |
|--------|----------|------|
| paperType | Integer | 纸张类型：1-间隙纸 2-连续纸 3-黑标纸 4-定孔纸 |
| printDirection | Integer | 打印方向 |
| blackLabelGap | BigDecimal | 黑标间隙（默认0.00） |
| blackLabelOffset | BigDecimal | 黑标偏移（默认0.00） |
| cutAfterPrint | Integer | 打印后切纸：0-否 1-是 |
| paperTearType | Integer | 撕纸类型：1-撕离 2-剥离 |
| paperFeedCount | Integer | 打印后走纸长度 |

### 机器和类型设置

| 字段名 | 数据类型 | 说明 |
|--------|----------|------|
| machineType | Integer | 机器类型：1-标签 2-票据 3-76针打 |
| ticketMachineType | Integer | 票据模板机器子类型：1-58票据 2-80票据 3-76针打 |
| labelType | Integer | 标签类型（形状）：1-矩形 2-圆角矩形 3-圆 |
| type | Integer | 模板类型：0-1.0老版本 1-行业模板 2-2.0新数据 |
| printType | Integer | 打印类型：0-标签打印 1-PDF打印 2-图片打印 |

### 多语言支持

| 字段名 | 数据类型 | 说明 |
|--------|----------|------|
| nameEn | String | 英语名称 |
| nameKor | String | 韩语名称 |
| nameJP | String | 日语名称 |
| nameHK | String | 繁体中文名称 |
| nameRU | String | 俄语名称 |

### 其他字段

| 字段名 | 数据类型 | 说明 |
|--------|----------|------|
| data | String | 标签内容数据（longtext） |
| updateTime | Date | 更新时间 |
| multiLabelType | Integer | 多排类型：0-不复制 1-仅复制首排标签 |
| shareUser | Integer | 分享用户 |
| mirrorImage | Boolean | 是否开启镜像：false-否 true-是 |

## 技术特点

### 1. 注解规范
- 使用`@TableName`指定表名
- 使用`@TableField`指定字段映射
- 使用Lombok的`@Data`和`@EqualsAndHashCode`

### 2. 数据类型选择
- **BigDecimal**: 用于精度要求高的浮点数（gap、height、width等）
- **Integer**: 用于整型字段
- **String**: 用于文本字段
- **Boolean**: 用于bit(1)类型的字段
- **Date**: 用于时间字段

### 3. 继承结构
```java
public class TemplateDomestic extends BaseEntity
```
继承BaseEntity获得：
- id（主键）
- createtime（创建时间）
- createuserid（创建用户ID）
- status（状态）
- 其他基础字段

## 使用示例

### 基本CRUD操作
```java
// 查询
TemplateDomestic template = templateDomesticDao.getById("1755587374743970137");

// 新增
TemplateDomestic newTemplate = new TemplateDomestic();
newTemplate.setName("新模板");
newTemplate.setUserId(201733);
newTemplate.setGroupId(285);
templateDomesticDao.save(newTemplate);

// 更新
template.setName("更新后的名称");
templateDomesticDao.updateById(template);

// 删除
templateDomesticDao.removeById("1755587374743970137");
```

### 条件查询
```java
// 按类型查询
QueryWrapper<TemplateDomestic> wrapper = new QueryWrapper<>();
wrapper.eq("type", 2); // 查询2.0新数据
wrapper.eq("machine_type", 1); // 标签机器类型
List<TemplateDomestic> templates = templateDomesticDao.list(wrapper);

// 按用户查询
wrapper.clear();
wrapper.eq("user_id", 201733);
wrapper.orderByDesc("create_time");
List<TemplateDomestic> userTemplates = templateDomesticDao.list(wrapper);
```

## 注意事项

### 1. 字段命名
- 数据库字段使用驼峰命名（如userId）
- 实体类属性也使用驼峰命名
- @TableField注解确保正确映射

### 2. 数据精度
- 浮点数字段使用BigDecimal确保精度
- 避免使用float/double导致的精度丢失

### 3. 布尔类型
- 数据库bit(1)字段映射为Boolean类型
- 自动处理true/false与1/0的转换

### 4. 时间字段
- createTime继承自BaseEntity
- updateTime单独定义，支持自动更新

## 相关文件

- **SQL文件**: `docs/行业模板/v1_template_domestic.sql`
- **数据文件**: `docs/行业模板/template-inster-updated.sql`
- **参考实体**: `src/main/java/net/snaptag/system/business/entity/Goods.java`

## 总结

TemplateDomestic实体类完整映射了v1_templet_domestic表的所有字段，支持：
- ✅ 完整的字段映射
- ✅ 合适的数据类型
- ✅ 标准的注解规范
- ✅ 多语言支持
- ✅ 继承基础实体类
- ✅ 符合项目代码风格

可以直接用于MyBatis-Plus的CRUD操作和业务逻辑开发。
