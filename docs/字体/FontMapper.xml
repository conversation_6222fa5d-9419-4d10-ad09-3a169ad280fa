<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.font.mapper.FontMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.example.font.entity.Font">
        <id column="font_id" property="fontId" />
        <result column="font_name" property="fontName" />
        <result column="font_english_name" property="fontEnglishName" />
        <result column="font_traditional_name" property="fontTraditionalName" />
        <result column="font_korean_name" property="fontKoreanName" />
        <result column="font_kind" property="fontKind" />
        <result column="font_url" property="fontUrl" />
        <result column="font_cover" property="fontCover" />
        <result column="sys_user_id" property="sysUserId" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="deleted" property="deleted" />
    </resultMap>

    <!-- 字体列表VO映射 -->
    <resultMap id="FontListVOMap" type="com.example.font.vo.FontListVO">
        <id column="font_id" property="fontId" />
        <result column="font_name" property="fontName" />
        <result column="font_kind" property="fontKind" />
        <result column="font_cover" property="fontCover" />
        <result column="sys_user_name" property="sysUserName" />
        <result column="create_time" property="createTime" />
        <result column="file_size_formatted" property="fileSizeFormatted" />
    </resultMap>

    <!-- 字体详情VO映射 -->
    <resultMap id="FontVOMap" type="com.example.font.vo.FontVO">
        <id column="font_id" property="fontId" />
        <result column="font_name" property="fontName" />
        <result column="font_english_name" property="fontEnglishName" />
        <result column="font_traditional_name" property="fontTraditionalName" />
        <result column="font_korean_name" property="fontKoreanName" />
        <result column="font_kind" property="fontKind" />
        <result column="font_url" property="fontUrl" />
        <result column="font_cover" property="fontCover" />
        <result column="sys_user_id" property="sysUserId" />
        <result column="sys_user_name" property="sysUserName" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="file_size" property="fileSize" />
        <result column="file_size_formatted" property="fileSizeFormatted" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        font_id, font_name, font_english_name, font_traditional_name, font_korean_name,
        font_kind, font_url, font_cover, sys_user_id, create_time, update_time, deleted
    </sql>

    <!-- 分页查询字体列表 -->
    <select id="selectFontPage" resultMap="FontListVOMap">
        SELECT 
            f.font_id,
            CASE 
                WHEN #{query.locale} = 'en-US' AND f.font_english_name IS NOT NULL AND f.font_english_name != '' 
                THEN f.font_english_name
                WHEN #{query.locale} = 'zh-TW' AND f.font_traditional_name IS NOT NULL AND f.font_traditional_name != '' 
                THEN f.font_traditional_name
                WHEN #{query.locale} = 'ko-KR' AND f.font_korean_name IS NOT NULL AND f.font_korean_name != '' 
                THEN f.font_korean_name
                ELSE f.font_name
            END AS font_name,
            f.font_kind,
            f.font_cover,
            u.sys_user_name,
            f.create_time
        FROM font f
        LEFT JOIN sys_user u ON f.sys_user_id = u.sys_user_id
        LEFT JOIN font_kind_lang fkl ON f.font_kind = fkl.font_kind
        WHERE f.deleted = 0
        <if test="query.fontName != null and query.fontName != ''">
            AND (f.font_name LIKE CONCAT('%', #{query.fontName}, '%')
                 OR f.font_english_name LIKE CONCAT('%', #{query.fontName}, '%')
                 OR f.font_traditional_name LIKE CONCAT('%', #{query.fontName}, '%')
                 OR f.font_korean_name LIKE CONCAT('%', #{query.fontName}, '%'))
        </if>
        <if test="query.fontKind != null and query.fontKind != ''">
            AND f.font_kind = #{query.fontKind}
        </if>
        <if test="query.sysUserId != null">
            AND f.sys_user_id = #{query.sysUserId}
        </if>
        <if test="query.locale != null and query.locale != ''">
            AND fkl.font_lang = #{query.locale}
        </if>
        <if test="query.startTime != null">
            AND f.create_time >= #{query.startTime}
        </if>
        <if test="query.endTime != null">
            AND f.create_time &lt;= #{query.endTime}
        </if>
        GROUP BY f.font_id
        ORDER BY 
        <choose>
            <when test="query.orderBy != null and query.orderBy != ''">
                ${query.orderBy}
                <if test="query.orderDirection != null and query.orderDirection != ''">
                    ${query.orderDirection}
                </if>
            </when>
            <otherwise>
                f.create_time DESC
            </otherwise>
        </choose>
    </select>

    <!-- 根据ID查询字体详情 -->
    <select id="selectFontDetail" resultMap="FontVOMap">
        SELECT 
            f.font_id,
            CASE 
                WHEN #{locale} = 'en-US' AND f.font_english_name IS NOT NULL AND f.font_english_name != '' 
                THEN f.font_english_name
                WHEN #{locale} = 'zh-TW' AND f.font_traditional_name IS NOT NULL AND f.font_traditional_name != '' 
                THEN f.font_traditional_name
                WHEN #{locale} = 'ko-KR' AND f.font_korean_name IS NOT NULL AND f.font_korean_name != '' 
                THEN f.font_korean_name
                ELSE f.font_name
            END AS font_name,
            f.font_english_name,
            f.font_traditional_name,
            f.font_korean_name,
            f.font_kind,
            f.font_url,
            f.font_cover,
            f.sys_user_id,
            u.sys_user_name,
            f.create_time,
            f.update_time
        FROM font f
        LEFT JOIN sys_user u ON f.sys_user_id = u.sys_user_id
        WHERE f.font_id = #{fontId} AND f.deleted = 0
    </select>

    <!-- 根据语言查询字体列表 -->
    <select id="selectFontsByLocale" resultMap="FontListVOMap">
        SELECT 
            f.font_id,
            CASE 
                WHEN #{locale} = 'en-US' AND f.font_english_name IS NOT NULL AND f.font_english_name != '' 
                THEN f.font_english_name
                WHEN #{locale} = 'zh-TW' AND f.font_traditional_name IS NOT NULL AND f.font_traditional_name != '' 
                THEN f.font_traditional_name
                WHEN #{locale} = 'ko-KR' AND f.font_korean_name IS NOT NULL AND f.font_korean_name != '' 
                THEN f.font_korean_name
                ELSE f.font_name
            END AS font_name,
            f.font_kind,
            f.font_cover,
            u.sys_user_name,
            f.create_time
        FROM font f
        LEFT JOIN sys_user u ON f.sys_user_id = u.sys_user_id
        LEFT JOIN font_kind_lang fkl ON f.font_kind = fkl.font_kind
        WHERE f.deleted = 0 AND fkl.font_lang = #{locale}
        ORDER BY f.create_time DESC
    </select>

    <!-- 根据分类查询字体列表 -->
    <select id="selectFontsByKind" resultMap="FontListVOMap">
        SELECT 
            f.font_id,
            CASE 
                WHEN #{locale} = 'en-US' AND f.font_english_name IS NOT NULL AND f.font_english_name != '' 
                THEN f.font_english_name
                WHEN #{locale} = 'zh-TW' AND f.font_traditional_name IS NOT NULL AND f.font_traditional_name != '' 
                THEN f.font_traditional_name
                WHEN #{locale} = 'ko-KR' AND f.font_korean_name IS NOT NULL AND f.font_korean_name != '' 
                THEN f.font_korean_name
                ELSE f.font_name
            END AS font_name,
            f.font_kind,
            f.font_cover,
            u.sys_user_name,
            f.create_time
        FROM font f
        LEFT JOIN sys_user u ON f.sys_user_id = u.sys_user_id
        WHERE f.deleted = 0 AND f.font_kind = #{fontKind}
        ORDER BY f.create_time DESC
    </select>

    <!-- 查询字体分类列表 -->
    <select id="selectFontKinds" resultType="java.lang.String">
        SELECT DISTINCT f.font_kind
        FROM font f
        LEFT JOIN font_kind_lang fkl ON f.font_kind = fkl.font_kind
        WHERE f.deleted = 0 
        <if test="locale != null and locale != ''">
            AND fkl.font_lang = #{locale}
        </if>
        ORDER BY f.font_kind
    </select>

    <!-- 统计字体数量 -->
    <select id="countFonts" resultType="java.lang.Long">
        SELECT COUNT(DISTINCT f.font_id)
        FROM font f
        LEFT JOIN font_kind_lang fkl ON f.font_kind = fkl.font_kind
        WHERE f.deleted = 0
        <if test="query.fontName != null and query.fontName != ''">
            AND (f.font_name LIKE CONCAT('%', #{query.fontName}, '%')
                 OR f.font_english_name LIKE CONCAT('%', #{query.fontName}, '%')
                 OR f.font_traditional_name LIKE CONCAT('%', #{query.fontName}, '%')
                 OR f.font_korean_name LIKE CONCAT('%', #{query.fontName}, '%'))
        </if>
        <if test="query.fontKind != null and query.fontKind != ''">
            AND f.font_kind = #{query.fontKind}
        </if>
        <if test="query.sysUserId != null">
            AND f.sys_user_id = #{query.sysUserId}
        </if>
        <if test="query.locale != null and query.locale != ''">
            AND fkl.font_lang = #{query.locale}
        </if>
        <if test="query.startTime != null">
            AND f.create_time >= #{query.startTime}
        </if>
        <if test="query.endTime != null">
            AND f.create_time &lt;= #{query.endTime}
        </if>
    </select>

    <!-- 检查字体名称是否存在 -->
    <select id="existsFontName" resultType="java.lang.Boolean">
        SELECT COUNT(*) > 0
        FROM font
        WHERE font_name = #{fontName} AND deleted = 0
        <if test="fontId != null">
            AND font_id != #{fontId}
        </if>
    </select>

</mapper>
