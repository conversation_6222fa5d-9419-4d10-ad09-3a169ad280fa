/*
 Navicat Premium Dump SQL

 Source Server         : vida
 Source Server Type    : MySQL
 Source Server Version : 50742 (5.7.42-log)
 Source Host           : localhost:3302
 Source Schema         : snaptag_test

 Target Server Type    : MySQL
 Target Server Version : 50742 (5.7.42-log)
 File Encoding         : 65001

 Date: 20/08/2025 11:16:29
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for v1_font_kind_lang
-- ----------------------------
DROP TABLE IF EXISTS `v1_font_kind_lang`;
CREATE TABLE `v1_font_kind_lang`  (
  `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '字体类型',
  `fontKind` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '字体类型',
  `fontLang` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '所属语言',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `idx_fontKind`(`fontKind`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '字体类型语言对照表' ROW_FORMAT = DYNAMIC;

SET FOREIGN_KEY_CHECKS = 1;
