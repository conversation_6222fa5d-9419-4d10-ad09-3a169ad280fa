# 字体API测试用例

## 修改后的API支持以下调用方式：

### 1. 查询指定分类的字体
```
GET /api/business/v1/domestic/font/getFont?fontKind=简体
```

### 2. 查询所有字体（不指定分类）
```
GET /api/business/v1/domestic/font/getFont
```

注意：locale字段现在通过关联font_kind_lang表自动获取，不需要作为参数传递

## 期望的返回结构：

```json
{
    "data": [
        {
            "fontId": "1",
            "fontName": "微软雅黑",
            "fontEnglishName": "Microsoft YaHei",
            "fontTraditionalName": "微軟雅黑",
            "fontKoreanName": "마이크로소프트 야헤이",
            "fontGermanyName": "微软雅黑",
            "fontItalyName": "微软雅黑",
            "fontSpainName": "微软雅黑",
            "fontFranceName": "微软雅黑",
            "fontKind": "简体",
            "fontUrl": "/fonts/yahei.ttf",
            "fontCover": "/images/yahei_cover.jpg",
            "fontValue": "yahei",
            "sysUserId": 1,
            "createTime": "2024-03-15 17:26:07",
            "locale": "zh-CN"
        }
    ],
    "head": {
        "method": "GET",
        "msg": "成功",
        "ret": 0,
        "timestamp": "2025-08-20 12:10:34",
        "uri": "/api/business/v1/domestic/font/getFont",
        "requestId": "...",
        "param": {
            "fontKind": "简体"
        }
    }
}
```

## 主要改进：

1. **fontKind参数变为可选**：不传fontKind时查询所有字体
2. **动态SQL查询**：使用`<if test>`条件判断是否添加fontKind过滤条件
3. **完整字体信息**：返回包含所有多语言字段的完整Font实体
4. **去除sys_user关联**：不再关联sys_user表，避免数据库错误
5. **保持返回结构一致**：使用项目标准的ReturnDto格式

## SQL逻辑：

```sql
SELECT f.*, fkl.fontLang as locale
FROM v1_font f
LEFT JOIN v1_font_kind_lang fkl ON fkl.fontKind = f.fontKind
WHERE 1 = 1
  -- 只有当fontKind不为空时才添加此条件
  AND f.fontKind = '简体'
ORDER BY f.createTime DESC
```

### 主要特点：
1. **使用 `f.*`** - 查询字体表的所有字段
2. **关联 font_kind_lang 表** - 通过 LEFT JOIN 获取 locale 信息
3. **动态 WHERE 条件** - fontKind 参数可选，不传则查询所有字体
4. **locale 来源于数据库** - 不再依赖参数传递，而是从 font_kind_lang 表获取
