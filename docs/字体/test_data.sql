-- 测试数据插入脚本

-- 插入字体测试数据
INSERT INTO v1_font (
    fontId, 
    fontName, 
    fontEnglishName, 
    fontTraditionalName, 
    fontKoreanName,
    fontGermanyName,
    fontItalyName,
    fontSpainName,
    fontFranceName,
    fontKind, 
    fontUrl, 
    fontCover, 
    fontValue,
    sysUserId, 
    createTime
) VALUES 
(
    'font_001', 
    '微软雅黑', 
    'Microsoft YaHei', 
    '微軟雅黑', 
    '마이크로소프트 야헤이',
    'Microsoft YaHei',
    'Microsoft YaHei',
    'Microsoft YaHei',
    'Microsoft YaHei',
    '简体', 
    '/fonts/yahei.ttf', 
    '/images/yahei_cover.jpg', 
    'yahei',
    1, 
    NOW()
),
(
    'font_002', 
    '宋体', 
    'SimSun', 
    '宋體', 
    '송체',
    'SimSun',
    'SimSun',
    'SimSun',
    'SimSun',
    '简体', 
    '/fonts/simsun.ttf', 
    '/images/simsun_cover.jpg', 
    'simsun',
    1, 
    NOW()
),
(
    'font_003', 
    '黑体', 
    'SimHei', 
    '黑體', 
    '흑체',
    'SimHei',
    'SimHei',
    'SimHei',
    'SimHei',
    '简体', 
    '/fonts/simhei.ttf', 
    '/images/simhei_cover.jpg', 
    'simhei',
    1, 
    NOW()
),
(
    'font_004', 
    '楷体', 
    'KaiTi', 
    '楷體', 
    '해서',
    'KaiTi',
    'KaiTi',
    'KaiTi',
    'KaiTi',
    '繁体', 
    '/fonts/kaiti.ttf', 
    '/images/kaiti_cover.jpg', 
    'kaiti',
    1, 
    NOW()
),
(
    'font_005', 
    'Arial', 
    'Arial', 
    'Arial', 
    'Arial',
    'Arial',
    'Arial',
    'Arial',
    'Arial',
    '英文', 
    '/fonts/arial.ttf', 
    '/images/arial_cover.jpg', 
    'arial',
    1, 
    NOW()
);

-- 插入字体分类语言对照数据
INSERT INTO v1_font_kind_lang (id, fontKind, fontLang) VALUES 
('fkl_001', '简体', 'zh-CN'),
('fkl_002', '简体', 'zh-TW'),
('fkl_003', '繁体', 'zh-TW'),
('fkl_004', '繁体', 'zh-CN'),
('fkl_005', '英文', 'en-US'),
('fkl_006', '英文', 'zh-CN'),
('fkl_007', '英文', 'zh-TW'),
('fkl_008', '简体', 'ko-KR'),
('fkl_009', '繁体', 'ko-KR'),
('fkl_010', '英文', 'ko-KR');

-- 查询验证数据
SELECT '=== 字体数据 ===' as info;
SELECT fontId, fontName, fontKind, createTime FROM v1_font;

SELECT '=== 字体分类语言对照数据 ===' as info;
SELECT * FROM v1_font_kind_lang;

SELECT '=== 根据分类查询字体 ===' as info;
SELECT f.fontId, f.fontName, f.fontKind 
FROM v1_font f 
WHERE f.fontKind = '简体';

SELECT '=== 根据语言查询字体分类 ===' as info;
SELECT DISTINCT f.fontKind 
FROM v1_font f 
LEFT JOIN v1_font_kind_lang fkl ON f.fontKind = fkl.fontKind 
WHERE fkl.fontLang = 'zh-CN';
