package net.snaptag.system.business.buservice;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import net.snaptag.system.account.buservice.UserAccountAndInfoBuService;
import net.snaptag.system.account.buservice.UserInfoBuService;
import net.snaptag.system.account.dto.UserLoginDto;
import net.snaptag.system.business.cache.DraftsCacheService;
import net.snaptag.system.business.dao.DraftsDao;
import net.snaptag.system.business.dto.*;
import net.snaptag.system.business.entity.Drafts;
import net.snaptag.system.business.entity.PrintPaperInfo;
import net.snaptag.system.business.entity.ResourceData;
import net.snaptag.system.business.enums.DraftsSubTypeEnums;
import net.snaptag.system.business.enums.PaperInfoEnums;
import net.snaptag.system.business.enums.ResourceTypeEnums;
import net.snaptag.system.business.utils.Constant;
import net.snaptag.system.business.utils.ToolUtils;
import net.snaptag.system.business.vo.DraftsParamVo;
import net.snaptag.system.sadais.cache.kit.CacheKit;
import net.snaptag.system.sadais.core.common.core.ToolsConst;
import net.snaptag.system.sadais.core.common.enums.ExceptionEnums;
import net.snaptag.system.sadais.core.common.exception.ServiceException;
import net.snaptag.system.sadais.mongo.common.Page;
import net.snaptag.system.sadais.util.core.ToolsKit;
import net.snaptag.system.sadais.web.config.CommonProperties;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
public class DraftsBuService {
    @Autowired
    private DraftsDao draftsDao;
    @Autowired
    private DraftsCacheService draftsCacheService;
    @Autowired
    private CommonProperties commonProperties;
    @Autowired
    private ResourceDataBuService resourceDataBuService;

    private static final String CACHE_KEY_USERID_DRAFF_RECENT_PAPERINFO_LIST = "comm:drafts:recent:parperinfo:userid:";

    @Autowired
    private UserInfoBuService userInfoBuService;
    @Autowired
    private PrintPaperInfoBuService printPaperInfoBuService;

    @Autowired
    private UserAccountAndInfoBuService userAccountAndInfoBuServiced;

    /**
     * 根据ID获取草稿箱对象
     *
     * @param id
     *            ID
     * @return 草稿箱对象
     * @throws ServiceException
     */
    public Drafts getDraftsById(String id) throws ServiceException {
        if (ToolsKit.isEmpty(id)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("记录信息ID不能为空");
        }
        Drafts drafts = draftsCacheService.getDraftsById(id);
        if (ToolsKit.isEmpty(drafts)) {
            try {
                drafts = draftsDao.getDraftsById(id);
            } catch (Exception e) {
                e.printStackTrace();
            }
            if (ToolsKit.isNotEmpty(drafts)) {
                draftsCacheService.saveDrafts(drafts);
            }
        }
        return drafts;
    }

    public void save(Drafts Drafts) {
        draftsDao.saveEntity(Drafts);
        draftsCacheService.saveDrafts(Drafts);
    }

    /**
     * 获取草稿箱ID列表
     *
     * @param userId
     *            类型
     * @param page
     *            当前页码
     * @param pageSize
     *            每页大小
     * @param lastId
     *            最后一条数据ID
     * @param version
     * @return 草稿箱ID列表
     */
    private List<String> getDraftsIdsList(String userId, int type, int subType,String materialColumnId, int length, int page, int pageSize, String lastId, String printerType, String paperType, String version, String keyword) {
        List<String> idsList = new ArrayList<>();
        List<Drafts> draftsList = new ArrayList<>();
        draftsList = draftsDao.findDraftsList(userId, type, subType,materialColumnId, length, page, pageSize, lastId, printerType, getPaperInfoListByPrinterType(printerType), paperType, keyword);
//        if (ToolUtils.compareVersion(Constant.VERSION340, version)){
//            draftsList = draftsDao.findDraftsList(userId, type, subType, length, page, pageSize, lastId, printerType, getPaperInfoListByPrinterType(printerType), paperType, keyword);
//        } else {
//            System.out.println("我在走旧的方法");
//            draftsList = draftsDao.findDraftsList2(userId, type, subType, length, page, pageSize, lastId, printerType, paperType);
//        }
        if (ToolsKit.isNotEmpty(draftsList)){
            for (Drafts Drafts : draftsList) {
                idsList.add(Drafts.getId());
            }
        }
        return idsList;
    }

    /**
     * 获取草稿箱列表
     *
     * @param userId
     *            用户ID
     * @param pageNo
     *            当前页码
     * @param pageSize
     *            每页大小
     * @param lastId
     *            最后一条数据ID
     * @return
     * @throws ServiceException
     */
    public List<DraftsDto> getDraftsList(String userId, String type, String subType,String materialColumnId, String length, int pageNo, int pageSize, String lastId, String printerType, String paperType, String version, String keyword)
            throws ServiceException {

        if (ToolsKit.isEmpty(userId)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("用户ID不能为空");
        }
        if (ToolsKit.isEmpty(type)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("类型不能为空");
        }
        List<DraftsDto> dtoList = new ArrayList<DraftsDto>();
        try {
            if (pageNo > 0) {
                pageNo--;
            }
            if (ToolsKit.isEmpty(subType)) {// 如果没有副类型，默认为编辑纸条
                subType = String.valueOf(DraftsSubTypeEnums.EDIT.getValue());
            }
            if (ToolsKit.isEmpty(length)) {
                length = PaperInfoEnums.LXZ1.getLength();
            }
            List<String> idsList = this.getDraftsIdsList(userId, Integer.parseInt(type), Integer.parseInt(subType),materialColumnId, Integer.parseInt(length), pageNo, pageSize,
                    lastId, printerType, paperType, version, keyword);
            if (ToolsKit.isNotEmpty(idsList)) {
                for (String id : idsList) {
                    Drafts drafts = this.getDraftsById(id);
                    if (ToolsKit.isNotEmpty(drafts)) {
                        DraftsDto dto = new DraftsDto();
                        dto.setId(drafts.getId());
                        if(StrUtil.isNotBlank(drafts.getName())){
                            dto.setName(drafts.getName());
                        }
                        dto.setTitle(drafts.getTitle());
                        dto.setIsMirror(drafts.getIsMirror());
                        dto.setPreviewPoint(drafts.getPreviewPoint());

                        ResourceData picRes = resourceDataBuService.getResourceDataById(drafts.getResPicId());
                        if (ToolsKit.isNotEmpty(picRes) && ToolsKit.isNotEmpty(picRes.getResUrl())) {
                            String picUrl = picRes.getResUrl().getPic();
                            if (ToolsKit.isNotEmpty(picUrl) && picUrl.contains(",")){
                                picUrl = picUrl.replaceAll(",", "," +commonProperties.getFileDomain());
                            }
                            dto.setPic(ToolsKit.URL.getUrlByServer(commonProperties.getFileDomain(), picUrl));
                            picRes.getResUrl().setPic(dto.getPic());
                            dto.setPicVo(picRes.getResUrl());
                        } else {
                            dto.setPic(StringUtils.EMPTY);
                        }
                        ResourceData dataRes = resourceDataBuService.getResourceDataById(drafts.getResDataId());
                        if (ToolsKit.isNotEmpty(dataRes) && ToolsKit.isNotEmpty(dataRes.getResUrl())) {
                            dto.setData(ToolsKit.URL.getUrlByServer(commonProperties.getFileDomain(), dataRes.getResUrl().getPic()));
                        } else {
                            dto.setData(StringUtils.EMPTY);
                        }
                        dto.setCreateTime(drafts.getCreatetime());
                        dto.setFmtTime(ToolsKit.Date.format(drafts.getCreatetime(), "yyyy-MM-dd HH:mm"));
                        dto.setParam(drafts.getDraftsParam());
                        dto.setType(drafts.getType());
                        dto.setPlaceType(drafts.getPlaceType());
                        dto.setPrinterType(drafts.getPrinterType());

                        PaperInfoDto paperInfoDto = new PaperInfoDto();
                        PaperInfoEnums enums = PaperInfoEnums.getMap().get(PaperInfoEnums.LXZ1.getLength());
                        if (PaperInfoEnums.getMap().get(drafts.getLength()+"")!=null){
                            enums = PaperInfoEnums.getMap().get(drafts.getLength()+"");
                        }
                        paperInfoDto.setHeight(enums.getHeight());
                        paperInfoDto.setWidth(enums.getWidth());
                        paperInfoDto.setName(enums.getName());
                        paperInfoDto.setLengthType(enums.getLength());

                        // 新增
                        paperInfoDto.setPaperLength(drafts.getPaperLength());
                        paperInfoDto.setPaperType(drafts.getPaperType());
                        paperInfoDto.setPaperWidth(drafts.getPaperWidth());
                        paperInfoDto.setPaperColor(drafts.getPaperColor());


                        dto.setPaperObj(paperInfoDto);

                        dto.setPicPrint(drafts.getPicPrintUrl());
                        dto.setMaterialColumnId(drafts.getMaterialColumnId());

                        dtoList.add(dto);
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return dtoList;
    }

    /**
     * 添加草稿箱
     * @param userId
     *            用户ID
     * @param paperLength
     * @param paperWidth
     * @param paperType
     * @param paperColor
     */
    public Map<String, String> addOrUpdateDrafts(String id, String name, String userId, String picUrl,String picPrintUrl, String dataUrl, String type, String subType,String materialColumnId, String length,
                                                 String typeId, String placeType, String paperLength, String paperWidth, String paperType, String paperColor, String printerType, String title, String isMirror, String previewPoint) {
        if (ToolsKit.isEmpty(userId)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("用户ID不能为空");
        }
        if(ToolsKit.isEmpty(name)){
            name = "";
        }
        if (ToolsKit.isEmpty(picUrl)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("图片地址不能为空");
        }
        if (ToolsKit.isEmpty(dataUrl)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("数据信息不能为空");
        }
        // type = "0";
        if (ToolsKit.isEmpty(type)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("类型不能为空");
        }
        if (ToolsKit.isEmpty(subType)) {// 如果没有副类型，默认为编辑纸条
            subType = String.valueOf(DraftsSubTypeEnums.EDIT.getValue());
        }
        if (ToolsKit.isEmpty(length)) {
            length = "-1";
        }

        if (ToolsKit.isEmpty(placeType)){
            placeType = "0"; //默认竖方向
        }

        if (ToolsKit.isEmpty(paperLength)){
            paperLength = "0";
        }
        if (ToolsKit.isEmpty(paperWidth)){
            paperWidth = "0";
        }
        if (ToolsKit.isEmpty(paperType)){
            paperType = "1";
        }
        if (ToolsKit.isEmpty(paperWidth)){
            paperWidth = "0";
        }

        if (ToolsKit.isEmpty(paperColor)){
            paperColor = "1"; // 1 默认白色
        }

        if (ToolsKit.isEmpty(isMirror)){
            isMirror = "0";
        }
        String s = "https%3A//share.xplable.com";
        String pic = ToolsKit.URL.replaceUrl(commonProperties.getFileDomain(), picUrl);
        String data = ToolsKit.URL.replaceUrl(commonProperties.getFileDomain(), dataUrl);
        Drafts drafts = null;
        if (ToolsKit.isNotEmpty(id)) {
            drafts = this.getDraftsById(id);
            if (ToolsKit.isNotEmpty(drafts)) {
                resourceDataBuService.updateResource(drafts.getResPicId(), pic, ResourceTypeEnums.PIC.getValue());
                resourceDataBuService.updateResource(drafts.getResDataId(), data, ResourceTypeEnums.DATA.getValue());

                draftsCacheService.removeDraftsIdToList(userId, drafts.getType(), drafts.getSubType(), Integer.parseInt(length), drafts.getId());
                drafts.setCreatetime(new Date());
                drafts.setType(Integer.parseInt(type));
                drafts.setLength(Integer.parseInt(length));

                drafts.setPaperLength(Float.parseFloat(paperLength));
                drafts.setPaperWidth(Float.parseFloat(paperWidth));
                drafts.setPaperType(Integer.parseInt(paperType));
                drafts.setPaperColor(Integer.parseInt(paperColor));
                drafts.setPrinterType(printerType);
                drafts.setPicPrintUrl(picPrintUrl);
                drafts.setTitle(title);
                drafts.setIsMirror(Integer.parseInt(isMirror));
                drafts.setPreviewPoint(previewPoint);
                drafts.setName(name);

                this.save(drafts);
                draftsCacheService.addDraftsIdToList(userId, drafts.getType(), drafts.getSubType(), drafts.getLength(), drafts.getId(), drafts.getCreatetime().getTime());
            } else {
                drafts = this.newDrafts(userId, name, Integer.parseInt(type), pic,picPrintUrl, data, Integer.parseInt(subType),materialColumnId, Integer.parseInt(length), null, Integer.parseInt(placeType), Float.parseFloat(paperLength), Float.parseFloat(paperWidth), Integer.parseInt(paperType), Integer.parseInt(paperColor), printerType, title, isMirror, previewPoint);
            }
        } else {
            drafts = this.newDrafts(userId, name, Integer.parseInt(type), pic,picPrintUrl, data, Integer.parseInt(subType), materialColumnId, Integer.parseInt(length), typeId, Integer.parseInt(placeType), Float.parseFloat(paperLength), Float.parseFloat(paperWidth), Integer.parseInt(paperType), Integer.parseInt(paperColor), printerType, title, isMirror, previewPoint);
        }
        Map<String, String> map = new HashMap<String, String>();
        map.put("id", drafts.getId());
        return map;
    }

    private Drafts newDrafts(String userId, String name, int type, String pic,String picPrintUrl, String data, int subType,String materialColumnId, int length, String typeId, int placeType, float paperLength, float paperWidth, int paperType, int paperColor, String printerType, String title, String isMirror, String previewPoint) {
        Drafts drafts = new Drafts();
        ToolsKit.setIdEntityData(drafts, userId);
        drafts.setUserId(userId);
        ResourceDataDto picRes = resourceDataBuService.saveResource(userId, pic, ResourceTypeEnums.PIC.getValue(), null);
        ResourceDataDto dataRes = resourceDataBuService.saveResource(userId, data, ResourceTypeEnums.DATA.getValue(), null);
        drafts.setResPicId(picRes.getId());
        drafts.setResDataId(dataRes.getId());
        drafts.setType(type);
        drafts.setSubType(subType);
        drafts.setLength(length);
        DraftsParamVo draftsParamVo = new DraftsParamVo();
        draftsParamVo.setTypeId(typeId);
        drafts.setDraftsParam(draftsParamVo);
        drafts.setCreatetime(new Date());
        drafts.setPlaceType(placeType);
        drafts.setPicPrintUrl(picPrintUrl);

        drafts.setPaperType(paperType);
        drafts.setPaperWidth(paperWidth);
        drafts.setPaperLength(paperLength);
        drafts.setPaperColor(paperColor);
        drafts.setPrinterType(printerType);

        drafts.setTitle(title);
        drafts.setIsMirror(Integer.parseInt(isMirror));
        drafts.setMaterialColumnId(materialColumnId);
        drafts.setPreviewPoint(previewPoint);

        drafts.setName(name);

        this.save(drafts);
        draftsCacheService.addDraftsIdToList(userId, type, subType, length, drafts.getId(), drafts.getCreatetime().getTime());
        return drafts;
    }

    /**
     * 删除草稿箱
     *
     * @param id
     *            记录ID
     */
    public void delDrafts(String id) {
        if (ToolsKit.isEmpty(id)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("记录ID不能为空");
        }
        Drafts drafts = this.getDraftsById(id);
        if (ToolsKit.isNotEmpty(drafts)) {
            drafts.setStatus(ToolsConst.DATA_DELETE_STATUS);
            draftsDao.saveEntity(drafts);
            draftsCacheService.removeDraftsById(drafts.getId());
            draftsCacheService.removeDraftsIdToList(drafts.getUserId(), drafts.getType(), drafts.getSubType(), drafts.getLength(), drafts.getId());
//            resourceDataBuService.delResource(drafts.getResPicId());
//            resourceDataBuService.delResource(drafts.getResDataId());
        }
    }


    /**
     * 获取草稿箱列表
     *
     * @param userId
     *            用户ID
     * @param pageNo
     *            当前页码
     * @param pageSize
     *            每页大小
     * @param lastId
     *            最后一条数据ID
     * @return
     * @throws ServiceException
     */
    public Page<DraftsDto> getDraftsPage(String userId, String type, String subType, String length, int pageNo, int pageSize, String lastId)
            throws ServiceException {
        Page<DraftsDto> resultPage= new Page(pageNo, pageSize);
        if (ToolsKit.isEmpty(userId)) {
//            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("用户ID不能为空");
        }
        if (ToolsKit.isEmpty(type)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("类型不能为空");
        }
        List<DraftsDto> dtoList = new ArrayList<DraftsDto>();
        try {
            if (pageNo > 0) {
                pageNo--;
            }
            if (ToolsKit.isEmpty(subType)) {// 如果没有副类型，默认为编辑纸条
                subType = String.valueOf(DraftsSubTypeEnums.EDIT.getValue());
            }
//            if (ToolsKit.isEmpty(length)) {
//                length = PaperInfoEnums.LXZ1.getLength();
//            }

            Page<Drafts> page = draftsDao.findDraftsPage(userId, Integer.parseInt(type), Integer.parseInt(subType), ToolsKit.isEmpty(length) ? null:Integer.parseInt(length), pageNo, pageSize,
                    lastId);

            List<String> idsList = new ArrayList<>();
            if (page!=null && ToolsKit.isNotEmpty(page.getResult())){
                page.getResult().forEach(item ->{
                    idsList.add(item.getId());
                });
            }
            if (ToolsKit.isNotEmpty(idsList)) {
                for (String id : idsList) {
                    Drafts drafts = this.getDraftsById(id);
                    if (ToolsKit.isNotEmpty(drafts)) {
                        DraftsDto dto = new DraftsDto();
                        dto.setPicPrint(drafts.getPicPrintUrl());
                        dto.setId(drafts.getId());
                        ResourceData picRes = resourceDataBuService.getResourceDataById(drafts.getResPicId());
                        if (ToolsKit.isNotEmpty(picRes) && ToolsKit.isNotEmpty(picRes.getResUrl())) {
                            String picUrl = picRes.getResUrl().getPic();
                            if (ToolsKit.isNotEmpty(picUrl) && picUrl.contains(",")){
                                picUrl = picUrl.replaceAll(",", "," +commonProperties.getFileDomain());
                            }
                            dto.setPic(ToolsKit.URL.getUrlByServer(commonProperties.getFileDomain(), picUrl));
                            picRes.getResUrl().setPic(dto.getPic());
                            dto.setPicVo(picRes.getResUrl());
                        } else {
                            dto.setPic(StringUtils.EMPTY);
                        }
                        ResourceData dataRes = resourceDataBuService.getResourceDataById(drafts.getResDataId());
                        if (ToolsKit.isNotEmpty(dataRes) && ToolsKit.isNotEmpty(dataRes.getResUrl())) {
                            dto.setData(ToolsKit.URL.getUrlByServer(commonProperties.getFileDomain(), dataRes.getResUrl().getPic()));
                        } else {
                            dto.setData(StringUtils.EMPTY);
                        }
                        dto.setCreateTime(drafts.getCreatetime());
                        dto.setFmtTime(ToolsKit.Date.format(drafts.getCreatetime(), "yyyy-MM-dd HH:mm"));
                        dto.setParam(drafts.getDraftsParam());
                        dto.setType(drafts.getType());
                        dto.setPlaceType(drafts.getPlaceType());
                        dto.setPrinterType(drafts.getPrinterType());

                        PaperInfoDto paperInfoDto = new PaperInfoDto();
                        PaperInfoEnums enums = PaperInfoEnums.getMap().get(PaperInfoEnums.LXZ1.getLength());
                        if (PaperInfoEnums.getMap().get(drafts.getLength()+"")!=null){
                            enums = PaperInfoEnums.getMap().get(drafts.getLength()+"");
                        }
                        paperInfoDto.setHeight(enums.getHeight());
                        paperInfoDto.setWidth(enums.getWidth());
                        paperInfoDto.setName(enums.getName());
                        paperInfoDto.setLengthType(enums.getLength());

                        // 新增
                        paperInfoDto.setPaperLength(drafts.getPaperLength());
                        paperInfoDto.setPaperType(drafts.getPaperType());
                        paperInfoDto.setPaperWidth(drafts.getPaperWidth());
                        paperInfoDto.setPaperColor(drafts.getPaperColor()==0?1:drafts.getPaperColor());

                        dto.setPaperObj(paperInfoDto);

                        // 获取用户的codeId

//                        UserInfo userInfo = userInfoBuService.getUserInfoById(drafts.getCreateuserid());
                        UserLoginDto userInfo = userAccountAndInfoBuServiced.getLoginInfo(drafts.getCreateuserid());
                        System.out.println("-------userid="+drafts.getCreateuserid()+"--------");
                        if (userInfo!=null){
                            System.out.println("userInfo=" + JSONObject.toJSON(userInfo));
                            dto.setCodeId(String.valueOf(userInfo.getUserInfoDto().getCodeId()));
                        }

                        dtoList.add(dto);
                    }
                }

                resultPage.setTotalCount(page.getTotalCount());
                resultPage.setResult(dtoList);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return resultPage;
    }


    public RecentPaperInfoDto addRecentOperation(String userId, RecentPaperInfoDto paperInfoDto) {
        if (ToolsKit.isEmpty(userId)){
//            throw new ServiceException("userId不能为空");
            return paperInfoDto;
        }
        paperInfoDto.setCreatetime(new Date());
        String key = CACHE_KEY_USERID_DRAFF_RECENT_PAPERINFO_LIST + userId;
        List<JSONObject> listPaperInfo = CacheKit.cache().get(key, List.class);
        CacheKit.cache().get(key, List.class);
        if (ToolsKit.isEmpty(listPaperInfo)){
            List<RecentPaperInfoDto> dtoList = new ArrayList<>();
            dtoList.add(paperInfoDto);
            CacheKit.cache().set(CACHE_KEY_USERID_DRAFF_RECENT_PAPERINFO_LIST + userId, dtoList, ToolsConst.MONTH_SECOND*12);
        } else {
            // 判断是否重复
            boolean isExist = Boolean.FALSE;

            // 把取出来的JSON对象转换成RecentPaperInfoDto
            List tempPaperInfoList = new ArrayList();
            for (int i = 0; i < listPaperInfo.size(); i++) {
                RecentPaperInfoDto infoDto = JSONObject.toJavaObject(listPaperInfo.get(i), RecentPaperInfoDto.class);
                if (infoDto.equals(paperInfoDto)){
                    isExist = true;
                    infoDto.setCreatetime(new Date());
                    infoDto.setPrinterType(paperInfoDto.getPrinterType());
                }
                tempPaperInfoList.add(infoDto);
            }

            if (isExist){
                tempPaperInfoList.sort(new Comparator<RecentPaperInfoDto>() {
                    @Override
                    public int compare(RecentPaperInfoDto o1, RecentPaperInfoDto o2) {
                        return o1.getCreatetime().getTime()>o2.getCreatetime().getTime()?-1:1;
                    }
                });
            } else {
                tempPaperInfoList.add(0, paperInfoDto);
                if (tempPaperInfoList.size()>5){
                    tempPaperInfoList = tempPaperInfoList.subList(0,5);
                }
            }

            CacheKit.cache().set(CACHE_KEY_USERID_DRAFF_RECENT_PAPERINFO_LIST + userId, tempPaperInfoList, ToolsConst.MONTH_SECOND*12);
        }
        return paperInfoDto;
    }

    public List<RecentPaperInfoDto> getRecentOperationList(String userId, String printerType) {
        if (ToolsKit.isEmpty(userId)){
            return new ArrayList<>();
//            throw new ServiceException("userId不能为空");
        }
        String key = CACHE_KEY_USERID_DRAFF_RECENT_PAPERINFO_LIST + userId;
        List<JSONObject> list = CacheKit.cache().get(key, List.class);

        List<RecentPaperInfoDto> result = new ArrayList();

        if (ToolsKit.isEmpty(list)){
            return new ArrayList<>();
        } else {
            for (JSONObject obj:list) {
                if (ToolsKit.isNotEmpty(printerType)){
                    System.out.println(obj);
                    if (printerType.equals(obj.getString("printerType"))){
                        result.add(JSONObject.toJavaObject(obj, RecentPaperInfoDto.class));
                    }
                } else {
                    result.add(JSONObject.toJavaObject(obj, RecentPaperInfoDto.class));
                }
            }
        }
        return result;
    }

    public List<PrintPaperInfoDto> getRecentOperationList(String userId, String printerType, Locale locale) {
        if (ToolsKit.isEmpty(userId)){
            return new ArrayList<>();
//            throw new ServiceException("userId不能为空");
        }
        String key = CACHE_KEY_USERID_DRAFF_RECENT_PAPERINFO_LIST + userId;
        List<JSONObject> list = CacheKit.cache().get(key, List.class);

        List<RecentPaperInfoDto> resultTemp = new ArrayList();

        if (ToolsKit.isEmpty(list)){
            return new ArrayList<>();
        } else {
            for (JSONObject obj:list) {
                if (ToolsKit.isNotEmpty(printerType)){
                    System.out.println(obj);
                    if (printerType.equals(obj.getString("printerType"))){
                        resultTemp.add(JSONObject.toJavaObject(obj, RecentPaperInfoDto.class));
                    }
                } else {
                    resultTemp.add(JSONObject.toJavaObject(obj, RecentPaperInfoDto.class));
                }
            }
        }

        List<PrintPaperInfoDto> result = new ArrayList<>();

        List<PrintPaperInfoDto> paperInfoDtoList = printPaperInfoBuService.getList(locale, null);
        if (ToolsKit.isNotEmpty(resultTemp)) {
            resultTemp.forEach(item -> {
                for (PrintPaperInfoDto paperInfo: paperInfoDtoList) {
                    if (paperInfo.getColorNum() == item.getPaperColor()
                            && Integer.parseInt(paperInfo.getType()+""+ paperInfo.getMaterial()) == item.getPaperType()
                            && paperInfo.getHeight() == item.getPaperWidth()
                            && (paperInfo.getWidth() == item.getPaperLength() || paperInfo.getType()==1)){
                        result.add(paperInfo);
                        break;
                    }
                }
            });
        }
        return result;
    }

    public List<PrintPaperInfo> getPaperInfoListByPrinterType(String printerType) {
        if (ToolsKit.isEmpty(printerType)) {
            return null;
        }

        List<PrintPaperInfo> dtoList = new ArrayList<>();
        List<PrintPaperInfo> list = printPaperInfoBuService.getList();
        for (PrintPaperInfo info:list) {

            if (ToolUtils.hasPrinterType(info.getPrinterType(), printerType)){
                dtoList.add(info);
            }

//            if((info.getPrinterType().contains(printerType.toLowerCase()+";" ) || info.getPrinterType().endsWith(printerType.toLowerCase()))){
//                dtoList.add(info);
//            }
        }

        return dtoList;
    }

    public boolean clearRecentOperation(String userId) {
        if (ToolsKit.isEmpty(userId)){
//            throw new ServiceException("userId不能为空");
            return true;
        }
        String key = CACHE_KEY_USERID_DRAFF_RECENT_PAPERINFO_LIST + userId;
        CacheKit.cache().del(key);
        return true;
    }
}
