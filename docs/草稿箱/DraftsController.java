package net.snaptag.system.business.controller;

import cn.hutool.core.util.StrUtil;
import net.snaptag.system.business.buservice.DraftsBuService;
import net.snaptag.system.business.buservice.ResourceDataBuService;
import net.snaptag.system.business.buservice.ShortLinkBuService;
import net.snaptag.system.business.dto.RecentPaperInfoDto;
import net.snaptag.system.business.dto.ResourceDataDto;
import net.snaptag.system.business.enums.ResourceTypeEnums;
import net.snaptag.system.business.utils.Constant;
import net.snaptag.system.business.utils.ToolUtils;
import net.snaptag.system.sadais.core.common.core.ToolsConst;
import net.snaptag.system.sadais.core.common.enums.ExceptionEnums;
import net.snaptag.system.sadais.core.common.exception.ServiceException;
import net.snaptag.system.sadais.util.core.ToolsKit;
import net.snaptag.system.sadais.web.config.CommonProperties;
import net.snaptag.system.sadais.web.core.BaseController;
import net.snaptag.system.sadais.web.dto.HeadInfoDto;
import net.snaptag.system.sadais.web.dto.ReturnDto;
import net.snaptag.system.sadais.web.utils.WebTools;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Locale;
import java.util.Map;

@RestController
@RequestMapping(Constant.PRE_MAPPING_URL + "/drafts")
public class DraftsController extends BaseController {
    @Autowired
    private DraftsBuService draftsBuService;
    @Autowired
    private ResourceDataBuService resourceDataBuService;
    @Autowired
    private CommonProperties commonProperties;
    @Autowired
    private ShortLinkBuService shortLinkBuService;

    /**
     * 获取草稿箱列表
     *
     * @return
     */
    @RequestMapping(value = "/getdraftslist")
    public ReturnDto getDraftsList() {
        try {
            String userId = this.getValue("userid");
            String type = this.getValue("type");
//            type = "0";
            String pageNo = this.getValue("pageno");
            String pageSize = this.getValue("pagesize");
            String lastId = this.getValue("lastid");
            String subType = this.getValue("subtype");
            String materialColumnId = this.getValue("materialColumnId");
            String length = this.getValue("length");
            String paperType = this.getValue("paperType");
            if (ToolsKit.isEmpty(pageNo)) {
                pageNo = "1";
            }

            if (ToolsKit.isEmpty(pageSize)) {
                pageSize = String.valueOf(ToolsConst.PHONEPAGESIZE);
            }
            lastId = "";

            String printerType = this.getValue("printerType");
            String keyword = this.getValue("keyword");
            HeadInfoDto headInfoDto = this.getHeadInfoDto();
            return this.returnSuccessJson(ExceptionEnums.SUCCESS,
                    draftsBuService.getDraftsList(userId, type, subType,materialColumnId, length, Integer.parseInt(pageNo), Integer.parseInt(pageSize), lastId, printerType, paperType, headInfoDto.getVersion(), keyword));
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    /**
     * 添加草稿箱
     *
     * @return
     */
    @RequestMapping(value = "/addorupdatedrafts")
    public ReturnDto addOrUpdateDrafts() {
        try {
            String id = this.getValue("id");
            String name = this.getValue("name");
            String userId = this.getValue("userId");
            String type = this.getValue("type");
            String picUrl = this.getValue("picUrl");
            String picPrintUrl = this.getValue("picPrintUrl");
            String dataUrl = this.getValue("dataUrl");
            String subType = this.getValue("subType");
            String materialColumnId = this.getValue("materialColumnId");
            String length = this.getValue("length");
            String typeId = this.getValue("typeId");
            String placeType = this.getValue("placeType");
            String paperLength = this.getValue("paperLength");
            String paperWidth =  this.getValue("paperWidth");
            String paperType = this.getValue("paperType");
            String paperColor = this.getValue("paperColor");
            String printerType = this.getValue("printerType");
            String title = this.getValue("title");
            String isMirror = this.getValue("isMirror");
            String previewPoint = this.getValue("previewPoint");


            return this.returnSuccessJson(ExceptionEnums.SUCCESS,
                    draftsBuService.addOrUpdateDrafts(id, name, userId, picUrl,picPrintUrl, dataUrl, type, subType,materialColumnId, length, typeId, placeType, paperLength, paperWidth, paperType, paperColor, printerType, title, isMirror, previewPoint));
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    /**
     * 删除草稿箱
     *
     * @return
     */
    @RequestMapping(value = "/deldrafts")
    public ReturnDto delDrafts() {
        try {
            String id = this.getValue("id");
            if (ToolsKit.isEmpty("id")) {
                throw new ServiceException("id不能为空");
            }
            String [] ids = id.split(",");
            for(int i=0; i<ids.length; i++) {
                if (ToolsKit.isNotEmpty(ids[i])){
                    draftsBuService.delDrafts(ids[i]);
                }
            }
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, null);
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    /**
     * 获取资源数据信息
     *
     * @return
     */
    @RequestMapping(value = "/getresourcedata")
    public ReturnDto getResourceData() {
        try {
            String resId = this.getValue("resid");
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, resourceDataBuService.getResourceData(resId));
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    /**
     * 保存资源数据信息
     *
     * @return
     */
    @RequestMapping(value = "/saveresource")
    public ReturnDto saveResource() {
        try {
            String userId = this.getValue("userId");
            if (ToolsKit.isEmpty(userId)){
                userId = this.getValue("userid");
            }
            if (ToolsKit.isEmpty(userId)){
                userId = Constant.DEFAULT_USER_COURSE_ID;
            }
            String resUrl = this.getValue("resUrl");
            String resType = this.getValue("resType");
            String content = this.getValue("content");
            if (ToolsKit.isEmpty(resType)) {
                throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("资源类型不能为空");
            }
            ResourceDataDto dto = resourceDataBuService.saveResource(userId, resUrl, Integer.parseInt(resType), content);
            if (ResourceTypeEnums.VOICE.getValue() == Integer.parseInt(resType)) {
                Map<String, String> map = new HashMap<String, String>();
                String fileDomain = ToolsKit.isNotEmpty(commonProperties.getTestShareDomain())?commonProperties.getTestShareDomain():commonProperties.getFileDomain();

                String url = fileDomain + commonProperties.getH5Env() + Constant.NOTE_INFO_URL + "?id=" + dto.getId();
                if (ToolsKit.isNotEmpty(commonProperties.getH5Env())){
                    map.put("url",  Constant.SHORTLINK_REDIRECT_TEST_URL + shortLinkBuService.getNextId(url));
                } else {
                    map.put("url",  Constant.SHORTLINK_REDIRECT_PROC_URL + shortLinkBuService.getNextId(url));
                }

                return this.returnSuccessJson(ExceptionEnums.SUCCESS, map);
            } else {
                return this.returnSuccessJson(ExceptionEnums.SUCCESS, dto);
            }
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    /***
     * 最近使用的标签纸张信息
     * @return
     */
    @RequestMapping(value = "/getrecentoperationlist")
    public ReturnDto getRecentOperationList(){
        try {
            HeadInfoDto headInfoDto = getHeadInfoDto();
            Locale locale = WebTools.getLocaleByLanguage(headInfoDto.getLanguage());
            String userId = this.getValue("userid");
            String printerType = this.getValue("printerType");

            String version = headInfoDto.getVersion();
            // 大于等于3.6.0版本，执行新接口数据返回
            return this.returnSuccessJson(ExceptionEnums.SUCCESS,draftsBuService.getRecentOperationList(userId, printerType, locale));
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    /***
     * 添加使用的标签纸张信息
     * @return
     */
    @RequestMapping(value = "/addrecentoperation")
    public ReturnDto addRecentOperation(){
        try {
            String userId = this.getValue("userid");
            RecentPaperInfoDto paperInfoDto = this.getBean(RecentPaperInfoDto.class);
            return this.returnSuccessJson(ExceptionEnums.SUCCESS,
                    draftsBuService.addRecentOperation(userId, paperInfoDto));
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    /***
     * 添加使用的标签纸张信息
     * @return
     */
    @RequestMapping(value = "/clearrecentoperation")
    public ReturnDto clearRecentOperation(){
        try {
            String userId = this.getValue("userid");
            return this.returnSuccessJson(ExceptionEnums.SUCCESS,
                    draftsBuService.clearRecentOperation(userId));
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    /****-------------------------支撑平台-------------------------------*/

    /**
     * 获取草稿箱列表（分页）
     *
     * @return
     */
    @RequestMapping(value = "/getdraftspage")
    public ReturnDto getDraftsPage() {
        try {
            String createUserId = this.getValue("createUserId");
            String type = this.getValue("type");
//            type = "0";
            String pageNo = this.getValue("pageno");
            String pageSize = this.getValue("pagesize");
            String lastId = this.getValue("lastid");
            String subType = this.getValue("subtype");
            String length = this.getValue("length");
            if (ToolsKit.isEmpty(pageNo)) {
                pageNo = "1";
            }
            if (ToolsKit.isEmpty(pageSize)) {
                pageSize = String.valueOf(ToolsConst.PHONEPAGESIZE);
            }
            lastId = "";
            return this.returnSuccessJson(ExceptionEnums.SUCCESS,
                    draftsBuService.getDraftsPage(createUserId, type, subType, length, Integer.parseInt(pageNo), Integer.parseInt(pageSize), lastId));
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

}
