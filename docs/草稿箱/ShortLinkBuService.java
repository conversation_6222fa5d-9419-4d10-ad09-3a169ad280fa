package net.snaptag.system.business.buservice;

import net.snaptag.system.business.dao.ShortLinkDao;
import net.snaptag.system.business.entity.ShortLink;
import net.snaptag.system.business.utils.Constant;
import net.snaptag.system.sadais.cache.kit.CacheKit;
import net.snaptag.system.sadais.core.common.core.ToolsConst;
import net.snaptag.system.sadais.util.core.ToolsKit;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date ：Created in 2022/3/24 9:42
 * @description：
 * @modified By：
 * @version: $
 */
@Service
public class ShortLinkBuService {
    private final static String CACHE_KEY= "XEASYLABEL:SHORTLINK:ID";
    @Autowired
    private ShortLinkDao shortLinkDao;

    public synchronized String getNextId(String url){
        Long idNumber = CacheKit.cache().get(CACHE_KEY, Long.class);
        if (ToolsKit.isEmpty(idNumber)){
            idNumber = shortLinkDao.getCount();
        }
        idNumber++;
        ShortLink shortLink = new ShortLink();
        ToolsKit.setIdEntityData(shortLink, Constant.DEFAULT_USER_COURSE_ID);
        shortLink.setUrl(url);
        shortLink.setId(String.valueOf(idNumber));
        shortLinkDao.saveEntity(shortLink);
        return shortLink.getId();
    }

    public String getUrlById(String id){
        ShortLink shortLink = shortLinkDao.getById(id, ToolsConst.DATA_SUCCESS_STATUS);
        if (shortLink!=null){
            return shortLink.getUrl();
        } else {
            return "";
        }
    }
}
