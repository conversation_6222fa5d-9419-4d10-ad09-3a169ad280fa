package net.snaptag.system.business.dao;

import net.snaptag.system.business.entity.Drafts;
import net.snaptag.system.business.entity.PrintPaperInfo;
import net.snaptag.system.business.enums.DraftsSubTypeEnums;
import net.snaptag.system.business.enums.PaperInfoEnums;
import net.snaptag.system.sadais.core.common.core.ToolsConst;
import net.snaptag.system.sadais.mongo.common.Page;
import net.snaptag.system.sadais.mongo.core.MongoDao;
import net.snaptag.system.sadais.util.core.ToolsKit;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;

/**
 * 草稿箱信息
 *
 * <AUTHOR> 2018年6月12日
 */
@Repository
public class DraftsDao extends MongoDao<Drafts> {
    /**
     * 获取草稿箱列表
     *
     * @return 草稿箱列表
     * @throws Exception
     */
    public List<Drafts> findDraftsList(String userId, int type, int subType,String materialColumnId, int length, int pageNo, int pageSize, String lastId, String printerType, List<PrintPaperInfo> printerPaperList, String paperType, String keyword) {
        Query query = new Query();
        List<Criteria> criteriaList = new ArrayList<>();

        criteriaList.add(Criteria.where(Drafts.USER_ID_FIELD).is(userId));
        criteriaList.add(Criteria.where(Drafts.TYPE_FIELD).is(type));
        criteriaList.add(Criteria.where(Drafts.STATUS_FIELD).is(ToolsConst.DATA_SUCCESS_STATUS));

        if (ToolsKit.isNotEmpty(subType) && subType != DraftsSubTypeEnums.None.getValue()){
            criteriaList.add(Criteria.where(Drafts.SUB_TYPE_FIELD).is(subType));
        }

        if (ToolsKit.isNotEmpty(materialColumnId)){
            criteriaList.add(Criteria.where("materialColumnId").is(materialColumnId));
        }

        if (ToolsKit.isNotEmpty(keyword)){
            Criteria [] cndOrArray = new Criteria[4];
            Pattern pattern = Pattern.compile("^.*" + keyword + ".*$");
            cndOrArray[0] = Criteria.where("title").regex(pattern.toString());
            cndOrArray[1] = Criteria.where("printerType").regex(pattern.toString());

            if (ToolsKit.Number.isNumber(keyword)){
                cndOrArray[2] = Criteria.where("paperWidth").is(Float.parseFloat(keyword));
                cndOrArray[3] = Criteria.where("paperLength").is(Float.parseFloat(keyword));
            }
            criteriaList.add(new Criteria().orOperator(cndOrArray));
        }

        if (ToolsKit.isNotEmpty(paperType)){
            // 此值是两位数，取前一个数，组成
            String first = paperType.charAt(0) + "";
            int max = (Integer.parseInt(first)+1) * 10;
            int min = Integer.parseInt(first) * 10;

            criteriaList.add(Criteria.where("paperType").gte(min).lt(max));
//            query.addCriteria(Criteria.where("paperType").gte(min).lt(max));
//            query.addCriteria(Criteria.where("paperType").lt(max));
        }

        if (ToolsKit.isNotEmpty(printerType)) {
            if (ToolsKit.isEmpty(printerPaperList)){
                criteriaList.add(Criteria.where("printerType").is(printerType));
//                query.addCriteria(Criteria.where("printerType").is(printerType));
            } else {
                List<Criteria> cndOrList = new ArrayList<>();
                cndOrList.add(Criteria.where("printerType").is(printerType));
                Map<String, String> mapExist = new HashMap<>();
                for (PrintPaperInfo obj: printerPaperList) {
                    Criteria [] cndAndList = new Criteria[3];
                    String [] paperIds = obj.getPaperId().split("-");
                    Integer color = Integer.parseInt(paperIds[paperIds.length-1]);
                    if (mapExist.get(obj.getWidth() + "" + obj.getHeight() + "" + color)!=null){
                        continue;
                    }

                    mapExist.put(obj.getWidth() + "" + obj.getHeight() + "" + color, "1");
                    if (Float.valueOf(obj.getWidth()).intValue()==1 || Float.valueOf(obj.getHeight()).intValue()==1){
                        cndAndList[0] = Criteria.where("paperColor").is(Integer.parseInt(paperIds[paperIds.length-1]));
                        cndAndList[1] = Criteria.where("paperWidth").is(obj.getHeight());
                        cndAndList[2] = Criteria.where("paperType").gte(11).lt(21);
                        cndOrList.add(new Criteria().andOperator(cndAndList));
                    } else {
                        cndAndList = new Criteria[4];
                        cndAndList[0] = Criteria.where("paperLength").is(obj.getWidth());
                        cndAndList[1] = Criteria.where("paperWidth").is(obj.getHeight());
                        cndAndList[2] = Criteria.where("paperColor").is(Integer.parseInt(paperIds[paperIds.length-1]));
                        cndAndList[3] = Criteria.where("paperType").gte(21);
                        cndOrList.add(new Criteria().andOperator(cndAndList));
                    }
                }
                Criteria [] cndOrArray = new Criteria[cndOrList.size()];
                for (int i = 0; i < cndOrList.size(); i++) {
                    cndOrArray[i] = cndOrList.get(i);
                }
                // query.addCriteria(new Criteria().orOperator(cndOrArray));
                criteriaList.add(new Criteria().orOperator(cndOrArray));
            }
        }

        Criteria [] tmp = new Criteria[criteriaList.size()];
        for (int i = 0; i < criteriaList.size(); i++) {
            tmp[i] = criteriaList.get(i);
        }
        query.addCriteria(new Criteria().andOperator(tmp));

        query.limit(pageSize);
        query.skip(pageNo * pageSize);
        query.with(Sort.by(Sort.Direction.DESC, Drafts.CREATETIME_FIELD));
        query.fields().include(Drafts.ID_FIELD).include(Drafts.CREATETIME_FIELD);
        System.out.println("=======================query="+query.getQueryObject());
        return this.findList(query);
    }

    /***
     * 用于3.3.0以下版本
     * @deprecated
     * @param userId
     * @param type
     * @param subType
     * @param length
     * @param pageNo
     * @param pageSize
     * @param lastId
     * @param printerType
     * @param paperType
     * @return
     */
    public List<Drafts> findDraftsList2(String userId, int type, int subType, int length, int pageNo, int pageSize, String lastId, String printerType, String paperType) {
        Query query = new Query();
        query.addCriteria(Criteria.where(Drafts.USER_ID_FIELD).is(userId));
        query.addCriteria(Criteria.where(Drafts.TYPE_FIELD).is(type));
//         query.addCriteria(Criteria.where(Drafts.SUB_TYPE_FIELD).is(subType));
        // query.addCriteria(Criteria.where(Drafts.LENGTH_FIELD).is(length));
        query.addCriteria(Criteria.where(Drafts.STATUS_FIELD).is(ToolsConst.DATA_SUCCESS_STATUS));
        if (ToolsKit.isNotEmpty(lastId)) {
            query.addCriteria(Criteria.where(Drafts.ID_FIELD).lt(lastId));
        }

        if (ToolsKit.isNotEmpty(paperType)){
            // 此值是两位数，取前一个数，组成
            String first = paperType.charAt(0) + "";
            int max = (Integer.parseInt(first)+1) * 10;
            int min = Integer.parseInt(first) * 10;

            query.addCriteria(Criteria.where("paperType").gte(min).lt(max));
//            query.addCriteria(Criteria.where("paperType").lt(max));
        }

        if (ToolsKit.isNotEmpty(printerType)) {
//            query.addCriteria(Criteria.where("printerType").is(printerType));
            List<PaperInfoEnums> condList = PaperInfoEnums.getEnumsByPrinterTYpe(printerType);
//             and ( (paperLength=1 and paperWidth=1) or (paperLength=1 and paperWidth=1) or (paperLength=1 and paperWidth=1)  )
            if (ToolsKit.isNotEmpty(condList)){
                List<Criteria> cndOrList = new ArrayList<>();
                for (PaperInfoEnums enumObj: condList) {
                    Criteria [] cndAndList = new Criteria[2];

                    if ("hp1".equals(printerType) || "hp2".equals(printerType)){
//                        cndAndList[1] = );
                        // hp1和hp2有连续纸
                        cndOrList.add(Criteria.where("paperWidth").is(enumObj.getHeight()));
                    } else {
                        cndAndList[0] = Criteria.where("paperLength").is(enumObj.getWidth());
                        cndAndList[1] = Criteria.where("paperWidth").is(enumObj.getHeight());
                        cndOrList.add(new Criteria().andOperator(cndAndList));
                    }
                }
                Criteria [] cndOrArray = new Criteria[cndOrList.size()];

                for (int i = 0; i < cndOrList.size(); i++) {
                    cndOrArray[i] = cndOrList.get(i);
                }

                query.addCriteria(new Criteria().orOperator(cndOrArray));
            }
        }

        query.limit(pageSize);
        query.skip(pageNo * pageSize);
        query.with(Sort.by(Sort.Direction.DESC, Drafts.CREATETIME_FIELD));
        query.fields().include(Drafts.ID_FIELD).include(Drafts.CREATETIME_FIELD);
//        System.out.println("=======================query="+query.getQueryObject());
        return this.findList(query);
    }

    /**
     * 获取草稿箱列表
     *
     * @return 草稿箱列表
     * @throws Exception
     */
    public Page<Drafts> findDraftsPage(String userId, Integer type, int subType, Integer length, int pageNo, int pageSize, String lastId) {
        Query query = new Query();
        if (ToolsKit.isNotEmpty(userId)){
            query.addCriteria(Criteria.where(Drafts.USER_ID_FIELD).is(userId));
        }

        if (ToolsKit.isNotEmpty(type)){
            query.addCriteria(Criteria.where(Drafts.TYPE_FIELD).is(type));
        }
        if (ToolsKit.isNotEmpty(length)){
            query.addCriteria(Criteria.where(Drafts.LENGTH_FIELD).is(length));
        }

        // query.addCriteria(Criteria.where(Drafts.SUB_TYPE_FIELD).is(subType));
        // query.addCriteria(Criteria.where(Drafts.LENGTH_FIELD).is(length));
        query.addCriteria(Criteria.where(Drafts.STATUS_FIELD).is(ToolsConst.DATA_SUCCESS_STATUS));
        if (ToolsKit.isNotEmpty(lastId)) {
            query.addCriteria(Criteria.where(Drafts.ID_FIELD).lt(lastId));
        }

//        query.addCriteria(Criteria.where(Drafts.LENGTH_FIELD).in(PaperInfoEnums.getAllLengthByOneLength(length)));

        query.limit(pageSize);
        query.skip(pageNo * pageSize);
        query.with(Sort.by(Sort.Direction.DESC, Drafts.CREATETIME_FIELD));
        query.fields().include(Drafts.ID_FIELD).include(Drafts.CREATETIME_FIELD);

//        System.out.println("======================================");
//        System.out.println(JSONObject.toJSON(query));
//        System.out.println("======================================");
        return this.findPage(query);
    }

    /**
     * 根据ID获取草稿箱对象
     *
     * @return 草稿箱对象
     * @throws Exception
     */
    public Drafts getDraftsById(String id) {
        Query query = new Query();
        query.addCriteria(Criteria.where(Drafts.ID_FIELD).is(id));
        query.addCriteria(Criteria.where(Drafts.STATUS_FIELD).is(ToolsConst.DATA_SUCCESS_STATUS));
        return this.findOne(query);
    }
}
