-- SnapTag数据库表结构初始化脚本
-- 从MongoDB迁移到MySQL

-- 创建数据库
CREATE DATABASE IF NOT EXISTS snaptag_test DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE snaptag_test;

-- 删除所有现有表（按依赖关系顺序）
DROP TABLE IF EXISTS v1_user_template_drafts_collect;
DROP TABLE IF EXISTS v1_template_drafts_language_config;
DROP TABLE IF EXISTS v1_template_drafts;
DROP TABLE IF EXISTS v1_web_print;
DROP TABLE IF EXISTS v1_user_feedback;
DROP TABLE IF EXISTS v1_system_msg;
DROP TABLE IF EXISTS v1_system_help_item;
DROP TABLE IF EXISTS v1_short_link;
DROP TABLE IF EXISTS v1_resource_data;
DROP TABLE IF EXISTS v1_print_paper_info;
DROP TABLE IF EXISTS v1_print_driver_update_info;
DROP TABLE IF EXISTS v1_paper_display;
DROP TABLE IF EXISTS v1_msg_column;
DROP TABLE IF EXISTS v1_msg_center;
DROP TABLE IF EXISTS v1_material_resource;
DROP TABLE IF EXISTS v1_material;
DROP TABLE IF EXISTS v1_goods;
DROP TABLE IF EXISTS v1_functions_setting;
DROP TABLE IF EXISTS v1_drafts;
DROP TABLE IF EXISTS v1_device_connect_log;
DROP TABLE IF EXISTS v1_dict;
DROP TABLE IF EXISTS v1_banner;
DROP TABLE IF EXISTS v1_user_title_type;
DROP TABLE IF EXISTS v1_user_third_party_auth;
DROP TABLE IF EXISTS v1_user_role;
DROP TABLE IF EXISTS v1_user_login_history;
DROP TABLE IF EXISTS v1_user_info;
DROP TABLE IF EXISTS v1_user_auth_account;
DROP TABLE IF EXISTS v1_user_account;
DROP TABLE IF EXISTS v1_role_resource;
DROP TABLE IF EXISTS v1_role_table;
DROP TABLE IF EXISTS v1_resource_table;
DROP TABLE IF EXISTS v1_update_info;

-- Banner表
CREATE TABLE IF NOT EXISTS v1_banner (
    id VARCHAR(64) NOT NULL PRIMARY KEY COMMENT '主键ID',
    name VARCHAR(255) COMMENT '名称',
    pics JSON COMMENT '图片map',
    jump_val TEXT COMMENT '消息跳转值',
    sort INT DEFAULT 0 COMMENT '排序',
    jump_type INT DEFAULT 0 COMMENT '跳转类型',
    `column` VARCHAR(100) COMMENT '栏目（gam 社区，mall商城）',
    start_time DATETIME COMMENT 'Banner有效开始时间',
    end_time DATETIME COMMENT 'Banner有效结束时间',
    share_title VARCHAR(255) COMMENT '分享时展示的标题',
    share_content TEXT COMMENT '分享时展示的内容',
    share_flag INT DEFAULT 0 COMMENT '是否分享',
    version VARCHAR(50) COMMENT '显示最小版本版本号',
    remark TEXT COMMENT '备注信息',
    param_android TEXT COMMENT '自定义组装的json格式，用于android前端调用',
    param_ios TEXT COMMENT '自定义组装的json格式，用于ios前端调用',
    locale_code VARCHAR(20) COMMENT '语言国际化',
    createtime DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    createuserid VARCHAR(64) COMMENT '创建人ID',
    updatetime DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    updateuserid VARCHAR(64) COMMENT '更新人ID',
    status VARCHAR(20) DEFAULT '审核通过' COMMENT '数据状态',
    source VARCHAR(50) COMMENT '数据来源',
    app_id VARCHAR(64) COMMENT '应用渠道ID',
    _class VARCHAR(255) COMMENT '类名',
    INDEX idx_createtime (createtime),
    INDEX idx_locale_code (locale_code),
    INDEX idx_app_id (app_id),
    INDEX idx_id (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='顶部广告信息表';

-- Material表
CREATE TABLE IF NOT EXISTS v1_material (
    id VARCHAR(64) NOT NULL PRIMARY KEY COMMENT '主键ID',
    p_id VARCHAR(64) COMMENT '父ID',
    name VARCHAR(255) COMMENT '名称',
    sort INT DEFAULT 0 COMMENT '排序',
    type INT DEFAULT 0 COMMENT '类型',
    sub_type INT DEFAULT 0 COMMENT '副类型',
    has_content INT DEFAULT 0 COMMENT '是否有内容',
    label TEXT COMMENT '标签，用分号隔开',
    icon JSON COMMENT '图标地址',
    position JSON COMMENT '方位',
    com VARCHAR(255) COMMENT '模板组件名',
    zh_title VARCHAR(255) COMMENT '中文标题',
    en_title VARCHAR(255) COMMENT '英文标题',
    createtime DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    createuserid VARCHAR(64) COMMENT '创建人ID',
    updatetime DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    updateuserid VARCHAR(64) COMMENT '更新人ID',
    status VARCHAR(20) DEFAULT '审核通过' COMMENT '数据状态',
    source VARCHAR(50) COMMENT '数据来源',
    app_id VARCHAR(64) COMMENT '应用渠道ID',
    _class VARCHAR(255) COMMENT '类名',
    INDEX idx_createtime (createtime),
    INDEX idx_app_id (app_id),
    INDEX idx_id (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='素材库';

-- Dict表
CREATE TABLE IF NOT EXISTS v1_dict (
    id VARCHAR(64) NOT NULL PRIMARY KEY COMMENT '主键ID',
    name VARCHAR(255) COMMENT '名称',
    type VARCHAR(100) COMMENT '类型',
    label VARCHAR(255) COMMENT '标签',
    value VARCHAR(500) COMMENT '值',
    locale_code VARCHAR(20) COMMENT 'i18n的code值',
    icon VARCHAR(500) COMMENT '图标地址',
    sort_num INT DEFAULT 0 COMMENT '排序',
    remark TEXT COMMENT '备注',
    createtime DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    createuserid VARCHAR(64) COMMENT '创建人ID',
    updatetime DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    updateuserid VARCHAR(64) COMMENT '更新人ID',
    status VARCHAR(20) DEFAULT '审核通过' COMMENT '数据状态',
    source VARCHAR(50) COMMENT '数据来源',
    app_id VARCHAR(64) COMMENT '应用渠道ID',
    INDEX idx_createtime (createtime),
    INDEX idx_app_id (app_id),
    INDEX idx_id (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='字典项';

-- 用户账号表
CREATE TABLE IF NOT EXISTS v1_user_account (
    id VARCHAR(64) NOT NULL PRIMARY KEY COMMENT '主键ID',
    mobile_account VARCHAR(20) COMMENT '手机号码',
    mail_account VARCHAR(100) COMMENT '邮箱地址',
    pwd VARCHAR(255) COMMENT '密码',
    salt VARCHAR(100) COMMENT '盐值',
    sinaaccount VARCHAR(100) COMMENT '新浪微博账号',
    sinapwd VARCHAR(255) COMMENT '新浪微博密码',
    qqaccount VARCHAR(100) COMMENT 'qq账号',
    qqpwd VARCHAR(255) COMMENT 'qq密码',
    weixinaccount VARCHAR(100) COMMENT '微信账号',
    weixinpwd VARCHAR(255) COMMENT '微信密码',
    fbaccount VARCHAR(100) COMMENT '脸书账号',
    fbpwd VARCHAR(255) COMMENT '脸书密码',
    twaccount VARCHAR(100) COMMENT '推特账号',
    twpwd VARCHAR(255) COMMENT '推特密码',
    waaccount VARCHAR(100) COMMENT 'WhatsApp账号',
    wapwd VARCHAR(255) COMMENT 'WhatsApp密码',
    googleaccount VARCHAR(100) COMMENT 'Google账号',
    googlepwd VARCHAR(255) COMMENT 'Google密码',
    appleaccount VARCHAR(100) COMMENT 'Apple账号',
    appleaccount2 VARCHAR(100) COMMENT 'Apple账号2',
    bind_name JSON COMMENT '第三方绑定名称',
    moblie_id VARCHAR(100) COMMENT '设备ID',
    device_system VARCHAR(100) COMMENT '设备系统',
    createtime DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    createuserid VARCHAR(64) COMMENT '创建人ID',
    updatetime DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    updateuserid VARCHAR(64) COMMENT '更新人ID',
    status VARCHAR(20) DEFAULT '审核通过' COMMENT '数据状态',
    source VARCHAR(50) COMMENT '数据来源',
    app_id VARCHAR(64) COMMENT '应用渠道ID',
    INDEX idx_id (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户账号表';

-- 用户信息表
CREATE TABLE IF NOT EXISTS v1_user_info (
    id VARCHAR(64) NOT NULL PRIMARY KEY COMMENT '主键ID',
    user_id VARCHAR(64) COMMENT '账号ID',
    nick_name VARCHAR(100) COMMENT '昵称',
    user_pic VARCHAR(500) COMMENT '用户头像',
    sex VARCHAR(10) COMMENT '性别',
    birthday DATE COMMENT '生日',
    current_weight DOUBLE DEFAULT 0 COMMENT '当前体重',
    target_weight DOUBLE DEFAULT 0 COMMENT '目标体重',
    height INT DEFAULT 0 COMMENT '身高',
    channel VARCHAR(50) COMMENT '渠道',
    code_id INT DEFAULT 0 COMMENT '用户codeId',
    is_edit INT DEFAULT 0 COMMENT '是否编辑过信息 0否 1是',
    is_official INT DEFAULT 0 COMMENT '是否是官方认证（3.2版本后，会弃用，由userTitleType替代）',
    grade_level INT DEFAULT 0 COMMENT '所在年级1--12对应小学一年级到高中三年级',
    role INT DEFAULT 0 COMMENT '身份 0：其他；1：学生；2：老师；3：家长',
    user_title_type INT DEFAULT 0 COMMENT '用户头衔 0:普通用户；1:官方认证；2:KOL认证；3:素材编辑',
    is_forbidden INT DEFAULT 0 COMMENT '是否禁用，0：否，1：禁用',
    createtime DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    createuserid VARCHAR(64) COMMENT '创建人ID',
    updatetime DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    updateuserid VARCHAR(64) COMMENT '更新人ID',
    status VARCHAR(20) DEFAULT '审核通过' COMMENT '数据状态',
    source VARCHAR(50) COMMENT '数据来源',
    app_id VARCHAR(64) COMMENT '应用渠道ID',
    INDEX idx_id (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户信息表';

-- 用户第三方授权信息表
CREATE TABLE IF NOT EXISTS v1_user_third_party_auth (
    id VARCHAR(64) NOT NULL PRIMARY KEY COMMENT '主键ID',
    user_id VARCHAR(64) COMMENT '用户ID',
    wechat_open_id JSON COMMENT '微信openID',
    wechat_union_id VARCHAR(100) COMMENT '微信unionID',
    qq_open_id JSON COMMENT 'qq openID',
    qq_union_id VARCHAR(100) COMMENT 'qq unionID',
    weibo_union_id VARCHAR(100) COMMENT '微博unionID',
    createtime DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    createuserid VARCHAR(64) COMMENT '创建人ID',
    updatetime DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    updateuserid VARCHAR(64) COMMENT '更新人ID',
    status VARCHAR(20) DEFAULT '审核通过' COMMENT '数据状态',
    source VARCHAR(50) COMMENT '数据来源',
    app_id VARCHAR(64) COMMENT '应用渠道ID',
    INDEX idx_id (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户第三方授权信息表';

-- 草稿箱表
CREATE TABLE IF NOT EXISTS v1_drafts (
    id VARCHAR(64) NOT NULL PRIMARY KEY COMMENT '主键ID',
    res_pic_id VARCHAR(64) COMMENT '资源图片ID',
    res_data_id VARCHAR(64) COMMENT '资源数据ID',
    user_id VARCHAR(64) COMMENT '用户ID',
    type INT DEFAULT 0 COMMENT '草稿箱类型 0--草稿箱 1--我的历史',
    sub_type INT DEFAULT 0 COMMENT '草稿箱副类型 0编辑纸条 1清单 2大字横幅 3便利贴 4网页打印',
    material_column_id VARCHAR(64) COMMENT '类型的ID',
    length INT DEFAULT 0 COMMENT '纸张长度',
    drafts_param JSON COMMENT '草稿箱参数',
    place_type INT DEFAULT 0 COMMENT '编辑方向，可选，默认0，打竖，1为横向',
    paper_type INT DEFAULT 0 COMMENT '纸张类型',
    paper_length FLOAT DEFAULT 0 COMMENT '纸张长度',
    paper_width FLOAT DEFAULT 0 COMMENT '纸张宽度',
    paper_color INT DEFAULT 0 COMMENT '纸张颜色',
    printer_type VARCHAR(100) COMMENT '打印机型号',
    pic_print_url VARCHAR(500) COMMENT '直接打印的url地址',
    title VARCHAR(255) COMMENT '标题',
    is_mirror INT DEFAULT 0 COMMENT '是否镜像，0：非，1：是',
    paper_name VARCHAR(255) COMMENT '自定义纸张名称',
    paper_is_black_flag BOOLEAN DEFAULT FALSE COMMENT '是否黑边',
    paper_is_custom BOOLEAN DEFAULT FALSE COMMENT '是否用户自定义',
    preview_point VARCHAR(255) COMMENT '预览中心点',
    name VARCHAR(255) COMMENT '标签名称',
    createtime DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    createuserid VARCHAR(64) COMMENT '创建人ID',
    updatetime DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    updateuserid VARCHAR(64) COMMENT '更新人ID',
    status VARCHAR(20) DEFAULT '审核通过' COMMENT '数据状态',
    source VARCHAR(50) COMMENT '数据来源',
    app_id VARCHAR(64) COMMENT '应用渠道ID',
    INDEX idx_id (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='草稿箱表';

-- 商品表
CREATE TABLE IF NOT EXISTS v1_goods (
    id VARCHAR(64) NOT NULL PRIMARY KEY COMMENT '主键ID',
    name VARCHAR(255) COMMENT '名称',
    sub_name VARCHAR(255) COMMENT '副标题',
    pic_url VARCHAR(500) COMMENT '图片地址',
    jump_val TEXT COMMENT '消息跳转值',
    sort_num INT DEFAULT 0 COMMENT '排序',
    sale_status INT DEFAULT 0 COMMENT '是否上架，0：未上架；1：上架',
    price VARCHAR(100) COMMENT '价格，包含单位',
    remark TEXT COMMENT '备注信息',
    param_android TEXT COMMENT '自定义组装的json格式，用于android前端调用',
    param_ios TEXT COMMENT '自定义组装的json格式，用于ios前端调用',
    createtime DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    createuserid VARCHAR(64) COMMENT '创建人ID',
    updatetime DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    updateuserid VARCHAR(64) COMMENT '更新人ID',
    status VARCHAR(20) DEFAULT '审核通过' COMMENT '数据状态',
    source VARCHAR(50) COMMENT '数据来源',
    app_id VARCHAR(64) COMMENT '应用渠道ID',
    INDEX idx_id (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商品表';

-- 消息中心表
CREATE TABLE IF NOT EXISTS v1_msg_center (
    id VARCHAR(64) NOT NULL PRIMARY KEY COMMENT '主键ID',
    user_id VARCHAR(64) COMMENT '用户ID',
    msg_title VARCHAR(255) COMMENT '消息标题',
    msg_content TEXT COMMENT '消息内容',
    msg_type INT DEFAULT 0 COMMENT '消息主类型',
    msg_sub_type INT DEFAULT 0 COMMENT '消息副类型',
    msg_time DATETIME COMMENT '消息时间',
    pic VARCHAR(500) COMMENT '图片地址',
    sender_user_id VARCHAR(64) COMMENT '消息发起者ID',
    param JSON COMMENT '参数',
    `read` INT DEFAULT 0 COMMENT '消息已读状态',
    createtime DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    createuserid VARCHAR(64) COMMENT '创建人ID',
    updatetime DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    updateuserid VARCHAR(64) COMMENT '更新人ID',
    status VARCHAR(20) DEFAULT '审核通过' COMMENT '数据状态',
    source VARCHAR(50) COMMENT '数据来源',
    app_id VARCHAR(64) COMMENT '应用渠道ID',
    INDEX idx_id (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='消息中心表';

-- 系统消息表
CREATE TABLE IF NOT EXISTS v1_system_msg (
    id VARCHAR(64) NOT NULL PRIMARY KEY COMMENT '主键ID',
    msg_title VARCHAR(255) COMMENT '消息标题',
    msg_content TEXT COMMENT '消息内容',
    msg_type INT DEFAULT 0 COMMENT '消息主类型',
    pic_map JSON COMMENT '消息图片',
    msg_sub_type INT DEFAULT 0 COMMENT '消息副类型 跳转类型 100表示无跳转 101表示h5页 102表示社区动态 103表示申请好友 104表示好友纸条 200官方消息',
    jump_val TEXT COMMENT '消息跳转值',
    start_date DATETIME COMMENT '开始时间',
    end_date DATETIME COMMENT '结束时间',
    version VARCHAR(50) COMMENT '版本号后显示',
    param_android TEXT COMMENT '自定义组装的json格式，用于android前端调用',
    param_ios TEXT COMMENT '自定义组装的json格式，用于ios前端调用',
    createtime DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    createuserid VARCHAR(64) COMMENT '创建人ID',
    updatetime DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    updateuserid VARCHAR(64) COMMENT '更新人ID',
    status VARCHAR(20) DEFAULT '审核通过' COMMENT '数据状态',
    source VARCHAR(50) COMMENT '数据来源',
    app_id VARCHAR(64) COMMENT '应用渠道ID',
    INDEX idx_id (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统消息表';

-- 用户反馈表
CREATE TABLE IF NOT EXISTS v1_user_feedback (
    id VARCHAR(64) NOT NULL PRIMARY KEY COMMENT '主键ID',
    user_id VARCHAR(64) COMMENT '用户ID',
    type VARCHAR(50) COMMENT '反馈类型',
    qtype VARCHAR(50) COMMENT '问题类型',
    content TEXT COMMENT '反馈文本内容',
    images JSON COMMENT '反馈图片地址列表',
    result TEXT COMMENT '系统响应结果信息',
    errormsg TEXT COMMENT '错误信息',
    mobile VARCHAR(20) COMMENT '手机号',
    user_no INT COMMENT '用户编号',
    client_info TEXT COMMENT '客户端信息',
    printer_type VARCHAR(100) COMMENT '打印机类型',
    createtime DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    createuserid VARCHAR(64) COMMENT '创建人ID',
    updatetime DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    updateuserid VARCHAR(64) COMMENT '更新人ID',
    status VARCHAR(20) DEFAULT '审核通过' COMMENT '数据状态',
    source VARCHAR(50) COMMENT '数据来源',
    app_id VARCHAR(64) COMMENT '应用渠道ID',
    INDEX idx_id (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户反馈表';

-- 设备连接日志表
CREATE TABLE IF NOT EXISTS v1_device_connect_log (
    id VARCHAR(64) NOT NULL PRIMARY KEY COMMENT '主键ID',
    user_id VARCHAR(64) COMMENT '用户ID',
    mac_address VARCHAR(100) COMMENT '设备mac地址',
    device_sn VARCHAR(100) COMMENT '设备SN',
    device_name VARCHAR(255) COMMENT '设备名称',
    device_version VARCHAR(100) COMMENT '设备固件版本号',
    app_version VARCHAR(100) COMMENT '应用版本',
    lastest_time DATETIME COMMENT '最后连接时间',
    createtime DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    createuserid VARCHAR(64) COMMENT '创建人ID',
    updatetime DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    updateuserid VARCHAR(64) COMMENT '更新人ID',
    status VARCHAR(20) DEFAULT '审核通过' COMMENT '数据状态',
    source VARCHAR(50) COMMENT '数据来源',
    app_id VARCHAR(64) COMMENT '应用渠道ID',
    INDEX idx_id (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='设备连接日志表';

-- 功能设置表
CREATE TABLE IF NOT EXISTS v1_functions_setting (
    id VARCHAR(64) NOT NULL PRIMARY KEY COMMENT '主键ID',
    code VARCHAR(100) COMMENT '功能编码',
    name VARCHAR(255) COMMENT '功能名称',
    printer_types TEXT COMMENT '所属打印机',
    language_codes TEXT COMMENT '所属语种',
    params TEXT COMMENT '参数',
    new_flag INT DEFAULT 0 COMMENT '是否展示新',
    sort_num INT DEFAULT 0 COMMENT '排序',
    type VARCHAR(100) COMMENT '功能类型',
    createtime DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    createuserid VARCHAR(64) COMMENT '创建人ID',
    updatetime DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    updateuserid VARCHAR(64) COMMENT '更新人ID',
    status VARCHAR(20) DEFAULT '审核通过' COMMENT '数据状态',
    source VARCHAR(50) COMMENT '数据来源',
    app_id VARCHAR(64) COMMENT '应用渠道ID',
    _class VARCHAR(255) COMMENT '类名',
    INDEX idx_createtime (createtime),
    INDEX idx_app_id (app_id),
    INDEX idx_id (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='功能设置表';

-- 素材资源表
CREATE TABLE IF NOT EXISTS v1_material_resource (
    id VARCHAR(64) NOT NULL PRIMARY KEY COMMENT '主键ID',
    m_id VARCHAR(64) COMMENT '素材库ID',
    length INT DEFAULT 0 COMMENT '纸张长度',
    res_map JSON COMMENT '资源集合',
    type INT DEFAULT 0 COMMENT '主类型',
    sub_type INT DEFAULT 0 COMMENT '副类型',
    place_type INT DEFAULT 0 COMMENT '0：竖向；1：横向',
    is_new INT DEFAULT 0 COMMENT '是否新品',
    show_new_time_limit DATETIME COMMENT '新品显示时间限制',
    label TEXT COMMENT '标签',
    sort_num INT DEFAULT 0 COMMENT '排序',
    locale_code VARCHAR(20) COMMENT '语言国际化',
    createtime DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    createuserid VARCHAR(64) COMMENT '创建人ID',
    updatetime DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    updateuserid VARCHAR(64) COMMENT '更新人ID',
    status VARCHAR(20) DEFAULT '审核通过' COMMENT '数据状态',
    source VARCHAR(50) COMMENT '数据来源',
    app_id VARCHAR(64) COMMENT '应用渠道ID',
    INDEX idx_id (id),
    INDEX idx_createtime (createtime),
    INDEX idx_app_id (app_id),
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='素材资源表';

-- 消息栏目表
CREATE TABLE IF NOT EXISTS v1_msg_column (
    id VARCHAR(64) NOT NULL PRIMARY KEY COMMENT '主键ID',
    user_id VARCHAR(64) COMMENT '用户ID',
    column_map JSON COMMENT '栏目vo',
    createtime DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    createuserid VARCHAR(64) COMMENT '创建人ID',
    updatetime DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    updateuserid VARCHAR(64) COMMENT '更新人ID',
    status VARCHAR(20) DEFAULT '审核通过' COMMENT '数据状态',
    source VARCHAR(50) COMMENT '数据来源',
    app_id VARCHAR(64) COMMENT '应用渠道ID',
    INDEX idx_id (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='消息栏目表';

-- 纸张显示表
CREATE TABLE IF NOT EXISTS v1_paper_display (
    id VARCHAR(64) NOT NULL PRIMARY KEY COMMENT '主键ID',
    name VARCHAR(255) COMMENT '名称',
    i18n_key VARCHAR(100) COMMENT '国际化键',
    types TEXT COMMENT '类型',
    printer_types TEXT COMMENT '打印机类型',
    language_codes TEXT COMMENT '语言代码',
    paper_ids TEXT COMMENT '纸张ID',
    sort_num INT DEFAULT 0 COMMENT '排序',
    createtime DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    createuserid VARCHAR(64) COMMENT '创建人ID',
    updatetime DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    updateuserid VARCHAR(64) COMMENT '更新人ID',
    status VARCHAR(20) DEFAULT '审核通过' COMMENT '数据状态',
    source VARCHAR(50) COMMENT '数据来源',
    app_id VARCHAR(64) COMMENT '应用渠道ID',
    _class VARCHAR(255) COMMENT '类名',
    INDEX idx_id (id),
    INDEX idx_createtime (createtime),
    INDEX idx_app_id (app_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='纸张显示表';

-- 打印驱动更新信息表
CREATE TABLE IF NOT EXISTS v1_print_driver_update_info (
    id VARCHAR(64) NOT NULL PRIMARY KEY COMMENT '主键ID',
    printer_model VARCHAR(255) COMMENT '打印机型号',
    version_name VARCHAR(255) COMMENT '固件名称',
    version_code VARCHAR(100) COMMENT '版本号编码',
    url VARCHAR(500) COMMENT '下载地址',
    param TEXT COMMENT '参数',
    remark TEXT COMMENT '版本描述',
    title VARCHAR(255) COMMENT '标题',
    need_force_update INT DEFAULT 0 COMMENT '是否强制升级',
    need_index_show INT DEFAULT 0 COMMENT '是否首页显示',
    used_flag INT DEFAULT 0 COMMENT '是否生效，不生效的不返回到前端',
    pre_driver_id VARCHAR(64) COMMENT '前置升级的固件id',
    need_check_power INT DEFAULT 0 COMMENT '0: 不用，1：要',
    power_value INT DEFAULT 0 COMMENT '符合升级的电量,0--100',
    md5 VARCHAR(255) COMMENT 'md5码',
    is_base_version INT DEFAULT 0 COMMENT '是否重要基础版本  0:否， 1：是',
    follow_status INT DEFAULT 0 COMMENT '增加流程状态',
    createtime DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    createuserid VARCHAR(64) COMMENT '创建人ID',
    updatetime DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    updateuserid VARCHAR(64) COMMENT '更新人ID',
    status VARCHAR(20) DEFAULT '审核通过' COMMENT '数据状态',
    source VARCHAR(50) COMMENT '数据来源',
    app_id VARCHAR(64) COMMENT '应用渠道ID',
    _class VARCHAR(255) COMMENT '类名',
    INDEX idx_id (id),
    INDEX idx_createtime (createtime),
    INDEX idx_app_id (app_id),
    INDEX idx_printer_model (printer_model)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='打印驱动更新信息表';

-- 打印纸张信息表
CREATE TABLE IF NOT EXISTS v1_print_paper_info (
    id VARCHAR(64) NOT NULL PRIMARY KEY COMMENT '主键ID',
    paper_id VARCHAR(64) COMMENT '纸张id',
    type INT DEFAULT 0 COMMENT '1为连续纸，2为间隙纸',
    material INT DEFAULT 0 COMMENT '介质，1为合成纸面材；2未普通纸面材',
    height FLOAT DEFAULT 0 COMMENT '纸张高度',
    width FLOAT DEFAULT 0 COMMENT '纸张长度，连续纸统一：1',
    gap_width FLOAT DEFAULT 0 COMMENT '间隙长度',
    rfid_flag INT DEFAULT 0 COMMENT '0:不带rfid，1：带rfid',
    color_code VARCHAR(20) COMMENT '颜色代码',
    zh_title VARCHAR(255) COMMENT '名称',
    en_title VARCHAR(255) COMMENT '名称-国际代码',
    list_name VARCHAR(255) COMMENT '列表前缀名',
    res_url VARCHAR(500) COMMENT '列表图',
    image_url VARCHAR(500) COMMENT '资源图',
    printer_type TEXT COMMENT '适合打印机，分号分隔',
    is_new INT DEFAULT 0 COMMENT '是否展示，0表示不显示，1表示显示',
    showtime_end DATETIME COMMENT '选择展示期限',
    direction INT DEFAULT 0 COMMENT '方向  0为 横，1为竖',
    is_hide INT DEFAULT 0 COMMENT '是否隐藏，0为不隐藏，1为隐藏',
    sort_num INT DEFAULT 0 COMMENT '排序字段',
    print_color_code VARCHAR(20) DEFAULT '#000000' COMMENT '打印色值',
    rotate INT DEFAULT 0 COMMENT '纸张方向',
    print_x FLOAT DEFAULT 0 COMMENT '打印范围X',
    print_y FLOAT DEFAULT 0 COMMENT '打印范围Y',
    print_h FLOAT DEFAULT 0 COMMENT '打印范围H',
    print_w FLOAT DEFAULT 0 COMMENT '打印范围W',
    die_cutting FLOAT DEFAULT 1.5 COMMENT '排废',
    is_black_flag INT DEFAULT 0 COMMENT '是否黑标，0：否； 1：是',
    createtime DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    createuserid VARCHAR(64) COMMENT '创建人ID',
    updatetime DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    updateuserid VARCHAR(64) COMMENT '更新人ID',
    status VARCHAR(20) DEFAULT '审核通过' COMMENT '数据状态',
    source VARCHAR(50) COMMENT '数据来源',
    app_id VARCHAR(64) COMMENT '应用渠道ID',
    _class VARCHAR(255) COMMENT '类名',
    default_texts_arr JSON COMMENT '默认文字数组',
    INDEX idx_id (id),
    INDEX idx_createtime (createtime),
    INDEX idx_app_id (app_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='打印纸张信息表';

-- 资源数据表
CREATE TABLE IF NOT EXISTS v1_resource_data (
    id VARCHAR(64) NOT NULL PRIMARY KEY COMMENT '主键ID',
    user_id VARCHAR(64) COMMENT '用户ID',
    res_url JSON COMMENT '资源地址',
    res_type INT DEFAULT 0 COMMENT '资源类型 0--编辑纸条数据 1--图片地址 2--语音 3--共享打印',
    flag VARCHAR(255) COMMENT '资源标识--MD5',
    content TEXT COMMENT '内容',
    createtime DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    createuserid VARCHAR(64) COMMENT '创建人ID',
    updatetime DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    updateuserid VARCHAR(64) COMMENT '更新人ID',
    status VARCHAR(20) DEFAULT '审核通过' COMMENT '数据状态',
    source VARCHAR(50) COMMENT '数据来源',
    app_id VARCHAR(64) COMMENT '应用渠道ID',
    INDEX idx_id (id),
    INDEX idx_createtime (createtime),
    INDEX idx_app_id (app_id),
    INDEX idx_user_id (user_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='资源数据表';

-- 短链接表
CREATE TABLE IF NOT EXISTS v1_short_link (
    id VARCHAR(64) NOT NULL PRIMARY KEY COMMENT '主键ID',
    url TEXT COMMENT 'URL地址',
    createtime DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    createuserid VARCHAR(64) COMMENT '创建人ID',
    updatetime DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    updateuserid VARCHAR(64) COMMENT '更新人ID',
    status VARCHAR(20) DEFAULT '审核通过' COMMENT '数据状态',
    source VARCHAR(50) COMMENT '数据来源',
    app_id VARCHAR(64) COMMENT '应用渠道ID',
    INDEX idx_id (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='短链接表';

-- 系统帮助项表
CREATE TABLE IF NOT EXISTS v1_system_help_item (
    id VARCHAR(64) NOT NULL PRIMARY KEY COMMENT '主键ID',
    title VARCHAR(255) COMMENT '标题',
    local_language_code VARCHAR(20) COMMENT '国际化编码',
    url VARCHAR(500) COMMENT '链接地址',
    cover_url VARCHAR(500) COMMENT '封面地址',
    type VARCHAR(100) COMMENT '帮助文档的类型',
    printer_type VARCHAR(100) COMMENT '打印机类型',
    sort_num INT DEFAULT 0 COMMENT '排序',
    createtime DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    createuserid VARCHAR(64) COMMENT '创建人ID',
    updatetime DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    updateuserid VARCHAR(64) COMMENT '更新人ID',
    status VARCHAR(20) DEFAULT '审核通过' COMMENT '数据状态',
    source VARCHAR(50) COMMENT '数据来源',
    app_id VARCHAR(64) COMMENT '应用渠道ID',
    INDEX idx_id (id),
    INDEX idx_createtime (createtime),
    INDEX idx_app_id (app_id),
    INDEX idx_type (type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统帮助项表';

-- 模板草稿表
CREATE TABLE IF NOT EXISTS v1_template_drafts (
    id VARCHAR(64) NOT NULL PRIMARY KEY COMMENT '主键ID',
    name VARCHAR(255) COMMENT '名称',
    content TEXT COMMENT '描述',
    recommend TEXT COMMENT '推荐描述',
    drafts_dto JSON COMMENT '图的具体信息，参考打印记录里面的数据结构',
    pic VARCHAR(500) COMMENT '缩略图地址',
    type VARCHAR(50) COMMENT '类型：居家收纳：living；厨房收纳：kitchen；办公收纳：office',
    is_hot INT DEFAULT 0 COMMENT '是否热门。0：非热门； 1：热门',
    sort_num INT DEFAULT 0 COMMENT '排序字段',
    locale_code VARCHAR(20) COMMENT '语言国际',
    printer_type VARCHAR(100) COMMENT '符合打印机的类型',
    paper_type VARCHAR(100) COMMENT '符合打印机的类型',
    paper_size VARCHAR(100) COMMENT '纸张尺寸开',
    createtime DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    createuserid VARCHAR(64) COMMENT '创建人ID',
    updatetime DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    updateuserid VARCHAR(64) COMMENT '更新人ID',
    status VARCHAR(20) DEFAULT '审核通过' COMMENT '数据状态',
    source VARCHAR(50) COMMENT '数据来源',
    app_id VARCHAR(64) COMMENT '应用渠道ID',
    INDEX idx_id (id),
    INDEX idx_name (name),
    INDEX idx_createtime (createtime),
    INDEX idx_printer_type (printer_type),
    INDEX idx_paper_type (paper_type),
    INDEX idx_paper_size (paper_size),
    INDEX idx_app_id (app_id),
    INDEX idx_type (type),
    INDEX idx_locale_code (locale_code)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='模板草稿表';

-- 模板草稿语言配置表
CREATE TABLE IF NOT EXISTS v1_template_drafts_language_config (
    id VARCHAR(64) NOT NULL PRIMARY KEY COMMENT '主键ID',
    language VARCHAR(20) COMMENT '语言',
    show_flag BOOLEAN DEFAULT TRUE COMMENT '显示标志',
    createtime DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    createuserid VARCHAR(64) COMMENT '创建人ID',
    updatetime DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    updateuserid VARCHAR(64) COMMENT '更新人ID',
    status VARCHAR(20) DEFAULT '审核通过' COMMENT '数据状态',
    source VARCHAR(50) COMMENT '数据来源',
    app_id VARCHAR(64) COMMENT '应用渠道ID',
    INDEX idx_id (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='模板草稿语言配置表';

-- 更新信息表
CREATE TABLE IF NOT EXISTS v1_update_info (
    id VARCHAR(64) NOT NULL PRIMARY KEY COMMENT '主键ID',
    channel VARCHAR(50) COMMENT '渠道',
    version VARCHAR(50) COMMENT '版本号',
    url VARCHAR(500) COMMENT '下载地址',
    param TEXT COMMENT '参数',
    remark TEXT COMMENT '版本描述',
    need_force_update INT DEFAULT 0 COMMENT '是否强制升级',
    need_index_show INT DEFAULT 0 COMMENT '是否首页显示',
    title VARCHAR(255) COMMENT '标题',
    createtime DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    createuserid VARCHAR(64) COMMENT '创建人ID',
    updatetime DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    updateuserid VARCHAR(64) COMMENT '更新人ID',
    status VARCHAR(20) DEFAULT '审核通过' COMMENT '数据状态',
    source VARCHAR(50) COMMENT '数据来源',
    app_id VARCHAR(64) COMMENT '应用渠道ID',
    INDEX idx_id (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='更新信息表';

-- 用户模板草稿收藏表
CREATE TABLE IF NOT EXISTS v1_user_template_drafts_collect (
    id VARCHAR(64) NOT NULL PRIMARY KEY COMMENT '主键ID',
    user_id VARCHAR(64) COMMENT '用户ID',
    template_drafts_id VARCHAR(64) COMMENT '模板草稿ID',
    printer_type VARCHAR(100) COMMENT '打印机类型',
    createtime DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    createuserid VARCHAR(64) COMMENT '创建人ID',
    updatetime DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    updateuserid VARCHAR(64) COMMENT '更新人ID',
    status VARCHAR(20) DEFAULT '审核通过' COMMENT '数据状态',
    source VARCHAR(50) COMMENT '数据来源',
    app_id VARCHAR(64) COMMENT '应用渠道ID',
    INDEX idx_id (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户模板草稿收藏表';

-- 网页打印表
CREATE TABLE IF NOT EXISTS v1_web_print (
    id VARCHAR(64) NOT NULL PRIMARY KEY COMMENT '主键ID',
    user_id VARCHAR(64) COMMENT '用户ID',
    name VARCHAR(255) COMMENT '分组名称',
    is_default INT DEFAULT 0 COMMENT '是否默认',
    from_default INT DEFAULT 0 COMMENT '来自默认',
    page_list JSON COMMENT '我的网站集合',
    createtime DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    createuserid VARCHAR(64) COMMENT '创建人ID',
    updatetime DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    updateuserid VARCHAR(64) COMMENT '更新人ID',
    status VARCHAR(20) DEFAULT '审核通过' COMMENT '数据状态',
    source VARCHAR(50) COMMENT '数据来源',
    app_id VARCHAR(64) COMMENT '应用渠道ID',
    INDEX idx_id (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='网页打印表';

-- 用户登录历史表
CREATE TABLE IF NOT EXISTS v1_user_login_history (
    id VARCHAR(64) NOT NULL PRIMARY KEY COMMENT '主键ID',
    user_id VARCHAR(64) COMMENT '用户ID',
    latest_login_time DATETIME COMMENT '最后登录的时间',
    app_version VARCHAR(100) COMMENT '使用的app版本',
    used_devices TEXT COMMENT '使用过的打印设备',
    phone_type VARCHAR(100) COMMENT '手机类型',
    createtime DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    createuserid VARCHAR(64) COMMENT '创建人ID',
    updatetime DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    updateuserid VARCHAR(64) COMMENT '更新人ID',
    status VARCHAR(20) DEFAULT '审核通过' COMMENT '数据状态',
    source VARCHAR(50) COMMENT '数据来源',
    app_id VARCHAR(64) COMMENT '应用渠道ID',
    INDEX idx_id (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户登录历史表';

-- 用户角色表
CREATE TABLE IF NOT EXISTS v1_user_role (
    id VARCHAR(64) NOT NULL PRIMARY KEY COMMENT '主键ID',
    user_account_id VARCHAR(64) COMMENT '用户账户表ID',
    role_id VARCHAR(64) COMMENT '角色表ID',
    project_id VARCHAR(64) COMMENT '项目ID',
    createtime DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    createuserid VARCHAR(64) COMMENT '创建人ID',
    updatetime DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    updateuserid VARCHAR(64) COMMENT '更新人ID',
    status VARCHAR(20) DEFAULT '审核通过' COMMENT '数据状态',
    source VARCHAR(50) COMMENT '数据来源',
    app_id VARCHAR(64) COMMENT '应用渠道ID',
    _class VARCHAR(255) COMMENT '类名',
    INDEX idx_id (id),
    INDEX idx_createtime (createtime),
    INDEX idx_app_id (app_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户角色表';

-- 资源表
CREATE TABLE IF NOT EXISTS v1_resource_table (
    id VARCHAR(64) NOT NULL PRIMARY KEY COMMENT '主键ID',
    p_id VARCHAR(64) COMMENT '父节点ID',
    res_name VARCHAR(255) COMMENT '资源名称',
    res_code VARCHAR(100) COMMENT '资源编码',
    path VARCHAR(500) COMMENT '访问路径',
    level INT DEFAULT 0 COMMENT '层级',
    sort INT DEFAULT 0 COMMENT '排序',
    type INT DEFAULT 0 COMMENT '资源类型 0菜单 1按钮',
    project_id VARCHAR(64) COMMENT '项目ID',
    icon VARCHAR(255) COMMENT '菜单图标',
    createtime DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    createuserid VARCHAR(64) COMMENT '创建人ID',
    updatetime DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    updateuserid VARCHAR(64) COMMENT '更新人ID',
    status VARCHAR(20) DEFAULT '审核通过' COMMENT '数据状态',
    source VARCHAR(50) COMMENT '数据来源',
    app_id VARCHAR(64) COMMENT '应用渠道ID',
    _class VARCHAR(255) COMMENT '类名',
    INDEX idx_id (id),
    INDEX idx_createtime (createtime),
    INDEX idx_app_id (app_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='资源表';

-- 角色资源表
CREATE TABLE IF NOT EXISTS v1_role_resource (
    id VARCHAR(64) NOT NULL PRIMARY KEY COMMENT '主键ID',
    role_id VARCHAR(64) COMMENT '角色ID',
    resource_id VARCHAR(64) COMMENT '资源ID',
    createtime DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    createuserid VARCHAR(64) COMMENT '创建人ID',
    updatetime DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    updateuserid VARCHAR(64) COMMENT '更新人ID',
    status VARCHAR(20) DEFAULT '审核通过' COMMENT '数据状态',
    source VARCHAR(50) COMMENT '数据来源',
    app_id VARCHAR(64) COMMENT '应用渠道ID',
    _class VARCHAR(255) COMMENT '类名',
    INDEX idx_id (id),
    INDEX idx_createtime (createtime),
    INDEX idx_app_id (app_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='角色资源表';

-- 角色表
CREATE TABLE IF NOT EXISTS v1_role_table (
    id VARCHAR(64) NOT NULL PRIMARY KEY COMMENT '主键ID',
    project_id VARCHAR(64) COMMENT '项目ID',
    role_name VARCHAR(255) COMMENT '角色名称',
    role_code VARCHAR(100) COMMENT '角色编码',
    createtime DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    createuserid VARCHAR(64) COMMENT '创建人ID',
    updatetime DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    updateuserid VARCHAR(64) COMMENT '更新人ID',
    status VARCHAR(20) DEFAULT '审核通过' COMMENT '数据状态',
    source VARCHAR(50) COMMENT '数据来源',
    app_id VARCHAR(64) COMMENT '应用渠道ID',
    _class VARCHAR(255) COMMENT '类名',
    INDEX idx_id (id),
    INDEX idx_createtime (createtime),
    INDEX idx_app_id (app_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='角色表';

-- 用户认证账号表
CREATE TABLE IF NOT EXISTS v1_user_auth_account (
    id VARCHAR(64) NOT NULL PRIMARY KEY COMMENT '主键ID',
    name VARCHAR(255) COMMENT '用户名称',
    account VARCHAR(255) COMMENT '用户账号',
    password VARCHAR(255) COMMENT '用户密码',
    salt VARCHAR(100) COMMENT '安全码',
    ass_attribute VARCHAR(500) COMMENT '关联属性',
    project_ids JSON COMMENT '项目ID集合',
    createtime DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    createuserid VARCHAR(64) COMMENT '创建人ID',
    updatetime DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    updateuserid VARCHAR(64) COMMENT '更新人ID',
    status VARCHAR(20) DEFAULT '审核通过' COMMENT '数据状态',
    source VARCHAR(50) COMMENT '数据来源',
    app_id VARCHAR(64) COMMENT '应用渠道ID',
    _class VARCHAR(255) COMMENT '类名',
    INDEX idx_id (id),
    INDEX idx_createtime (createtime),
    INDEX idx_app_id (app_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户认证账号表';

-- 用户头衔类型表
CREATE TABLE IF NOT EXISTS v1_user_title_type (
    id VARCHAR(64) NOT NULL PRIMARY KEY COMMENT '主键ID',
    code INT COMMENT '编号（需要唯一）',
    name VARCHAR(255) COMMENT '头衔名称',
    name_url VARCHAR(500) COMMENT '名称url地址',
    border_url VARCHAR(500) COMMENT '头衔url地址',
    is_official INT DEFAULT 0 COMMENT '0：表示官方；1：其他（用戶）这个头衔只能官方授权还是用户自己可以设置，为以后扩充用',
    createtime DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    createuserid VARCHAR(64) COMMENT '创建人ID',
    updatetime DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    updateuserid VARCHAR(64) COMMENT '更新人ID',
    status VARCHAR(20) DEFAULT '审核通过' COMMENT '数据状态',
    source VARCHAR(50) COMMENT '数据来源',
    app_id VARCHAR(64) COMMENT '应用渠道ID',
    _class VARCHAR(255) COMMENT '类名',
    INDEX idx_id (id),
    INDEX idx_createtime (createtime),
    INDEX idx_app_id (app_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户头衔类型表';
