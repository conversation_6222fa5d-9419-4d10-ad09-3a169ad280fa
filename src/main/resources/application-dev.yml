# 最小化本地配置 - 只保留Nacos连接信息
# 所有业务配置都从Nacos获取

spring:
  application:
    name: SnapTag
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ***************************************************************************************************************************************************************************************
    username: xprinter_test
    password: Vida@2022
    hikari:
      maximum-pool-size: 100
      minimum-idle: 10
      connection-timeout: 60000
      idle-timeout: 300000
      max-lifetime: 1800000
# Nacos配置 - 覆盖本地配置
nacos:
  config:
    server-addr: ${NACOS_CONFIG_SERVER_ADDR:localhost:8848}
    data-id: ${NACOS_CONFIG_DATA_ID:SnapTag-dev.yml}
    group: DEFAULT_GROUP
    type: YAML
    auto-refresh: true
    namespace: ${NACOS_CONFIG_NAMESPACE:93d46b33-48c9-4d59-8204-147c093992b0}
    # 启用预加载配置，优先级高于本地配置
    bootstrap:
      enable: true
      log-enable: true
    # 启用远程配置优先，覆盖本地配置
    enable-remote-sync-config: true
    # 配置优先级设置
    override-none: false
    override-system-properties: false
