# 最小化本地配置 - 只保留Nacos连接信息
# 所有业务配置都从Nacos获取

spring:
  application:
    name: SnapTag

# Nacos配置 - 覆盖本地配置
nacos:
  config:
    server-addr: ${NACOS_CONFIG_SERVER_ADDR}
    data-id: ${NACOS_CONFIG_DATA_ID}
    group: DEFAULT_GROUP
    type: YAML
    auto-refresh: true
    namespace: ${NACOS_CONFIG_NAMESPACE}
    # 启用预加载配置，优先级高于本地配置
    bootstrap:
      enable: true
      log-enable: true
    # 启用远程配置优先，覆盖本地配置
    enable-remote-sync-config: true
    # 配置优先级设置
    override-none: false
    override-system-properties: false
