package net.snaptag.system.test.controller;

import net.snaptag.system.business.entity.Material;
import net.snaptag.system.business.dao.MaterialDao;
import net.snaptag.system.sadais.core.common.core.ToolsConst;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Material测试控制器
 * 用于测试Material实体的icon字段映射
 */
@RestController
@RequestMapping("/test/material")
public class MaterialTestController {

    @Autowired
    private MaterialDao materialDao;

    @Autowired
    private org.springframework.jdbc.core.JdbcTemplate jdbcTemplate;

    @Autowired
    private net.snaptag.system.business.dao.MaterialResourceDao materialResourceDao;

    /**
     * 测试获取Material的icon字段
     */
    @GetMapping("/icon/{id}")
    public Map<String, Object> testIconMapping(@PathVariable String id) {
        Map<String, Object> result = new HashMap<>();

        try {
            Material material = materialDao.getById(id);
            if (material != null) {
                result.put("success", true);
                result.put("id", material.getId());
                result.put("name", material.getName());
                result.put("icon", material.getIcon());
                result.put("iconType",
                        material.getIcon() != null ? material.getIcon().getClass().getSimpleName() : "null");
                result.put("iconLength", material.getIcon() != null ? material.getIcon().length() : 0);
                result.put("iconContent", material.getIcon());
            } else {
                result.put("success", false);
                result.put("message", "Material not found with id: " + id);
            }
        } catch (Exception e) {
            result.put("success", false);
            result.put("error", e.getMessage());
            result.put("errorType", e.getClass().getSimpleName());
            e.printStackTrace();
        }

        return result;
    }

    /**
     * 获取所有Material列表（限制前10条）
     */
    @GetMapping("/list")
    public Map<String, Object> getMaterialList() {
        Map<String, Object> result = new HashMap<>();

        try {
            List<Material> materials = materialDao.findMaterialList();
            result.put("success", true);
            result.put("totalCount", materials.size());

            // 只返回前10条记录的基本信息
            List<Map<String, Object>> materialList = new java.util.ArrayList<>();
            for (int i = 0; i < Math.min(10, materials.size()); i++) {
                Material material = materials.get(i);
                Map<String, Object> materialInfo = new HashMap<>();
                materialInfo.put("id", material.getId());
                materialInfo.put("name", material.getName());
                materialInfo.put("hasIcon", material.getIcon() != null && !material.getIcon().trim().isEmpty());
                materialInfo.put("iconLength", material.getIcon() != null ? material.getIcon().length() : 0);
                materialList.add(materialInfo);
            }
            result.put("materials", materialList);

        } catch (Exception e) {
            result.put("success", false);
            result.put("error", e.getMessage());
            result.put("errorType", e.getClass().getSimpleName());
            e.printStackTrace();
        }

        return result;
    }

    /**
     * 测试创建一个带icon的Material
     */
    @GetMapping("/test-create")
    public Map<String, Object> testCreateMaterial() {
        Map<String, Object> result = new HashMap<>();

        try {
            Material material = new Material();
            material.setName("Test Material Debug");
            material.setType(1);
            material.setSubType(1);
            material.setSort(999);

            // 创建icon JSON字符串
            String iconJson = "{\"60x60\": \"/api/img/test/test_icon.png\", \"120x120\": \"/api/img/test/test_icon_large.png\"}";
            material.setIcon(iconJson);

            // 调试信息：保存前的icon
            result.put("iconBeforeSave", material.getIcon());
            result.put("iconLengthBeforeSave", material.getIcon() != null ? material.getIcon().length() : 0);

            // 尝试保存
            boolean saveResult = materialDao.save(material);

            // 保存后立即查询验证
            Material savedMaterial = materialDao.getById(material.getId());

            result.put("success", true);
            result.put("saveResult", saveResult);
            result.put("materialId", material.getId());
            result.put("iconAfterSave", savedMaterial != null ? savedMaterial.getIcon() : null);
            result.put("iconLengthAfterSave",
                    savedMaterial != null && savedMaterial.getIcon() != null ? savedMaterial.getIcon().length() : 0);
            result.put("message", "Test material created and verified");

        } catch (Exception e) {
            result.put("success", false);
            result.put("error", e.getMessage());
            result.put("errorType", e.getClass().getSimpleName());
            result.put("stackTrace", java.util.Arrays.toString(e.getStackTrace()));
            e.printStackTrace();
        }

        return result;
    }

    /**
     * 直接查询数据库中的icon字段
     */
    @GetMapping("/db-icon/{id}")
    public Map<String, Object> testDatabaseIcon(@PathVariable String id) {
        Map<String, Object> result = new HashMap<>();

        try {
            // 直接查询数据库
            String sql = "SELECT id, name, icon FROM v1_material WHERE id = ?";
            List<Map<String, Object>> rows = jdbcTemplate.queryForList(sql, id);

            if (!rows.isEmpty()) {
                Map<String, Object> row = rows.get(0);
                result.put("success", true);
                result.put("id", row.get("id"));
                result.put("name", row.get("name"));
                result.put("iconRaw", row.get("icon"));
                result.put("iconType", row.get("icon") != null ? row.get("icon").getClass().getSimpleName() : "null");
                result.put("iconString", row.get("icon") != null ? row.get("icon").toString() : "null");
            } else {
                result.put("success", false);
                result.put("message", "Material not found in database with id: " + id);
            }

        } catch (Exception e) {
            result.put("success", false);
            result.put("error", e.getMessage());
            result.put("errorType", e.getClass().getSimpleName());
            e.printStackTrace();
        }

        return result;
    }

    /**
     * 测试MaterialResource的resMap字段映射
     */
    @GetMapping("/resource/test")
    public Map<String, Object> testMaterialResourceMapping() {
        Map<String, Object> result = new HashMap<>();

        try {
            // 查询第一个MaterialResource记录
            List<net.snaptag.system.business.entity.MaterialResource> resources = materialResourceDao.list();

            if (!resources.isEmpty()) {
                net.snaptag.system.business.entity.MaterialResource resource = resources.get(0);
                result.put("success", true);
                result.put("id", resource.getId());
                result.put("mId", resource.getMId());
                result.put("resMap", resource.getResMap());
                result.put("resMapType",
                        resource.getResMap() != null ? resource.getResMap().getClass().getSimpleName() : "null");
                result.put("resMapLength", resource.getResMap() != null ? resource.getResMap().length() : 0);
                result.put("resMapContent", resource.getResMap());

                result.put("totalResources", resources.size());
            } else {
                result.put("success", false);
                result.put("message", "No MaterialResource found");
            }

        } catch (Exception e) {
            result.put("success", false);
            result.put("error", e.getMessage());
            result.put("errorType", e.getClass().getSimpleName());
            result.put("stackTrace", java.util.Arrays.toString(e.getStackTrace()));
            e.printStackTrace();
        }

        return result;
    }

    /**
     * 直接查询数据库中的resMap字段
     */
    @GetMapping("/resource/db-resmap/{id}")
    public Map<String, Object> testDatabaseResMap(@PathVariable String id) {
        Map<String, Object> result = new HashMap<>();

        try {
            // 直接查询数据库
            String sql = "SELECT id, m_id, res_map FROM v1_material_resource WHERE id = ?";
            List<Map<String, Object>> rows = jdbcTemplate.queryForList(sql, id);

            if (!rows.isEmpty()) {
                Map<String, Object> row = rows.get(0);
                result.put("success", true);
                result.put("id", row.get("id"));
                result.put("mId", row.get("m_id"));
                result.put("resMapRaw", row.get("res_map"));
                result.put("resMapType",
                        row.get("res_map") != null ? row.get("res_map").getClass().getSimpleName() : "null");
                result.put("resMapString", row.get("res_map") != null ? row.get("res_map").toString() : "null");
            } else {
                result.put("success", false);
                result.put("message", "MaterialResource not found in database with id: " + id);
            }

        } catch (Exception e) {
            result.put("success", false);
            result.put("error", e.getMessage());
            result.put("errorType", e.getClass().getSimpleName());
            e.printStackTrace();
        }

        return result;
    }

    /**
     * 测试Material的position字段映射
     */
    @GetMapping("/position/test")
    public Map<String, Object> testMaterialPositionMapping() {
        Map<String, Object> result = new HashMap<>();

        try {
            // 查询有position数据的Material记录
            List<Material> materials = materialDao.list();

            Material materialWithPosition = null;
            for (Material material : materials) {
                if (material.getPosition() != null && !material.getPosition().trim().isEmpty()) {
                    materialWithPosition = material;
                    break;
                }
            }

            if (materialWithPosition != null) {
                result.put("success", true);
                result.put("id", materialWithPosition.getId());
                result.put("name", materialWithPosition.getName());
                result.put("position", materialWithPosition.getPosition());
                result.put("positionType",
                        materialWithPosition.getPosition() != null
                                ? materialWithPosition.getPosition().getClass().getSimpleName()
                                : "null");
                result.put("positionLength",
                        materialWithPosition.getPosition() != null ? materialWithPosition.getPosition().length() : 0);
                result.put("positionContent", materialWithPosition.getPosition());

                result.put("totalMaterials", materials.size());
            } else {
                result.put("success", false);
                result.put("message", "No Material found with position data");
                result.put("totalMaterials", materials.size());
            }

        } catch (Exception e) {
            result.put("success", false);
            result.put("error", e.getMessage());
            result.put("errorType", e.getClass().getSimpleName());
            result.put("stackTrace", java.util.Arrays.toString(e.getStackTrace()));
            e.printStackTrace();
        }

        return result;
    }

    /**
     * 直接查询数据库中的position字段
     */
    @GetMapping("/position/db/{id}")
    public Map<String, Object> testDatabasePosition(@PathVariable String id) {
        Map<String, Object> result = new HashMap<>();

        try {
            // 直接查询数据库
            String sql = "SELECT id, name, position FROM v1_material WHERE id = ?";
            List<Map<String, Object>> rows = jdbcTemplate.queryForList(sql, id);

            if (!rows.isEmpty()) {
                Map<String, Object> row = rows.get(0);
                result.put("success", true);
                result.put("id", row.get("id"));
                result.put("name", row.get("name"));
                result.put("positionRaw", row.get("position"));
                result.put("positionType",
                        row.get("position") != null ? row.get("position").getClass().getSimpleName() : "null");
                result.put("positionString", row.get("position") != null ? row.get("position").toString() : "null");
            } else {
                result.put("success", false);
                result.put("message", "Material not found in database with id: " + id);
            }

        } catch (Exception e) {
            result.put("success", false);
            result.put("error", e.getMessage());
            result.put("errorType", e.getClass().getSimpleName());
            e.printStackTrace();
        }

        return result;
    }

    /**
     * 创建一个包含position数据的测试Material
     */
    @GetMapping("/position/test-create")
    public Map<String, Object> testCreateMaterialWithPosition() {
        Map<String, Object> result = new HashMap<>();

        try {
            // 创建新的Material
            Material material = new Material();
            material.setName("Test Material With Position");
            material.setType(1);
            material.setStatus(ToolsConst.DATA_SUCCESS_STATUS);

            // 创建position JSON字符串
            String positionJson = "[\"top\", \"center\", \"bottom\"]";
            material.setPosition(positionJson);

            // 调试信息：保存前的position
            result.put("positionBeforeSave", material.getPosition());
            result.put("positionLengthBeforeSave",
                    material.getPosition() != null ? material.getPosition().length() : 0);

            // 保存到数据库
            boolean saved = materialDao.save(material);

            // 重新查询保存后的数据
            Material savedMaterial = materialDao.getById(material.getId());

            result.put("success", saved);
            result.put("materialId", material.getId());
            result.put("saved", saved);
            result.put("positionAfterSave", savedMaterial != null ? savedMaterial.getPosition() : null);
            result.put("positionLengthAfterSave",
                    savedMaterial != null && savedMaterial.getPosition() != null ? savedMaterial.getPosition().length()
                            : 0);

        } catch (Exception e) {
            result.put("success", false);
            result.put("error", e.getMessage());
            result.put("errorType", e.getClass().getSimpleName());
            result.put("stackTrace", java.util.Arrays.toString(e.getStackTrace()));
            e.printStackTrace();
        }

        return result;
    }

    /**
     * 测试token过期时的返回值
     * 这个接口需要token验证，可以用来测试token过期的情况
     */
    @GetMapping("/token/test-expire")
    public Map<String, Object> testTokenExpire() {
        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("message", "如果你看到这个消息，说明token是有效的");
        result.put("timestamp", System.currentTimeMillis());
        return result;
    }

    /**
     * 检查BaseEntity中status字段在数据库中的实际值
     */
    @GetMapping("/status/check")
    public Map<String, Object> checkStatusValues() {
        Map<String, Object> result = new HashMap<>();

        try {
            // 直接查询数据库中的status字段值
            String sql = "SELECT id, name, status FROM v1_material LIMIT 10";
            List<Map<String, Object>> rows = jdbcTemplate.queryForList(sql);

            result.put("success", true);
            result.put("totalRows", rows.size());
            result.put("statusValues", new ArrayList<>());

            Set<String> uniqueStatusValues = new HashSet<>();
            for (Map<String, Object> row : rows) {
                Map<String, Object> materialInfo = new HashMap<>();
                materialInfo.put("id", row.get("id"));
                materialInfo.put("name", row.get("name"));
                materialInfo.put("status", row.get("status"));
                materialInfo.put("statusType",
                        row.get("status") != null ? row.get("status").getClass().getSimpleName() : "null");

                ((List<Map<String, Object>>) result.get("statusValues")).add(materialInfo);

                if (row.get("status") != null) {
                    uniqueStatusValues.add(row.get("status").toString());
                }
            }

            result.put("uniqueStatusValues", uniqueStatusValues);

            // 查询所有不同的status值
            String distinctSql = "SELECT DISTINCT status FROM v1_material WHERE status IS NOT NULL";
            List<Map<String, Object>> distinctRows = jdbcTemplate.queryForList(distinctSql);

            Set<String> allStatusValues = new HashSet<>();
            for (Map<String, Object> row : distinctRows) {
                if (row.get("status") != null) {
                    allStatusValues.add(row.get("status").toString());
                }
            }

            result.put("allDistinctStatusValues", allStatusValues);

        } catch (Exception e) {
            result.put("success", false);
            result.put("error", e.getMessage());
            result.put("errorType", e.getClass().getSimpleName());
            e.printStackTrace();
        }

        return result;
    }

    /**
     * 删除测试时创建的Material数据
     */
    @GetMapping("/cleanup/test-data")
    public Map<String, Object> cleanupTestData() {
        Map<String, Object> result = new HashMap<>();

        try {
            // 删除测试时创建的Material记录（名称包含"Test Material"的记录）
            String deleteSql = "DELETE FROM v1_material WHERE name LIKE '%Test Material%'";
            int deletedCount = jdbcTemplate.update(deleteSql);

            result.put("success", true);
            result.put("deletedCount", deletedCount);
            result.put("message", "已删除 " + deletedCount + " 条测试数据");

        } catch (Exception e) {
            result.put("success", false);
            result.put("error", e.getMessage());
            result.put("errorType", e.getClass().getSimpleName());
            e.printStackTrace();
        }

        return result;
    }

    /**
     * 检查所有实体中的JSON字段映射情况
     */
    @GetMapping("/json-fields/check-all")
    public Map<String, Object> checkAllJsonFields() {
        Map<String, Object> result = new HashMap<>();

        try {
            List<Map<String, Object>> entityChecks = new ArrayList<>();

            // 检查Banner.pics字段
            entityChecks.add(checkBannerPics());

            // 检查SystemMsg.picMap字段
            entityChecks.add(checkSystemMsgPicMap());

            // 检查ResourceData.resUrl字段
            entityChecks.add(checkResourceDataResUrl());

            // 检查UserAccount.bindName字段
            entityChecks.add(checkUserAccountBindName());

            // 检查UserAuthAccount.projectIds字段
            entityChecks.add(checkUserAuthAccountProjectIds());

            // 检查UserThirdPartyAuth.wechatOpenId字段
            entityChecks.add(checkUserThirdPartyAuthWechatOpenId());

            // 检查Drafts.draftsParam字段
            entityChecks.add(checkDraftsDraftsParam());

            // 检查TemplateDrafts.draftsDto字段
            entityChecks.add(checkTemplateDraftsDraftsDto());

            // 检查MsgCenter.param字段
            entityChecks.add(checkMsgCenterParam());

            // 检查UserFeedback.images字段
            entityChecks.add(checkUserFeedbackImages());

            // 检查WebPrint.pageList字段
            entityChecks.add(checkWebPrintPageList());

            result.put("success", true);
            result.put("totalChecks", entityChecks.size());
            result.put("entityChecks", entityChecks);

            // 统计有问题的字段
            long problemCount = entityChecks.stream()
                    .mapToLong(check -> (Boolean) check.get("hasMapping") ? 0 : 1)
                    .sum();

            result.put("problemCount", problemCount);
            result.put("needsFix", problemCount > 0);

        } catch (Exception e) {
            result.put("success", false);
            result.put("error", e.getMessage());
            result.put("errorType", e.getClass().getSimpleName());
            e.printStackTrace();
        }

        return result;
    }

    private Map<String, Object> checkBannerPics() {
        Map<String, Object> check = new HashMap<>();
        check.put("entity", "Banner");
        check.put("field", "pics");
        check.put("type", "Map<String, String>");

        try {
            String sql = "SELECT id, name, pics FROM v1_banner LIMIT 1";
            List<Map<String, Object>> rows = jdbcTemplate.queryForList(sql);

            if (!rows.isEmpty()) {
                Object pics = rows.get(0).get("pics");
                check.put("hasData", pics != null);
                check.put("dbValue", pics);
                check.put("dbType", pics != null ? pics.getClass().getSimpleName() : "null");
                check.put("hasMapping", true); // 需要实际测试MyBatis-Plus映射
            } else {
                check.put("hasData", false);
                check.put("hasMapping", false);
            }
        } catch (Exception e) {
            check.put("hasData", false);
            check.put("hasMapping", false);
            check.put("error", e.getMessage());
        }

        return check;
    }

    private Map<String, Object> checkSystemMsgPicMap() {
        Map<String, Object> check = new HashMap<>();
        check.put("entity", "SystemMsg");
        check.put("field", "picMap");
        check.put("type", "Map<String, String>");

        try {
            String sql = "SELECT id, msg_title, pic_map FROM v1_system_msg LIMIT 1";
            List<Map<String, Object>> rows = jdbcTemplate.queryForList(sql);

            if (!rows.isEmpty()) {
                Object picMap = rows.get(0).get("pic_map");
                check.put("hasData", picMap != null);
                check.put("dbValue", picMap);
                check.put("dbType", picMap != null ? picMap.getClass().getSimpleName() : "null");
                check.put("hasMapping", true);
            } else {
                check.put("hasData", false);
                check.put("hasMapping", false);
            }
        } catch (Exception e) {
            check.put("hasData", false);
            check.put("hasMapping", false);
            check.put("error", e.getMessage());
        }

        return check;
    }

    private Map<String, Object> checkResourceDataResUrl() {
        Map<String, Object> check = new HashMap<>();
        check.put("entity", "ResourceData");
        check.put("field", "resUrl");
        check.put("type", "PicVo");

        try {
            String sql = "SELECT id, user_id, res_url FROM v1_resource_data LIMIT 1";
            List<Map<String, Object>> rows = jdbcTemplate.queryForList(sql);

            if (!rows.isEmpty()) {
                Object resUrl = rows.get(0).get("res_url");
                check.put("hasData", resUrl != null);
                check.put("dbValue", resUrl);
                check.put("dbType", resUrl != null ? resUrl.getClass().getSimpleName() : "null");
                check.put("hasMapping", true);
            } else {
                check.put("hasData", false);
                check.put("hasMapping", false);
            }
        } catch (Exception e) {
            check.put("hasData", false);
            check.put("hasMapping", false);
            check.put("error", e.getMessage());
        }

        return check;
    }

    private Map<String, Object> checkUserAccountBindName() {
        Map<String, Object> check = new HashMap<>();
        check.put("entity", "UserAccount");
        check.put("field", "bindName");
        check.put("type", "Map<String, String>");

        try {
            String sql = "SELECT id, mobile_account, bind_name FROM v1_user_account LIMIT 1";
            List<Map<String, Object>> rows = jdbcTemplate.queryForList(sql);

            if (!rows.isEmpty()) {
                Object bindName = rows.get(0).get("bind_name");
                check.put("hasData", bindName != null);
                check.put("dbValue", bindName);
                check.put("dbType", bindName != null ? bindName.getClass().getSimpleName() : "null");
                check.put("hasMapping", true);
            } else {
                check.put("hasData", false);
                check.put("hasMapping", false);
            }
        } catch (Exception e) {
            check.put("hasData", false);
            check.put("hasMapping", false);
            check.put("error", e.getMessage());
        }

        return check;
    }

    private Map<String, Object> checkUserAuthAccountProjectIds() {
        Map<String, Object> check = new HashMap<>();
        check.put("entity", "UserAuthAccount");
        check.put("field", "projectIds");
        check.put("type", "List<String>");

        try {
            String sql = "SELECT id, user_id, project_ids FROM v1_user_auth_account LIMIT 1";
            List<Map<String, Object>> rows = jdbcTemplate.queryForList(sql);

            if (!rows.isEmpty()) {
                Object projectIds = rows.get(0).get("project_ids");
                check.put("hasData", projectIds != null);
                check.put("dbValue", projectIds);
                check.put("dbType", projectIds != null ? projectIds.getClass().getSimpleName() : "null");
                check.put("hasMapping", true);
            } else {
                check.put("hasData", false);
                check.put("hasMapping", false);
            }
        } catch (Exception e) {
            check.put("hasData", false);
            check.put("hasMapping", false);
            check.put("error", e.getMessage());
        }

        return check;
    }

    private Map<String, Object> checkUserThirdPartyAuthWechatOpenId() {
        Map<String, Object> check = new HashMap<>();
        check.put("entity", "UserThirdPartyAuth");
        check.put("field", "wechatOpenId");
        check.put("type", "Map<String, String>");

        try {
            String sql = "SELECT id, user_id, wechat_open_id FROM v1_user_third_party_auth LIMIT 1";
            List<Map<String, Object>> rows = jdbcTemplate.queryForList(sql);

            if (!rows.isEmpty()) {
                Object wechatOpenId = rows.get(0).get("wechat_open_id");
                check.put("hasData", wechatOpenId != null);
                check.put("dbValue", wechatOpenId);
                check.put("dbType", wechatOpenId != null ? wechatOpenId.getClass().getSimpleName() : "null");
                check.put("hasMapping", true);
            } else {
                check.put("hasData", false);
                check.put("hasMapping", false);
            }
        } catch (Exception e) {
            check.put("hasData", false);
            check.put("hasMapping", false);
            check.put("error", e.getMessage());
        }

        return check;
    }

    private Map<String, Object> checkDraftsDraftsParam() {
        Map<String, Object> check = new HashMap<>();
        check.put("entity", "Drafts");
        check.put("field", "draftsParam");
        check.put("type", "DraftsParamVo");

        try {
            String sql = "SELECT id, user_id, drafts_param FROM v1_drafts LIMIT 1";
            List<Map<String, Object>> rows = jdbcTemplate.queryForList(sql);

            if (!rows.isEmpty()) {
                Object draftsParam = rows.get(0).get("drafts_param");
                check.put("hasData", draftsParam != null);
                check.put("dbValue", draftsParam);
                check.put("dbType", draftsParam != null ? draftsParam.getClass().getSimpleName() : "null");
                check.put("hasMapping", true);
            } else {
                check.put("hasData", false);
                check.put("hasMapping", false);
            }
        } catch (Exception e) {
            check.put("hasData", false);
            check.put("hasMapping", false);
            check.put("error", e.getMessage());
        }

        return check;
    }

    private Map<String, Object> checkTemplateDraftsDraftsDto() {
        Map<String, Object> check = new HashMap<>();
        check.put("entity", "TemplateDrafts");
        check.put("field", "draftsDto");
        check.put("type", "DraftsDto");

        try {
            String sql = "SELECT id, name, drafts_dto FROM v1_template_drafts LIMIT 1";
            List<Map<String, Object>> rows = jdbcTemplate.queryForList(sql);

            if (!rows.isEmpty()) {
                Object draftsDto = rows.get(0).get("drafts_dto");
                check.put("hasData", draftsDto != null);
                check.put("dbValue", draftsDto);
                check.put("dbType", draftsDto != null ? draftsDto.getClass().getSimpleName() : "null");
                check.put("hasMapping", true);
            } else {
                check.put("hasData", false);
                check.put("hasMapping", false);
            }
        } catch (Exception e) {
            check.put("hasData", false);
            check.put("hasMapping", false);
            check.put("error", e.getMessage());
        }

        return check;
    }

    private Map<String, Object> checkMsgCenterParam() {
        Map<String, Object> check = new HashMap<>();
        check.put("entity", "MsgCenter");
        check.put("field", "param");
        check.put("type", "MsgCenterParamVo");

        try {
            String sql = "SELECT id, user_id, param FROM v1_msg_center LIMIT 1";
            List<Map<String, Object>> rows = jdbcTemplate.queryForList(sql);

            if (!rows.isEmpty()) {
                Object param = rows.get(0).get("param");
                check.put("hasData", param != null);
                check.put("dbValue", param);
                check.put("dbType", param != null ? param.getClass().getSimpleName() : "null");
                check.put("hasMapping", true);
            } else {
                check.put("hasData", false);
                check.put("hasMapping", false);
            }
        } catch (Exception e) {
            check.put("hasData", false);
            check.put("hasMapping", false);
            check.put("error", e.getMessage());
        }

        return check;
    }

    private Map<String, Object> checkUserFeedbackImages() {
        Map<String, Object> check = new HashMap<>();
        check.put("entity", "UserFeedback");
        check.put("field", "images");
        check.put("type", "List<String>");

        try {
            String sql = "SELECT id, user_id, images FROM v1_user_feedback LIMIT 1";
            List<Map<String, Object>> rows = jdbcTemplate.queryForList(sql);

            if (!rows.isEmpty()) {
                Object images = rows.get(0).get("images");
                check.put("hasData", images != null);
                check.put("dbValue", images);
                check.put("dbType", images != null ? images.getClass().getSimpleName() : "null");
                check.put("hasMapping", true);
            } else {
                check.put("hasData", false);
                check.put("hasMapping", false);
            }
        } catch (Exception e) {
            check.put("hasData", false);
            check.put("hasMapping", false);
            check.put("error", e.getMessage());
        }

        return check;
    }

    private Map<String, Object> checkWebPrintPageList() {
        Map<String, Object> check = new HashMap<>();
        check.put("entity", "WebPrint");
        check.put("field", "pageList");
        check.put("type", "List<WebPagePrintVo>");

        try {
            String sql = "SELECT id, user_id, page_list FROM v1_web_print LIMIT 1";
            List<Map<String, Object>> rows = jdbcTemplate.queryForList(sql);

            if (!rows.isEmpty()) {
                Object pageList = rows.get(0).get("page_list");
                check.put("hasData", pageList != null);
                check.put("dbValue", pageList);
                check.put("dbType", pageList != null ? pageList.getClass().getSimpleName() : "null");
                check.put("hasMapping", true);
            } else {
                check.put("hasData", false);
                check.put("hasMapping", false);
            }
        } catch (Exception e) {
            check.put("hasData", false);
            check.put("hasMapping", false);
            check.put("error", e.getMessage());
        }

        return check;
    }
}
