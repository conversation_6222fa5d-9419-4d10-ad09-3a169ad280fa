package net.snaptag.system.account.utils;

import org.apache.http.HttpEntity;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.CookieStore;
import org.apache.http.client.config.CookieSpecs;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.protocol.HttpClientContext;
import org.apache.http.impl.client.BasicCookieStore;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;

import java.io.IOException;
import java.util.Map;

public class HttpClientUtil {

    public static String doGet(String url) {
        return doGet(url, null);
    }

    public static String doGet(String url, Map<String, String> headers) {
        CloseableHttpClient httpClient = null;
        CloseableHttpResponse response = null;
        String result = "";
        try {
            // 创建cookie store的本地实例
            CookieStore cookieStore = new BasicCookieStore();
            // 创建HttpClient上下文
            HttpClientContext.create().setCookieStore(cookieStore);
            // 通过址默认配置创建一个httpClient实例
            // httpClient = HttpClients.createDefault();
            httpClient = HttpClients.custom().setDefaultCookieStore(cookieStore).build();

            // 创建httpGet远程连接实例
            HttpGet httpGet = new HttpGet(url);
            if (headers != null && headers.size() > 0) {
                for (Map.Entry<String, String> entry : headers.entrySet()) {
                    httpGet.addHeader(entry.getKey(), entry.getValue());
                }
            }
            // 设置配置请求参数
            // 连接主机服务超时时间、请求超时时间、数据读取超时时间
            RequestConfig requestConfig = RequestConfig.custom()
                    .setCookieSpec(CookieSpecs.STANDARD)
                    .setConnectTimeout(50000).setConnectionRequestTimeout(50000).setSocketTimeout(10000)
                    .build();
            // 为httpGet实例设置配置
            httpGet.setConfig(requestConfig);
            // 执行get请求得到返回对象
            response = httpClient.execute(httpGet);

            // 获取常用Cookie,包括_xsrf信息,放在发送请求之后
//            System.out.println("访问知乎首页后的获取的常规Cookie:===============");
//            for (Cookie c : cookieStore.getCookies()) {
//                System.out.println(c.getName() + ": " + c.getValue());
//            }
            // 通过返回对象获取返回数据
            HttpEntity entity = response.getEntity();
            // 通过EntityUtils中的toString方法将结果转换为字符串, GBK解决中文乱码
            result = org.apache.http.util.EntityUtils.toString(entity, "utf-8");
        } catch (ClientProtocolException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            // 关闭资源
            if (null != response) {
                try {
                    response.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            if (null != httpClient) {
                try {
                    httpClient.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return result;
    }

}
