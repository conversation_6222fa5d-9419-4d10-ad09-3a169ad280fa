package net.snaptag.system.account.enums;

import net.snaptag.system.sadais.core.common.enums.ICacheEnums;

import java.util.LinkedHashMap;
public enum UserDeviceInfoCacheEnums implements ICacheEnums {
    USER_DEVICE_INFO_BY_USERID("pc:account:ude:by:uid:", "用户设备信息对象");
    private final String                               key;
    private final String                               desc;
    private static final LinkedHashMap<String, String> map;
    static {
        map = new LinkedHashMap<String, String>();
        for (UserDeviceInfoCacheEnums userDeviceInfoCacheEnums : UserDeviceInfoCacheEnums.values()) {
            map.put(userDeviceInfoCacheEnums.getKey(), userDeviceInfoCacheEnums.getDesc());
        } 
    } 

    private UserDeviceInfoCacheEnums(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public String getKey() {
        return key;
    }

    public String getDesc() {
        return desc;
    }

    public static LinkedHashMap<String, String> getMap() {
        return map;
    }
}
