package net.snaptag.system.account.enums;

import net.snaptag.system.sadais.core.common.enums.ICacheEnums;

import java.util.LinkedHashMap;
public enum UserThirdPartyAuthCacheEnums implements ICacheEnums {
    USER_THIRD_PARTY_AUTH_USER_ID("pc:account:utpa:by:userid:", "用户第三方授权信息缓存"),
    USER_THIRD_PARTY_AUTH_UNION_ID("pc:account:utpa:by:unionid:", "用户第三方授权信息缓存");
    private final String                               key;
    private final String                               desc;
    private static final LinkedHashMap<String, String> map;
    static {
        map = new LinkedHashMap<String, String>();
        for (UserThirdPartyAuthCacheEnums userThirdPartyAuthCacheEnums : UserThirdPartyAuthCacheEnums.values()) {
            map.put(userThirdPartyAuthCacheEnums.getKey(), userThirdPartyAuthCacheEnums.getDesc());
        }
    }

    private UserThirdPartyAuthCacheEnums(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public String getKey() {
        return key;
    }

    public String getDesc() {
        return desc;
    }

    public static LinkedHashMap<String, String> getMap() {
        return map;
    }
}
