package net.snaptag.system.account.enums;

import java.util.LinkedHashMap;

public enum UserPublishEnums {
    USER_TITLE_TYPE_UPDATE_PUB_SUB("pc:user:title:type:update:pub:sub", "用户头衔更新广播");
    private final String                               key;
    private final String                               desc;
    private static final LinkedHashMap<String, String> map;

    static {
        map = new LinkedHashMap<String, String>();
        for (UserPublishEnums userPublishEnums : UserPublishEnums.values()) {
            map.put(userPublishEnums.getKey(), userPublishEnums.getDesc());
        }
    }

    private UserPublishEnums(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public String getKey() {
        return key;
    }

    public String getDesc() {
        return desc;
    }

    public static LinkedHashMap<String, String> getMap() {
        return map;
    }
}
