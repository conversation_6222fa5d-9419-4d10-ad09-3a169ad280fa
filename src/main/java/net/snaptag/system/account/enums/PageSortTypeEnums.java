package net.snaptag.system.account.enums;
import java.util.LinkedHashMap;
/**
 * 
 *
 */
public enum PageSortTypeEnums {
    //查询key，描述,UserBodayFatData 字段key，BodyFatRecordLog 字段key
    TIME("time", "时间","createtime","createtime"), 
    AGE("age", "年龄","birthday","birthday"), 
    WEIGHT("weight", "体重","currentWeight","currentWeight");
    private final String                                          key;
    private final String                                          desc;
    private final String                                          attribute;
    private final String                                          alias;
    private static final LinkedHashMap<String, PageSortTypeEnums> map;
    static {
        map = new LinkedHashMap<>();
        for (PageSortTypeEnums pageSortTypeEnums : PageSortTypeEnums.values()) {
            map.put(pageSortTypeEnums.getKey(), pageSortTypeEnums);
        }
    }

    PageSortTypeEnums(String key, String desc,String attribute,String alias) {
        this.key = key;
        this.desc = desc;
        this.attribute=attribute;
        this.alias=alias;
    }

    public String getKey() {
        return key;
    }

    public String getDesc() {
        return desc;
    }

    public String getAttribute() {
        return attribute;
    }
    
    public String getAlias() {
        return alias;
    }
    
    public static LinkedHashMap<String, PageSortTypeEnums> getMap() {
        return map;
    }
}
