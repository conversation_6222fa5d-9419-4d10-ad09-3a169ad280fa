package net.snaptag.system.account.enums;

import net.snaptag.system.sadais.core.common.enums.ICacheEnums;

import java.util.LinkedHashMap;
public enum UserInfoCacheEnums implements ICacheEnums {
    USER_INFO_BY_USERID("pc:account:ui:by:uid:", "用户信息对象"),
    USER_INFO_BY_ID("pc:account:ui:by:id:", "用户信息对象"),
    USER_CODEID_TO_USERID("pc:account:cid:to:uid:", "codeid关联userid");
    private final String                               key;
    private final String                               desc;
    private static final LinkedHashMap<String, String> map;
    static {
        map = new LinkedHashMap<String, String>();
        for (UserInfoCacheEnums userInfoCacheEnums : UserInfoCacheEnums.values()) {
            map.put(userInfoCacheEnums.getKey(), userInfoCacheEnums.getDesc());
        } 
    }

    private UserInfoCacheEnums(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public String getKey() {
        return key;
    }

    public String getDesc() {
        return desc;
    }

    public static LinkedHashMap<String, String> getMap() {
        return map;
    }
}
