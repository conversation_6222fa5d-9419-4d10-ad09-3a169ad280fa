package net.snaptag.system.account.enums;

import java.util.HashMap;
import java.util.Map;

public enum UserRoleEnums {
    STUDENT(1, "学生"),
    TEACHER(2, "老师"),
    PARENT(3, "家长"),
    OTHER(4, "其他");
    public Integer value;
    public String desc;

    UserRoleEnums(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public Integer getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    /**
     * 根据from获取微信的openId 类型
     *
     * @param from
     *            来源
     * @return 类型
     */
    public static String getByFrom(Integer from) {
        if (from != null) {
            for (UserRoleEnums objEnums : UserRoleEnums.values()) {
                if (objEnums.value.intValue()==from) {
                    return objEnums.getDesc();
                }
            }
        }
        return "";
    }

    public static Map<String, Object> getMapByValue(Integer id) {
        if ("".equals(UserRoleEnums.getByFrom(id))){
            return null;
        } else {
            Map<String, Object> map = new HashMap<>();
            map.put("id", id);
            map.put("name", UserRoleEnums.getByFrom(id));
            return map;
        }
    }
}
