package net.snaptag.system.account.controller.user;

import com.vida.sls.annotation.SLSLog;
import com.vida.sls.enums.OperationType;
import net.snaptag.system.account.buservice.UserAccountBuService;
import net.snaptag.system.account.utils.Constant;
import net.snaptag.system.sadais.core.common.enums.ExceptionEnums;
import net.snaptag.system.sadais.core.common.exception.ServiceException;
import net.snaptag.system.sadais.web.core.BaseController;
import net.snaptag.system.sadais.web.dto.ReturnDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
/**
 * 统计任务统计模块
 */
@RestController
@RequestMapping(Constant.PRE_MAPPING_URL + "/analysis")
public class AnalysisController extends BaseController {
    @Autowired
    private UserAccountBuService userAccountBuService;

    /**
     * 用户注册数
     * 
     * @return
     */
    @RequestMapping(value = "/getaccountcount")
    @SLSLog(value = "用户注册数", configKey = "business", businessType = "统计模块", operation = OperationType.SELECT)
    public ReturnDto getAccountCount() {
        try {
            String startDate = this.getValue("startdate");
            String endDate = this.getValue("enddate");
            // return this.returnSuccessJson(ExceptionEnums.SUCCESS, userAccountBuService.getAnalysisDtoList("用户注册数", startDate, endDate));
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        } catch (Exception e) {
            return this.returnFailJson(e);
        }
        return this.returnSuccessJson(ExceptionEnums.SUCCESS, "");
    }
}
