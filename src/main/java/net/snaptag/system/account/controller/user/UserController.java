package net.snaptag.system.account.controller.user;

import com.vida.sls.annotation.SLSLog;
import com.vida.sls.enums.OperationType;
import net.snaptag.system.account.buservice.*;
import net.snaptag.system.account.dto.UserThirdPartyAuthDto;
import net.snaptag.system.account.entity.UserThirdPartyAuth;
import net.snaptag.system.account.utils.Constant;
import net.snaptag.system.sadais.core.common.core.ToolsConst;
import net.snaptag.system.sadais.core.common.enums.ExceptionEnums;
import net.snaptag.system.sadais.core.common.exception.ServiceException;
import net.snaptag.system.sadais.util.core.ToolsKit;
import net.snaptag.system.sadais.web.core.BaseController;
import net.snaptag.system.sadais.web.dto.HeadInfoDto;
import net.snaptag.system.sadais.web.dto.ReturnDto;
import net.snaptag.system.sadais.web.utils.WebTools;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Locale;

/**
 * 用户信息模块
 */
@RestController
@RequestMapping(Constant.PRE_MAPPING_URL + "/user")
public class UserController extends BaseController {
    @Autowired
    private UserInfoBuService userInfoBuService;
    @Autowired
    private UserAccountBuService accountBuService;

    @Autowired
    private UserAccountAndInfoBuService userAccountAndInfoBuService;

    @Autowired
    private UserLoginHistoryBuService userLoginHistoryBuService;
    @Autowired
    private UserThirdPartyAuthBuService userThirdPartyAuthBuService;
    @Autowired
    private UserAccountBuService userAccountBuService;

    /**
     * 更新用户信息
     *
     * @return
     */
    @RequestMapping(value = "/updateuserinfo")
    @SLSLog(value = "更新用户信息", configKey = "business", businessType = "用户信息模块", operation = OperationType.UPDATE)
    public ReturnDto updateUserInfo() {
        try {
            HeadInfoDto headInfoDto = this.getHeadInfoDto();
            Locale locale = WebTools.getLocaleByLanguage(headInfoDto.getLanguage());
            String userId = this.getValue("userId");
            String height = this.getValue("height");
            String nickName = this.getValue("nickName");
            String birthday = this.getValue("birthDay");
            String currentWeight = this.getValue("currentWeight");
            String targetWeight = this.getValue("targetWeight");
            String sex = this.getValue("sex");
            String customerPic = this.getValue("pic");
            String role = this.getValue("role");
            String gradeLevel = this.getValue("gradeLevel");
            String userTitleType = this.getValue("userTitleType");
            String isForbidden = this.getValue("isForbidden");
            userInfoBuService.updateUserInfo(userId, nickName, sex, customerPic, birthday, currentWeight, targetWeight, height, locale, gradeLevel, role, userTitleType, isForbidden);
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, null);
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    @RequestMapping(value = "/connectDevice")
    @SLSLog(value = "更新用户连接设备", configKey = "business", businessType = "用户信息模块", operation = OperationType.UPDATE)
    public ReturnDto connectDevice() {
        try {
            String userId = this.getValue("userid");
            String deviceType = this.getValue("printerType");
            userLoginHistoryBuService.updateUsedDevice(userId, deviceType);
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, "");
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    /**
     * 更新用户信息
     *
     * @return
     */
    @RequestMapping(value = "/updateusertitletype")
    @SLSLog(value = "更新用户信息", configKey = "business", businessType = "用户信息模块", operation = OperationType.UPDATE)
    public ReturnDto updateUserTitleType() {
        try {
            HeadInfoDto headInfoDto = this.getHeadInfoDto();
            Locale locale = WebTools.getLocaleByLanguage(headInfoDto.getLanguage());
            String userId = this.getValue("id");
            String userTitleType = this.getValue("userTitleType");
            userInfoBuService.updateUserInfo(userId, null, null, null, null, null, null, null, locale, null, null, userTitleType, null);
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, null);
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    /**
     * 获取用户信息
     *
     * @return
     */
    @RequestMapping(value = "/getuserinfo")
    @SLSLog(value = "获取用户信息", configKey = "business", businessType = "用户信息模块", operation = OperationType.SELECT)
    public ReturnDto getUserInfo() {
        try {
            HeadInfoDto headInfoDto = this.getHeadInfoDto();
            Locale locale = WebTools.getLocaleByLanguage(headInfoDto.getLanguage());
            String userId = this.getValue("userid");
            String otherId = this.getValue("otherid");
            if (ToolsKit.isNotEmpty(otherId)) {// 如果是查看别人的空间
                userId = this.getValue("otherid");
                otherId = this.getValue("userid");
            }
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, userAccountAndInfoBuService.getLoginInfo(userId, otherId, locale));
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    /**
     * 获取用户信息
     *
     * @return
     */
    @RequestMapping(value = "/getuserinfobycodeid")
    @SLSLog(value = "获取用户信息", configKey = "business", businessType = "用户信息模块", operation = OperationType.SELECT)
    public ReturnDto getUserInfoByCodeId() {
        try {
            HeadInfoDto headInfoDto = this.getHeadInfoDto();
            Locale locale = WebTools.getLocaleByLanguage(headInfoDto.getLanguage());
            String codeId = this.getValue("codeid");
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, userAccountAndInfoBuService.getLoginInfoByCodeId(codeId, locale));
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    /**
     * 获取第三方授权信息
     *
     * @return
     */
    @RequestMapping(value = "/getuserthirdpartyauth")
    @SLSLog(value = "获取第三方授权信息", configKey = "business", businessType = "用户信息模块", operation = OperationType.SELECT)
    public ReturnDto getUserThirdPartyAuth() {
        try {
            HeadInfoDto headInfoDto = this.getHeadInfoDto();
            Locale locale = WebTools.getLocaleByLanguage(headInfoDto.getLanguage());
            String userId = this.getValue("userid");
            UserThirdPartyAuth userThirdPartyAuth = userThirdPartyAuthBuService.getUserThirdPartyAuthByUserId(userId, locale);
            UserThirdPartyAuthDto dto = null;
            if (ToolsKit.isNotEmpty(userThirdPartyAuth)) {
                dto = new UserThirdPartyAuthDto();
                ToolsKit.Bean.copyProperties(userThirdPartyAuth, dto);
            }
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, dto);
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    /**
     * 查询出用户信息记录
     *
     * @return
     */
    @RequestMapping(value = "/finduserinfopage")
    @SLSLog(value = "查询出用户信息记录", configKey = "business", businessType = "用户信息模块", operation = OperationType.SELECT)
    public ReturnDto findUserInfoPage() {
        try {
            HeadInfoDto headInfoDto = this.getHeadInfoDto();
            Locale locale = WebTools.getLocaleByLanguage(headInfoDto.getLanguage());
            String startDate = this.getValue("startDate");
            String endDate = this.getValue("endDate");
            String pageNo = this.getValue("pageNo");
            String pageSize = this.getValue("pageSize");
            String keyword = this.getValue("keyword");
            String mobile = this.getValue("mobile");
            String latestLoginTimeBegin = this.getValue("latestLoginTimeBegin");
            String latestLoginTimeEnd = this.getValue("latestLoginTimeEnd");
            if (ToolsKit.isEmpty(pageNo)) {
                pageNo = "1";
            }
            if (ToolsKit.isEmpty(pageSize)) {
                pageSize = String.valueOf(ToolsConst.PHONEPAGESIZE);
            }

            return this.returnSuccessJson(ExceptionEnums.SUCCESS, userAccountAndInfoBuService.findUserInfoPage2(keyword, mobile, startDate, endDate, latestLoginTimeBegin, latestLoginTimeEnd,
                    Integer.parseInt(pageNo), Integer.parseInt(pageSize), locale));

        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    /**
     * 查询出用户信息记录
     *
     * @return
     */
    @RequestMapping(value = "/finduserinfopageBykeyword")
    @SLSLog(value = "查询出用户信息记录(关键词）", configKey = "business", businessType = "用户信息模块", operation = OperationType.SELECT)
    public ReturnDto findUserInfoPageByKeyword() {
        try {
            HeadInfoDto headInfoDto = this.getHeadInfoDto();
            String pageNo = this.getValue("pageNo");
            String pageSize = this.getValue("pageSize");
            String keyword = this.getValue("keyword");
            if (ToolsKit.isEmpty(pageNo)) {
                pageNo = "1";
            }
            if (ToolsKit.isEmpty(pageSize)) {
                pageSize = String.valueOf(ToolsConst.PHONEPAGESIZE);
            }

            return this.returnSuccessJson(ExceptionEnums.SUCCESS, userAccountAndInfoBuService.findUserInfoPageByKeyword(keyword,
                    Integer.parseInt(pageNo), Integer.parseInt(pageSize)));

        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    //------------------------------------------------------

    @RequestMapping(value = "/removeUsers")
    @SLSLog(value = "删除用户信息", configKey = "business", businessType = "用户信息模块", operation = OperationType.DELETE)
    public ReturnDto removeUsers() throws Exception {
        try {
            String userMobiles = this.getValue("mobiles");
            String loginway = this.getValue("loginway");
            String unionid = this.getValue("unionid");
            accountBuService.removeUsers(userMobiles, loginway, unionid);
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, 0);
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    @RequestMapping(value = "/clearUsersCaches")
    @SLSLog(value = "清除用户缓存", configKey = "business", businessType = "用户信息模块", operation = OperationType.DELETE)
    public ReturnDto clearUsersCaches() throws Exception {
        try {
//            String userMobiles = this.getValue("mobiles");
//            String loginway = this.getValue("loginway");
//            String unionid = this.getValue("unionid");
            String userId = this.getValue("userId");
            userAccountAndInfoBuService.clearUsersCachesByUserId(userId);
//            accountBuService.clearUsersCaches(userMobiles, loginway, unionid);
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, 0);
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    @RequestMapping(value = "/removeUsersByMail")
    @SLSLog(value = "删除用户信息", configKey = "business", businessType = "用户信息模块", operation = OperationType.DELETE)
    public ReturnDto removeUsersByMail() throws Exception {
        try {
            String userMobiles = this.getValue("mobiles");
            accountBuService.removeUsersByMail(userMobiles);
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, 0);
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    @RequestMapping(value = "/clearUsersCachesByMail")
    @SLSLog(value = "删除用户信息", configKey = "business", businessType = "用户信息模块", operation = OperationType.DELETE)
    public ReturnDto clearUsersCachesByMail() throws Exception {
        try {
            String userMobiles = this.getValue("mobiles");
            accountBuService.clearUsersCachesByMail(userMobiles);
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, 0);
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    @RequestMapping(value = "/getUserIds")
    @SLSLog(value = "获取用户信息", configKey = "business", businessType = "用户信息模块", operation = OperationType.SELECT)
    public ReturnDto getUserIds() throws Exception {
        try {
            String userMobiles = this.getValue("mobiles");
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, accountBuService.getUserIds(userMobiles));
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

}
