package net.snaptag.system.account.controller.auth;


import com.vida.sls.annotation.SLSLog;
import com.vida.sls.enums.OperationType;
import net.snaptag.system.account.buservice.RoleTableBuService;
import net.snaptag.system.account.utils.Constant;
import net.snaptag.system.sadais.core.common.core.ToolsConst;
import net.snaptag.system.sadais.core.common.enums.ExceptionEnums;
import net.snaptag.system.sadais.core.common.exception.ServiceException;
import net.snaptag.system.sadais.util.core.ToolsKit;
import net.snaptag.system.sadais.web.core.BaseController;
import net.snaptag.system.sadais.web.dto.ReturnDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 角色模块
 */
@RestController
@RequestMapping(Constant.PRE_MAPPING_URL + "/role")
public class RoleController extends BaseController {
    @Autowired
    private RoleTableBuService roleTableBuService;

    /**
     * 保存
     *
     * @return
     */
    @RequestMapping(value = "/saveorupdate")
    @SLSLog(value = "保存权限", configKey = "business", businessType = "角色模块", operation = OperationType.UPDATE)
    public ReturnDto saveOrUpdate() {
        try {
            String id = this.getValue("id");
            String roleName = this.getValue("roleName");
            String roleCode = this.getValue("roleCode");
            String projectId = this.getValue("projectId");
            roleTableBuService.saveOrUpdate(id, roleName, roleCode, projectId);
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, null);
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    /**
     * 删除
     *
     * @return
     */
    @RequestMapping(value = "/del")
    @SLSLog(value = "删除权限", configKey = "business", businessType = "角色模块", operation = OperationType.DELETE)
    public ReturnDto del() {
        try {
            String id = this.getValue("id");
            roleTableBuService.del(id);
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, null);
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    /**
     * 查询列表
     *
     * @return
     */
    @RequestMapping(value = "/findpage")
    @SLSLog(value = "查询列表", configKey = "business", businessType = "角色模块", operation = OperationType.SELECT)
    public ReturnDto findPage() {
        try {
            String name = this.getValue("name");
            String pageNo = this.getValue("pageno");
            String pageSize = this.getValue("pagesize");
            if (ToolsKit.isEmpty(pageNo)) {
                pageNo = "1";
            }
            if (ToolsKit.isEmpty(pageSize)) {
                pageSize = String.valueOf(ToolsConst.PHONEPAGESIZE);
            }
            return this.returnSuccessJson(ExceptionEnums.SUCCESS,
                    roleTableBuService.findPage(name, Integer.parseInt(pageNo), Integer.parseInt(pageSize)));
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    @RequestMapping(value = "/findAllList")
    @SLSLog(value = "查询所有权限列表", configKey = "business", businessType = "角色模块", operation = OperationType.SELECT)
    public ReturnDto findAllList() {
        try {
            return this.returnSuccessJson(ExceptionEnums.SUCCESS,
                    roleTableBuService.findList(""));
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    /**
     * 分配角色
     *
     * @return
     */
    @RequestMapping(value = "/disresource")
    @SLSLog(value = "分配角色", configKey = "business", businessType = "角色模块", operation = OperationType.UPDATE)
    public ReturnDto disresource() {
        try {
            String roleId = this.getValue("roleId");
            String resIds = this.getValue("resIds");
            roleTableBuService.disresource(roleId, resIds);
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, null);
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    @RequestMapping(value = "/findResourcesTreeByRoleId")
    @SLSLog(value = "获取角色树", configKey = "business", businessType = "角色模块", operation = OperationType.SELECT)
    public ReturnDto getResourcesTree() {
        try {
            String id = this.getValue("roleId");
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, roleTableBuService.findResourcesTreeByRoleId(id));
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }
}
