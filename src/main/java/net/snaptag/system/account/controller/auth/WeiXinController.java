package net.snaptag.system.account.controller.auth;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.vida.sls.annotation.SLSLog;
import com.vida.sls.enums.OperationType;
import net.snaptag.system.account.utils.Constant;
import net.snaptag.system.sadais.core.common.enums.ExceptionEnums;
import net.snaptag.system.sadais.core.common.exception.ServiceException;
import net.snaptag.system.sadais.core.common.utils.AESUtil;
import net.snaptag.system.sadais.util.core.ToolsKit;
import net.snaptag.system.sadais.web.common.JsonKit;
import net.snaptag.system.sadais.web.config.CommonProperties;
import net.snaptag.system.sadais.web.core.BaseController;
import net.snaptag.system.sadais.web.dto.ReturnDto;
import net.snaptag.system.sadais.web.utils.SignWeiXinUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * 微信模块
 */
@RestController
@RequestMapping(Constant.PRE_MAPPING_URL + "/wx")
public class WeiXinController extends BaseController {
    @Autowired
    private CommonProperties commonProperties;

    /**
     * 获取微信用户信息
     *
     * @param
     * @return
     */
    @RequestMapping(value = "/getwxinfo")
    @SLSLog(value = "获取微信用户信息", configKey = "business", businessType = "微信模块", operation = OperationType.SELECT)
    public ReturnDto getWxInfo() {
        try {
            String code = this.getValue("code");
            String encryptedData = this.getValue("encryptedData");
            String iv = this.getValue("iv");
            if (ToolsKit.isEmpty(code)) {
                throw new ServiceException().setCode(ExceptionEnums.ERROR.getCode()).setMessage("授权code不能为空");
            }
            if (ToolsKit.isEmpty(encryptedData)) {
                throw new ServiceException().setCode(ExceptionEnums.ERROR.getCode()).setMessage("加密数据不能为空");
            }
            String accessData = SignWeiXinUtil.getGzAccessToken(commonProperties.getMaWxAppId(), commonProperties.getMaWxAppSecret());
            String sessionKey = SignWeiXinUtil.getSRSessionKey(code, commonProperties.getMaWxAppId(), commonProperties.getMaWxAppSecret());
            JSONObject JSONObject = JSON.parseObject(sessionKey);
            AESUtil aes = new AESUtil();
            String resultByte = aes.decrypt(encryptedData, JSONObject.getString("session_key"), iv);
            Map<String, Object> map = new HashMap<String, Object>();
            map.put("accessData", accessData);
            map.put("wxInfo", JsonKit.jsonParseObject(resultByte));
            System.out.println(JSON.toJSONString(map));
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, map);
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }
}
