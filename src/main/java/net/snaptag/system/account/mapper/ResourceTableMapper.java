package net.snaptag.system.account.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import net.snaptag.system.account.entity.ResourceTable;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * ResourceTable Mapper接口
 */
@Mapper
public interface ResourceTableMapper extends BaseMapper<ResourceTable> {

    /**
     * 分页查询ResourceTable列表
     */
    @Select("SELECT * FROM v1_resource_table WHERE status = #{status} ORDER BY createtime DESC")
    IPage<ResourceTable> findPage(Page<ResourceTable> page, @Param("status") String status);

    /**
     * 查询所有ResourceTable列表
     */
    @Select("SELECT * FROM v1_resource_table WHERE status = #{status} ORDER BY createtime DESC")
    List<ResourceTable> findAllList(@Param("status") String status);
}
