package net.snaptag.system.account.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import net.snaptag.system.account.entity.UserAuthAccount;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * UserAuthAccount Mapper接口
 */
@Mapper
public interface UserAuthAccountMapper extends BaseMapper<UserAuthAccount> {

    /**
     * 分页查询UserAuthAccount列表
     */
    @Select("SELECT * FROM v1_user_auth_account WHERE status = #{status} ORDER BY createtime DESC")
    IPage<UserAuthAccount> findPage(Page<UserAuthAccount> page, @Param("status") String status);

    /**
     * 查询所有UserAuthAccount列表
     */
    @Select("SELECT * FROM v1_user_auth_account WHERE status = #{status} ORDER BY createtime DESC")
    List<UserAuthAccount> findAllList(@Param("status") String status);
}
