package net.snaptag.system.account.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import net.snaptag.system.account.entity.UserThirdPartyAuth;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * UserThirdPartyAuth Mapper接口
 */
@Mapper
public interface UserThirdPartyAuthMapper extends BaseMapper<UserThirdPartyAuth> {

    /**
     * 分页查询UserThirdPartyAuth列表
     */
    @Select("SELECT * FROM v1_user_third_party_auth WHERE status = #{status} ORDER BY createtime DESC")
    IPage<UserThirdPartyAuth> findPage(Page<UserThirdPartyAuth> page, @Param("status") String status);

    /**
     * 查询所有UserThirdPartyAuth列表
     */
    @Select("SELECT * FROM v1_user_third_party_auth WHERE status = #{status} ORDER BY createtime DESC")
    List<UserThirdPartyAuth> findAllList(@Param("status") String status);

    /**
     * 根据用户ID查询第三方授权信息
     */
    @Select("SELECT * FROM v1_user_third_party_auth WHERE user_id = #{userId} AND status = #{status}")
    UserThirdPartyAuth findByUserId(@Param("userId") String userId, @Param("status") String status);

    /**
     * 根据微信UnionID查询第三方授权信息
     */
    @Select("SELECT * FROM v1_user_third_party_auth WHERE wechat_union_id = #{wechatUnionId} AND status = #{status}")
    UserThirdPartyAuth findByWechatUnionId(@Param("wechatUnionId") String wechatUnionId, @Param("status") String status);
}
