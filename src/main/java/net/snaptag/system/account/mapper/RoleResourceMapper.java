package net.snaptag.system.account.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import net.snaptag.system.account.entity.RoleResource;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * RoleResource Mapper接口
 */
@Mapper
public interface RoleResourceMapper extends BaseMapper<RoleResource> {

    /**
     * 分页查询RoleResource列表
     */
    @Select("SELECT * FROM v1_role_resource WHERE status = #{status} ORDER BY createtime DESC")
    IPage<RoleResource> findPage(Page<RoleResource> page, @Param("status") String status);

    /**
     * 查询所有RoleResource列表
     */
    @Select("SELECT * FROM v1_role_resource WHERE status = #{status} ORDER BY createtime DESC")
    List<RoleResource> findAllList(@Param("status") String status);

    /**
     * 根据角色ID查询资源
     */
    @Select("SELECT * FROM v1_role_resource WHERE role_id = #{roleId} AND status = #{status}")
    List<RoleResource> findByRoleId(@Param("roleId") String roleId, @Param("status") String status);
}
