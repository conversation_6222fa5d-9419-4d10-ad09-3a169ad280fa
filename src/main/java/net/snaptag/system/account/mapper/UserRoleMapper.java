package net.snaptag.system.account.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import net.snaptag.system.account.entity.UserRole;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * UserRole Mapper接口
 */
@Mapper
public interface UserRoleMapper extends BaseMapper<UserRole> {

    /**
     * 分页查询UserRole列表
     */
    @Select("SELECT * FROM v1_user_role WHERE status = #{status} ORDER BY createtime DESC")
    IPage<UserRole> findPage(Page<UserRole> page, @Param("status") String status);

    /**
     * 查询所有UserRole列表
     */
    @Select("SELECT * FROM v1_user_role WHERE status = #{status} ORDER BY createtime DESC")
    List<UserRole> findAllList(@Param("status") String status);

    /**
     * 根据用户ID查询角色
     */
    @Select("SELECT * FROM v1_user_role WHERE user_id = #{userId} AND status = #{status}")
    List<UserRole> findByUserId(@Param("userId") String userId, @Param("status") String status);
}
