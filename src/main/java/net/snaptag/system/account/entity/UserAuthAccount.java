package net.snaptag.system.account.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import net.snaptag.system.common.BaseEntity;

/**
 * <AUTHOR>
 * @date ：Created in 2023/11/20 14:23
 * @description：用户认证账号
 * @modified By：
 * @version: $
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("v1_user_auth_account")
public class UserAuthAccount extends BaseEntity {
    private static final long serialVersionUID = 1L;
    public static final String COLL = "v1_user_auth_account";
    public static final String NAME_FIELD = "name";
    public static final String ACCOUNT_FIELD = "account";
    public static final String APP_ID_FIELD = "app_id";

    /**
     * 用户名称
     */
    @TableField("name")
    private String name;

    /**
     * 用户账号
     */
    @TableField("account")
    private String account;

    /**
     * 用户密码
     */
    @TableField("password")
    private String password;

    /**
     * 安全码
     */
    @TableField("salt")
    private String salt;

    /**
     * 关联属性
     */
    @TableField("ass_attribute")
    private String assAttribute;

    /**
     * 项目ID集合 - JSON格式字符串
     * 例如: ["project1", "project2", "project3"]
     */
    @TableField(value = "project_ids")
    private String projectIds;

}
