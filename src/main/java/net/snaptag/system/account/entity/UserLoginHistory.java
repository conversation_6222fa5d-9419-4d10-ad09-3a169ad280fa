package net.snaptag.system.account.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import net.snaptag.system.common.BaseEntity;

import java.util.Date;

/**
 * <AUTHOR>
 * @date ：Created in 2023/3/20 9:04
 * @description：用户登录历史
 * @modified By：
 * @version: $
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("v1_user_login_history")
public class UserLoginHistory extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private String userId;

    /**
     * 最后登录的时间
     */
    @TableField("latest_login_time")
    private Date latestLoginTime;

    /**
     * 使用的app版本
     */
    @TableField("app_version")
    private String appVersion;

    /**
     * 使用过的打印设备
     */
    @TableField("used_devices")
    private String usedDevices;

    /**
     * 手机类型
     */
    @TableField("phone_type")
    private String phoneType;

}

