package net.snaptag.system.account.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import net.snaptag.system.common.BaseEntity;

/**
 * 角色资源
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("v1_role_resource")
public class RoleResource extends BaseEntity {
    private static final long serialVersionUID = 1L;
    public static final String COLL = "v1_role_resource";
    public static final String ROLE_ID_FIELD = "role_id";
    public static final String RESOURCE_ID_FIELD = "resource_id";

    /**
     * 角色表ID
     */
    @TableField("role_id")
    private String roleId;

    /**
     * 资源表ID
     */
    @TableField("resource_id")
    private String resourceId;
}
