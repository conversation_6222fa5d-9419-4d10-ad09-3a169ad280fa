package net.snaptag.system.account.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import net.snaptag.system.common.BaseEntity;

/**
 * 资源表
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("v1_resource_table")
public class ResourceTable extends BaseEntity {
    private static final long serialVersionUID = 1L;
    public static final String COLL = "v1_resource_table";
    public static final String RES_NAME_FIELD = "res_name";
    public static final String LEVEL_FIELD = "level";
    public static final String TYPE_FIELD = "type";
    public static final String APP_ID_FIELD = "app_id";
    public static final String SORT_FIELD = "sort";

    /**
     * 父节点ID
     */
    @TableField("p_id")
    private String pId;

    /**
     * 资源名称
     */
    @TableField("res_name")
    private String resName;

    /**
     * 资源编码
     */
    @TableField("res_code")
    private String resCode;

    /**
     * 访问路径
     */
    @TableField("path")
    private String path;

    /**
     * 层级
     */
    @TableField("level")
    private int level;

    /**
     * 排序
     */
    @TableField("sort")
    private int sort;

    /**
     * 资源类型 0菜单 1按钮
     */
    @TableField("type")
    private int type;

    /**
     * 项目ID
     */
    @TableField("project_id")
    private String projectId;

    /**
     * 菜单图标
     */
    @TableField("icon")
    private String icon;

}
