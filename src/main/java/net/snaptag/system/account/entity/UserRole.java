package net.snaptag.system.account.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import net.snaptag.system.common.BaseEntity;

/**
 * 用户角色表
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("v1_user_role")
public class UserRole extends BaseEntity {
    private static final long serialVersionUID = 1L;
    public static final String COLL = "v1_user_role";
    public static final String USER_ACCOUNT_ID_FIELD = "user_account_id";
    public static final String ROLE_ID_FIELD = "role_id";
    public static final String PROJECT_ID_FIELD = "project_id";

    /**
     * 用户账户表ID
     */
    @TableField("user_account_id")
    private String userAccountId;

    /**
     * 角色表ID
     */
    @TableField("role_id")
    private String roleId;

    /**
     * 项目ID
     */
    @TableField("project_id")
    private String projectId;
}
