package net.snaptag.system.account.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import net.snaptag.system.account.entity.UserAccount;
import net.snaptag.system.account.mapper.UserAccountMapper;
import net.snaptag.system.common.DataConst;
import net.snaptag.system.sadais.util.core.ToolsKit;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Repository
public class UserAccountDao extends ServiceImpl<UserAccountMapper, UserAccount> {

    @Autowired
    private UserAccountMapper userAccountMapper;
    /**
     * 根据用户ID查询用户账号信息
     *
     * @param id
     *            用户ID
     * @return 用户账号信息
     * @throws Exception
     */
    public UserAccount getUserAccountById(String id) throws Exception {
        QueryWrapper<UserAccount> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("id", id);
        queryWrapper.eq("status", DataConst.DATA_SUCCESS_STATUS);
        return this.getOne(queryWrapper);
    }

    /**
     * 根据账号查询用户账号信息
     *
     * @param account
     *            账号
     * @return 用户账号信息
     * @throws Exception
     */
    public UserAccount getUserAccountByAccount(String propertyName, String account) throws Exception {
        QueryWrapper<UserAccount> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(propertyName, account);
        queryWrapper.eq("status", DataConst.DATA_SUCCESS_STATUS);
        queryWrapper.select("id");
        return this.getOne(queryWrapper);
    }

    /**
     * 统计数量
     * 
     * @param startDate
     *            开始时间
     * @param endDate
     *            结束时间
     * @return
     */
    public long getDaysCount(Date startDate, Date endDate) {
        QueryWrapper<UserAccount> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", DataConst.DATA_SUCCESS_STATUS);
        if (ToolsKit.isNotEmpty(startDate) && ToolsKit.isNotEmpty(endDate)) {
            queryWrapper.between("createtime", startDate, endDate);
        }
        return this.count(queryWrapper);
    }

    public List<String> findByMobile(String mobile) {
        if (ToolsKit.isEmpty(mobile)){
            return null;
        }

        QueryWrapper<UserAccount> queryWrapper = new QueryWrapper<>();
        queryWrapper.like("mobile_account", mobile);
        queryWrapper.eq("status", DataConst.DATA_SUCCESS_STATUS);
        queryWrapper.select("id");

        List<UserAccount> list = this.list(queryWrapper);
        if (ToolsKit.isNotEmpty(list)){
            List<String> ids = new ArrayList<>();
            list.forEach(item -> {
                ids.add(item.getId());
            });
            return ids;
        }
        return null;
    }

    public String getIdByAccountLike(String accountLike) {
        QueryWrapper<UserAccount> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", DataConst.DATA_SUCCESS_STATUS);
        queryWrapper.and(wrapper -> wrapper
            .eq("mobile_account", accountLike)
            .or().eq("mail_account", accountLike)
            .or().eq("qq_account", accountLike)
            .or().eq("weixin_account", accountLike)
            .or().eq("sina_account", accountLike)
        );
        queryWrapper.select("id");

        UserAccount userAccount = this.getOne(queryWrapper);
        if (userAccount!=null){
            return userAccount.getId();
        }
        return "";
    }
}