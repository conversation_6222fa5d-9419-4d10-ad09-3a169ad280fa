package net.snaptag.system.account.dao;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import net.snaptag.system.account.entity.UserInfo;
import net.snaptag.system.account.mapper.UserInfoMapper;
import net.snaptag.system.common.DataConst;
import net.snaptag.system.sadais.util.core.ToolsKit;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Repository
public class UserInfoDao extends ServiceImpl<UserInfoMapper, UserInfo> {

    @Autowired
    private UserInfoMapper userInfoMapper;
    /**
     * 根据用户ID查询用户信息
     *
     * @param userId
     *            用户ID
     * @return 用户信息
     * @throws Exception
     */
    public UserInfo getUserInfoByUserId(String userId) throws Exception {
        QueryWrapper<UserInfo> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId);
        queryWrapper.eq("status", DataConst.DATA_SUCCESS_STATUS);
        return this.getOne(queryWrapper);
    }

    /**
     * 根据ID查询用户信息
     *
     * @param id
     *            ID
     * @return 用户信息
     * @throws Exception
     */
    public UserInfo getUserInfoById(String id) throws Exception {
        QueryWrapper<UserInfo> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("id", id);
        queryWrapper.eq("status", DataConst.DATA_SUCCESS_STATUS);
        return this.getOne(queryWrapper);
    }

    /**
     * 根据codeID查询用户信息
     *
     * @return 用户信息
     * @throws Exception
     */
    public UserInfo getUserInfoByCodeId(int codeId) throws Exception {
        QueryWrapper<UserInfo> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("code_id", codeId);
        queryWrapper.eq("status", DataConst.DATA_SUCCESS_STATUS);
        return this.getOne(queryWrapper);
    }

    /**
     * 根据时间段和昵称查询出用户信息记录
     * 
     * @param startDate
     *            开始时间
     * @param endDate
     *            结束时间
     * @param nickName
     *            昵称
     * @return
     */
    public IPage<UserInfo> findUserInfoList(String codeId, Date startDate, Date endDate, String nickName, int pageNo, int pageSize, String appId, String sort,
            String sortType, String sex) {
        QueryWrapper<UserInfo> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", DataConst.DATA_SUCCESS_STATUS);

        if (ToolsKit.isNotEmpty(startDate) && ToolsKit.isNotEmpty(endDate)) {
            queryWrapper.between("createtime", startDate, endDate);
        }
        if (ToolsKit.isNotEmpty(nickName)) {
            queryWrapper.like("nick_name", nickName);
        }
        if (ToolsKit.isNotEmpty(appId)) {
            queryWrapper.eq("app_id", appId);
        }
        if (ToolsKit.isNotEmpty(codeId) && ToolsKit.Number.isNumber(codeId) && !"0".equals(codeId)) {
            queryWrapper.eq("code_id", Integer.parseInt(codeId));
        }
        if (ToolsKit.isNotEmpty(sex)) {
            queryWrapper.eq("sex", sex);
        }

        if ("ASC".equals(sort)) {
            queryWrapper.orderByAsc("createtime");
        } else {
            queryWrapper.orderByDesc("createtime");
        }

        queryWrapper.select("id");
        Page<UserInfo> page = new Page<>(pageNo + 1, pageSize);
        return this.page(page, queryWrapper);
    }

    /**
     * 获取最大codeId记录
     * 
     * @return
     */
    public UserInfo getMaxCodeIdUser() {
        QueryWrapper<UserInfo> queryWrapper = new QueryWrapper<>();
        queryWrapper.orderByDesc("code_id");
        queryWrapper.last("LIMIT 1");
        return this.getOne(queryWrapper);
    }

    public IPage<UserInfo> findUserInfoList(String keyword, Date startDate, Date endDate, int pageNo, int pageSize, List<String> ids) {
        QueryWrapper<UserInfo> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", DataConst.DATA_SUCCESS_STATUS);

        if (ToolsKit.isNotEmpty(startDate) && ToolsKit.isNotEmpty(endDate)) {
            queryWrapper.between("createtime", startDate, endDate);
        }
        if (ToolsKit.isNotEmpty(keyword)) {
            if (ToolsKit.Number.isInteger(keyword)){
                queryWrapper.and(wrapper -> wrapper.like("nick_name", keyword).or().eq("code_id", Integer.parseInt(keyword)));
            } else {
                queryWrapper.like("nick_name", keyword);
            }
        }

        if (ToolsKit.isNotEmpty(ids)){
            queryWrapper.in("user_id", ids);
        }

        queryWrapper.orderByDesc("createtime");
        queryWrapper.select("id");
        Page<UserInfo> page = new Page<>(pageNo + 1, pageSize);
        return this.page(page, queryWrapper);
    }
}