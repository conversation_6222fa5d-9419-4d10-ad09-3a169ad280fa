package net.snaptag.system.account.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import net.snaptag.system.account.entity.RoleResource;
import net.snaptag.system.account.mapper.RoleResourceMapper;
import net.snaptag.system.common.DataConst;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 角色资源
 */
@Repository
public class RoleResourceDao extends ServiceImpl<RoleResourceMapper, RoleResource> {

    @Autowired
    private RoleResourceMapper roleResourceMapper;
    /**
     * 根据角色ID集合获取角色资源列表
     */
    public List<RoleResource> findRoleResourceByRoleId(List<String> roleIds) {
        QueryWrapper<RoleResource> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("role_id", roleIds);
        queryWrapper.eq("status", DataConst.DATA_SUCCESS_STATUS);
        return this.list(queryWrapper);
    }

    /**
     * 根据角色ID查询数量
     */
    public long getCountByRole(List<String> roleIds) {
        QueryWrapper<RoleResource> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("role_id", roleIds);
        queryWrapper.eq("status", DataConst.DATA_SUCCESS_STATUS);
        return this.count(queryWrapper);
    }

    /**
     * 根据资源ID查询数量
     */
    public long getCountByResource(List<String> resIds) {
        QueryWrapper<RoleResource> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("resource_id", resIds);
        queryWrapper.eq("status", DataConst.DATA_SUCCESS_STATUS);
        return this.count(queryWrapper);
    }

    /**
     * 根据角色ID删除
     */
    public void delByRoleId(String roleId) {
        UpdateWrapper<RoleResource> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("role_id", roleId);
        updateWrapper.eq("status", DataConst.DATA_SUCCESS_STATUS);
        updateWrapper.set("status", DataConst.DATA_DELETE_STATUS);
        this.update(updateWrapper);
    }
}
