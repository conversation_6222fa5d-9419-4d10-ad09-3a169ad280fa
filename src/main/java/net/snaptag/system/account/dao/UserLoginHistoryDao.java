package net.snaptag.system.account.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import net.snaptag.system.account.entity.UserLoginHistory;
import net.snaptag.system.account.mapper.UserLoginHistoryMapper;
import net.snaptag.system.common.DataConst;
import net.snaptag.system.sadais.util.core.ToolsKit;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2023/3/20 9:11
 * @description：用户登录历史DAO
 * @modified By：
 * @version: $
 */
@Repository
public class UserLoginHistoryDao extends ServiceImpl<UserLoginHistoryMapper, UserLoginHistory> {

    @Autowired
    private UserLoginHistoryMapper userLoginHistoryMapper;

    public List<String> findByLoginTime(Date loginStart, Date loginEnd) {
        QueryWrapper<UserLoginHistory> queryWrapper = new QueryWrapper<>();
        if (ToolsKit.isNotEmpty(loginStart) && ToolsKit.isNotEmpty(loginEnd)) {
            queryWrapper.between("latest_login_time", loginStart, loginEnd);
        }
        queryWrapper.eq("status", DataConst.DATA_SUCCESS_STATUS);
        queryWrapper.select("user_id");

        List<UserLoginHistory> list = this.list(queryWrapper);
        if (ToolsKit.isNotEmpty(list)){
            List<String> ids = new ArrayList<>();
            list.forEach(item -> {
                ids.add(item.getUserId());
            });
            return ids;
        }
        return null;
    }
}
