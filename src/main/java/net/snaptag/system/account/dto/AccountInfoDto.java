package net.snaptag.system.account.dto;
import java.io.Serializable;
public class AccountInfoDto implements Serializable {
    /**
     * 账号信息--服务器返回
     */
    private static final long serialVersionUID = 1L;
    private String            loginway;             // 登录途径
    private String            name;                 // 名称
    private String            account;              // 账号信息
    private int               isSetupPwd;           // 是否设置了密码
    private String            mobile;               // 手机号
    private String            email;                // 邮箱
    private String            nationCode;           // 国际区号

    public int getIsSetupPwd() {
        return isSetupPwd;
    }

    public void setIsSetupPwd(int isSetupPwd) {
        this.isSetupPwd = isSetupPwd;
    }

    public String getLoginway() {
        return loginway;
    }

    public void setLoginway(String loginway) {
        this.loginway = loginway;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getAccount() {
        return account;
    }

    public void setAccount(String account) {
        this.account = account;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getNationCode() {
        return nationCode;
    }

    public void setNationCode(String nationCode) {
        this.nationCode = nationCode;
    }
}
