package net.snaptag.system.account.dto;
import java.io.Serializable;
import java.util.Map;
public class UserAccountDto implements Serializable {
    /**
     * 用户账户信息--服务器返回
     */
    private static final long   serialVersionUID = 1L;
    private String              userId;               // 用户ID
    private String              mobileAccount;        // 手机号码
    private String              mailAccount;          // 邮箱地址
    private String              pwd;                  // 密码
    private String              sinaaccount;          // 新浪微博账号
    private String              sinapwd;              // 新浪微博密码
    private String              qqaccount;            // qq账号
    private String              qqpwd;                // qq密码
    private String              weixinaccount;        // 微信账号
    private String              weixinpwd;            // 微信密码
    private Map<String, String> bindName;             // 第三方绑定名称
    private String              moblieId;             // 设备ID
    private String              deviceSystem;         // 设备系统
    private String              appId;                // 渠道ID

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getMobileAccount() {
        return mobileAccount;
    }

    public void setMobileAccount(String mobileAccount) {
        this.mobileAccount = mobileAccount;
    }

    public String getMailAccount() {
        return mailAccount;
    }

    public void setMailAccount(String mailAccount) {
        this.mailAccount = mailAccount;
    }

    public String getPwd() {
        return pwd;
    }

    public void setPwd(String pwd) {
        this.pwd = pwd;
    }

    public String getSinaaccount() {
        return sinaaccount;
    }

    public void setSinaaccount(String sinaaccount) {
        this.sinaaccount = sinaaccount;
    }

    public String getSinapwd() {
        return sinapwd;
    }

    public void setSinapwd(String sinapwd) {
        this.sinapwd = sinapwd;
    }

    public String getQqaccount() {
        return qqaccount;
    }

    public void setQqaccount(String qqaccount) {
        this.qqaccount = qqaccount;
    }

    public String getQqpwd() {
        return qqpwd;
    }

    public void setQqpwd(String qqpwd) {
        this.qqpwd = qqpwd;
    }

    public String getWeixinaccount() {
        return weixinaccount;
    }

    public void setWeixinaccount(String weixinaccount) {
        this.weixinaccount = weixinaccount;
    }

    public String getWeixinpwd() {
        return weixinpwd;
    }

    public void setWeixinpwd(String weixinpwd) {
        this.weixinpwd = weixinpwd;
    }

    public Map<String, String> getBindName() {
        return bindName;
    }

    public void setBindName(Map<String, String> bindName) {
        this.bindName = bindName;
    }

    public String getMoblieId() {
        return moblieId;
    }

    public void setMoblieId(String moblieId) {
        this.moblieId = moblieId;
    }

    public String getDeviceSystem() {
        return deviceSystem;
    }

    public void setDeviceSystem(String deviceSystem) {
        this.deviceSystem = deviceSystem;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }
}
