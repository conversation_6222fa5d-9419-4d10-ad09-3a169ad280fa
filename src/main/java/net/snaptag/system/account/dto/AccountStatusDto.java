package net.snaptag.system.account.dto;
import java.io.Serializable;
public class AccountStatusDto implements Serializable {
    /**
     * 账号状态--服务器返回
     */
    private static final long serialVersionUID = 1L;
    private int               loginStatus;          // 登录状态 1-需要绑定 2-直接登录

    public AccountStatusDto() {
    }

    public AccountStatusDto(int loginStatus) {
        this.loginStatus = loginStatus;
    }

    public int getLoginStatus() {
        return loginStatus;
    }

    public void setLoginStatus(int loginStatus) {
        this.loginStatus = loginStatus;
    }
}
