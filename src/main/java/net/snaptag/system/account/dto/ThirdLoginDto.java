package net.snaptag.system.account.dto;
import java.io.Serializable;
public class ThirdLoginDto implements Serializable {
    /**
     * 
     */
    private static final long serialVersionUID = 1L;
    private String            accessToken;          // 用户授权时生成的access_token--新浪微博，网页授权接口调用凭证,注意：此access_token与基础支持的access_token不同--微信,QQ
    private String            openId;               // 用户的唯一标识--微信，QQ
    private String            appId;                // 应用的唯一ID--QQ
    private String            from;                 // 来源

    public String getAccessToken() {
        return accessToken;
    }

    public void setAccessToken(String accessToken) {
        this.accessToken = accessToken;
    }

    public String getOpenId() {
        return openId;
    }

    public void setOpenId(String openId) {
        this.openId = openId;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getFrom() {
        return from;
    }

    public void setFrom(String from) {
        this.from = from;
    }
}
