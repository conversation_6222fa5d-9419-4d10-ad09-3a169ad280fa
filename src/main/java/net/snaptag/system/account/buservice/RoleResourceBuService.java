package net.snaptag.system.account.buservice;

import net.snaptag.system.account.dao.RoleResourceDao;
import net.snaptag.system.account.entity.RoleResource;
import net.snaptag.system.common.DataConst;
import net.snaptag.system.sadais.util.core.ToolsKit;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 角色资源
 */
@Service
public class RoleResourceBuService {
    @Autowired
    private RoleResourceDao roleResourceDao;

    /**
     * 根据角色ID集合获取角色资源列表
     * 
     * @param roleIds
     *            角色ID集合
     * @return
     */
    public List<RoleResource> findRoleResourceByRoleId(List<String> roleIds) {
        return roleResourceDao.findRoleResourceByRoleId(roleIds);
    }

    /**
     * 根据角色ID集合获取角色资源列表
     * 
     * @param roleIds
     *            角色ID集合
     * @return
     */
    public List<String> findProjectIdList(List<String> roleIds) {
        List<RoleResource> roleResourceList = this.findRoleResourceByRoleId(roleIds);
        List<String> resourceIdList = new ArrayList<String>();
        for (RoleResource roleResource : roleResourceList) {
            if (!resourceIdList.contains(roleResource.getResourceId())) {
                resourceIdList.add(roleResource.getResourceId());
            }
        }
        return resourceIdList;
    }

    /**
     * 根据角色ID查询数量
     * 
     * @param roleId
     *            角色ID
     * @return
     */
    public long getCountByRole(List<String> roleIds) {
        return roleResourceDao.getCountByRole(roleIds);
    }

    /**
     * 根据资源ID查询数量
     * 
     * @param resId
     *            资源ID
     * @return
     */
    public long getCountByResource(List<String> resIds) {
        return roleResourceDao.getCountByResource(resIds);
    }

    /**
     * 保存角色资源对象
     * 
     * @param roleId
     *            角色ID
     * @param resId
     *            资源ID
     */
    public void saveRoleResource(String roleId, String resId) {
        RoleResource roleResource = new RoleResource();
        roleResource.setStatus(DataConst.DATA_SUCCESS_STATUS);
        roleResource.setCreatetime(new java.util.Date());
        roleResource.setCreateuserid("SYSTEM_USER_ID");
        roleResource.setRoleId(roleId);
        roleResource.setResourceId(resId);
        roleResourceDao.saveOrUpdate(roleResource);
    }

    public void deleteByRoleId(String roleId){
        if (ToolsKit.isEmpty(roleId)) {
            return;
        }
        roleResourceDao.delByRoleId(roleId);
    }
}
