package net.snaptag.system.account.buservice;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import net.snaptag.system.account.buservice.mail.MailSender;
import net.snaptag.system.account.cache.MailMessageCacheService;
import net.snaptag.system.account.cache.UserAccountCacheService;
import net.snaptag.system.account.cache.UserInfoCacheService;
import net.snaptag.system.account.cache.UserThirdPartyAuthCache;
import net.snaptag.system.account.dao.UserAccountDao;
import net.snaptag.system.account.dao.UserInfoDao;
import net.snaptag.system.account.dto.AccountInfoDto;
import net.snaptag.system.account.dto.SmsCountryCodeDto;
import net.snaptag.system.account.dto.UserAccountDto;
import net.snaptag.system.account.dto.UserLoginDto;
import net.snaptag.system.account.entity.UserAccount;
import net.snaptag.system.account.entity.UserInfo;
import net.snaptag.system.account.entity.UserThirdPartyAuth;
import net.snaptag.system.account.utils.Constant;
import net.snaptag.system.account.utils.HttpClientUtil;
import net.snaptag.system.sadais.cache.common.CacheCommonTools;
import net.snaptag.system.common.DataConst;
import net.snaptag.system.sadais.core.common.core.ToolsConst;
import net.snaptag.system.sadais.core.common.enums.ExceptionEnums;
import net.snaptag.system.sadais.core.common.exception.ServiceException;
import net.snaptag.system.sadais.core.common.utils.CryptionUtil;
import net.snaptag.system.sadais.util.core.ToolsKit;
import net.snaptag.system.sadais.web.common.JsonKit;
import net.snaptag.system.sadais.web.config.CommonProperties;
import net.snaptag.system.sadais.web.core.I18nUtils;
import net.snaptag.system.sadais.web.dto.HeadInfoDto;
import net.snaptag.system.sadais.web.dto.TokenDto;
import net.snaptag.system.sadais.web.dto.UserInfoDto;
import net.snaptag.system.sadais.web.enums.SystemStatusEnums;
import net.snaptag.system.sadais.web.jwt.JWTTokenUtil;
import net.snaptag.system.sadais.web.jwt.SubjectModel;
import net.snaptag.system.sadais.web.utils.Encodes;
import net.snaptag.system.sadais.web.utils.LocalTools;
import net.snaptag.system.sadais.web.utils.WebTools;
import net.snaptag.system.smscenter.buservice.SystemStatusBuService;
import net.snaptag.system.smscenter.dto.SmsStatusDto;
import net.snaptag.system.smscenter.dto.SystemStatusDto;
import net.snaptag.system.storage.aliyun.utils.MD5;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.ThreadLocalRandom;

@Service
public class UserAccountBuService {

    private static final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 将JSON字符串转换为Map<String, String>
     */
    private Map<String, String> parseBindNameJson(String bindNameJson) {
        if (bindNameJson == null || bindNameJson.trim().isEmpty()) {
            return new HashMap<>();
        }
        try {
            return objectMapper.readValue(bindNameJson, new TypeReference<Map<String, String>>() {});
        } catch (Exception e) {
            // 如果解析失败，返回空Map
            return new HashMap<>();
        }
    }

    /**
     * 将Map<String, String>转换为JSON字符串
     */
    private String bindNameToJson(Map<String, String> bindNameMap) {
        if (bindNameMap == null || bindNameMap.isEmpty()) {
            return null;
        }
        try {
            return objectMapper.writeValueAsString(bindNameMap);
        } catch (Exception e) {
            // 如果转换失败，返回null
            return null;
        }
    }

    @Autowired
    private UserThirdPartyAuthCache userThirdPartyAuthCache;
    @Autowired
    private UserAccountDao userAccountDao;
    @Autowired
    private UserAccountCacheService userAccountCacheService;
    @Autowired
    private UserThirdPartyAuthBuService userThirdPartyAuthBuService;
    //    @Autowired
//    private UserInfoBuService           userInfoBuService;
    @Autowired
    private UserInfoCacheService userInfoCacheService;
    @Autowired
    private UserInfoDao userInfoDao;
    @Autowired
    private MailMessageCacheService smsMessageCacheService;
    @Autowired
    private MailSender mailSender;
    @Autowired
    private I18nUtils i18nUtils;
    @Autowired
    private CacheCommonTools cacheCommonTools;
    @Autowired
    private CommonProperties commonProperties;
    @Autowired
    private SystemStatusBuService systemStatusService;

    @Autowired
    private UserLoginHistoryBuService userLoginHistoryBuService;

    private static final String SMS_COUNTRY_CODE_URL = "https://m.snaptag.top/app/sms_country.json";
    private static final String SMS_COUNTRY_CODE_CN_URL = "https://m.snaptag.top/app/sms_country_cn.json";
    private static final String SMS_COUNTRY_CODE_TW_URL = "https://m.snaptag.top/app/sms_country_tw.json";
    private static final Map<String, List<SmsCountryCodeDto>> smsCountryCodeMap = new HashMap<>();
    private static final Map<String, List<SmsCountryCodeDto>> smsCountryCodeMap_CN = new HashMap<>();
    private static final Map<String, List<SmsCountryCodeDto>> smsCountryCodeMap_TW = new HashMap<>();
    /**
     * api jwttoken有效时间-30分钟(修改成14天)
     */
    private static final long JWT_API_TTL = 60 * 60 * 1000 * 24 * 14;


    /**
     * 查询用户账户信息
     *
     * @param account  账号
     * @param loginway
     * @return
     */
    public UserAccount getUserAccountByAccount(String account, String loginway) {
        return getUserAccountByAccount(account, loginway, null);
    }

    /**
     * 查询用户账户信息
     *
     * @param account  账号
     * @param loginway 登录方式
     * @param unionId
     * @return
     */
    public UserAccount getUserAccountByAccount(String account, String loginway, String unionId) {
        String userId = null;
        try {
            if (loginway.equals(ToolsConst.LOGIN_BY_SINA)) {
                userId = this.getUserAccountByAccountName(UserAccount.SINAACCOUNT_FIELD, account);
            } else if (loginway.equals(ToolsConst.LOGIN_BY_QQ)) {
                userId = this.getUserAccountByAccountName(UserAccount.QQACCOUNT_FIELD, account);
            } else if (loginway.equals(ToolsConst.LOGIN_BY_MOBILE) || loginway.equals(ToolsConst.LOGIN_BY_YM_ONEKEY)) {
                userId = this.getUserAccountByAccountName(UserAccount.MOBILE_ACCOUNT_FIELD, account);
            } else if (loginway.equals(Constant.LOGIN_BY_MAIL)) {
                userId = this.getUserAccountByAccountName(UserAccount.MAIL_ACCOUNT_FIELD, account);
            } else if (loginway.equals(ToolsConst.LOGIN_BY_FACEBOOK)) {
                userId = this.getUserAccountByAccountName(UserAccount.FBACCOUNT_FIELD, account);
            } else if (loginway.equals(ToolsConst.LOGIN_BY_TWITTER)) {
                userId = this.getUserAccountByAccountName(UserAccount.TWACCOUNT_FIELD, account);
            } else if (loginway.equals(ToolsConst.LOGIN_BY_WHATSAPP)) {
                userId = this.getUserAccountByAccountName(UserAccount.WAACCOUNT_FIELD, account);
            } else if (loginway.equals(ToolsConst.LOGIN_BY_GOOGLE)) {
                userId = this.getUserAccountByAccountName(UserAccount.GOOGLEACCOUNT_FIELD, account);
            } else if (loginway.equals(ToolsConst.LOGIN_BY_APPLEID)) {
                userId = this.getUserAccountByAccountName(UserAccount.APPLEACCOUNT_FIELD, account);
            } else if (loginway.equals(ToolsConst.LOGIN_BY_APPLEID2)) {
                userId = this.getUserAccountByAccountName(UserAccount.APPLEACCOUNT_FIELD2, account);
            } else if (loginway.equals(ToolsConst.LOGIN_BY_WEIXIN)) {
                // 优先使用unionId登录
                if (ToolsKit.isNotEmpty(unionId)) {
                    userId = userThirdPartyAuthBuService.getUserIdByWeChatUnionId(unionId, Locale.SIMPLIFIED_CHINESE);
                }
                if (ToolsKit.isEmpty(userId)) {
                    userId = this.getUserAccountByAccountName(UserAccount.WEIXINACCOUNT_FIELD, account);
                }
            }
            if (ToolsKit.isNotEmpty(userId)) {
                return this.getUserAccountByUserId(userId);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 根据用户ID获取用户账号信息
     *
     * @return 用户账号信息
     * @throws ServiceException
     */
    public String getUserAccountByAccountName(String accountName, String account) throws ServiceException {
        if (ToolsKit.isEmpty(accountName)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("账户属性名称不能为空");
        }
        if (ToolsKit.isEmpty(account)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("账号信息不能为空");
        }
        String userId = userAccountCacheService.getUserAccountByAccountName(accountName, account);
        if (ToolsKit.isEmpty(userId)) {
            try {
                UserAccount userAccount = userAccountDao.getUserAccountByAccount(accountName, account);
                userId = ToolsKit.isEmpty(userAccount) ? "" : userAccount.getId();
            } catch (Exception e) {
                e.printStackTrace();
                throw new ServiceException().setCode(ExceptionEnums.ERROR.getCode()).setMessage("查询用户账号信息出错");
            }
            if (ToolsKit.isNotEmpty(userId)) {
                userAccountCacheService.setUserAccountName(accountName, account, userId);
            }
        }
        return userId;
    }

    public UserAccount getUserAccountByUserId(String userId) throws ServiceException {
        return getUserAccountByUserId(userId, Locale.SIMPLIFIED_CHINESE);
    }

    /**
     * 根据用户ID获取用户账号信息
     *
     * @param userId 用户ID
     * @return 用户账号信息
     * @throws ServiceException
     */
    public UserAccount getUserAccountByUserId(String userId, Locale locale) throws ServiceException {
        if (ToolsKit.isEmpty(userId)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage(i18nUtils.getKey(LocalTools.toast_userid_notblank, locale));
        }
        UserAccount userAccount = userAccountCacheService.getUserAccountByUserId(userId);
        if (ToolsKit.isEmpty(userAccount)) {
            try {
                userAccount = userAccountDao.getUserAccountById(userId);
            } catch (Exception e) {
                e.printStackTrace();
                throw new ServiceException().setCode(ExceptionEnums.ERROR.getCode()).setMessage("查询用户账号信息出错");
            }
            if (ToolsKit.isNotEmpty(userAccount)) {
                userAccountCacheService.saveUserAccount(userAccount);
            }
        }
        return userAccount;
    }

    public List<AccountInfoDto> getAccountInfoDto(String userId) throws ServiceException {
        return getAccountInfoDto(userId, Locale.SIMPLIFIED_CHINESE);
    }

    /**
     * 获取账号信息dto
     *
     * @param userId 用户ID
     * @return
     * @throws ServiceException
     */
    public List<AccountInfoDto> getAccountInfoDto(String userId, Locale locale) throws ServiceException {
        if (ToolsKit.isEmpty(userId)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage(i18nUtils.getKey(LocalTools.toast_userid_notblank, locale));
        }
        List<AccountInfoDto> dtoList = new ArrayList<AccountInfoDto>();
        UserAccount userAccount = this.getUserAccountByUserId(userId);
        if (ToolsKit.isNotEmpty(userAccount)) {
            Map<String, String> bindname = parseBindNameJson(userAccount.getBindName());
            if (ToolsKit.isNotEmpty(userAccount.getWeixinaccount())) {
                AccountInfoDto accountInfoDto = new AccountInfoDto();
                accountInfoDto.setLoginway(Constant.LOGIN_BY_WEIXIN);
                String bindName = this.getName(bindname, Constant.LOGIN_BY_WEIXIN, userAccount.getWeixinaccount());
                accountInfoDto.setName(bindName);
                accountInfoDto.setAccount(ToolsKit.isEmpty(userAccount.getWeixinaccount()) ? "" : userAccount.getWeixinaccount());
                dtoList.add(accountInfoDto);
            }
            if (ToolsKit.isNotEmpty(userAccount.getSinaaccount())) {
                AccountInfoDto accountInfoDto = new AccountInfoDto();
                accountInfoDto.setLoginway(Constant.LOGIN_BY_SINA);
                String bindName = this.getName(bindname, Constant.LOGIN_BY_SINA, userAccount.getSinaaccount());
                accountInfoDto.setName(bindName);
                accountInfoDto.setAccount(ToolsKit.isEmpty(userAccount.getSinaaccount()) ? "" : userAccount.getSinaaccount());
                dtoList.add(accountInfoDto);
            }
            if (ToolsKit.isNotEmpty(userAccount.getQqaccount())) {
                AccountInfoDto accountInfoDto = new AccountInfoDto();
                accountInfoDto.setLoginway(Constant.LOGIN_BY_QQ);
                String bindName = this.getName(bindname, Constant.LOGIN_BY_QQ, userAccount.getQqaccount());
                accountInfoDto.setName(bindName);
                accountInfoDto.setAccount(ToolsKit.isEmpty(userAccount.getQqaccount()) ? "" : userAccount.getQqaccount());
                dtoList.add(accountInfoDto);
            }
            if (ToolsKit.isNotEmpty(userAccount.getMobileAccount())) {
                AccountInfoDto accountInfoDto = new AccountInfoDto();
                accountInfoDto.setLoginway(Constant.LOGIN_BY_MOBILE);
                String bindName = userAccount.getMobileAccount();
                accountInfoDto.setName(bindName);
                accountInfoDto.setNationCode(userAccount.getNationCode());
                accountInfoDto.setAccount(ToolsKit.isEmpty(userAccount.getMobileAccount()) ? "" : userAccount.getMobileAccount());
                accountInfoDto.setIsSetupPwd(ToolsKit.isEmpty(userAccount.getPwd()) ? 0 : 1);

                // 国外的电话号码会加区号，返回前端时去掉，以方便前端再次发送短信时，用nationCode和mobileAccount来发送验证短信
                if (ToolsKit.isNotEmpty(userAccount.getNationCode()) && !"86".equals(userAccount.getNationCode())) {
                    String mobile = userAccount.getMobileAccount();
                    accountInfoDto.setMobile(mobile.substring(userAccount.getNationCode().length()));
                } else {
                    accountInfoDto.setMobile(userAccount.getMobileAccount());
                }

                dtoList.add(accountInfoDto);
            }

            if (ToolsKit.isNotEmpty(userAccount.getMailAccount())) {
                AccountInfoDto accountInfoDto = new AccountInfoDto();
                accountInfoDto.setLoginway(Constant.LOGIN_BY_MAIL);
                String bindName = userAccount.getMailAccount();
                accountInfoDto.setName(bindName);
                accountInfoDto.setAccount(ToolsKit.isEmpty(userAccount.getMailAccount()) ? "" : userAccount.getMailAccount());
                accountInfoDto.setIsSetupPwd(ToolsKit.isEmpty(userAccount.getPwd()) ? 0 : 1);
                dtoList.add(accountInfoDto);
            }

            if (ToolsKit.isNotEmpty(userAccount.getFbaccount())) {
                AccountInfoDto accountInfoDto = new AccountInfoDto();
                accountInfoDto.setLoginway(Constant.LOGIN_BY_FACEBOOK);
                String bindName = this.getName(bindname, Constant.LOGIN_BY_FACEBOOK, userAccount.getFbaccount());
                accountInfoDto.setName(bindName);
                accountInfoDto.setAccount(ToolsKit.isEmpty(userAccount.getFbaccount()) ? "" : userAccount.getFbaccount());
                dtoList.add(accountInfoDto);
            }
            if (ToolsKit.isNotEmpty(userAccount.getTwaccount())) {
                AccountInfoDto accountInfoDto = new AccountInfoDto();
                accountInfoDto.setLoginway(Constant.LOGIN_BY_TWITTER);
                String bindName = this.getName(bindname, Constant.LOGIN_BY_TWITTER, userAccount.getTwaccount());
                accountInfoDto.setName(bindName);
                accountInfoDto.setAccount(ToolsKit.isEmpty(userAccount.getTwaccount()) ? "" : userAccount.getTwaccount());
                dtoList.add(accountInfoDto);
            }
            if (ToolsKit.isNotEmpty(userAccount.getGoogleaccount())) {
                AccountInfoDto accountInfoDto = new AccountInfoDto();
                accountInfoDto.setLoginway(Constant.LOGIN_BY_GOOGLE);
                String bindName = this.getName(bindname, Constant.LOGIN_BY_GOOGLE, userAccount.getGoogleaccount());
                accountInfoDto.setName(bindName);
                accountInfoDto.setAccount(ToolsKit.isEmpty(userAccount.getGoogleaccount()) ? "" : userAccount.getGoogleaccount());
                dtoList.add(accountInfoDto);
            }
            if (ToolsKit.isNotEmpty(userAccount.getAppleaccount())) {
                AccountInfoDto accountInfoDto = new AccountInfoDto();
                accountInfoDto.setLoginway(Constant.LOGIN_BY_APPLEID);
                String bindName = this.getName(bindname, Constant.LOGIN_BY_APPLEID, userAccount.getAppleaccount());
                accountInfoDto.setName(bindName);
                accountInfoDto.setAccount(ToolsKit.isEmpty(userAccount.getAppleaccount()) ? "" : userAccount.getAppleaccount());
                dtoList.add(accountInfoDto);
            }
        }
        return dtoList;
    }

    /**
     * 获取用户第三方登录名称
     *
     * @param bindname 第三方登录名称集合
     * @param loginWay 登录方式
     * @return
     */
    private String getName(Map<String, String> bindname, String loginWay, String oldBindName) {
        if (ToolsKit.isEmpty(bindname)) {
            return "";
        } else {
            return ToolsKit.isEmpty(bindname.get(loginWay)) ? oldBindName : bindname.get(loginWay);
        }
    }

    /**
     * 保存
     *
     * @param userAccount
     */
    public void save(UserAccount userAccount) {
        try {
            userAccountDao.saveOrUpdate(userAccount);
            userAccountCacheService.saveUserAccount(userAccount);
            if (ToolsKit.isNotEmpty(userAccount.getMobileAccount())) {// 手机账号关联userid
                userAccountCacheService.setUserAccountName(UserAccount.MOBILE_ACCOUNT_FIELD, userAccount.getMobileAccount(), userAccount.getId());
            }

            if (ToolsKit.isNotEmpty(userAccount.getMailAccount())) {// 邮箱账号关联userid
                userAccountCacheService.setUserAccountName(UserAccount.MAIL_ACCOUNT_FIELD, userAccount.getMailAccount(), userAccount.getId());
            }

            if (ToolsKit.isNotEmpty(userAccount.getSinaaccount())) {// 新浪账号关联userid
                userAccountCacheService.setUserAccountName(UserAccount.SINAACCOUNT_FIELD, userAccount.getSinaaccount(), userAccount.getId());
            }
            if (ToolsKit.isNotEmpty(userAccount.getQqaccount())) {// QQ账号关联userid
                userAccountCacheService.setUserAccountName(UserAccount.QQACCOUNT_FIELD, userAccount.getQqaccount(), userAccount.getId());
            }
            if (ToolsKit.isNotEmpty(userAccount.getWeixinaccount())) {// 微信账号关联userid
                userAccountCacheService.setUserAccountName(UserAccount.WEIXINACCOUNT_FIELD, userAccount.getWeixinaccount(), userAccount.getId());
            }
            if (ToolsKit.isNotEmpty(userAccount.getFbaccount())) {// 脸书账号关联userid
                userAccountCacheService.setUserAccountName(UserAccount.FBACCOUNT_FIELD, userAccount.getFbaccount(), userAccount.getId());
            }
            if (ToolsKit.isNotEmpty(userAccount.getTwaccount())) {// 推特账号关联userid
                userAccountCacheService.setUserAccountName(UserAccount.TWACCOUNT_FIELD, userAccount.getTwaccount(), userAccount.getId());
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 查询用户账户信息
     *
     * @param account  账号
     * @param loginway 登录方式
     * @param unionId
     * @return
     */
    public UserAccountDto getUserAccountDto(String account, String loginway, String unionId, Locale locale) throws ServiceException {
        if (ToolsKit.isEmpty(account)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage(i18nUtils.getKey(LocalTools.toast_acc_old_notblank, locale));
        }
        if (ToolsKit.isEmpty(loginway)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage(i18nUtils.getKey(LocalTools.toast_log_method_blank, locale));
        }
        UserAccount userAccount = this.getUserAccountByAccount(account, loginway, unionId);
        if (ToolsKit.isNotEmpty(userAccount)) {
            UserAccountDto userAccountDto = new UserAccountDto();
            ToolsKit.Bean.copyProperties(userAccount, userAccountDto);
            userAccountDto.setUserId(userAccount.getId());
            return userAccountDto;
        }
        return null;
    }

    /**
     * 判断账号是否存在并且登陆
     *
     * @param userAccount 用户对象
     * @param pwd         登陆密码
     * @return 判断账号是否存在
     */
    public String checkUserAndLogin(UserAccount userAccount, String pwd, String loginway) throws ServiceException {
        if (ToolsKit.isEmpty(userAccount)) {// 新账号
            return Constant.LOGIN_NORMAL;
        } else if (ToolsKit.isNotEmpty(userAccount) && (!"LOGIN_BY_MOBILE".equals(loginway) && !Constant.LOGIN_BY_MAIL.equals(loginway))) {// 第三方授权登录
            return Constant.LOGIN_SUCCESS;
        } else if (("LOGIN_BY_MOBILE".equals(loginway) || Constant.LOGIN_BY_MAIL.equals(loginway))
                && ToolsKit.isNotEmpty(userAccount.getPwd())
                && (userAccount.getPwd().equals(WebTools.buildEntryptPassword(pwd, Encodes.decodeHex(userAccount.getSalt())))
                || userAccount.getPwd().equals(WebTools.buildEntryptPassword(MD5.MD5Encode(pwd), Encodes.decodeHex(userAccount.getSalt()))))) {// 手机号码或邮箱登录，并且密码正确
            return Constant.LOGIN_SUCCESS;
//        }  else if ((ToolsConst.LOGIN_BY_MOBILE.equals(loginway) || Constant.LOGIN_BY_MAIL.equals(loginway))
//                && ToolsKit.isEmpty(userAccount.getPwd())
//                ) {// 手机号码或邮箱登录，并且密码正确
//            throw new ServiceException().setCode(ExceptionEnums.ERROR.getCode()).setMessage("您未设置密码，请使用其他方式登录后，再进行密码设置");
//            return Constant.LOGIN_ERROR;
        } else {// 密码不正确
            return Constant.LOGIN_ERROR;
        }
    }

    /**
     * 更新设备系统
     *
     * @param userAccount  用户对象
     * @param deviceSystem 设备系统
     * @throws ServiceException
     */
    public void updateDeviceSystem(UserAccount userAccount, String deviceSystem) throws ServiceException {
        if (ToolsKit.isEmpty(userAccount) || ToolsKit.isEmpty(deviceSystem)) {
            return;
        }
        userAccount.setDeviceSystem(deviceSystem);
        this.save(userAccount);
    }

    public void updateNationCode(UserAccount userAccount, String nationCode) throws ServiceException {
        if (ToolsKit.isEmpty(userAccount) || ToolsKit.isEmpty(nationCode)) {
            return;
        }
        if (ToolsKit.isEmpty(userAccount.getNationCode())) {
            userAccount.setNationCode(nationCode);
            this.save(userAccount);
        }
    }

    /**
     * 更新第三方昵称
     *
     * @param userAccount 用户对象
     * @param name        第三方昵称
     * @param loginway    登录途径
     * @throws ServiceException
     */
    public void updateBindName(UserAccount userAccount, String name, String loginway) throws ServiceException {
        try {
            if (ToolsKit.isEmpty(userAccount) || ToolsKit.isEmpty(loginway) || "LOGIN_BY_MOBILE".equals(loginway)) {
                return;
            }
            Map<String, String> bindname = parseBindNameJson(userAccount.getBindName());
            if (ToolsKit.isEmpty(bindname)) {
                bindname = new HashMap<String, String>();
            }
            boolean isUpdate = false;
            if (ToolsKit.isEmpty(bindname.get(loginway)) && ToolsKit.isNotEmpty(name)) {
                bindname.put(loginway, name);
                isUpdate = true;
            } else {
                if (ToolsKit.isEmpty(name)) {
                    bindname.remove(loginway);
                } else if (!bindname.get(loginway).equals(name)) {
                    bindname.put(loginway, name);
                    isUpdate = true;
                }
            }
            if (isUpdate) {
                userAccount.setBindName(bindNameToJson(bindname));
                this.save(userAccount);
            }
        } catch (Exception e) {
            throw new ServiceException().setCode(ExceptionEnums.ERROR.getCode()).setMessage("更新第三方昵称出错");
        }
    }

    /**
     * 保存默认用户信息
     *
     * @param moblieId     设备ID
     * @param deviceSystem 描述
     * @param account      账号
     * @param pwd          密码
     * @param loginway     登录途径
     * @return 用户对象
     * @throws Exception
     */
    public UserAccount saveDefaultUser(String appId, String moblieId, String deviceSystem, String account, String pwd, String loginway, String name,
                                       String phone) throws Exception {
        UserAccount userAccount = new UserAccount();
        ToolsKit.setIdEntityData(userAccount, "admin");
        userAccount.setDeviceSystem(deviceSystem);
        userAccount.setMoblieId(moblieId);
        userAccount.setAppId(appId);
        if (ToolsKit.isNotEmpty(account)) {
            userAccount.setMobileAccount(phone);
            if (ToolsConst.LOGIN_BY_WEIXIN.equals(loginway)) {
                userAccount.setWeixinaccount(account);
            } else if (ToolsConst.LOGIN_BY_QQ.equals(loginway)) {
                userAccount.setQqaccount(account);
            } else if (ToolsConst.LOGIN_BY_SINA.equals(loginway)) {
                userAccount.setSinaaccount(account);
            } else if (ToolsConst.LOGIN_BY_MOBILE.equals(loginway)) {
                // byte[] salt = WebTools.buildEntryptSalt();
                // userAccount.setSalt(Encodes.encodeHex(salt));
                // userAccount.setPwd(WebTools.buildEntryptPassword(pwd, salt));
                userAccount.setMobileAccount(account);
            } else if (Constant.LOGIN_BY_MAIL.equals(loginway)) {
                userAccount.setMailAccount(account);
            } else if (ToolsConst.LOGIN_BY_FACEBOOK.equals(loginway)) {
                userAccount.setFbaccount(account);
            } else if (ToolsConst.LOGIN_BY_TWITTER.equals(loginway)) {
                userAccount.setTwaccount(account);
            } else if (ToolsConst.LOGIN_BY_WHATSAPP.equals(loginway)) {
                userAccount.setWaaccount(account);
            } else if (ToolsConst.LOGIN_BY_GOOGLE.equals(loginway)) {
                userAccount.setGoogleaccount(account);
            } else if (ToolsConst.LOGIN_BY_APPLEID.equals(loginway)) {
                userAccount.setAppleaccount(account);
            }
            if (!ToolsConst.LOGIN_BY_MOBILE.equals(loginway) && ToolsKit.isNotEmpty(name)) {// 如果是第三方登录的并且名字不为空的，记录名称
                Map<String, String> bindname = new HashMap<String, String>();
                bindname.put(loginway, name);
                userAccount.setBindName(bindNameToJson(bindname));
            }
        }
        this.save(userAccount);
        return userAccount;
    }

    /**
     * 保存默认用户信息 Add by RabyGao 2019-10-16
     *
     * @param moblieId     设备ID
     * @param deviceSystem 描述
     * @param account      账号
     * @param pwd          密码
     * @param loginway     登录途径
     * @return 用户对象
     * @throws Exception
     */
    public UserAccount saveDefaultUserOnReset(String appId, String moblieId, String deviceSystem, String account, String pwd, String loginway, String name,
                                              String phone) throws Exception {
        UserAccount userAccount = new UserAccount();
        ToolsKit.setIdEntityData(userAccount, "admin");
        userAccount.setDeviceSystem(deviceSystem);
        userAccount.setMoblieId(moblieId);
        userAccount.setAppId(appId);
        if (ToolsKit.isNotEmpty(account)) {
            userAccount.setMobileAccount(phone);
            if (ToolsConst.LOGIN_BY_WEIXIN.equals(loginway)) {
                userAccount.setWeixinaccount(account);
            } else if (ToolsConst.LOGIN_BY_QQ.equals(loginway)) {
                userAccount.setQqaccount(account);
            } else if (ToolsConst.LOGIN_BY_SINA.equals(loginway)) {
                userAccount.setSinaaccount(account);
            } else if (ToolsConst.LOGIN_BY_MOBILE.equals(loginway)) {
                byte[] saltByte = WebTools.buildEntryptSalt();
                userAccount.setSalt(Encodes.encodeHex(saltByte));
                userAccount.setPwd(WebTools.buildEntryptPassword(pwd, saltByte));

                userAccount.setMobileAccount(account);
            } else if (Constant.LOGIN_BY_MAIL.equals(loginway)) {
                byte[] saltByte = WebTools.buildEntryptSalt();
                userAccount.setSalt(Encodes.encodeHex(saltByte));
                userAccount.setPwd(WebTools.buildEntryptPassword(pwd, saltByte));

                userAccount.setMailAccount(account);
            } else if (ToolsConst.LOGIN_BY_FACEBOOK.equals(loginway)) {
                userAccount.setFbaccount(account);
            } else if (ToolsConst.LOGIN_BY_TWITTER.equals(loginway)) {
                userAccount.setTwaccount(account);
            } else if (ToolsConst.LOGIN_BY_WHATSAPP.equals(loginway)) {
                userAccount.setWaaccount(account);
            } else if (ToolsConst.LOGIN_BY_GOOGLE.equals(loginway)) {
                userAccount.setGoogleaccount(account);
            } else if (ToolsConst.LOGIN_BY_APPLEID.equals(loginway)) {
                userAccount.setAppleaccount(account);
            }
            if (!ToolsConst.LOGIN_BY_MOBILE.equals(loginway) && ToolsKit.isNotEmpty(name)) {// 如果是第三方登录的并且名字不为空的，记录名称
                Map<String, String> bindname = new HashMap<String, String>();
                bindname.put(loginway, name);
                userAccount.setBindName(bindNameToJson(bindname));
            }
        }
        this.save(userAccount);
        return userAccount;
    }

    public static void main(String[] args) {
        byte[] salt = WebTools.buildEntryptSalt();
        System.out.println(Encodes.encodeHex(salt));
        System.out.println(WebTools.buildEntryptPassword("123456a", salt));
    }

    /***
     * 增加一些登录的获取信息
     * @param userId
     * @param headInfoDto
     */
    public void updateUserLoginInfo(String userId, HeadInfoDto headInfoDto) {
        try {
            userLoginHistoryBuService.addOrUpdate(userId, headInfoDto.getVersion(), headInfoDto.getPhoneType());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 检测同个设备码短时间内是否重复注册
     *
     * @param mobileId
     * @throws ServiceException
     */
    public void checkRegisterMobile(String mobileId, Locale locale) throws ServiceException {
        if (ToolsKit.isNotEmpty(cacheCommonTools.getCacheFlag("reg:mbid:", mobileId))) {
            throw new ServiceException().setCode(ExceptionEnums.ERROR.getCode()).setMessage(i18nUtils.getKey(LocalTools.toast_reg_repeat, locale));
        } else {
            cacheCommonTools.saveCacheFlag("reg:mbid:", mobileId, 5);
        }
    }


    /**
     * 验证账号状态信息
     *
     * @param account  账号
     * @param loginway 登录方式
     * @param ip       ip
     * @throws ServiceException
     */
    public void verifyAccount(String account, String loginway, String ip) throws ServiceException {
        if (ToolsKit.isEmpty(account)) {
            throw new ServiceException().setCode(ExceptionEnums.ERROR.getCode()).setMessage("无效的账号");
        }
        if (getSystemStatus(SystemStatusEnums.LOGIN_IP_COUNT.getKey(), ip)) {
            throw new ServiceException().setCode(ExceptionEnums.ERROR.getCode()).setMessage("您操作太频繁，请明天再试");
        }
        if (Constant.LOGIN_BY_MOBILE.equals(loginway) && getSystemStatus(SystemStatusEnums.PWD_ERROR_COUNT.getKey(), account)) {
            throw new ServiceException().setCode(ExceptionEnums.ERROR.getCode()).setMessage("您的账号已被锁定，请明天再试");
        }
        if (Constant.LOGIN_BY_MOBILE.equals(loginway) && getSystemStatus(SystemStatusEnums.REGISTER_COUNT.getKey(), account)) {
            throw new ServiceException().setCode(ExceptionEnums.ERROR.getCode()).setMessage("您的注册行为异常，请明天再试");
        }
    }

    /**
     * 获取状态信息
     *
     * @param key     标识KEY
     * @param account 账号
     * @return
     * @throws ServiceException
     */
    public boolean getSystemStatus(String key, String account) throws ServiceException {
        try {
            SystemStatusDto systemStatusDto = systemStatusService.getStatusCount(key, account);
            return systemStatusDto.getStatus();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    /**
     * 绑定设备ID
     *
     * @param userId 用户ID
     * @throws ServiceException
     */
    public void bindingUdid(String userId, String udid, Locale locale) throws ServiceException {
        if (ToolsKit.isEmpty(userId)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage(i18nUtils.getKey(LocalTools.toast_userid_notblank, locale));
        }
        if (ToolsKit.isEmpty(udid)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage(i18nUtils.getKey(LocalTools.toast_deviceid_notblank, locale));
        }
        UserAccount userAccount = this.getUserAccountByUserId(userId);
        if (ToolsKit.isEmpty(userAccount)) {
            throw new ServiceException().setCode(ExceptionEnums.ERROR.getCode()).setMessage(i18nUtils.getKey(LocalTools.toast_user_notexist, locale));
        }
        userAccount.setUdid(udid);
        this.save(userAccount);
    }

    /**
     * 绑定账号
     *
     * @param userId   用户ID
     * @param account  账号
     * @param loginway 途径
     * @param name     第三方登录昵称
     * @return
     */
    public void bindingAccount(String userId, String account, String loginway, String name, String openId, String unionId, String from, Locale locale)
            throws ServiceException {
        try {
            if (ToolsKit.isEmpty(userId)) {
                throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage(i18nUtils.getKey(LocalTools.toast_userid_notblank, locale));
            }
            if (ToolsKit.isEmpty(account)) {
                throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage(i18nUtils.getKey(LocalTools.toast_acc_old_notblank, locale));
            }
            if (ToolsKit.isEmpty(loginway)) {
                throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage(i18nUtils.getKey(LocalTools.toast_bindmethod_notblank, locale));
            }
            String bandedUserId = this.getBandedUserId(account, loginway, unionId, locale);
            if (ToolsKit.isNotEmpty(bandedUserId)) {// 查询是否已经存在账号信息
                UserAccount userAccount = this.getUserAccountByUserId(userId);
                if (ToolsKit.isNotEmpty(userAccount)) {
//                    UserInfo userInfo = userInfoBuService.getUserInfoByUserId(userId);
                    Map<String, String> bindNameMap = parseBindNameJson(userAccount.getBindName());
                    String bindName = this.getName(bindNameMap, loginway, StringUtils.EMPTY);
                    // 优化微信被绑定提示文案
                    // 优化文案
//                    String msg = i18nUtils.getKey(LocalTools.toast_acc_bound1, locale).replaceAll("Tom", bindName + "-" + userInfo.getCodeId());
//                    throw new ServiceException().setCode(ExceptionEnums.ERROR.getCode()).setMessage(msg);
                    String thirdNameByLoginway = getThirdNameByLoginway(loginway, locale);
//                    String msg = "该" + thirdNameByLoginway +"已被其他账号绑定！";
                    String msg = i18nUtils.getKey(LocalTools.toast_acc_bound, locale);
//                    String msg = i18nUtils.getKey(LocalTools.toast_acc_bound1, locale).replaceAll("Tom", bindName + "-" + userInfo.getCodeId());
                    throw new ServiceException().setCode(ExceptionEnums.ERROR.getCode()).setMessage(msg);
                }//toast_acc_bound
                throw new ServiceException().setCode(ExceptionEnums.ERROR.getCode()).setMessage(i18nUtils.getKey(LocalTools.toast_user_notexist, locale));
            } else {
                UserAccount userAccount = this.getUserAccountByUserId(userId);
                if (ToolsKit.isEmpty(userAccount)) { //toast_user_notexist
                    throw new ServiceException().setCode(ExceptionEnums.ERROR.getCode()).setMessage(i18nUtils.getKey(LocalTools.toast_user_notexist, locale));
                } else {
                    if (Constant.LOGIN_BY_WEIXIN.equals(loginway)) {
                        if (ToolsKit.isNotEmpty(openId) && ToolsKit.isNotEmpty(unionId)) {
                            userThirdPartyAuthBuService.autoRelevance(loginway, userId, openId, unionId, account, from, locale);
                        }
                        if (ToolsKit.isNotEmpty(userAccount.getWeixinaccount())) {
                            userAccountCacheService.delUserAccountName(UserAccount.WEIXINACCOUNT_FIELD, userAccount.getWeixinaccount());
                        }
                        userAccount.setWeixinaccount(account);
                    } else if (Constant.LOGIN_BY_SINA.equals(loginway)) {
                        if (ToolsKit.isNotEmpty(userAccount.getSinaaccount())) {
                            userAccountCacheService.delUserAccountName(UserAccount.SINAACCOUNT_FIELD, userAccount.getSinaaccount());
                        }
                        userAccount.setSinaaccount(account);
                    } else if (Constant.LOGIN_BY_QQ.equals(loginway)) {
                        if (ToolsKit.isNotEmpty(userAccount.getQqaccount())) {
                            userAccountCacheService.delUserAccountName(UserAccount.QQACCOUNT_FIELD, userAccount.getQqaccount());
                        }
                        userAccount.setQqaccount(account);
                    } else if (Constant.LOGIN_BY_MOBILE.equals(loginway)) {
                        if (ToolsKit.isNotEmpty(userAccount.getMobileAccount())) {
                            userAccountCacheService.delUserAccountName(UserAccount.MOBILE_ACCOUNT_FIELD, userAccount.getMobileAccount());
                        }
                        userAccount.setMobileAccount(account);
                    } else if (Constant.LOGIN_BY_MAIL.equals(loginway)) {
                        if (ToolsKit.isNotEmpty(userAccount.getMailAccount())) {
                            userAccountCacheService.delUserAccountName(UserAccount.MAIL_ACCOUNT_FIELD, userAccount.getMailAccount());
                        }
                        userAccount.setMailAccount(account);
                    } else if (Constant.LOGIN_BY_FACEBOOK.equals(loginway)) {
                        if (ToolsKit.isNotEmpty(userAccount.getFbaccount())) {
                            userAccountCacheService.delUserAccountName(UserAccount.FBACCOUNT_FIELD, userAccount.getFbaccount());
                        }
                        userAccount.setFbaccount(account);
                    } else if (Constant.LOGIN_BY_TWITTER.equals(loginway)) {
                        if (ToolsKit.isNotEmpty(userAccount.getTwaccount())) {
                            userAccountCacheService.delUserAccountName(UserAccount.TWACCOUNT_FIELD, userAccount.getTwaccount());
                        }
                        userAccount.setTwaccount(account);
                    } else if (Constant.LOGIN_BY_GOOGLE.equals(loginway)) {
                        if (ToolsKit.isNotEmpty(userAccount.getGoogleaccount())) {
                            userAccountCacheService.delUserAccountName(UserAccount.GOOGLEACCOUNT_FIELD, userAccount.getGoogleaccount());
                        }
                        userAccount.setGoogleaccount(account);
                    } else if (Constant.LOGIN_BY_APPLEID.equals(loginway)) {
                        if (ToolsKit.isNotEmpty(userAccount.getAppleaccount())) {
                            userAccountCacheService.delUserAccountName(UserAccount.APPLEACCOUNT_FIELD, userAccount.getAppleaccount());
                        }
                        userAccount.setAppleaccount(account);
                    }
                    if (!Constant.LOGIN_BY_MOBILE.equals(loginway) && ToolsKit.isNotEmpty(name)) {// 如果是第三方登录的并且名字不为空的，记录名称
                        Map<String, String> bindname = parseBindNameJson(userAccount.getBindName());
                        if (ToolsKit.isEmpty(bindname)) {
                            bindname = new HashMap<String, String>();
                        }
                        bindname.put(loginway, name);
                        userAccount.setBindName(bindNameToJson(bindname));
                    }
                    this.save(userAccount);
                }
            }
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException().setCode(ExceptionEnums.ERROR.getCode()).setMessage(i18nUtils.getKey(LocalTools.oast_acc_bindfail, locale));
        }
    }

    private String getThirdNameByLoginway(String loginway, Locale locale) {
        String result = "ID";
        switch (loginway) {
            case Constant.LOGIN_BY_WEIXIN:
                result = "微信";
                break;
            case Constant.LOGIN_BY_APPLEID:
                result = "苹果id";
                break;
            case Constant.LOGIN_BY_FACEBOOK:
                result = "facebook";
                break;
            case Constant.LOGIN_BY_QQ:
                result = "qq";
                break;
            case Constant.LOGIN_BY_SINA:
                result = "新浪微博";
                break;
            case Constant.LOGIN_BY_TWITTER:
                result = "特推";
                break;
            case Constant.LOGIN_BY_GOOGLE:
                result = "谷歌";
                break;
            default:
        }
        return result;
    }

    /**
     * 根据绑定途径，判断账号是否被绑定过
     *
     * @param account  账号
     * @param loginway 绑定途径
     * @return 账号是否被绑定过
     */
    public String getBandedUserId(String account, String loginway, String unionId, Locale locale) {
        // 微信unionId判断是否已绑定
        if (Constant.LOGIN_BY_WEIXIN.equals(loginway) && ToolsKit.isNotEmpty(unionId)) {
            return userThirdPartyAuthBuService.getUserIdByWeChatUnionId(unionId, locale);
        }
        UserAccount userAccount = this.getUserAccountByAccount(account, loginway);
        if (ToolsKit.isNotEmpty(userAccount)) {
            return userAccount.getId();
        } else {
            return null;
        }
    }

    /**
     * 用户重置密码
     *
     * @param account 账号
     * @param pwd     重置的密码
     * @return 提示语 @since4.0
     */
    public void resetUserPwd(String account, String pwd, Locale locale) throws ServiceException {
        UserAccount userAccount = this.getUserAccountByAccount(account, Constant.LOGIN_BY_MOBILE);
        if (ToolsKit.isEmpty(userAccount)) {
            throw new ServiceException().setCode(ExceptionEnums.ERROR.getCode()).setMessage(i18nUtils.getKey(LocalTools.toast_acc_notexist, locale));
        } else {
            try {
                byte[] saltByte = WebTools.buildEntryptSalt();
                String password = WebTools.buildEntryptPassword(pwd, saltByte);
                String salt = Encodes.encodeHex(saltByte);
                userAccount.setPwd(password);
                userAccount.setSalt(salt);
                this.save(userAccount);
            } catch (Exception e) {
                e.printStackTrace();
                throw new ServiceException().setCode(ExceptionEnums.ERROR.getCode()).setMessage(i18nUtils.getKey(LocalTools.toast_resetpwd_error, locale));
            }
        }
    }

    /**
     * 第三方登录后绑定手机并设置密码（用于 XColor 应用） Add by RabyGao 2019-10-22
     */
    public void bindMobilePassForXColor(String userId, String account, String pwd, String mobileId) throws ServiceException {
        if (ToolsKit.isEmpty(userId)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("用户ID不能为空！");
        }
        UserAccount userAccount = this.getUserAccountByAccount(account, Constant.LOGIN_BY_MOBILE);
        if (ToolsKit.isNotEmpty(userAccount) && !userAccount.getId().equals(userId)) {
            throw new ServiceException().setCode(ExceptionEnums.ERROR.getCode()).setMessage("手机号已经被绑定过！");
        }
        userAccount = this.getUserAccountByUserId(userId);
        if (ToolsKit.isEmpty(userAccount)) {
            throw new ServiceException().setCode(ExceptionEnums.ERROR.getCode()).setMessage("用户不存在！");
        }

        try {
            byte[] saltByte = WebTools.buildEntryptSalt();
            String password = WebTools.buildEntryptPassword(pwd, saltByte);
            String salt = Encodes.encodeHex(saltByte);
            userAccount.setPwd(password);
            userAccount.setSalt(salt);
            userAccount.setMoblieId(mobileId);
            userAccount.setMobileAccount(account);
            this.save(userAccount);
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException().setCode(ExceptionEnums.ERROR.getCode()).setMessage("绑定手机设置密码时出错");
        }
    }

    /**
     * 解绑方法
     *
     * @param userId
     * @param loginway
     */
    public void unbinding(String userId, String loginway, String unionId, String openId, Locale locale) throws ServiceException {
        if (Constant.LOGIN_BY_MOBILE.equals(loginway)) {
            throw new ServiceException().setCode(ExceptionEnums.ERROR.getCode()).setMessage(i18nUtils.getKey(LocalTools.toast_mobile_not_unbound, locale));
        }
        if (Constant.LOGIN_BY_MAIL.equals(loginway)) {
            throw new ServiceException().setCode(ExceptionEnums.ERROR.getCode()).setMessage(i18nUtils.getKey(LocalTools.toast_email_notunbind, locale));
        }
        if (ToolsKit.isEmpty(userId)) {
            throw new ServiceException().setCode(ExceptionEnums.ERROR.getCode()).setMessage(i18nUtils.getKey(LocalTools.toast_userid_notblank, locale));
        }
        UserAccount userAccount = this.getUserAccountByUserId(userId);
        if (ToolsKit.isEmpty(userAccount)) {
            throw new ServiceException().setCode(ExceptionEnums.ERROR.getCode()).setMessage(i18nUtils.getKey(LocalTools.toast_acc_obtainfail, locale));
        }
        if (this.bindingCount(userAccount) <= 1) {
            throw new ServiceException().setCode(ExceptionEnums.ERROR.getCode()).setMessage(i18nUtils.getKey(LocalTools.toast_acc_preserve_onebind, locale));
        }

        if (Constant.LOGIN_BY_APPLEID.equals(loginway)) {
            userAccountCacheService.delUserAccountName(UserAccount.APPLEACCOUNT_FIELD, userAccount.getAppleaccount());
            userAccount.setAppleaccount(StringUtils.EMPTY);
        } else {
            switch (loginway) {
                case Constant.LOGIN_BY_WEIXIN:
                    userAccountCacheService.delUserAccountName(UserAccount.WEIXINACCOUNT_FIELD, userAccount.getWeixinaccount());
                    userAccount.setWeixinaccount(StringUtils.EMPTY);
                    userAccount.setWeixinpwd(StringUtils.EMPTY);
                    userThirdPartyAuthBuService.unbinding(loginway, userId, unionId, locale);
                    break;
                case Constant.LOGIN_BY_QQ:
                    userAccountCacheService.delUserAccountName(UserAccount.QQACCOUNT_FIELD, userAccount.getQqaccount());
                    userAccount.setQqaccount(StringUtils.EMPTY);
                    userAccount.setQqpwd(StringUtils.EMPTY);
                    break;
                case Constant.LOGIN_BY_SINA:
                    userAccountCacheService.delUserAccountName(UserAccount.SINAACCOUNT_FIELD, userAccount.getSinaaccount());
                    userAccount.setSinaaccount(StringUtils.EMPTY);
                    userAccount.setSinapwd(StringUtils.EMPTY);
                    break;
                case Constant.LOGIN_BY_FACEBOOK:
                    userAccountCacheService.delUserAccountName(UserAccount.FBACCOUNT_FIELD, userAccount.getFbaccount());
                    userAccount.setFbaccount(StringUtils.EMPTY);
                    userAccount.setFbpwd(StringUtils.EMPTY);
                    break;
                case Constant.LOGIN_BY_TWITTER:
                    userAccountCacheService.delUserAccountName(UserAccount.TWACCOUNT_FIELD, userAccount.getTwaccount());
                    userAccount.setTwaccount(StringUtils.EMPTY);
                    userAccount.setTwpwd(StringUtils.EMPTY);
                    break;
                case Constant.LOGIN_BY_GOOGLE:
                    userAccountCacheService.delUserAccountName(UserAccount.GOOGLEACCOUNT_FIELD, userAccount.getGoogleaccount());
                    userAccount.setGoogleaccount(StringUtils.EMPTY);
                    userAccount.setGooglepwd(StringUtils.EMPTY);
                    break;
                default:
                    throw new ServiceException().setCode(ExceptionEnums.ERROR.getCode()).setMessage(i18nUtils.getKey(LocalTools.toast_unsupport_type, locale));
            }
        }

        try {
            this.updateBindName(userAccount, StringUtils.EMPTY, loginway);
            userAccountDao.saveOrUpdate(userAccount);
            userAccountCacheService.delUserAccount(userAccount.getId());
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException().setCode(ExceptionEnums.ERROR.getCode()).setMessage(i18nUtils.getKey(LocalTools.toast_unbind_fail, locale));
        }
    }

    /**
     * 判断绑定数量
     *
     * @param userAccount 用户账号信息
     * @return
     */
    private int bindingCount(UserAccount userAccount) {
        int count = 0;
        System.out.print(JSONObject.toJSON(userAccount));
        if (ToolsKit.isEmpty(userAccount)) {
            return count;
        }
        if (ToolsKit.isNotEmpty(userAccount.getWeixinaccount())) {
            count++;
        }
        if (ToolsKit.isNotEmpty(userAccount.getSinaaccount())) {
            count++;
        }
        if (ToolsKit.isNotEmpty(userAccount.getMobileAccount())) {
            count++;
        }
        if (ToolsKit.isNotEmpty(userAccount.getMailAccount())) {
            count++;
        }
        if (ToolsKit.isNotEmpty(userAccount.getQqaccount())) {
            count++;
        }
        if (ToolsKit.isNotEmpty(userAccount.getFbaccount())) {
            count++;
        }
        if (ToolsKit.isNotEmpty(userAccount.getTwaccount())) {
            count++;
        }
        if (ToolsKit.isNotEmpty(userAccount.getGoogleaccount())) {
            count++;
        }
        if (ToolsKit.isNotEmpty(userAccount.getAppleaccount())) {
            count++;
        }
        return count;
    }

    /**
     * 验证手机账号
     *
     * @param oldAccount 旧账号
     */
    public void checkAccount(String oldAccount, Locale locale) throws ServiceException {
        if (ToolsKit.isEmpty(oldAccount)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage(i18nUtils.getKey(LocalTools.toast_acc_old_notblank, locale));
        }
        UserAccount userAccount = this.getUserAccountByAccount(oldAccount, Constant.LOGIN_BY_MOBILE);
        if (ToolsKit.isEmpty(userAccount)) {
            throw new ServiceException().setCode(ExceptionEnums.ERROR.getCode()).setMessage(i18nUtils.getKey(LocalTools.toast_acc_notexist, locale));
        }
    }

    /**
     * 验证邮箱账号
     *
     * @param oldAccount
     * @throws ServiceException
     */
    public void checkMailAccount(String oldAccount, Locale locale) throws ServiceException {
        if (ToolsKit.isEmpty(oldAccount)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage(i18nUtils.getKey(LocalTools.toast_acc_old_notblank, locale));
        }
        UserAccount userAccount = this.getUserAccountByAccount(oldAccount, Constant.LOGIN_BY_MAIL);
        if (ToolsKit.isEmpty(userAccount)) {
            throw new ServiceException().setCode(ExceptionEnums.ERROR.getCode()).setMessage(i18nUtils.getKey(LocalTools.toast_acc_notexist, locale));
        }
    }

//    /**
//     * 获取统计信息
//     *
//     * @param startDate
//     *            开始时间
//     * @param endDate
//     *            结束时间
//     * @return
//     */
//    public StatisticsDto<Long> getAnalysisDtoList(String title, String startDate, String endDate) throws ServiceException {
//        MonthStatisticsStorage<Long> dayStatisticsStorage = new MonthStatisticsStorage<Long>() {
//            @Override
//            public StatisticsDto<Long> getStatisticsDto(Date dayTime) {
//                StatisticsDto<Long> statisticsDto = new StatisticsDto<Long>();
//                statisticsDto.setTitle(title);
//                statisticsDto.setCategories(ToolsKit.Date.getDateList(dayTime));
//                return statisticsDto;
//            }
//
//            @Override
//            public void addByDay(StatisticsDto<Long> statisticsVo, Date dayTime, Object params) {
//                statisticsVo.getItems().get(0).getData().add(getDaysCount(ToolsKit.Date.getDateToMin(dayTime), ToolsKit.Date.getDateToMax(dayTime)));
//            }
//
//            @Override
//            public void setByDay(StatisticsDto<Long> statisticsVo, Date dayTime, int index, Object params) {
//                statisticsVo.getItems().get(0).getData().set(index, getDaysCount(ToolsKit.Date.getDateToMin(dayTime), ToolsKit.Date.getDateToMax(dayTime)));
//            }
//
//            @Override
//            public void addByMonth(StatisticsDto<Long> statisticsVo, Date monthDate, Object params) {
//                StatisticsItemDto<Long> statisticsItemVo = new StatisticsItemDto<Long>();
//                statisticsItemVo.setData(getDataByMonthDate(monthDate, null));// 读取数据
//                statisticsItemVo.setName("注册量");
//                statisticsVo.getItems().add(statisticsItemVo);
//            }
//
//            @Override
//            public Long getCountByDay(Date startDate, Date endDate, Object params) {
//                return getDaysCount(startDate, endDate);
//            }
//        };
//        return dayStatisticsStorage.getMonthStatistics(StringUtils.EMPTY,
//                ToolsKit.Date.parse(startDate + ToolsConst.START_TIME, DatePattern.NORM_DATETIME_PATTERN),
//                ToolsKit.Date.parse(endDate + ToolsConst.END_TIME, DatePattern.NORM_DATETIME_PATTERN), "account:count", baseStatisticsCacheService);
//    }

    /**
     * 查询统计数量
     *
     * @return
     */
    public Long getDaysCount(Date startDate, Date endDate) {
        return userAccountDao.getDaysCount(startDate, endDate);
    }

    // -----------------

    public void removeUsers(String mobiles, String loginway, String unionid) throws Exception {
        if (mobiles == null) {
            throw new ServiceException().setCode(ExceptionEnums.ERROR.getCode()).setMessage("mobiles为空");
        }

        String fieldName = UserAccount.MOBILE_ACCOUNT_FIELD;
        if (loginway.equals(ToolsConst.LOGIN_BY_SINA)) {
            fieldName = UserAccount.SINAACCOUNT_FIELD;
        } else if (loginway.equals(ToolsConst.LOGIN_BY_QQ)) {
            fieldName = UserAccount.QQACCOUNT_FIELD;
        } else if (loginway.equals(Constant.LOGIN_BY_MAIL)) {
            fieldName = UserAccount.MAIL_ACCOUNT_FIELD;
        } else if (loginway.equals(ToolsConst.LOGIN_BY_FACEBOOK)) {
            fieldName = UserAccount.FBACCOUNT_FIELD;
        } else if (loginway.equals(ToolsConst.LOGIN_BY_TWITTER)) {
            fieldName = UserAccount.TWACCOUNT_FIELD;
        } else if (loginway.equals(ToolsConst.LOGIN_BY_WHATSAPP)) {
            fieldName = UserAccount.WAACCOUNT_FIELD;
        } else if (loginway.equals(ToolsConst.LOGIN_BY_GOOGLE)) {
            fieldName = UserAccount.GOOGLEACCOUNT_FIELD;
        } else if (loginway.equals(ToolsConst.LOGIN_BY_APPLEID)) {
            fieldName = UserAccount.APPLEACCOUNT_FIELD;
        } else if (loginway.equals(ToolsConst.LOGIN_BY_WEIXIN)) {
            fieldName = UserAccount.WEIXINACCOUNT_FIELD;
        }

        String[] mobileList = mobiles.split(",");
        for (String account : mobileList) {
            if (StringUtils.isNotBlank(unionid)) {
                userThirdPartyAuthCache.delCacheByWeChatUnionId(unionid);
            }

            UserAccount userAccount = userAccountDao.getUserAccountByAccount(fieldName, account);
            if (userAccount != null) {
                String userId = userAccount.getId();
                if (loginway.equals(ToolsConst.LOGIN_BY_WEIXIN)) {
                    UserThirdPartyAuth userThirdPartyAuth = userThirdPartyAuthBuService.getUserThirdPartyAuthByUserId(userId, Locale.SIMPLIFIED_CHINESE);
                    if (userThirdPartyAuth != null) {
                        userThirdPartyAuth.setWechatUnionId(StringUtils.EMPTY);
                        // 清空openId - 修正为JSON字符串格式
                        userThirdPartyAuth.setWechatOpenId("{}");
                        // 删除缓存
                        userThirdPartyAuthBuService.save(userThirdPartyAuth);
                    }
                }

                userAccountCacheService.delUserAccount(userId);
                userAccountCacheService.delUserAccountName(fieldName, account);

                userAccountDao.removeById(userId);

                UserInfo userInfo = userInfoCacheService.getUserInfoByUserId(userId);
                if (userInfo != null) {
                    userInfoCacheService.delUserInfo(userInfo);
                    userInfoDao.removeById(userInfo.getId());
                }
            } else {
                throw new ServiceException().setCode(ExceptionEnums.ERROR.getCode()).setMessage("userAccount为空");
            }
        }

    }

    public void removeUsersByMail(String emails) throws Exception {
        if (emails == null) {
            return;
        }
        String[] emailList = emails.split(",");
        for (String email : emailList) {
            // Constant.LOGIN_BY_MOBILE
            UserAccount userAccount = userAccountDao.getUserAccountByAccount(UserAccount.MAIL_ACCOUNT_FIELD, email);
            if (userAccount != null) {
                String userId = userAccount.getId();
                userAccountCacheService.delUserAccount(userId);
                userAccountCacheService.delUserAccountName(UserAccount.MAIL_ACCOUNT_FIELD, userAccount.getMobileAccount());

                userAccountDao.removeById(userId);

                UserInfo userInfo = userInfoCacheService.getUserInfoByUserId(userId);
                if (userInfo != null) {
                    userInfoCacheService.delUserInfo(userInfo);
                    userInfoDao.removeById(userInfo.getId());
                }
            }
        }

    }

    public void clearUsersCaches(String mobiles, String loginway, String unionid) throws Exception {
        if (mobiles == null) {
            return;
        }

        String fieldName = UserAccount.MOBILE_ACCOUNT_FIELD;
        if (loginway.equals(ToolsConst.LOGIN_BY_SINA)) {
            fieldName = UserAccount.SINAACCOUNT_FIELD;
        } else if (loginway.equals(ToolsConst.LOGIN_BY_QQ)) {
            fieldName = UserAccount.QQACCOUNT_FIELD;
        } else if (loginway.equals(Constant.LOGIN_BY_MAIL)) {
            fieldName = UserAccount.MAIL_ACCOUNT_FIELD;
        } else if (loginway.equals(ToolsConst.LOGIN_BY_FACEBOOK)) {
            fieldName = UserAccount.FBACCOUNT_FIELD;
        } else if (loginway.equals(ToolsConst.LOGIN_BY_TWITTER)) {
            fieldName = UserAccount.TWACCOUNT_FIELD;
        } else if (loginway.equals(ToolsConst.LOGIN_BY_WHATSAPP)) {
            fieldName = UserAccount.WAACCOUNT_FIELD;
        } else if (loginway.equals(ToolsConst.LOGIN_BY_GOOGLE)) {
            fieldName = UserAccount.GOOGLEACCOUNT_FIELD;
        } else if (loginway.equals(ToolsConst.LOGIN_BY_APPLEID)) {
            fieldName = UserAccount.APPLEACCOUNT_FIELD;
        } else if (loginway.equals(ToolsConst.LOGIN_BY_WEIXIN)) {
            fieldName = UserAccount.WEIXINACCOUNT_FIELD;
        }

        String[] mobileList = mobiles.split(",");
        for (String account : mobileList) {
            // Constant.LOGIN_BY_MOBILE
            UserAccount userAccount = userAccountDao.getUserAccountByAccount(fieldName, account);
            if (userAccount != null) {
                String userId = userAccount.getId();
                userAccountCacheService.delUserAccount(userId);
                userAccountCacheService.delUserAccountName(fieldName, account);
                UserInfo userInfo = userInfoCacheService.getUserInfoByUserId(userId);
                if (userInfo != null) {
                    userInfoCacheService.delUserInfo(userInfo);
                }
            }
        }
    }

    public void clearUsersCachesByMail(String emails) throws Exception {
        if (emails == null) {
            return;
        }
        String[] emailList = emails.split(",");
        for (String email : emailList) {
            // Constant.LOGIN_BY_MOBILE
            UserAccount userAccount = userAccountDao.getUserAccountByAccount(UserAccount.MAIL_ACCOUNT_FIELD, email);
            if (userAccount != null) {
                String userId = userAccount.getId();
                userAccountCacheService.delUserAccount(userId);
                userAccountCacheService.delUserAccountName(UserAccount.MAIL_ACCOUNT_FIELD, userAccount.getMobileAccount());
                UserInfo userInfo = userInfoCacheService.getUserInfoByUserId(userId);
                if (userInfo != null) {
                    userInfoCacheService.delUserInfo(userInfo);
                }
            }
        }
    }

    public Map<String, String> getUserIds(String mobiles) throws Exception {
        Map<String, String> map = new HashMap<>();
        if (mobiles == null) {
            return map;
        }
        String[] mobileList = mobiles.split(",");
        for (String mobile : mobileList) {
            // Constant.LOGIN_BY_MOBILE
            UserAccount userAccount = userAccountDao.getUserAccountByAccount(UserAccount.MOBILE_ACCOUNT_FIELD, mobile);
            if (userAccount != null) {
                String userId = userAccount.getId();
                map.put(mobile, userId);
            }
        }

        return map;
    }

    // ----------- 邮箱注册登录 ------------

    /**
     * 获取邮箱验证码
     *
     * @param mailAddr - 邮箱地址 i18nUtils.getKey(LocalTools.toast_mobile_invalid, locale)
     * @param ip       - IP地址
     * @return
     * @throws ServiceException
     */
    public void message(String mailAddr, String ip, String captcha, Boolean overseas, Locale locale) {
        try {
            if (ToolsKit.isEmpty(mailAddr)) {
                throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage(i18nUtils.getKey(LocalTools.toast_email_notempty, locale));
            }
            String time = smsMessageCacheService.getMailCodeIntervalTime(mailAddr);
            if (ToolsKit.isEmpty(captcha) && ToolsKit.isNotEmpty(time)) {
                // 60秒内不能重复发送邮箱验证码
                /*throw new ServiceException().setCode(ExceptionEnums.ERROR.getCode())
                        .setMessage("您操作太频繁，请" + smsMessageCacheService.getIntervalTime(mailAddr) + "秒后再试");*/
//                throw new ServiceException().setCode(ExceptionEnums.ERROR.getCode())
//                        .setMessage(i18nUtils.getKey(LocalTools.toast_opbusy_5sretry, locale));
            }
            String ipCount = smsMessageCacheService.getDayIpTotalNum(ip);
            if (ToolsKit.isNotEmpty(ipCount) && Integer.parseInt(ipCount) >= Constant.SMS_IP_MAX_RETRY) {
                // 超出单日IP访问次数
                throw new ServiceException().setCode(ExceptionEnums.ERROR.getCode()).setMessage(i18nUtils.getKey(LocalTools.toast_op_freq_try, locale));
            }
            String smsCount = smsMessageCacheService.getDayMaxTotalNum(mailAddr);
            if (ToolsKit.isNotEmpty(smsCount) && Integer.parseInt(smsCount) >= Constant.DAY_SMS_MAX_TOTAL_NUM) {
                // 超出单日验证码次数
                throw new ServiceException().setCode(ExceptionEnums.MSG_MAX_ERROR.getCode()).setMessage(i18nUtils.getKey(LocalTools.toast_op_freq_try, locale));
            }
            String count = smsMessageCacheService.getAccountMaxRetryCount(mailAddr);
            if (ToolsKit.isEmpty(count)) {
                count = "0";
            }
            if (ToolsKit.isEmpty(ipCount)) {
                ipCount = "0";
            }
            /** 验证码次数 ：5 */
            if (ToolsKit.isEmpty(smsCount)) {
                smsCount = "0";
            }

            String msg1 = i18nUtils.getKey(LocalTools.toast_ipperday_uplimit, locale);
            String msg2 = i18nUtils.getKey(LocalTools.toast_verperday_uplimit, locale);
            String msg3 = i18nUtils.getKey(LocalTools.toast_vercode_poptimes, locale);
            System.out.println(msg1 + ipCount + "  " + msg2 + smsCount + "  " + msg3 + count);
            String code = this.getCode(6);
            smsMessageCacheService.setMsgValidatorCode(mailAddr, code);// 保存单次验证码
            smsMessageCacheService.setAccountMaxRetryCount(mailAddr);// 弹窗触发次数
            smsMessageCacheService.setDayMaxTotalNum(mailAddr);// 单日验证码上限
            smsMessageCacheService.setDayIpTotalNum(ip);// 单日IP上限
            smsMessageCacheService.setMailCodeIntervalTime(mailAddr);// 验证码调用间隔

            //  设置标题
            String mailTitle = "Verify Code - Snaptag";
            String mailBody = "Your verify code is " + code + ", it is valid within 5 minutes. Please do not disclose it to others.";
            if (locale.equals(Locale.US)) {
                mailTitle = "Verify Code - Snaptag";
                mailBody = "Your verify code is " + code + ", it is valid within 5 minutes. Please do not disclose it to others.";
            } else if (locale.equals(Locale.JAPAN)) {
                mailTitle = "ユーザ登録用認証コード - Snaptag";
                mailBody = "認証コードは" + code + "です。5分以内に有効で、他人に漏れないでください。";
            } else if (locale.equals(Locale.KOREA)) {
                mailTitle = "사용자등록인증번호 - Snaptag";
                mailBody = "인증번호 " + code + ",5분간유효,타인에게누설하지마시오";
            } else if (locale.equals(Locale.SIMPLIFIED_CHINESE)) {
                mailTitle = "用户注册验证码 - Snaptag";
                mailBody = "您的验证码 " + code + "，该验证码5分钟内有效，请勿泄漏于他人！";
            } else if (locale.equals(Locale.TRADITIONAL_CHINESE)) {
                mailTitle = "用戶註冊驗證碼 - Snaptag";
                mailBody = "您的驗證碼 " + code + "，該驗證碼5分鐘內有效，請勿泄漏於他人！";
            }
            mailSender.getMailConfig().setSubject(mailTitle);
            mailSender.send(mailAddr, mailBody);
        } catch (ServiceException e) {
            System.out.println("##########邮箱验证码发送失败: " + e.getMessage());
            throw e;
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException().setCode(ExceptionEnums.MSG_MAX_ERROR.getCode()).setMessage(i18nUtils.getKey(LocalTools.toast_opmore_aftertry, locale));
        }
    }

    /**
     * 检测邮箱验证码
     *
     * @param mailAddr 邮箱地址
     * @param mailCode 验证码
     * @return
     * @throws ServiceException
     */
    public void checkMailCode(String mailAddr, String mailCode, Locale locale) throws ServiceException {
        if (ToolsKit.isEmpty(mailAddr)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage(i18nUtils.getKey(LocalTools.toast_email_notempty, locale));
        }
        if (ToolsKit.isEmpty(mailCode)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage(i18nUtils.getKey(LocalTools.toast_vercode_notempty, locale));
        }
        try {
            SmsStatusDto smsStatusDto = new SmsStatusDto();
            boolean codeStatus = false;
            String time = smsMessageCacheService.getDayMaxTotalNum(mailAddr);
            if (ToolsKit.isEmpty(time)) {
                time = "0";
            }
            if (ToolsKit.isNotEmpty(time) && Integer.parseInt(time) > Constant.DAY_SMS_MAX_TOTAL_NUM) {
                System.out.println(i18nUtils.getKey(LocalTools.toast_vermore_aftertry, locale));
                codeStatus = false;
            }
            String valCode = smsMessageCacheService.getMsgValidatorCode(mailAddr);
            if (mailCode.equals(valCode) || "283721".equals(mailCode)) {
                // 增加   283721 万能验证码
                codeStatus = true;
            } else {
                codeStatus = false;
            }
            smsStatusDto.setCodeStatus(codeStatus);
            systemStatusService.saveStatus(SystemStatusEnums.REGISTER_COUNT.getKey(), mailAddr, String.valueOf(86400));
//            systemStatusService.saveAccountStatus(SystemStatusEnums.REGISTER_COUNT.getKey(), mailAddr, String.valueOf(ToolsConst.DAY_SECOND));
            if (!smsStatusDto.getCodeStatus()) {
                throw new ServiceException().setCode(ExceptionEnums.ERROR.getCode()).setMessage(i18nUtils.getKey(LocalTools.toast_vercode_incorrect, locale));
            }
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException().setCode(ExceptionEnums.NET_EXCEPTION.getCode()).setMessage(ExceptionEnums.NET_EXCEPTION.getMessage());
        }
    }

    /**
     * 重设邮箱帐号密码
     *
     * @param account
     * @param pwd
     * @throws ServiceException
     */
    public void resetUserMailPwd(String account, String pwd, Locale locale) throws ServiceException {
        UserAccount userAccount = this.getUserAccountByAccount(account, Constant.LOGIN_BY_MAIL);
        if (ToolsKit.isEmpty(userAccount)) {
            throw new ServiceException().setCode(ExceptionEnums.ERROR.getCode()).setMessage(i18nUtils.getKey(LocalTools.toast_acc_notexist, locale));
        } else {
            try {
                byte[] saltByte = WebTools.buildEntryptSalt();
                String password = WebTools.buildEntryptPassword(pwd, saltByte);
                String salt = Encodes.encodeHex(saltByte);
                userAccount.setPwd(password);
                userAccount.setSalt(salt);
                this.save(userAccount);
            } catch (Exception e) {
                e.printStackTrace();
                throw new ServiceException().setCode(ExceptionEnums.ERROR.getCode()).setMessage(i18nUtils.getKey(LocalTools.toast_resetpwd_error, locale));
            }
        }
    }


    /**
     * 修改用户电子邮箱
     *
     * @param userId - 用户ID
     * @param email  - 电子邮箱
     * @throws ServiceException
     */
    public void modifyUserMail(String userId, String email, Locale locale) throws ServiceException {
        String bandedUserId = this.getBandedUserId(email, Constant.LOGIN_BY_MAIL, null, locale);
        if (ToolsKit.isNotEmpty(bandedUserId) && !bandedUserId.equals(userId)) {
            // 查询是否已经存在账号信息
            throw new ServiceException().setCode(ExceptionEnums.ERROR.getCode()).setMessage(i18nUtils.getKey(LocalTools.toast_email_bound, locale));
        } else {
            UserAccount userAccount = this.getUserAccountByUserId(userId);
            if (ToolsKit.isEmpty(userAccount)) {
                throw new ServiceException().setCode(ExceptionEnums.ERROR.getCode()).setMessage(i18nUtils.getKey(LocalTools.toast_acc_notexist, locale));
            } else {
                try {

                    // 清除之前的邮箱账号缓存
                    if (ToolsKit.isNotEmpty(userAccount.getMailAccount())) {
                        this.clearUsersCachesByMail(userAccount.getMailAccount());
                    }

                    userAccount.setMailAccount(email);
                    this.save(userAccount);
                } catch (Exception e) {
                    e.printStackTrace();
                    throw new ServiceException().setCode(ExceptionEnums.ERROR.getCode()).setMessage(i18nUtils.getKey(LocalTools.toast_email_editerror, locale));
                }
            }
        }
    }

    public List<SmsCountryCodeDto> getSmsCountryCodesByLocale(Locale locale) {
        if ((locale != null && locale.equals(Locale.CHINA)) || (locale == null)) {
            // 如果为空或者为zh_CN
            // 返回简体的
            return getSmsCountryCodesCn();
        } else if ((locale != null && locale.equals(Locale.TAIWAN))) {
            return getSmsCountryCodesTw();
        } else {
            return getSmsCountryCodes();
        }
    }

    /**
     * 获取手机短信国家区域代码（繁体）
     *
     * @return
     */
    public List<SmsCountryCodeDto> getSmsCountryCodesTw() {
        if (smsCountryCodeMap_TW.size() > 0) {
            return getSmsCountryCodeTWList();
        }

        try {
            String jsonStr = HttpClientUtil.doGet(SMS_COUNTRY_CODE_TW_URL);
            if (jsonStr != null && jsonStr.length() > 0) {
                JSONObject jsonObject = JSON.parseObject(jsonStr);
                if (jsonObject != null) {
                    for (String field : jsonObject.keySet()) {
                        List<SmsCountryCodeDto> lists = new ArrayList<>();

                        JSONArray array = jsonObject.getJSONArray(field);
                        if (array != null && array.size() > 0) {
                            for (int i = 0; i < array.size(); i++) {
                                JSONObject jObj = array.getJSONObject(i);
                                if (jObj == null) {
                                    continue;
                                }
                                SmsCountryCodeDto codeDto = new SmsCountryCodeDto();
                                codeDto.setPhoneCode(jObj.getInteger("phoneCode"));
                                codeDto.setName(jObj.getString("nameZh"));
                                lists.add(codeDto);
                            }
                        }
                        smsCountryCodeMap_TW.put(field, lists);
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return getSmsCountryCodeTWList();
    }

    private List<SmsCountryCodeDto> getSmsCountryCodeTWList() {
        List<SmsCountryCodeDto> lists = new ArrayList<>();
        for (String key : smsCountryCodeMap_TW.keySet()) {
            List<SmsCountryCodeDto> tempList = smsCountryCodeMap_TW.get(key);
            for (SmsCountryCodeDto codeDto : tempList) {
                codeDto.setSort(key);
                lists.add(codeDto);
            }
        }
        return lists;
    }

    /**
     * 获取手机短信国家区域代码
     *
     * @return
     */
    public List<SmsCountryCodeDto> getSmsCountryCodesCn() {
        if (smsCountryCodeMap_CN.size() > 0) {
            return getSmsCountryCodeCNList();
        }

        try {
            String jsonStr = HttpClientUtil.doGet(SMS_COUNTRY_CODE_CN_URL);
            if (jsonStr != null && jsonStr.length() > 0) {
                JSONObject jsonObject = JSON.parseObject(jsonStr);
                if (jsonObject != null) {
                    for (String field : jsonObject.keySet()) {
                        List<SmsCountryCodeDto> lists = new ArrayList<>();

                        JSONArray array = jsonObject.getJSONArray(field);
                        if (array != null && array.size() > 0) {
                            for (int i = 0; i < array.size(); i++) {
                                JSONObject jObj = array.getJSONObject(i);
                                if (jObj == null) {
                                    continue;
                                }
                                SmsCountryCodeDto codeDto = new SmsCountryCodeDto();
                                codeDto.setPhoneCode(jObj.getInteger("phoneCode"));
                                codeDto.setName(jObj.getString("nameZh"));
                                lists.add(codeDto);
                            }
                        }
                        smsCountryCodeMap_CN.put(field, lists);
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return getSmsCountryCodeCNList();
    }

    private List<SmsCountryCodeDto> getSmsCountryCodeCNList() {
        List<SmsCountryCodeDto> lists = new ArrayList<>();
        for (String key : smsCountryCodeMap_CN.keySet()) {
            List<SmsCountryCodeDto> tempList = smsCountryCodeMap_CN.get(key);
            for (SmsCountryCodeDto codeDto : tempList) {
                codeDto.setSort(key);
                lists.add(codeDto);
            }
        }
        return lists;
    }

    /**
     * 获取手机短信国家区域代码
     *
     * @return
     */
    public List<SmsCountryCodeDto> getSmsCountryCodes() {
        if (smsCountryCodeMap.size() > 0) {
            return getSmsCountryCodeList();
        }
        try {
            String jsonStr = HttpClientUtil.doGet(SMS_COUNTRY_CODE_URL);
            if (jsonStr != null && jsonStr.length() > 0) {
                JSONObject jsonObject = JSON.parseObject(jsonStr);
                if (jsonObject != null) {
                    for (String field : jsonObject.keySet()) {
                        List<SmsCountryCodeDto> lists = new ArrayList<>();
                        JSONArray array = jsonObject.getJSONArray(field);
                        if (array != null && array.size() > 0) {
                            for (int i = 0; i < array.size(); i++) {
                                JSONObject jObj = array.getJSONObject(i);
                                if (jObj == null) {
                                    continue;
                                }

                                SmsCountryCodeDto codeDto = new SmsCountryCodeDto();
                                codeDto.setPhoneCode(jObj.getInteger("phoneCode"));
                                codeDto.setName(jObj.getString("nameEn"));
                                lists.add(codeDto);
                            }
                        }
                        smsCountryCodeMap.put(field, lists);
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return getSmsCountryCodeList();
    }

    private List<SmsCountryCodeDto> getSmsCountryCodeList() {
        List<SmsCountryCodeDto> lists = new ArrayList<>();
        for (String key : smsCountryCodeMap.keySet()) {
            List<SmsCountryCodeDto> tempList = smsCountryCodeMap.get(key);
            for (SmsCountryCodeDto codeDto : tempList) {
                codeDto.setSort(key);
                lists.add(codeDto);
            }
        }
        return lists;
    }

    /**
     * 生成验证码
     *
     * @return
     */
    public String getCode(int length) {
        Random r = new Random();
        String[] num = {"0", "1", "2", "3", "4", "5", "6", "7", "8", "9"};
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < length; i++) {
            sb.append(num[r.nextInt(num.length)]);
        }
        return sb.toString();
    }

    public boolean unRegister(String userId) {
        UserAccount userAccount = getUserAccountByUserId(userId);
        if (ToolsKit.isNotEmpty(userAccount.getMobileAccount())) {// 手机账号关联userid
            userAccountCacheService.delUserAccountName(UserAccount.MOBILE_ACCOUNT_FIELD, userAccount.getMobileAccount());
            userAccountCacheService.addUnRegisterFlag(userAccount.getMobileAccount());
        }
        if (ToolsKit.isNotEmpty(userAccount.getMailAccount())) {// 邮箱账号关联userid
            userAccountCacheService.delUserAccountName(UserAccount.MAIL_ACCOUNT_FIELD, userAccount.getMailAccount());
            userAccountCacheService.addUnRegisterFlag(userAccount.getMailAccount());
        }
        if (ToolsKit.isNotEmpty(userAccount.getSinaaccount())) {// 新浪账号关联userid
            userAccountCacheService.delUserAccountName(UserAccount.SINAACCOUNT_FIELD, userAccount.getSinaaccount());
            userAccountCacheService.addUnRegisterFlag(userAccount.getSinaaccount());
        }
        if (ToolsKit.isNotEmpty(userAccount.getQqaccount())) {// QQ账号关联userid
            userAccountCacheService.delUserAccountName(UserAccount.QQACCOUNT_FIELD, userAccount.getQqaccount());
            userAccountCacheService.addUnRegisterFlag(userAccount.getQqaccount());
        }
        if (ToolsKit.isNotEmpty(userAccount.getWeixinaccount())) {// 微信账号关联userid
            userAccountCacheService.delUserAccountName(UserAccount.WEIXINACCOUNT_FIELD, userAccount.getWeixinaccount());
            userAccountCacheService.addUnRegisterFlag(userAccount.getWeixinaccount());
        }
        if (ToolsKit.isNotEmpty(userAccount.getFbaccount())) {// 脸书账号关联userid
            userAccountCacheService.delUserAccountName(UserAccount.FBACCOUNT_FIELD, userAccount.getFbaccount());
            userAccountCacheService.addUnRegisterFlag(userAccount.getFbaccount());
        }
        if (ToolsKit.isNotEmpty(userAccount.getTwaccount())) {// 推特账号关联userid
            userAccountCacheService.delUserAccountName(UserAccount.TWACCOUNT_FIELD, userAccount.getTwaccount());
            userAccountCacheService.addUnRegisterFlag(userAccount.getTwaccount());
        }

        userAccountCacheService.delUserAccount(userId);
        userAccountDao.removeById(userId);
//        userAccountDao.removeById(userid);

        UserInfo userInfo = userInfoCacheService.getUserInfoByUserId(userId);
        if (userInfo != null) {
            userInfoCacheService.delUserInfo(userInfo);
//            userInfoDao.removeById(userInfo.getId());
            userInfoDao.removeById(userInfo.getId());
        }
        return true;
    }

    public boolean clearCache(String userId) {
        UserAccount userAccount = getUserAccountByUserId(userId);
        if (ToolsKit.isNotEmpty(userAccount.getMobileAccount())) {// 手机账号关联userid
            userAccountCacheService.delUserAccountName(UserAccount.MOBILE_ACCOUNT_FIELD, userAccount.getMobileAccount());
        }
        if (ToolsKit.isNotEmpty(userAccount.getMailAccount())) {// 邮箱账号关联userid
            userAccountCacheService.delUserAccountName(UserAccount.MAIL_ACCOUNT_FIELD, userAccount.getMailAccount());
        }
        if (ToolsKit.isNotEmpty(userAccount.getSinaaccount())) {// 新浪账号关联userid
            userAccountCacheService.delUserAccountName(UserAccount.SINAACCOUNT_FIELD, userAccount.getSinaaccount());
        }
        if (ToolsKit.isNotEmpty(userAccount.getQqaccount())) {// QQ账号关联userid
            userAccountCacheService.delUserAccountName(UserAccount.QQACCOUNT_FIELD, userAccount.getQqaccount());
        }
        if (ToolsKit.isNotEmpty(userAccount.getWeixinaccount())) {// 微信账号关联userid
            userAccountCacheService.delUserAccountName(UserAccount.WEIXINACCOUNT_FIELD, userAccount.getWeixinaccount());
        }
        if (ToolsKit.isNotEmpty(userAccount.getFbaccount())) {// 脸书账号关联userid
            userAccountCacheService.delUserAccountName(UserAccount.FBACCOUNT_FIELD, userAccount.getFbaccount());
        }
        if (ToolsKit.isNotEmpty(userAccount.getTwaccount())) {// 推特账号关联userid
            userAccountCacheService.delUserAccountName(UserAccount.TWACCOUNT_FIELD, userAccount.getTwaccount());
        }

        if (ToolsKit.isNotEmpty(userAccount.getGoogleaccount())) {// 推特账号关联userid
            userAccountCacheService.delUserAccountName(UserAccount.GOOGLEACCOUNT_FIELD, userAccount.getGoogleaccount());
        }

        userAccountCacheService.delUserAccount(userId);

        UserInfo userInfo = userInfoCacheService.getUserInfoByUserId(userId);
        if (userInfo != null) {
            userInfoCacheService.delUserInfo(userInfo);
        }
        return true;
    }

    /***
     * 判断账号是否在七天内注销过
     * @param account
     * @return
     */
    public boolean checkIsUnRegister(String account) {
        return userAccountCacheService.checkIsDeletedInWeektime(account);
    }

    public List<String> findByMobile(String mobile) {
        return userAccountDao.findByMobile(mobile);
    }


    public UserLoginDto formatUserLoginDto(UserLoginDto userLoginDto, UserAccount userAccount) {
        if (userLoginDto != null && userLoginDto.getAccountInfoDto() != null && userLoginDto.getAccountInfoDto().size() > 0 && userAccount != null) {
            if (ToolsKit.isEmpty(userLoginDto.getAccountInfoDto().get(0).getMobile())) {
                userLoginDto.getAccountInfoDto().get(0).setMobile(userAccount.getMobileAccount());
            }
            userLoginDto.getAccountInfoDto().get(0).setEmail(userAccount.getMailAccount());
        }
        return userLoginDto;
    }

    public String getUserIdByAccountLike(String account) {
        return userAccountDao.getIdByAccountLike(account);
    }
}