package net.snaptag.system.account.buservice;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DatePattern;
import net.snaptag.system.account.dao.ResourceTableDao;
import net.snaptag.system.account.entity.ResourceTable;
import net.snaptag.system.account.enums.SearchQueryEnum;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import net.snaptag.system.common.DataConst;
import net.snaptag.system.sadais.core.common.enums.ExceptionEnums;
import net.snaptag.system.sadais.core.common.exception.ServiceException;
import net.snaptag.system.sadais.util.core.ToolsKit;
import net.snaptag.system.sadais.web.dto.QueryDto;
import net.snaptag.system.sadais.web.dto.ResourceDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 资源表
 */
@Service
public class ResourceTableBuService {
    @Autowired
    private ResourceTableDao resourceTableDao;
    @Autowired
    private RoleResourceBuService roleResourceBuService;

    /**
     * 根据资源ID集合获取资源列表
     * 
     * @param resourceIds
     *            资源ID集合
     * @return
     */
    public List<ResourceTable> findResourceByIds(List<String> resourceIds) {
        return resourceTableDao.findResourceByIds(resourceIds);
    }

    /**
     * 根据ID删除用户账号
     * 
     * @param id
     * @throws ServiceException
     */
    public void del(String id) throws ServiceException {
        if (ToolsKit.isEmpty(id)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("用户账号ID不能为空");
        }
        List<String> idList = new ArrayList<>();
        idList.add(id);
        long resCount = roleResourceBuService.getCountByResource(idList);
        if (resCount > 0) {
            throw new ServiceException().setCode(ExceptionEnums.ERROR.getCode()).setMessage("资源记录正被资源使用");
        }
        resourceTableDao.removeByIds(idList);
    }

    /**
     * 获取列表信息
     * 
     * @param queryDto
     * @return
     */
    public IPage<ResourceDto> findPageList(String appId, QueryDto queryDto) throws ServiceException {
        Page<ResourceDto> result = new Page<ResourceDto>();
        try {
            Date startDate = null;
            Date endDate = null;
            if (ToolsKit.isNotEmpty(queryDto.getStartDate())) {
                startDate = ToolsKit.Date.parse(queryDto.getStartDate() + " 00:00:00", DatePattern.NORM_DATETIME_PATTERN);
            }
            if (ToolsKit.isNotEmpty(queryDto.getEndDate())) {
                endDate = ToolsKit.Date.parse(queryDto.getEndDate() + " 23:59:59", DatePattern.NORM_DATETIME_PATTERN);
            }
            if (queryDto.getPageNo() > 0) {
                queryDto.setPageNo(queryDto.getPageNo() - 1);
            }
            IPage<ResourceTable> pageList = resourceTableDao.findPageList(appId, queryDto.getPageNo(), queryDto.getPageSize(),
                    queryDto.getSearchValue().get(SearchQueryEnum.NAME.getValue()), startDate, endDate);
            result.setCurrent(pageList.getCurrent());
            result.setSize(pageList.getSize());
            result.setTotal(pageList.getTotal());
            if (ToolsKit.isNotEmpty(pageList.getRecords())) {
                List<ResourceDto> dtoList = new ArrayList<ResourceDto>();
                for (ResourceTable resourceTable : pageList.getRecords()) {
                    ResourceDto dto = new ResourceDto();
                    ToolsKit.Bean.copyProperties(resourceTable, dto);
                    dtoList.add(dto);
                }
                result.setRecords(dtoList);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }

    /**
     * 递归获取子节点菜单
     * 
     * @param resId
     * @param resourceTableList
     * @return
     */
    public List<ResourceDto> getChild(String resId, List<ResourceTable> resourceTableList) {
        List<ResourceDto> childList = new ArrayList<ResourceDto>();
        for (ResourceTable resourceTable : resourceTableList) {
            // 遍历所有节点，比较判断父节点ID是否与父ID相等
            if (ToolsKit.isNotEmpty(resourceTable.getPId()) && resourceTable.getPId().equals(resId)) {
                ResourceDto dto = new ResourceDto();
                BeanUtil.copyProperties(resourceTable, dto);
                dto.setResourceDtoList(getChild(resourceTable.getId(), resourceTableList));
                childList.add(dto);
            }
        }
        // 递归退出条件
        if (childList.size() == 0) {
            return null;
        }
        return childList;
    }

    /**
     * 新增或更新信息
     * 
     * @throws ServiceException
     */
    public void saveOrUpdate(ResourceDto resourceDto) throws ServiceException {
        if (ToolsKit.isEmpty(resourceDto.getResName())) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("名称不能为空");
        }
//        if (ToolsKit.isEmpty(resourceDto.getPath())) {
//            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("访问地址不能为空");
//        }
        ResourceTable resourceTable = null;
        if (ToolsKit.isEmpty(resourceDto.getId())) {
            resourceTable = new ResourceTable();
            resourceTable.setStatus(DataConst.DATA_SUCCESS_STATUS);
            resourceTable.setCreatetime(new Date());
            resourceTable.setCreateuserid("SYSTEM_USER_ID");
        } else {
            resourceTable = resourceTableDao.getById(resourceDto.getId());
        }
        ToolsKit.Bean.copyProperties(resourceDto, resourceTable);
        if (ToolsKit.isEmpty(resourceDto.getId())) {
            resourceTable.setId(null);
        }
        resourceTableDao.saveOrUpdate(resourceTable);
    }

    public List<ResourceDto> getResourcesTree(QueryDto queryDto) {
        return getChild("0",resourceTableDao.findListByCond(""));
    }
}
