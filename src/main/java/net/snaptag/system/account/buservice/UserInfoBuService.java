package net.snaptag.system.account.buservice;

import cn.hutool.core.date.DatePattern;
import net.snaptag.system.account.cache.UserInfoCacheService;
import net.snaptag.system.account.dao.UserInfoDao;
import net.snaptag.system.account.dto.UserInfoDto;
import net.snaptag.system.account.entity.UserInfo;
import net.snaptag.system.account.enums.PageSortTypeEnums;
import com.baomidou.mybatisplus.core.metadata.IPage;
import net.snaptag.system.sadais.core.common.enums.ExceptionEnums;
import net.snaptag.system.sadais.core.common.exception.ServiceException;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import net.snaptag.system.sadais.util.core.ToolsKit;
import net.snaptag.system.sadais.web.config.CommonProperties;
import net.snaptag.system.sadais.web.core.I18nUtils;
import net.snaptag.system.sadais.web.utils.LocalTools;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Locale;
@Service
public class UserInfoBuService {
    @Autowired
    private UserInfoDao userInfoDao;
    @Autowired
    private UserInfoCacheService userInfoCacheService;
//    @Autowired
//    private UserAccountBuService userAccountBuService;
    @Autowired
    private CommonProperties     commonProperties;

    @Autowired
    private UserLoginHistoryBuService userLoginHistoryBuService;

    @Autowired
    private I18nUtils i18nUtils;

    public String getUserId(int codeId) throws ServiceException {
        return getUserId(codeId, Locale.SIMPLIFIED_CHINESE);
    }

    /**
     * 根据用户ID获取用户信息
     * 
     * @param codeId
     *            ID
     * @return 用户信息
     * @throws ServiceException
     */
    public String getUserId(int codeId, Locale locale) throws ServiceException {
        if (codeId <= 0) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage(i18nUtils.getKey(LocalTools.toast_codeid_notblank, locale));
        }
        String userId = userInfoCacheService.getUserIdByCodeId(codeId);
        UserInfo userInfo = null;
        if (ToolsKit.isEmpty(userId)) {
            try {
                userInfo = userInfoDao.getUserInfoByCodeId(codeId);
            } catch (Exception e) {
                e.printStackTrace();
                throw new ServiceException().setCode(ExceptionEnums.ERROR.getCode()).setMessage(i18nUtils.getKey(LocalTools.toast_inquire_info_incorrect, locale));
            }
            if (ToolsKit.isNotEmpty(userInfo)) {
                userId = userInfo.getUserId();
                userInfoCacheService.saveUserInfo(userInfo);
            }
        }
        return userId;
    }

    public UserInfo getUserInfoById(String id) throws ServiceException {
        return getUserInfoById(id, Locale.SIMPLIFIED_CHINESE);
    }

    /**
     * 根据用户ID获取用户信息
     * 
     * @param id
     *            ID
     * @return 用户信息
     * @throws ServiceException
     */
    public UserInfo getUserInfoById(String id, Locale locale) throws ServiceException {
        if (ToolsKit.isEmpty(id)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage(i18nUtils.getKey(LocalTools.toast_userid_notblank, locale));
        }
        UserInfo userInfo = userInfoCacheService.getUserInfoById(id);
        if (ToolsKit.isEmpty(userInfo)) {
            try {
                userInfo = userInfoDao.getUserInfoById(id);
            } catch (Exception e) {
                e.printStackTrace();
                throw new ServiceException().setCode(ExceptionEnums.ERROR.getCode()).setMessage(i18nUtils.getKey(LocalTools.toast_inquire_info_incorrect, locale));
            }
            if (ToolsKit.isNotEmpty(userInfo)) {
                userInfoCacheService.saveUserInfo(userInfo);
            }
        }
        return userInfo;
    }

    public UserInfo getUserInfoByUserId(String userId) throws ServiceException {
        return getUserInfoByUserId(userId, Locale.SIMPLIFIED_CHINESE);
    }

    /**
     * 根据用户ID获取用户信息
     * 
     * @param userId
     *            用户ID
     * @return 用户信息
     * @throws ServiceException
     */
    public UserInfo getUserInfoByUserId(String userId, Locale locale) throws ServiceException {
        if (ToolsKit.isEmpty(userId)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage(i18nUtils.getKey(LocalTools.toast_userid_notblank, locale));
        }
        UserInfo userInfo = userInfoCacheService.getUserInfoByUserId(userId);
        if (ToolsKit.isEmpty(userInfo)) {
            try {
                userInfo = userInfoDao.getUserInfoByUserId(userId);
            } catch (Exception e) {
                e.printStackTrace();
                throw new ServiceException().setCode(ExceptionEnums.ERROR.getCode()).setMessage(i18nUtils.getKey(LocalTools.toast_inquire_info_incorrect, locale));
            }
            if (ToolsKit.isNotEmpty(userInfo)) {
                userInfoCacheService.saveUserInfo(userInfo);
            }
        }
        return userInfo;
    }

    /**
     * 保存
     * 
     * @param userInfo
     */
    public void save(UserInfo userInfo) {
        try {
            userInfoDao.saveOrUpdate(userInfo);
            userInfoCacheService.saveUserInfo(userInfo);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 更新用户信息
     * 
     * @param userId
     *            用户ID
     * @param nickName
     *            用户名称
     * @param userPic
     *            用户头像
     * @param sex
     *            性别
     * @throws ServiceException
     */
    public void updateUserInfo(String userId, String nickName, String sex, String userPic) throws ServiceException {
        updateUserInfo(userId, nickName, sex, userPic, null, null, null, null, Locale.SIMPLIFIED_CHINESE,null,null,null,null);
    }

    /**
     * 更新用户信息
     * 
     * @param userId
     *            用户ID
     * @param nickName
     *            用户名称
     * @param sex
     *            性别
     * @param userPic
     *            用户头像
     * @param birthday
     *            生日
     * @param currentWeight
     *            当前体重
     * @param targetWeight
     *            目标体重
     * @param height
     *            身高
     * @param userTitleType
     * @throws ServiceException
     */
    public void updateUserInfo(String userId, String nickName, String sex, String userPic, String birthday, String currentWeight, String targetWeight,
                               String height, Locale locale, String gradeLevel, String role, String userTitleType, String isForbidden) throws ServiceException {
        if (ToolsKit.isEmpty(userId)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage(i18nUtils.getKey(LocalTools.toast_userid_notblank, locale));
        }
        try {
            UserInfo userInfo = this.getUserInfoByUserId(userId);
            if (ToolsKit.isEmpty(userInfo)) {
                userInfo = new UserInfo();
                ToolsKit.setIdEntityData(userInfo, userId);
            }
            userInfo.setUserId(userId);
            if (ToolsKit.isNotEmpty(userInfo)) {
                if (ToolsKit.isNotEmpty(nickName)) {
                    userInfo.setNickName(nickName);
                }
                if (ToolsKit.isNotEmpty(userPic)) {
                    userPic = ToolsKit.URL.replaceUrl(commonProperties.getFileDomain(), userPic);
                    if (!ToolsKit.URL.isUrl(userPic) && !userPic.startsWith("/")) {
                        userPic = "/" + userPic;
                    }
                    userInfo.setUserPic(userPic);
                }
                if (ToolsKit.isNotEmpty(sex)) {
                    userInfo.setSex(sex);
                }
                if (ToolsKit.isNotEmpty(birthday)) {
                    userInfo.setBirthday(net.snaptag.system.sadais.util.core.ToolsKit.Date.parseDate(birthday));
                }
                if (ToolsKit.isNotEmpty(currentWeight)) {
                    double tempCurrentWeight = Double.valueOf(currentWeight);
                    if (tempCurrentWeight > 0) {
                        userInfo.setCurrentWeight(tempCurrentWeight);
                    }
                }
                if (ToolsKit.isNotEmpty(targetWeight)) {
                    double tempTargetWeight = Double.valueOf(targetWeight);
                    if (tempTargetWeight > 0) {
                        userInfo.setTargetWeight(tempTargetWeight);
                    }
                }
                if (ToolsKit.isNotEmpty(height)) {
                    int tempHeight = Integer.parseInt(height);
                    if (tempHeight > 0) {
                        userInfo.setHeight(tempHeight);
                    }
                }

                if (ToolsKit.isNotEmpty(gradeLevel)) {
                    int temp = Integer.parseInt(gradeLevel);
                    userInfo.setGradeLevel(temp);
                }

                if (ToolsKit.isNotEmpty(role)) {
                    int temp = Integer.parseInt(role);
                    userInfo.setRole(temp);
                }

                if (ToolsKit.isNotEmpty(userTitleType)){
                    int temp = Integer.parseInt(userTitleType);
                    userInfo.setUserTitleType(temp);
                }

                if (ToolsKit.isNotEmpty(isForbidden)) {
                    userInfo.setIsForbidden(Integer.parseInt(isForbidden));
                }

                userInfo.setIsEdit(1);
                this.save(userInfo);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

//    public UserLoginDto getLoginInfo(String userId) throws ServiceException {
//        return getLoginInfo(userId, Locale.SIMPLIFIED_CHINESE);
//    }

    /**
     * 获取用户登录信息
     * 
     * @param userId
     *            用户ID
     * @return
     */
//    public UserLoginDto getLoginInfo(String userId, Locale locale) throws ServiceException {
//        if (ToolsKit.isEmpty(userId)) {
//            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage(i18nUtils.getKey(LocalTools.toast_userid_notblank, locale));
//        }
//        return this.getLoginInfo(userId, null, locale);
//    }

    /**
     * 获取用户登录信息
     * 
     * @param userId
     *            用户ID
     * @return
     */
//    public UserLoginDto getLoginInfo(String userId, String otherId, Locale locale) throws ServiceException {
//        if (ToolsKit.isEmpty(userId)) {
//            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage(i18nUtils.getKey(LocalTools.toast_userid_notblank, locale));
//        }
//        UserLoginDto userLoginDto = new UserLoginDto(LoginStatusEnums.LOGIN.getValue());
//        userLoginDto.setUserInfoDto(this.getUserInfoDto(userId, otherId));
//        userLoginDto.setAccountInfoDto(userAccountBuService.getAccountInfoDto(userId, locale));
//        return userLoginDto;
//    }

//    private UserInfoDto getUserInfoDto(String userId, String otherId) {
//        try {
//            UserInfo userInfo = this.getUserInfoByUserId(userId);
//            UserAccount userAccount = userAccountBuService.getUserAccountByUserId(userId);
//            if (ToolsKit.isNotEmpty(userInfo)) {
//                UserInfoDto userInfoDto = this.getUserInfoDto(userInfo);
//                if (ToolsKit.isNotEmpty(userAccount) && ToolsKit.isNotEmpty(userAccount.getUdid())) {
//                    userInfoDto.setIsBandedUdid(ToolsConst.STATUS_1);
//                }
//                userInfoDto.setUserId(userId);
//                if (userInfo.getBirthday()!=null){
//                    userInfoDto.setBirthDay(ToolsKit.Date.formatDate(userInfo.getBirthday()));
//                    userInfoDto.setAge(ToolsKit.Date.ageOfNow(userInfo.getBirthday()));
//                }
//                userInfoDto.setCreateDate(ToolsKit.Date.formatDateTime(userAccount.getCreatetime()));
//
//                userInfoDto.setUserLoginHistoryDto(userLoginHistoryBuService.findByUserId(userId));
//
//                return userInfoDto;
//            }
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//        return null;
//    }

    public UserInfoDto getUserInfoDto(UserInfo userInfo) {
        UserInfoDto userInfoDto = new UserInfoDto();
        try {
            if (ToolsKit.isNotEmpty(userInfo)) {
                ToolsKit.Bean.copyProperties(userInfo, userInfoDto);
                userInfoDto.setUserId(userInfo.getUserId());
                userInfoDto.setUserPic(ToolsKit.URL.getUrlByServer(commonProperties.getFileDomain(), userInfo.getUserPic()));
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return userInfoDto;
    }

    /**
     * 获取最大codeId
     * 
     * @return codeId
     */
    public int getMaxCodeId() {
        UserInfo userInfo = userInfoDao.getMaxCodeIdUser();
        if (ToolsKit.isNotEmpty(userInfo)) {
            return userInfo.getCodeId() + 1;
        }
        return 1;
    }

    /**
     * 注册登录
     *
     * @param moblieId
     *            电话
     * @param deviceSystem
     *            描述
     * @param channel
     *            渠道
     * @param account
     *            账号
     * @param pwd
     *            密码
     * @param loginway
     *            登录途径
     * @param overseas
     *            是否海外版
     * @return RegisterReturnDto
     */
//    public UserLoginDto registerAndLogin(String moblieId, String deviceSystem, String channel, String account, String pwd, String loginway, Boolean overseas, Locale locale)
//            throws ServiceException {
//        return registerAndLogin(null, moblieId, deviceSystem, channel, account, pwd, loginway, null, null, null, null, overseas, locale);
//    }

//    /**
//     * 注册登录
//     *
//     * @param moblieId
//     *            电话
//     * @param deviceSystem
//     *            描述
//     * @param channel
//     *            渠道
//     * @param account
//     *            账号
//     * @param pwd
//     *            密码
//     * @param loginway
//     *            登录途径
//     * @param name
//     *            第三方登录昵称
//     * @return RegisterReturnDto
//     */
//    public UserLoginDto registerAndLogin(String appId, String moblieId, String deviceSystem, String channel, String account, String pwd, String loginway,
//                                         String name, String userPic, String sex, String phone, Boolean overseas, Locale locale) throws ServiceException {
//        try {
//            moblieId = ToolsKit.isEmpty(moblieId) ? ToolsKit.getDefaultMobile() : moblieId;
//            deviceSystem = ToolsKit.isEmpty(deviceSystem) ? ToolsConst.DEVICE_SYSTEM_ANDROID : deviceSystem.toUpperCase();
//            userPic = ToolsKit.isEmpty(userPic) ? commonProperties.getUserDefaultPic() : userPic;
//            int codeIdNum = userAccountBuService.getCodeId();
//            if (codeIdNum <= 0) {
//                throw new ServiceException().setCode(ExceptionEnums.ERROR.getCode()).setMessage(i18nUtils.getKey(LocalTools.toast_codeid_create_error, locale));
//            }
//
//            if (overseas) {
//                name = ToolsKit.isEmpty(name) ? "user_" + codeIdNum : name;
//            } else if (Constant.LOGIN_BY_MOBILE.equals(loginway)) {
//                name = commonProperties.getDefaultName() + account.substring(7);
//            } else {
//                name = ToolsKit.isEmpty(name) ? commonProperties.getDefaultName() + codeIdNum : name;
//            }
//            // 增加默认性别
//            sex = ToolsKit.isEmpty(sex)?"0":sex;
//
//
//
//            UserAccount userAccount = userAccountBuService.saveDefaultUser(appId, moblieId, deviceSystem, account, pwd, loginway, name, phone);
////            UserInfo userInfo = this.saveDefaultFitExtuser(appId, userAccount.getId(), codeIdNum, name, channel, ToolsKit.getSexStr(sex),
////                    net.snaptag.system.sadais.util.core.ToolsKit.Date.parse(ToolsConst.DEFAULT_USER_BIRTHDAY, DatePattern.NORM_DATE_PATTERN), ToolsConst.DEFAULT_USER_HEIGHT,
////                    ToolsConst.DEFAULT_USER_WEIGHT, userPic);
//            UserInfo userInfo = this.saveDefaultFitExtuser(appId, userAccount.getId(), codeIdNum, name, channel, ToolsKit.getSexStr(sex),
//                    null, ToolsConst.DEFAULT_USER_HEIGHT,
//                    ToolsConst.DEFAULT_USER_WEIGHT, userPic);
//            UserLoginDto userLoginDto = new UserLoginDto(LoginStatusEnums.LOGIN.getValue());
//            userLoginDto.setUserInfoDto(this.getUserInfoDto(userInfo));
//            userLoginDto.setAccountInfoDto(userAccountBuService.getAccountInfoDto(userAccount.getId(), locale));
//            return userLoginDto;
//        } catch (ServiceException e) {
//            throw e;
//        } catch (Exception e) {
//            e.printStackTrace();
//            throw new ServiceException().setCode(ExceptionEnums.ERROR.getCode()).setMessage(i18nUtils.getKey(LocalTools.toast_reg_fail_try, locale));
//        }
//    }

    /**
     * 注册登录（用于 XColor 应用） Add by RabyGao 2019-10-16
     *
     * @param moblieId - 电话
     * @param deviceSystem - 描述
     * @param channel - 渠道
     * @param account - 账号
     * @param pwd - 密码
     * @param loginway - 登录途径
     * @param name - 第三方登录昵称
     * @return RegisterReturnDto
     */
//    public UserLoginDto registerOnReset(String appId, String moblieId, String deviceSystem, String channel, String account, String pwd, String loginway,
//                                         String name, String userPic, String sex, String phone) throws ServiceException {
//        try {
//            moblieId = ToolsKit.isEmpty(moblieId) ? ToolsKit.getDefaultMobile() : moblieId;
//            deviceSystem = ToolsKit.isEmpty(deviceSystem) ? ToolsConst.DEVICE_SYSTEM_ANDROID : deviceSystem.toUpperCase();
//            userPic = ToolsKit.isEmpty(userPic) ? commonProperties.getUserDefaultPic() : userPic;
//            int codeIdNum = userAccountBuService.getCodeId();
//            if (codeIdNum <= 0) {
//                throw new ServiceException().setCode(ExceptionEnums.ERROR.getCode()).setMessage("创建codeId错误");
//            }
//            name = ToolsKit.isEmpty(name) ? commonProperties.getDefaultName() + codeIdNum : name;
//            UserAccount userAccount = userAccountBuService.saveDefaultUserOnReset(appId, moblieId, deviceSystem, account, pwd, loginway, name, phone);
//            UserInfo userInfo = this.saveDefaultFitExtuser(appId, userAccount.getId(), codeIdNum, name, channel, ToolsKit.getSexStr(sex),
//                    net.snaptag.system.sadais.util.core.ToolsKit.Date.parse(ToolsConst.DEFAULT_USER_BIRTHDAY, DatePattern.NORM_DATE_PATTERN), ToolsConst.DEFAULT_USER_HEIGHT,
//                    ToolsConst.DEFAULT_USER_WEIGHT, userPic);
//            UserLoginDto userLoginDto = new UserLoginDto(LoginStatusEnums.LOGIN.getValue());
//            userLoginDto.setUserInfoDto(this.getUserInfoDto(userInfo));
//            userLoginDto.setAccountInfoDto(userAccountBuService.getAccountInfoDto(userAccount.getId()));
//            return userLoginDto;
//        } catch (ServiceException e) {
//            throw e;
//        } catch (Exception e) {
//            e.printStackTrace();
//            throw new ServiceException().setCode(ExceptionEnums.ERROR.getCode()).setMessage("由于网络的原因，注册失败，请重试!");
//        }
//    }

    public UserInfo saveDefaultFitExtuser(String appId, String userId, int codeId, String name, String channel, String sex, Date birthday, int height,
            double weight, String userPic) throws Exception {
        UserInfo userInfo = new UserInfo();
        ToolsKit.setIdEntityData(userInfo, userId);
        userInfo.setUserId(userId);
        userInfo.setCodeId(codeId);
        userInfo.setNickName(name);
        userInfo.setUserPic(userPic);
        userInfo.setSex(sex);
        userInfo.setBirthday(birthday);
        userInfo.setCurrentWeight(weight);
        userInfo.setTargetWeight(0.0);
        userInfo.setHeight(height);
        userInfo.setChannel(channel);
        userInfo.setAppId(appId);
        this.save(userInfo);
        return userInfo;
    }

    public Page<UserInfoDto> findUserInfoPage(String codeId, String startDate, String endDate, String nickName, int pageNo, int pageSize, String appId,
                                              String sort, String sortType, String sex) throws ServiceException {
        return findUserInfoPage(codeId, startDate, endDate, nickName, pageNo, pageSize, appId, sort, sortType, sex, Locale.SIMPLIFIED_CHINESE);
    }

    /**
     * 根据时间段和昵称查询出用户信息记录
     * 
     * @param startDate
     *            开始时间
     * @param endDate
     *            结束时间
     * @param nickName
     *            昵称
     * @return
     * @throws ServiceException
     */
    public Page<UserInfoDto> findUserInfoPage(String codeId, String startDate, String endDate, String nickName, int pageNo, int pageSize, String appId,
                                                                                     String sort, String sortType, String sex, Locale locale) throws ServiceException {
        Date start = null;
        Date end = null;
        if (ToolsKit.isNotEmpty(startDate)) {
            start = ToolsKit.Date.parse(startDate + " 00:00:00", DatePattern.NORM_DATETIME_PATTERN);
        }
        if (ToolsKit.isNotEmpty(endDate)) {
            end = ToolsKit.Date.parse(endDate + " 23:59:59", DatePattern.NORM_DATETIME_PATTERN);
        }
        if (pageNo > 0) {
            pageNo--;
        }
        String sortName = null;
        if (ToolsKit.isNotEmpty(sortType)) {
            PageSortTypeEnums pageSortTypeEnums = PageSortTypeEnums.getMap().get(sortType);
            sortName = pageSortTypeEnums.getAttribute();
        }
        Page<UserInfoDto> result = new Page<UserInfoDto>(pageNo + 1, pageSize);
        IPage<UserInfo> page = userInfoDao.findUserInfoList(codeId, start, end, nickName, pageNo, pageSize, appId, sort, sortName, sex);
        result.setCurrent(page.getCurrent());
        result.setSize(page.getSize());
        result.setTotal(page.getTotal());
        if (ToolsKit.isNotEmpty(page.getRecords())) {
            List<UserInfoDto> list = new ArrayList<UserInfoDto>();
            for (UserInfo userInfo : page.getRecords()) {
                UserInfo info = this.getUserInfoById(userInfo.getId(), locale);

                if (ToolsKit.isNotEmpty(info)) {
//                    UserInfoDto userInfoDto = new UserInfoDto();
//                    ToolsKit.Bean.copyProperties(info, userInfoDto);
//                    userInfoDto.setUserPic(ToolsKit.URL.getUrlByServer(commonProperties.getFileDomain(), info.getUserPic()));
//                    userInfoDto.setAge(ToolsKit.Date.ageOfNow(info.getBirthday()));
//                    userInfoDto.setCreateDate(ToolsKit.Date.format(info.getCreatetime(), DatePattern.NORM_DATETIME_MINUTE_PATTERN));
//
//                    /**加上新属性*/
//                    userInfoDto.setUserTitleObj(userTitleTypeService.getByCode(userInfo.getUserTitleType()));
//                    userInfoDto.setGradeLevelObj(UserGradeLevelEnums.getMapByValue(userInfo.getGradeLevel()));
//                    userInfoDto.setRoleObj(UserRoleEnums.getMapByValue(userInfo.getRole()));

                    list.add(getUserInfoDto(info));
                }
            }
            result.setRecords(list);
        }
        return result;
    }

    public IPage<UserInfo>  findUserInfoList(String keyword, Date startDate, Date endDate, int pageNo, int pageSize, List ids) {
        return userInfoDao.findUserInfoList(keyword, startDate, endDate, pageNo, pageSize, ids);
    }
}