package net.snaptag.system.account.buservice;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import net.snaptag.system.account.buservice.apple.AppleUitl;
import net.snaptag.system.account.dto.ThirdLoginDto;
import net.snaptag.system.account.enums.WeChatOpenIdType;
import net.snaptag.system.account.utils.Constant;
import net.snaptag.system.account.utils.ToolUtils;
import net.snaptag.system.account.utils.YouMengUtils;
import net.snaptag.system.sadais.core.common.core.ToolsConst;
import net.snaptag.system.sadais.core.common.enums.ExceptionEnums;
import net.snaptag.system.sadais.core.common.exception.ServiceException;
import net.snaptag.system.sadais.util.core.ToolsKit;
import net.snaptag.system.sadais.web.common.JsonKit;
import net.snaptag.system.sadais.web.common.WebKit;
import net.snaptag.system.sadais.web.config.CommonProperties;
import net.snaptag.system.sadais.web.core.I18nUtils;
import net.snaptag.system.sadais.web.dto.HeadInfoDto;
import net.snaptag.system.sadais.web.utils.LocalTools;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.HashMap;
import java.util.Locale;
import java.util.Map;
@Service
public class ThirdLoginBuService {

    @Autowired
    private I18nUtils i18nUtils;

    @Autowired
    private CommonProperties commonProperties;

    /**
     * 检测第三方登录状态
     * 
     * @param loginway
     *            登录方式
     * @param request
     *            请求对象 i18nUtils.getKey(LocalTools.toast_log_incorrect, locale)
     * @throws ServiceException
     */
    public void checkThirdLoginStatus(String loginway, HttpServletRequest request, Locale locale) throws ServiceException {
        ThirdLoginDto thirdLoginDto = this.getThirdLoginDto(loginway, request, locale);
        if (ToolsConst.LOGIN_BY_SINA.equals(loginway)) {// 新浪微博
            Map<String, Object> params = new HashMap<String, Object>();
            // params.put("access_token", thirdLoginDto.getAccessToken());
            String url = Constant.SINA_THIRD_LOGIN_CHECK_URL + "?access_token=" + thirdLoginDto.getAccessToken();
            String result = WebKit.postFormForObject(url, params);
            if (ToolsKit.isEmpty(result)) { //
                throw new ServiceException().setCode(ExceptionEnums.ERROR.getCode()).setMessage(i18nUtils.getKey(LocalTools.toast_auth_fail, locale));
            }
            JSONObject jsonObject = JSON.parseObject(result);
            if (jsonObject.getIntValue("error_code") > 0) {
                throw new ServiceException().setCode(ExceptionEnums.ERROR.getCode()).setMessage(i18nUtils.getKey(LocalTools.toast_auth_fail, locale));
            }
        } else if (ToolsConst.LOGIN_BY_WEIXIN.equals(loginway)) {// 微信
            this.checkWeChatAccount(thirdLoginDto, locale);
        } else if (ToolsConst.LOGIN_BY_QQ.equals(loginway)) {// QQ
            // 客户端的 appId 覆盖服务端的appId    Update By RabyGao 2019年10月16日
            String appId = WebKit.getValue("appid", request);
            if (ToolsKit.isNotEmpty(appId) && appId.trim().length() > 0) {
                thirdLoginDto.setAppId(appId);
            }
            System.out.println("================qq登录=================");
            String url = Constant.QQ_THIRD_LOGIN_CHECK_URL + "?access_token=" + thirdLoginDto.getAccessToken() + "&openid=" + thirdLoginDto.getOpenId()
                    + "&oauth_consumer_key=" + thirdLoginDto.getAppId();
            System.out.println("url=" + url);
            String result = WebKit.getForObject(url);
            System.out.println(result);
            if (ToolsKit.isEmpty(result)) {
                throw new ServiceException().setCode(ExceptionEnums.ERROR.getCode()).setMessage(i18nUtils.getKey(LocalTools.toast_auth_fail, locale));
            }
            JSONObject jsonObject = JSON.parseObject(result);
            if (jsonObject.getIntValue("ret") != 0) {
                throw new ServiceException().setCode(ExceptionEnums.ERROR.getCode()).setMessage(i18nUtils.getKey(LocalTools.toast_auth_fail, locale));
            }
        }
    }

    /***
     * 根据友盟一键登录产生的accesstoken，获取手机号码
     * @param request
     * @return 返回手机号码
     * @throws InvalidKeyException
     * @throws NoSuchAlgorithmException
     */
    public String getPhoneNumByYoMengToken(String system, HttpServletRequest request, HeadInfoDto headInfoDto) {
        System.out.println("-----------当前手机操作系统为：" + system);
        system = ToolsKit.isEmpty(system) ? ToolsConst.DEVICE_SYSTEM_ANDROID : system.toUpperCase();

        String accessToken = ToolUtils.getRequestValue(request, "accesstoken");
        String mobile = "";
        try{
            String ymAppId = ToolsConst.DEVICE_SYSTEM_ANDROID.equals(system) ? commonProperties.getYmOneKeyAndroidAppId() :  commonProperties.getYmOneKeyIosAppId();
            mobile = YouMengUtils.getMobile(ymAppId, commonProperties.getYmOneKeyAlyAppId(), commonProperties.getYmOneKeyAlyAppSercet(), accessToken);
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
        if (ToolsKit.isEmpty(mobile)){
            throw new ServiceException("一键登录无法获取手机号码，请联系管理员");
        }
        return mobile;
    }

    public void checkWeChatAccount(ThirdLoginDto thirdLoginDto, Locale locale) {
        System.out.println(JSON.toJSONString(thirdLoginDto));
        if (WeChatOpenIdType.HEALTH.value.equals(thirdLoginDto.getFrom())) {// 小程序access_token验证方式
//            String result = WebKit.getForObject(
//                    Constant.WEIXIN_THIRD_LOGIN_CHECK_URL + "?access_token=" + thirdLoginDto.getAccessToken() + "&openid=" + thirdLoginDto.getOpenId());
//            System.out.println(result);
//            if (ToolsKit.isEmpty(result)) {
//                throw new ServiceException().setCode(ExceptionEnums.ERROR.getCode()).setMessage("授权失败");
//            }
//            JSONObject jsonObject = JSON.parseObject(result);
//            int errcode = jsonObject.getIntValue("errcode");
//            System.out.println(jsonObject.toJSONString());
//            if (errcode != 0 && errcode != 48001) {
//                throw new ServiceException().setCode(ExceptionEnums.ERROR.getCode()).setMessage("授权失败");
//            }
        } else {// 公众号及应用access_token验证方式
            String result = WebKit.getForObject(
                    Constant.WEIXIN_THIRD_LOGIN_CHECK_URL + "?access_token=" + thirdLoginDto.getAccessToken() + "&openid=" + thirdLoginDto.getOpenId());
            System.out.println(result);
            if (ToolsKit.isEmpty(result)) {
                throw new ServiceException().setCode(ExceptionEnums.ERROR.getCode()).setMessage(i18nUtils.getKey(LocalTools.toast_auth_fail, locale));
            }
            JSONObject jsonObject = JSON.parseObject(result);
            System.out.println(jsonObject.toJSONString());
            if (jsonObject.getIntValue("errcode") != 0) {
                throw new ServiceException().setCode(ExceptionEnums.ERROR.getCode()).setMessage(i18nUtils.getKey(LocalTools.toast_auth_fail, locale));
            }
        }
    }

    /**
     * 第三方登录dto
     * 
     * @param loginway
     *            登录方式
     * @param request
     *            请求对象
     * @return
     * @throws ServiceException
     */
    public ThirdLoginDto getThirdLoginDto(String loginway, HttpServletRequest request, Locale locale) throws ServiceException {
        ThirdLoginDto thirdLoginDto = new ThirdLoginDto();
        if (ToolsConst.LOGIN_BY_SINA.equals(loginway)) {
            String accessToken = ToolUtils.getRequestValue(request, "accesstoken");
            if (ToolsKit.isEmpty(accessToken)) {
                throw new ServiceException().setCode(ExceptionEnums.ERROR.getCode()).setMessage(i18nUtils.getKey(LocalTools.toast_sinaweibo_auth_fail, locale));
            }
            thirdLoginDto.setAccessToken(accessToken);
        } else if (ToolsConst.LOGIN_BY_QQ.equals(loginway)) {
            String accessToken = ToolUtils.getRequestValue(request, "accesstoken");
            String openId = ToolUtils.getRequestValue(request, "openid");
            if (ToolsKit.isEmpty(openId) || ToolsKit.isEmpty(accessToken)) {
                throw new ServiceException().setCode(ExceptionEnums.ERROR.getCode()).setMessage(i18nUtils.getKey(LocalTools.toast_auth_qq_fail, locale));
            }
            thirdLoginDto.setAccessToken(accessToken);
            thirdLoginDto.setOpenId(openId);
            thirdLoginDto.setAppId(commonProperties.getAndroidQQAppId());
            String jsonString = WebKit.getHeadInfoDto(request);
            if (ToolsKit.isNotEmpty(jsonString)) {
                HeadInfoDto headInfoDto = JsonKit.jsonParseObject(jsonString, HeadInfoDto.class);
                if (ToolsKit.isNotEmpty(headInfoDto.getDeviceSystem()) && ToolsConst.DEVICE_SYSTEM_IOS.equals(headInfoDto.getDeviceSystem())) {
                    thirdLoginDto.setAppId(commonProperties.getIosQQAppId());
                }
            }
        } else if (ToolsConst.LOGIN_BY_WEIXIN.equals(loginway)) {
            String accessToken = ToolUtils.getRequestValue(request, "accesstoken");
            String openId = ToolUtils.getRequestValue(request, "openid");
            if (ToolsKit.isEmpty(accessToken) || ToolsKit.isEmpty(openId)) {
                throw new ServiceException().setCode(ExceptionEnums.ERROR.getCode()).setMessage(i18nUtils.getKey(LocalTools.toast_auth_wechat_fail, locale));
            }
            thirdLoginDto.setAccessToken(accessToken);
            thirdLoginDto.setOpenId(openId);
        }
        thirdLoginDto.setFrom(ToolUtils.getRequestValue(request, "from"));
        return thirdLoginDto;
    }

    // --------------- 苹果ID身份验证 ---------------

    public void checkAppleIDLoginStatus(String account, String jwt, Locale locale) throws ServiceException {
        if (!AppleUitl.appleAuth(account, jwt)) {
            throw new ServiceException().setCode(ExceptionEnums.ERROR.getCode()).setMessage(i18nUtils.getKey(LocalTools.toast_auth_fail, locale));
        }
    }

}
