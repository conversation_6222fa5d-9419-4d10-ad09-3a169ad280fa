package net.snaptag.system.account.buservice;

import net.snaptag.system.account.dao.RoleTableDao;
import net.snaptag.system.account.entity.RoleResource;
import net.snaptag.system.account.entity.RoleTable;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import net.snaptag.system.common.DataConst;
import net.snaptag.system.sadais.core.common.enums.ExceptionEnums;
import net.snaptag.system.sadais.core.common.exception.ServiceException;
import net.snaptag.system.sadais.util.core.ToolsKit;
import net.snaptag.system.sadais.web.common.JsonKit;
import net.snaptag.system.sadais.web.dto.ResourceDto;
import net.snaptag.system.sadais.web.dto.RoleDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 角色表
 */
@Service
public class RoleTableBuService {
    @Autowired
    private RoleTableDao roleTableDao;
    @Autowired
    private RoleResourceBuService roleResourceBuService;
    @Autowired
    private UserRoleBuService userRoleBuService;
    @Autowired
    private ResourceTableBuService resourceTableBuService;

    public RoleTable getByRoleCode(String roleCode) {
        QueryWrapper<RoleTable> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("role_code", roleCode);
        return roleTableDao.getOne(queryWrapper);
    }

    /**
     * 根据角色ID获取角色对象
     * 
     * @param id
     *            角色ID
     * @return
     */
    public RoleTable getRoleTableById(String id) {
        return roleTableDao.getRoleTableById(id);
    }

    /**
     * 获取角色dto列表
     * 
     * @param roleIdList
     *            角色ID集合
     * @return
     */
    public List<RoleDto> getRoleDtoList(List<String> roleIdList) {
        List<RoleDto> role = new ArrayList<RoleDto>();
        for (String roleId : roleIdList) {
            RoleTable roleTable = this.getRoleTableById(roleId);
            if (ToolsKit.isNotEmpty(roleTable)) {
                role.add(this.getRoleDto(roleTable));
            }
        }
        return role;
    }

    /**
     * 是否是超级管理员
     *
     *            用户账号ID
     * @return
     */
    public boolean isSuperAdmin(List<RoleDto> role) {
        boolean isSuperAdmin = false;
        if (ToolsKit.isNotEmpty(role)) {
            for (RoleDto roleDto : role) {
                if (("2").equals(roleDto.getRoleCode()) || ("3").equals(roleDto.getRoleCode())) {
                    isSuperAdmin = true;
                    break;
                }
            }
        }
        return isSuperAdmin;
    }

    private RoleDto getRoleDto(RoleTable roleTable) {
        RoleDto roleDto = new RoleDto();
        roleDto.setId(roleTable.getId());
        roleDto.setRoleCode(roleTable.getRoleCode());
        roleDto.setRoleName(roleTable.getRoleName());
        return roleDto;
    }

    /**
     * 获取角色列表
     *
     * @param name
     *            名称
     * @param pageNo
     *            页码
     * @param pageSize
     *            页面大小
     * @return 历史信息集合
     */
    public IPage<RoleDto> findPage(String name, int pageNo, int pageSize) throws ServiceException {
        if (pageNo > 0) {
            pageNo--;
        }
        Page<RoleDto> result = new Page<RoleDto>(pageNo + 1, pageSize);
        try {
            IPage<RoleTable> pageList = roleTableDao.findRoleTableList(name, pageNo, pageSize, null);
            result.setCurrent(pageList.getCurrent());
            result.setSize(pageList.getSize());
            result.setTotal(pageList.getTotal());
            if (ToolsKit.isNotEmpty(pageList.getRecords())) {
                List<RoleDto> dtoList = new ArrayList<RoleDto>();
                for (RoleTable roleTable : pageList.getRecords()) {
                    dtoList.add(this.getRoleDto(roleTable));
                }
                result.setRecords(dtoList);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }

    public List<RoleDto> findList(String name) throws ServiceException {
        List<RoleDto> dtoList = new ArrayList<RoleDto>();
        try {
            List<RoleTable> list = roleTableDao.list();
            if (ToolsKit.isNotEmpty(list)) {
                for (RoleTable roleTable : list) {
                    dtoList.add(this.getRoleDto(roleTable));
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return dtoList;
    }

    /**
     * 根据ID删除用户账号
     * 
     * @param id
     * @throws ServiceException
     */
    public void del(String id) throws ServiceException {
        if (ToolsKit.isEmpty(id)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("用户账号ID不能为空");
        }
        List<String> idList = JsonKit.jsonParseArray(id, String.class);
        long userCount = userRoleBuService.getCountByRole(idList);
        if (userCount > 0) {
            throw new ServiceException().setCode(ExceptionEnums.ERROR.getCode()).setMessage("角色记录正被账号使用");
        }
        long roleCount = roleResourceBuService.getCountByRole(idList);
        if (roleCount > 0) {
            throw new ServiceException().setCode(ExceptionEnums.ERROR.getCode()).setMessage("角色记录正被资源使用");
        }
        roleTableDao.removeByIds(idList);
    }

    /**
     * 保存角色记录
     * 
     * @param roleName
     *            角色名称
     * @param roleCode
     *            角色编码
     * @param projectId
     *            项目ID
     * @throws ServiceException
     */
    public void saveOrUpdate(String id, String roleName, String roleCode, String projectId) throws ServiceException {
        if (ToolsKit.isEmpty(roleName)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("角色名称不能为空");
        }
        if (ToolsKit.isEmpty(roleCode)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("角色编码不能为空");
        }
//        long count = roleTableDao.getCountByCode(roleCode);
//        if (count > 0) {
//            throw new ServiceException().setCode(ExceptionEnums.ERROR.getCode()).setMessage("该角色编码记录已存在");
//        }
        RoleTable roleTable = null;
        if (ToolsKit.isNotEmpty(id)) {
            roleTable = roleTableDao.getRoleTableById(id);
            if (ToolsKit.isEmpty(roleTable)) {
                throw new ServiceException().setCode(ExceptionEnums.ERROR.getCode()).setMessage("查询不到角色信息");
            }
        } else {
            roleTable = new RoleTable();
            roleTable.setStatus(DataConst.DATA_SUCCESS_STATUS);
            roleTable.setCreatetime(new java.util.Date());
            roleTable.setCreateuserid("SYSTEM_USER_ID");
        }
        roleTable.setRoleName(roleName);
        roleTable.setRoleCode(roleCode);
        roleTableDao.saveOrUpdate(roleTable);
    }

    /**
     * 分配角色资源
     * 
     * @param roleId
     *            角色ID
     * @param resIds
     *            资源ID
     * @throws ServiceException
     */
    public void disresource(String roleId, String resIds) throws ServiceException {
        if (ToolsKit.isEmpty(roleId)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("角色ID不能为空");
        }
//        if (ToolsKit.isEmpty(resIds)) {
//            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("资源ID不能为空");
//        }
        RoleTable roleTable = roleTableDao.getRoleTableById(roleId);
        if (ToolsKit.isEmpty(roleTable)) {
            throw new ServiceException().setCode(ExceptionEnums.ERROR.getCode()).setMessage("角色记录不存在");
        }
        roleResourceBuService.deleteByRoleId(roleId);

        if (ToolsKit.isEmpty(resIds)){
            return;
        }
        String[] resList = resIds.split(",");
        if (resList.length > 0) {
            for (String resId : resList) {
                roleResourceBuService.saveRoleResource(roleId, resId);
            }
        }
    }

    public Map<String, Object> findResourcesTreeByRoleId(String id) {
        List<String> ids = new ArrayList<>();
        ids.add(id);
        List<RoleResource> roleResourceList = roleResourceBuService.findRoleResourceByRoleId(ids);
        List<String> selectedIds = new ArrayList<>();
        if(ToolsKit.isNotEmpty(roleResourceList)){
            selectedIds = roleResourceList.stream().map(RoleResource::getResourceId).collect(Collectors.toList());
        }


//        List<ResourceDto> resourceTree = settingResourceTree(roleResources);
        Map<String, Object> result = new HashMap<>();
        result.put("selected", selectedIds);
        result.put("treeData", resourceTableBuService.getResourcesTree(null));
        return result;
    }

    private List<ResourceDto> settingResourceTree(List<RoleResource> roleResources) {
        List<ResourceDto> resourceTree = resourceTableBuService.getResourcesTree(null);
        if (ToolsKit.isEmpty(resourceTree)){
            return new ArrayList<>();
        }

        Map<String, Object> roleMap = new HashMap<>();
        if (ToolsKit.isNotEmpty(roleResources)){
            roleResources.forEach(item -> {
                roleMap.put(item.getResourceId(), 1);
            });
        }
        settingResourceTree(roleMap, resourceTree);

        return resourceTree;
    }

    private void settingResourceTree(Map<String, Object> roleMap, List<ResourceDto> resourceTree) {
        if (resourceTree==null || resourceTree.size()==0){
            return;
        }
        for (ResourceDto dto:resourceTree) {
            if (roleMap.get(dto.getId())!=null){
                dto.setHasChecked(Boolean.TRUE);
            } else {
                dto.setHasChecked(Boolean.FALSE);
            }
            settingResourceTree(roleMap, dto.getResourceDtoList());
        }
    }
}
