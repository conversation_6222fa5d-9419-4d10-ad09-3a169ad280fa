package net.snaptag.system.account.buservice.apple;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.auth0.jwk.InvalidPublicKeyException;
import com.auth0.jwk.Jwk;
import net.snaptag.system.account.utils.HttpClientUtil;
import io.jsonwebtoken.*;
import org.apache.commons.codec.binary.Base64;

import java.security.PublicKey;

/**
 * 苹果授权登录(Sign in with Apple)
 * 需要调用苹果api获取公钥，接口地址为：https://appleid.apple.com/auth/keys
 */
public class AppleUitl {

    public static Boolean appleAuth(String appleId, String jwt) {
        String url = "https://appleid.apple.com/auth/keys";
        String result = HttpClientUtil.doGet(url);
        JSONObject jsonObject = JSONObject.parseObject(result);
        // System.out.println(result);
        String keys = jsonObject.getString("keys");
        JSONArray arr = JSONObject.parseArray(keys);
        if (arr != null && arr.size() > 0) {
            for (int i = 0; i < arr.size(); i++) {
                JSONObject jsonObj = arr.getJSONObject(i);
                Jwk jwa = Jwk.fromValues(jsonObj);
                try {
                    // 生成苹果公钥
                    PublicKey publicKey = jwa.getPublicKey();
                    // System.out.println("publicKey: " + publicKey);
                    // 分割前台传过来的identifyToken（jwt格式的token）用base64解码使用：
                    if (jwt.split("\\.").length > 1) {
                        String claim = new String(Base64.decodeBase64(jwt.split("\\.")[1]));
                        String aud = JSONObject.parseObject(claim).get("aud").toString();
                        String sub = JSONObject.parseObject(claim).get("sub").toString();
                        // ID不匹配
                        appleId = appleId.replaceAll("_sadais_apple", "");
                        if (!sub.equals(appleId)) {
                            return false;
                        }
                        // sub 为客户端ID
                        // System.out.println("aud: " + aud);
                        // System.out.println("sub: " + sub);
                        boolean verResult = verify(publicKey, jwt, aud, sub);
                        if (verResult) {
                            return verResult;
                        }
                    }
                } catch (InvalidPublicKeyException e) {
                    System.out.println("转换苹果公钥失败");
                }
            }
        }
        return false;
    }

    public static Boolean verify(PublicKey key, String jwt, String audience, String subject) {
        JwtParser jwtParser = Jwts.parser().setSigningKey(key);
        jwtParser.requireIssuer("https://appleid.apple.com");
        jwtParser.requireAudience(audience);
        jwtParser.requireSubject(subject);
        try {
            Jws<Claims> claim = jwtParser.parseClaimsJws(jwt);
            if (claim != null && claim.getBody().containsKey("auth_time")) {
                // System.out.println("---------SUCCESS--------");
                return true;
            }
            return false;
        } catch (ExpiredJwtException e) {
            System.out.println("苹果token过期");
        } catch (Exception e) {
            System.out.println("苹果token非法：" + e.getMessage());
        }
        return false;
    }

}
