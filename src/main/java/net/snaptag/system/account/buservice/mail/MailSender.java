package net.snaptag.system.account.buservice.mail;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.mail.Message;
import javax.mail.Session;
import javax.mail.Transport;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeMessage;

/**
 * 邮件发送服务类
 */
@Service
public class MailSender {

    @Autowired
    private MailConfig mailConfig;

    /**
     * 发送邮件
     * @param mailTo - 收件人
     * @param content - 邮件内容
     */
    public void send(String mailTo, String content) {
        send(mailTo, mailConfig.getSubject(), content);
    }

    /**
     * 发送邮件
     * @param mailTo - 收件人
     * @param subject - 邮件标题
     * @param content - 邮件内容
     */
    public void send(String mailTo, String subject, String content) {
        Transport transport = null;
        try {
            // 得到回话对象
            Session session = Session.getInstance(mailConfig.mailProperties());
            // 获取邮件对象
            Message message = new MimeMessage(session);
            // 设置发件人邮箱地址
            message.setFrom(new InternetAddress(mailConfig.getAccount()));
            // 一个收件人
            message.setRecipient(Message.RecipientType.TO, new InternetAddress(mailTo));

            // 设置邮件标题
            message.setSubject(subject);
            // 设置邮件内容
            //message.setText(content);
            // 在这里 contentType 要设置成text/html，编码格式也要视情况而设定
            message.setContent(content, "text/html;charset=utf-8");

            // 得到邮差对象
            transport = session.getTransport();
            // 连接自己的邮箱账户, 密码为QQ邮箱开通的stmp服务后得到的客户端授权码
            transport.connect(mailConfig.getAccount(), mailConfig.getPassword());
            // 发送邮件
            transport.sendMessage(message, message.getAllRecipients());
        } catch (Exception e) {
            e.printStackTrace();
            try {
                if (transport != null) {
                    transport.close();
                }
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        }
    }

    public MailConfig getMailConfig() {
        return mailConfig;
    }

    public void setMailConfig(MailConfig mailConfig) {
        this.mailConfig = mailConfig;
    }
}
