package net.snaptag.system.account.cache;

import net.snaptag.system.account.entity.UserThirdPartyAuth;
import net.snaptag.system.account.enums.UserThirdPartyAuthCacheEnums;
import net.snaptag.system.sadais.cache.kit.CacheKit;
import net.snaptag.system.sadais.core.common.core.ToolsConst;
import net.snaptag.system.sadais.util.core.ToolsKit;
import org.springframework.stereotype.Service;
@Service
public class UserThirdPartyAuthCache {
    /**
     * 根据user id获取缓存对象
     */
    public UserThirdPartyAuth getThirdPartyByUserId(String userId) {
        return CacheKit.cache().get(UserThirdPartyAuthCacheEnums.USER_THIRD_PARTY_AUTH_USER_ID.getKey() + userId, UserThirdPartyAuth.class);
    }

    /**
     * 根据user id插入缓存
     */
    public void saveUserThirdPartyAuth(UserThirdPartyAuth userThirdPartyAuth) {
        CacheKit.cache().set(UserThirdPartyAuthCacheEnums.USER_THIRD_PARTY_AUTH_USER_ID.getKey() + userThirdPartyAuth.getUserId(), userThirdPartyAuth,
                ToolsConst.DAY_SECOND * 3);
    }

    /**
     * 根据user id获取缓存对象
     */
    public String getThirdPartyByWeChatUnionId(String weChatUnionId) {
        return CacheKit.cache().get(UserThirdPartyAuthCacheEnums.USER_THIRD_PARTY_AUTH_UNION_ID.getKey() + weChatUnionId, String.class);
    }

    /**
     * 根据user id插入缓存
     */
    public void setByWeChatUnionId(String weChatUnionId, String userId) {
        if (ToolsKit.isNotEmpty(weChatUnionId)) {
            CacheKit.cache().set(UserThirdPartyAuthCacheEnums.USER_THIRD_PARTY_AUTH_UNION_ID.getKey() + weChatUnionId, userId, ToolsConst.DAY_SECOND * 3);
        }
    }

    /**
     * 根据用户id删除缓存
     *
     * @param userId
     *            用户id
     */
    public long delCacheByUserId(String userId) {
        return CacheKit.cache().del(UserThirdPartyAuthCacheEnums.USER_THIRD_PARTY_AUTH_USER_ID.getKey() + userId);
    }

    /**
     * 根据用户id删除缓存
     *
     * @param weChatUnionId
     *            weChatUnionId
     */
    public long delCacheByWeChatUnionId(String weChatUnionId) {
        return CacheKit.cache().del(UserThirdPartyAuthCacheEnums.USER_THIRD_PARTY_AUTH_UNION_ID.getKey() + weChatUnionId);
    }
}
