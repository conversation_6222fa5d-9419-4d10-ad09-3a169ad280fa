package net.snaptag.system;

import com.alibaba.nacos.spring.context.annotation.config.NacosPropertySource;
import net.snaptag.system.sadais.cache.common.CacheCommonTools;
import net.snaptag.system.sadais.web.core.HeaderFilter;
import net.snaptag.system.sadais.web.core.JWTFilterChain;
import net.snaptag.system.sadais.web.core.ResponseFilter;
import net.snaptag.system.sadais.web.core.VerificationFilterChain;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.web.servlet.FilterRegistrationBean;

import org.springframework.context.annotation.Bean;
import org.springframework.core.annotation.Order;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;
import org.springframework.web.filter.CorsFilter;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.TimeZone;

@SpringBootApplication
@EnableScheduling
@NacosPropertySource(
        dataId = "${nacos.config.data-id}",
        autoRefreshed = true,
        first = true  // 设置为第一优先级，覆盖本地配置
)
public class SnapTagApplication {

    public static void main(String[] args) {
        TimeZone.setDefault(TimeZone.getTimeZone("GMT+08:00"));
        SpringApplication.run(SnapTagApplication.class, args);
        System.out.println("【【【【【【 Snap&Tag 微服务 】】】】】】已启动...");
    }

    @Bean
    public CacheCommonTools setCacheCommonTools() {
        return new CacheCommonTools();
    }

    /**
     * 响应参数处理过滤器
     *
     * @return
     */
    @Bean
    public FilterRegistrationBean setResponseFilter() {
        FilterRegistrationBean filterBean = new FilterRegistrationBean();
        filterBean.setFilter(new ResponseFilter());
        filterBean.setName("ResponseFilter");
        filterBean.addUrlPatterns("/**");
        filterBean.setOrder(Integer.MAX_VALUE);
        return filterBean;
    }

    /**
     * 统一处理过滤器
     *
     * @return
     */
    @Bean
    public FilterRegistrationBean setBaseFilter() {
        FilterRegistrationBean filterBean = new FilterRegistrationBean();
        filterBean.setFilter(new VerificationFilterChain());
        filterBean.setName("VerificationFilterChain");
        filterBean.addUrlPatterns("/*");
        filterBean.setOrder(1);
        return filterBean;
    }


    /**
     * jwttoken检测过滤器
     *
     * @return
     */
    @Bean
    public FilterRegistrationBean setJWTFilter() {
        FilterRegistrationBean filterBean = new FilterRegistrationBean();
        filterBean.setFilter(new JWTFilterChain());
        filterBean.setName("JWTFilterChain");
        filterBean.addUrlPatterns("/*");
        filterBean.setOrder(2);
        return filterBean;
    }

    /**
     * 处理参数
     *
     * @return
     */
    @Bean
    public FilterRegistrationBean setHeaderFilter() {
        FilterRegistrationBean filterBean = new FilterRegistrationBean();
        filterBean.setFilter(new HeaderFilter());
        filterBean.setName("HeaderFilter");
        filterBean.addUrlPatterns("/*");
        filterBean.setOrder(1);
        return filterBean;
    }

//    @Bean
//    @Order(0)
//    public CorsFilter corsFilter() {
//        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
//        CorsConfiguration config = new CorsConfiguration();
//
//        config.addAllowedHeader("*");                // 设置访问源请求头
//        config.addAllowedMethod("GET");              // 允许GET方法访问
//        config.addAllowedMethod("POST");             // 允许POST方法访问
//        config.addAllowedMethod("OPTIONS");          // 允许OPTIONS方法访问
//        config.setAllowCredentials(Boolean.TRUE);
//        // 2.4以后这样设置 替换 addAllowedOrigin("*)
//        config.setAllowedOrigins(Arrays.asList(CorsConfiguration.ALL));
//        config.setMaxAge(3600L);
//
//
////        config.setAllowCredentials(true);
////        config.addAllowedOrigin("*");
////        config.addAllowedHeader("*");
////        config.addAllowedMethod("*");
////        config.setMaxAge(3600L);
////        config.setAllowCredentials(true);
////        List<String> arr = new ArrayList<String>();
////        arr.add("X-forwared-port");
////        arr.add("X-forwarded-host");
////        arr.add("X-Total-Count");
////        config.setExposedHeaders(arr);
//        List<String> exposedHeaders = new ArrayList<>();
//        exposedHeaders.add("X-forwared-port");
//        exposedHeaders.add("X-forwarded-host");
//        exposedHeaders.add("X-Total-Count");
//        config.setExposedHeaders(exposedHeaders);
//        source.registerCorsConfiguration("/**", config);
//        return new CorsFilter(source);
//    }
}
