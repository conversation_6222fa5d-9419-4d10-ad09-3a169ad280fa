package net.snaptag.system;

//import net.snaptag.system.business.cache.*;
//import net.snaptag.system.business.enums.SystemPublishEnums;
import net.snaptag.system.business.cache.*;
import net.snaptag.system.business.enums.SystemPublishEnums;
import net.snaptag.system.sadais.cache.kit.CacheKit;
import net.snaptag.system.sadais.cache.plugin.CachePlugin;
import net.snaptag.system.sadais.sms.plugin.AldyPlugin;
import net.snaptag.system.sadais.web.config.AldySmsProperties;
import net.snaptag.system.sadais.web.config.RedisProperties;
import net.snaptag.system.sadais.web.config.StsProperties;
import net.snaptag.system.storage.plugin.StsPlugin;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Component
@Order(value = 1)
public class InitDataRunner implements ApplicationRunner {
    @Autowired
    private RedisProperties   redisProperties;
    @Autowired
    private StsProperties stsProperties;
    @Autowired
    private AldySmsProperties aldySmsProperties;
    @Autowired
    private MaterialCacheService materialCacheService;
    @Autowired
    private BannerCacheBuService bannerCacheBuService;
    @Autowired
    private SystemMsgCacheBuService systemMsgCacheBuService;
    @Autowired
    private PrinterDriverUpgradeCacheService printerDriverUpgradeCacheService;
//
    @Autowired
    private PubSubService pubSubService;

    @Override
    public void run(ApplicationArguments args) throws Exception {
        initPlugin();
        initData();
    }

    /**
     * 初始化插件
     */
    private void initPlugin() {
        try {
            int database = 1;
            if (StringUtils.isNotBlank(redisProperties.getRedisDatabase())) {
                database = Integer.parseInt(redisProperties.getRedisDatabase().trim());
            }

            System.out.println("redisProperties.getRedisHost()="+redisProperties.getRedisHost());
            System.out.println("redisProperties.getRedisPort()="+redisProperties.getRedisPort());
            System.out.println("redisProperties.getRedisPassword()="+redisProperties.getRedisPassword());
            System.out.println("database="+database);

            new CachePlugin(redisProperties.getRedisHost(), redisProperties.getRedisPort(), redisProperties.getRedisPassword(), database).start();

//            new CachePlugin(redisProperties.getRedisHost(), redisProperties.getRedisPort()).start();
            new AldyPlugin(aldySmsProperties.getRegionId(), aldySmsProperties.getAccessKey(), aldySmsProperties.getAccessKeySecret(),
                    aldySmsProperties.getEndpointName(), aldySmsProperties.getProduct(), aldySmsProperties.getDomain(), aldySmsProperties.getMaxRecNum(),
                    aldySmsProperties.getSignName(), aldySmsProperties.getCharEncode(), aldySmsProperties.getSmsTemplateUrl()).start();
            new StsPlugin(stsProperties.getStsAccesskey(), stsProperties.getStsAccesskeySecret(), stsProperties.getStsRegion(), stsProperties.getStsEndPoint(),
                    stsProperties.getStsRoleArn(), stsProperties.getStsRoleSessionName(), stsProperties.getStsDurationSeconds(), stsProperties.getCharEncode(),
                    stsProperties.getStsEndPoints()).start();
//            List<Class<?>> classList = new ArrayList<Class<?>>();
//            classList.add(RequestEventLogBuService.class);
//            IPlugin queuePlugin = new QueuePlugin(1000, classList);
//            queuePlugin.start();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 初始化数据
     */
    private void initData() {
        materialCacheService.initMaterial();
        materialCacheService.initMaterialResource();
        bannerCacheBuService.initBanner();
        systemMsgCacheBuService.initSystemMsg();
        systemMsgCacheBuService.initUpdateInfo();
        printerDriverUpgradeCacheService.init();
        LinkedHashMap<String, String> map = SystemPublishEnums.getMap();
        List<String> pubList = new ArrayList<String>();
        for (Map.Entry<String, String> entry : map.entrySet()) {
            pubList.add(entry.getKey());
        }
        CacheKit.cache().subscribe(pubSubService, pubList);
    }
}