package net.snaptag.system.config;

/**
 * <AUTHOR>
 * @date ：Created in 2024/9/19 14:27
 * @description：
 * @modified By：
 * @version: $
 */

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;
import org.springframework.web.filter.CorsFilter;

import java.util.Arrays;
import java.util.List;

@Configuration
public class MyWebConfig {

    @Bean
    @Order(0)
    public CorsFilter corsFilter() {
        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        CorsConfiguration config = new CorsConfiguration();
//        config.addAllowedOrigin("http://localhost:8080");
        config.setAllowedOriginPatterns(List.of("http://localhost:8080","http://127.0.0.1:8084","http://platformtest.snaptag.top","https://platform.snaptag.top","http://platform.snaptag.top","https://m.snaptag.top"));
        config.addAllowedHeader("*"); // 允许任何头
        config.addAllowedMethod("*"); // 允许任何方法（post、get等）
        config.setAllowCredentials(true);
        source.registerCorsConfiguration("/**", config);
        return new CorsFilter(source);
    }
}