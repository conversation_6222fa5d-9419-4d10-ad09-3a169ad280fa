package net.snaptag.system.common;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 基础实体类
 */
@Data
public class BaseEntity implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    public static final String ID_FIELD = "id";
    public static final String CREATETIME_FIELD = "createtime";
    public static final String UPDATETIME_FIELD = "updatetime";
    public static final String STATUS_FIELD = "status";
    public static final String APP_ID_FIELD = "app_id";
    
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;
    
    /**
     * 创建时间
     */
    @TableField(value = "createtime", fill = FieldFill.INSERT)
    private Date createtime;
    
    /**
     * 创建人ID
     */
    @TableField(value = "createuserid", fill = FieldFill.INSERT)
    private String createuserid;
    
    /**
     * 更新时间
     */
    @TableField(value = "updatetime", fill = FieldFill.INSERT_UPDATE)
    private Date updatetime;
    
    /**
     * 更新人ID
     */
    @TableField(value = "updateuserid", fill = FieldFill.INSERT_UPDATE)
    private String updateuserid;
    
    /**
     * 数据状态（逻辑删除字段）
     */
    @TableLogic
    @TableField("status")
    private String status;
    
    /**
     * 数据来源
     */
    @TableField("source")
    private String source;
    
    /**
     * 应用渠道ID
     */
    @TableField("app_id")
    private String appId;
}
