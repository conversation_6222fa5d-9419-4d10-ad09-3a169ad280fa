package net.snaptag.system.smscenter.buservice;

import net.snaptag.system.sadais.cache.common.CacheCommonTools;
import net.snaptag.system.sadais.core.common.enums.ExceptionEnums;
import net.snaptag.system.sadais.core.common.exception.ServiceException;
import net.snaptag.system.sadais.util.core.ToolsKit;
import net.snaptag.system.sadais.web.enums.SystemStatusEnums;
import net.snaptag.system.smscenter.dto.SystemStatusDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class SystemStatusBuService {
    @Autowired
    private CacheCommonTools cacheCommonTools;

    /**
     * 保存状态信息
     * 
     * @param key
     * @param account
     * @throws ServiceException
     */
    public void saveStatus(String key, String account, String seconds) throws ServiceException {
        if (ToolsKit.isEmpty(key)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("标识key不能为空");
        }
        if (ToolsKit.isEmpty(account)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("账号不能为空");
        }
        if (ToolsKit.isEmpty(seconds)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("时效不能为空");
        }
        cacheCommonTools.saveCacheFlag(key, account, Integer.parseInt(seconds));
    }

    /**
     * 获取状态信息
     * 
     * @param key
     * @param account
     * @throws ServiceException
     */
    public SystemStatusDto getStatusCount(String key, String account) throws ServiceException {
        if (ToolsKit.isEmpty(key)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("标识key不能为空");
        }
        if (ToolsKit.isEmpty(account)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("账号不能为空");
        }
        String count = cacheCommonTools.getCacheFlag(key, account);
        SystemStatusDto dto = new SystemStatusDto();
        if (ToolsKit.isNotEmpty(count)) {
            int num = Integer.parseInt(count.replace("\"", ""));
            dto.setStatusCount(num);
            int max = SystemStatusEnums.get(key);
            dto.setStatus(num > max);
        }
        return dto;
    }
}
