package net.snaptag.system.smscenter.buservice;

import net.snaptag.system.account.buservice.UserAccountBuService;
import net.snaptag.system.account.dto.UserAccountDto;
import net.snaptag.system.sadais.core.common.core.ToolsConst;
import net.snaptag.system.sadais.core.common.enums.ExceptionEnums;
import net.snaptag.system.sadais.core.common.exception.ServiceException;
import net.snaptag.system.sadais.sms.core.SmsMessage;
import net.snaptag.system.sadais.sms.kit.SmsKit;
import net.snaptag.system.sadais.sms.utils.SmsChanelEnum;
import net.snaptag.system.sadais.util.core.ToolsKit;
import net.snaptag.system.sadais.web.common.JsonKit;
import net.snaptag.system.sadais.web.core.I18nUtils;
import net.snaptag.system.sadais.web.utils.LocalTools;
import net.snaptag.system.smscenter.cache.SmsMessageCacheService;
import net.snaptag.system.smscenter.dto.SmsStatusDto;
import net.snaptag.system.smscenter.enums.SmsTypeEnums;
import net.snaptag.system.smscenter.utils.Constant;
import net.snaptag.system.smscenter.utils.ToolUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Locale;
import java.util.Map;
import java.util.Random;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Service
public class SmsMessageBuService {
    @Autowired
    private SmsMessageCacheService smsMessageCacheService;
    @Autowired
    private UserAccountBuService     userAccountService;
    @Autowired
    private I18nUtils i18nUtils;

    /**
     * 中国大陆手机号正则表达式
     */
    public static final String CHINA_MOBILE_PATTERN = "^(\\+?0?86\\-?)?1[345789]\\d{9}$";

    /**
     * 保存图片验证码
     * 
     * @param account
     * @param vCode
     */
    public void setCaptcha(String account, String vCode) {
        smsMessageCacheService.setCaptcha(account, vCode);
    }

    /**
     * 检查验证码， 重试超过三次即直接退出 二次的弹出验证码窗，输入验证码 验证不正确的直接退出
     *
     * @param retryCount
     *            重试次数
     * @param account
     *            电话号码
     * @param captcha
     *            验证码
     * @return
     */
    public void checkCaptcha(int retryCount, String account, String captcha, String ip, Locale locale) {
        if (retryCount > Constant.SMS_ACCOUNT_MAX_RETRY * 3) {// 由于图形验证码输入成功后，清除缓存里的计数值，所以该条件无法进入
            smsMessageCacheService.setDayIpTotalNum(ip);
            throw new ServiceException().setCode(ExceptionEnums.ERROR.getCode()).setMessage(i18nUtils.getKey(LocalTools.toast_vermore_aftertry, locale));
            // throw new ServiceException().setCode(ExceptionEnums.ERROR.getCode()).setMessage("获取验证码次数太多");
        } else if (ToolsKit.isEmpty(captcha) && retryCount >= Constant.SMS_ACCOUNT_MAX_RETRY) {// 第三次就单图形验证码
            throw new ServiceException().setCode(ExceptionEnums.SMS_RETRY_COUNT_MUCH.getCode()).setMessage(ExceptionEnums.SMS_RETRY_COUNT_MUCH.getMessage());
        } else if (ToolsKit.isNotEmpty(captcha)) {
            String tmpCaptcha = smsMessageCacheService.getCaptcha(account);
            if (ToolsKit.isEmpty(tmpCaptcha)) {
                // throw new ServiceException().setCode(ExceptionEnums.SMS_MUCH_TIME_OUT.getCode()).setMessage("图片验证码超时或已过期");
                throw new ServiceException().setCode(ExceptionEnums.SMS_MUCH_TIME_OUT.getCode()).setMessage(i18nUtils.getKey(LocalTools.toast_vercode_notexist, locale));
            }
            if (!captcha.equalsIgnoreCase(tmpCaptcha)) {
                // throw new ServiceException().setCode(ExceptionEnums.SMS_MUCH_TIME_OUT.getCode()).setMessage("输入的验证码不正确");
                throw new ServiceException().setCode(ExceptionEnums.SMS_MUCH_TIME_OUT.getCode()).setMessage(i18nUtils.getKey(LocalTools.toast_vercode_notexist, locale));
            } else {
                // 没抛出异常且验证码是相等的就删除这个KEY
                smsMessageCacheService.removeAccountMaxRetryCount(account);
            }
        }
//        if (ToolsKit.isNotEmpty(captcha)) {
//            String tmpCaptcha = smsMessageCacheService.getCaptcha(account);
//            if (ToolsKit.isEmpty(tmpCaptcha)) {
//                // throw new ServiceException().setCode(ExceptionEnums.SMS_MUCH_TIME_OUT.getCode()).setMessage("图片验证码超时或已过期");
//                throw new ServiceException().setCode(ExceptionEnums.SMS_MUCH_TIME_OUT.getCode()).setMessage(i18nUtils.getKey(LocalTools.toast_vercode_notexist, locale));
//            }
//            if (!captcha.equalsIgnoreCase(tmpCaptcha)) {
//                // throw new ServiceException().setCode(ExceptionEnums.SMS_MUCH_TIME_OUT.getCode()).setMessage("输入的验证码不正确");
//                throw new ServiceException().setCode(ExceptionEnums.SMS_MUCH_TIME_OUT.getCode()).setMessage(i18nUtils.getKey(LocalTools.toast_vercode_notexist, locale));
//            } else {
//                // 没抛出异常且验证码是相等的就删除这个KEY
//                smsMessageCacheService.removeAccountMaxRetryCount(account);
//            }
//        } else {
//            throw new ServiceException().setCode(ExceptionEnums.SMS_RETRY_COUNT_MUCH.getCode()).setMessage(ExceptionEnums.SMS_RETRY_COUNT_MUCH.getMessage());
//        }
    }

    /**
     * 获取短信验证码
     *
     * @param phone
     *            手机号
     * @param from
     *            获取途径
     * @return
     * @throws ServiceException
     */
    public void message(String phone, String from, String ip, String captcha, Boolean overseas, Locale locale) throws Exception {
        try {
            if (ToolsKit.isEmpty(phone)) {
                throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage(i18nUtils.getKey(LocalTools.toast_mobile_notblank, locale));
            }
            String time = smsMessageCacheService.getSmsCodeIntervalTime(phone);
            if (ToolsKit.isEmpty(captcha) && ToolsKit.isNotEmpty(time)) {// 60秒内不能重复发送短信
                String msg = i18nUtils.getKey(LocalTools.toast_opbusy_5sretry, locale).replaceAll("5", smsMessageCacheService.getIntervalTime(phone) + "");
                throw new ServiceException().setCode(ExceptionEnums.ERROR.getCode()).setMessage(msg);
            }
            String ipCount = smsMessageCacheService.getDayIpTotalNum(ip);
            if (ToolsKit.isNotEmpty(ipCount) && Integer.parseInt(ipCount) >= Constant.SMS_IP_MAX_RETRY) {// 超出单日IP访问次数
                throw new ServiceException().setCode(ExceptionEnums.ERROR.getCode()).setMessage(i18nUtils.getKey(LocalTools.toast_op_freq_try, locale));
            }
            String smsCount = smsMessageCacheService.getDayMaxTotalNum(phone);
            if (ToolsKit.isNotEmpty(smsCount) && Integer.parseInt(smsCount) >= Constant.DAY_SMS_MAX_TOTAL_NUM) {// 超出单日验证码次数
                throw new ServiceException().setCode(ExceptionEnums.MSG_MAX_ERROR.getCode()).setMessage(i18nUtils.getKey(LocalTools.toast_op_freq_try, locale));
            }
            String count = smsMessageCacheService.getAccountMaxRetryCount(phone);
            if (ToolsKit.isEmpty(count)) count = ToolsConst.STATUS_0 + StringUtils.EMPTY;
            if (ToolsKit.isEmpty(ipCount)) ipCount = ToolsConst.STATUS_0 + StringUtils.EMPTY;
            /** 验证码次数 ：5 */
            if (ToolsKit.isEmpty(smsCount)) smsCount = ToolsConst.STATUS_0 + StringUtils.EMPTY;
            // 添加验证码判断
            checkCaptcha(Integer.parseInt(count), phone, captcha, ip, locale);
            if (SmsTypeEnums.BINDING.getValue().equals(from)) { // 0注册, 1绑定手机

//                UserAccountDto userAccountDto = userAccountService.getUserAccountDto(phone, "bymobile");
                UserAccountDto userAccountDto = userAccountService.getUserAccountDto(phone, "bymobile", StringUtils.EMPTY, locale);
                if (ToolsKit.isEmpty(userAccountDto) || ToolsKit.isEmpty(userAccountDto.getMobileAccount())) {
                    throw new ServiceException().setCode(ExceptionEnums.ERROR.getCode()).setMessage(i18nUtils.getKey(LocalTools.toast_acc_notexist, locale));
                }
            }
            System.out.println("IP单日上限：" + ipCount + "  单日验证码上限：" + smsCount + "  验证码弹窗次数：" + count + "  phone: " + phone);
            String code = this.getCode(6);
            smsMessageCacheService.setMsgValidatorCode(phone, code);// 保存单次验证码
            smsMessageCacheService.setAccountMaxRetryCount(phone);// 弹窗触发次数
            smsMessageCacheService.setDayMaxTotalNum(phone);// 单日验证码上限
            smsMessageCacheService.setDayIpTotalNum(ip);// 单日IP上限
            smsMessageCacheService.setSmsCodeIntervalTime(phone);// 验证码调用间隔
            Map<String, String> paramMap = new HashMap<String, String>();
            paramMap.put("code", code);

            // 模板类型: 1为中国大陆，否则为海外
            String smsTemplateType = "1";
            if (overseas) {
                if (!isChinaMobileNumber("", phone)) {
                    // 海外短信模板
                    if (locale.equals(Locale.US)) {
                        smsTemplateType = "2";
                    } else if (locale.equals(Locale.JAPAN)) {
                        smsTemplateType = "3";
                    } else if (locale.equals(Locale.KOREA)) {
                        smsTemplateType = "4";
                    } else if (locale.equals(Locale.TRADITIONAL_CHINESE)) {
                        smsTemplateType = "5";
                    } else {
                        smsTemplateType = "2";
                    }
                } else {
                    // 国内短信模板
                    if (locale.equals(Locale.US)) {
                        smsTemplateType = "6";
                    } else if (locale.equals(Locale.JAPAN)) {
                        smsTemplateType = "7";
                    } else if (locale.equals(Locale.KOREA)) {
                        smsTemplateType = "8";
                    } else if (locale.equals(Locale.TRADITIONAL_CHINESE)) {
                        smsTemplateType = "9";
                    }
                }
            }

            /** 使用当前发送短信的次数来选择运营商 */
            SmsChanelEnum smsChanel = SmsChanelEnum.ALIYUN;
            int randomNum = ToolsKit.Number.getRandomNum(0, 11);
            // if (randomNum % 2 != 0) {
            // smsChanel = SmsChanelEnum.WELINK;
            // }
            System.out.println(JsonKit.toJsonString(smsChanel));
            SmsKit.getInstance().channel(smsChanel).send(phone, smsTemplateType, paramMap);
        } catch (ServiceException e) {
            System.out.println("##########短信发送失败: " + e.getMessage());
            throw e;
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException().setCode(ExceptionEnums.MSG_MAX_ERROR.getCode()).setMessage(i18nUtils.getKey(LocalTools.toast_op_freq_try, locale));
        }
    }

    /**
     * 生成验证码
     * 
     * @return
     */
    public String getCode(int length) {
        Random r = new Random();
        String[] num = { "0", "1", "2", "3", "4", "5", "6", "7", "8", "9"};
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < length; i++) {
            sb.append(num[r.nextInt(num.length)]);
        }
        return sb.toString();
    }

    /**
     * 验证短信验证码
     * 
     * @param account
     *            电话号码
     * @param smsCode
     *            验证码
     * @return
     */
    public SmsStatusDto checkCode(String account, String smsCode, Locale locale) throws ServiceException {
        if (ToolsKit.isEmpty(account)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage(i18nUtils.getKey(LocalTools.toast_mobile_notblank, locale));
        }
        if (ToolsKit.isEmpty(smsCode)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage(i18nUtils.getKey(LocalTools.toast_msgcode_notblank, locale));
        }
        SmsStatusDto dto = new SmsStatusDto();
        boolean codeStatus = false;
        if ("********".equals(smsCode)) {// 如果是万能验证码直接返回成功
            codeStatus = true;
            dto.setCodeStatus(codeStatus);
            return dto;
        }
        if (ToolsKit.isEmpty(account) || ToolsKit.isEmpty(smsCode)) {
            codeStatus = false;
        }
        String time = smsMessageCacheService.getDayMaxTotalNum(account);
        if (ToolsKit.isEmpty(time)) time = "0";
        if (ToolsKit.isNotEmpty(time) && Integer.parseInt(time) > Constant.DAY_SMS_MAX_TOTAL_NUM) {
            System.out.println("您的验证码验证太频繁了，请明天再试！");
            codeStatus = false;
        }
        String code = smsMessageCacheService.getMsgValidatorCode(account);
        if (smsCode.equals(code)) {
            codeStatus = true;
        } else {
            codeStatus = false;
        }
        dto.setCodeStatus(codeStatus);
        return dto;
    }

    /**
     * 发送短信
     * 
     * @param phone
     *            电话号码
     * @param type
     *            模板类型
     * @param paramMap
     *            模板参数
     * @return
     * @throws ServiceException
     */
    public SmsMessage sendSmsCode(String phone, String type, String paramMap, Locale locale) throws ServiceException {
        if (ToolsKit.isEmpty(phone)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage(i18nUtils.getKey(LocalTools.toast_mobile_notblank, locale));
        }
        if (ToolsKit.isEmpty(type)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage(i18nUtils.getKey(LocalTools.toast_infotype_notblank, locale));
        }
        /** 使用当前发送短信的次数来选择运营商 */
        SmsChanelEnum smsChanel = SmsChanelEnum.ALIYUN;
        int randomNum = ToolsKit.Number.getRandomNum(0, 11);
        // if (randomNum % 2 != 0) {
        // smsChanel = SmsChanelEnum.WELINK;
        // }
        return SmsKit.getInstance().channel(smsChanel).send(phone, type, JsonKit.jsonParseObject(paramMap, Map.class));
    }

    /**
     * 是否是中国大陆手机号
     * @param nationalCode - 国际区号(如中国大陆为 86)
     * @param mobileNumber - 手机号
     * @return
     */
    private static boolean isChinaMobileNumber(String nationalCode, String mobileNumber) {
        boolean isCnMobileNumber = false;
        Pattern pattern = Pattern.compile(ToolUtils.CHINA_MOBILE_PATTERN);
        Matcher matcher = pattern.matcher(new StringBuilder().append(nationalCode).append(mobileNumber).toString());
        if (matcher.matches()) {
            isCnMobileNumber = true;
        }
        return isCnMobileNumber;
    }

}
