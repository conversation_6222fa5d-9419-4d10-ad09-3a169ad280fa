package net.snaptag.system.smscenter.dto;
import java.io.Serializable;

public class SystemStatusDto implements Serializable {
    /**
     * 系统状态信息--服务器返回
     */
    private static final long serialVersionUID = 1L;
    private int               statusCount;          // 状态数量
    private boolean           status;               // 状态

    public int getStatusCount() {
        return statusCount;
    }

    public void setStatusCount(int statusCount) {
        this.statusCount = statusCount;
    }

    public boolean getStatus() {
        return status;
    }

    public void setStatus(boolean status) {
        this.status = status;
    }
}
