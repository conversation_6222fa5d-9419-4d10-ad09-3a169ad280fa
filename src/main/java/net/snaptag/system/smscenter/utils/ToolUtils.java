package net.snaptag.system.smscenter.utils;

import net.snaptag.system.sadais.util.core.ToolsKit;

import javax.servlet.http.HttpServletRequest;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.util.Random;

public class ToolUtils {

    public static final String CHINA_MOBILE_PATTERN = "^(\\+?0?86\\-?)?1[345789]\\d{9}$";

    /**
     * 获取IP地址
     * 
     * @param request
     * @return
     */
    public static String getIp(HttpServletRequest request) {
        String ip = request.getHeader("X-Forwarded-For");
        if (ToolsKit.isEmpty(ip)) {
            ip = request.getHeader("X-Real-IP");
            if (ToolsKit.isEmpty(ip)) {
                ip = request.getRemoteHost();
            }
        }
        return ip.split(",")[0];
    }

    /**
     * 获取请求参数
     * 
     * @param request
     * @param key
     * @return
     */
    public static String getRequestValue(HttpServletRequest request, String key) {
        try {
            if (key.indexOf("[]") > -1) {
                String[] tmpArray = request.getParameterValues(key);
                StringBuilder sb = new StringBuilder();
                for (String str : tmpArray) {
                    sb.append(str + ",");
                }
                if (ToolsKit.isNotEmpty(sb)) sb.deleteCharAt(sb.length() - 1);
                return ToolsKit.isNotEmpty(sb) ? sb.toString() : "";
            }
            String values = request.getParameter(key);
            if (ToolsKit.isEmpty(values)) {
                values = ToolsKit.isEmpty(request.getAttribute(key)) ? "" : request.getAttribute(key).toString();
            }
            return values;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 画验证码
     * 
     * @param image
     * @return
     */
    public static String drawGraphic(BufferedImage image) {
        // 获取图形上下文
        Graphics g = image.createGraphics();
        // 生成随机类
        Random random = new Random();
        // 设定背景色
        g.setColor(getRandColor(200, 250));
        g.fillRect(0, 0, Constant.WIDTH, Constant.HEIGHT);
        // 设定字体
        g.setFont(new Font("Times New Roman", Font.PLAIN, 18));
        // 随机产生155条干扰线，使图象中的认证码不易被其它程序探测到
        g.setColor(getRandColor(160, 200));
        for (int i = 0; i < 155; i++) {
            int x = random.nextInt(Constant.WIDTH);
            int y = random.nextInt(Constant.HEIGHT);
            int xl = random.nextInt(12);
            int yl = random.nextInt(12);
            g.drawLine(x, y, x + xl, y + yl);
        }
        String sRand = "";
        // 取随机产生的认证码(4位数字)
        for (int i = 0; i < 4; i++) {
            String rand = String.valueOf(Constant.strArr[random.nextInt(Constant.strArr.length)]);
            sRand += rand;
            // 将认证码显示到图象中
            g.setColor(new Color(20 + random.nextInt(110), 20 + random.nextInt(110), 20 + random.nextInt(110)));
            // 调用函数出来的颜色相同，可能是因为种子太接近，所以只能直接生成
            g.drawString(rand, 16 * i + 11, 19);
        }
        // 图象生效
        g.dispose();
        return sRand.toLowerCase();
    }

    /**
     * 给定范围获得随机颜色
     * 
     * @param fc
     * @param bc
     * @return
     */
    private static Color getRandColor(int fc, int bc) {
        Random random = new Random();
        if (fc > 255) fc = 255;
        if (bc > 255) bc = 255;
        int r = fc + random.nextInt(bc - fc);
        int g = fc + random.nextInt(bc - fc);
        int b = fc + random.nextInt(bc - fc);
        return new Color(r, g, b);
    }
}
