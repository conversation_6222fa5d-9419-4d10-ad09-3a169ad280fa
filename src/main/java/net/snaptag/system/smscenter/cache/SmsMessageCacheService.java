package net.snaptag.system.smscenter.cache;

import cn.hutool.core.date.DatePattern;
import net.snaptag.system.sadais.cache.kit.CacheKit;
import net.snaptag.system.sadais.core.common.core.ToolsConst;
import net.snaptag.system.smscenter.enums.SmsMessageCacheEnums;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * 短信验证码缓存
 */
@Service
public class SmsMessageCacheService {
    /**
     * 设置验证码弹窗次数
     * 
     * @param account
     *            手机号码
     */
    public void setAccountMaxRetryCount(String account) {
        String date = net.snaptag.system.sadais.util.core.ToolsKit.Date.format(new Date(), DatePattern.PURE_DATE_PATTERN);
        String key = SmsMessageCacheEnums.MESSAGE_ACCOUNT_RETRY.getKey() + date + ":" + account;
        CacheKit.cache().incr(key, ToolsConst.DAY_SECOND);
    }

    /**
     * 获取验证码弹窗次数
     * 
     * @param account
     *            手机号码
     * @return
     */
    public String getAccountMaxRetryCount(String account) {
        String date = net.snaptag.system.sadais.util.core.ToolsKit.Date.format(new Date(), DatePattern.PURE_DATE_PATTERN);
        String key = SmsMessageCacheEnums.MESSAGE_ACCOUNT_RETRY.getKey() + date + ":" + account;
        return CacheKit.cache().get(key, String.class);
    }

    /**
     * 移除验证码弹窗次数
     * 
     * @param account
     *            手机号码
     * @return
     */
    public void removeAccountMaxRetryCount(String account) {
        String date = net.snaptag.system.sadais.util.core.ToolsKit.Date.format(new Date(), DatePattern.PURE_DATE_PATTERN);
        String key = SmsMessageCacheEnums.MESSAGE_ACCOUNT_RETRY.getKey() + date + ":" + account;
        CacheKit.cache().del(key);
    }

    /**
     * 设置短信验证码
     * 
     * @param account
     *            手机号码
     * @return
     */
    public void setMsgValidatorCode(String account, String code) {
        String key = SmsMessageCacheEnums.MESSAGE_VALIDATOR_BY_ACCOUNT.getKey() + account;
        CacheKit.cache().set(key, code, ToolsConst.FIVE_MINUTES);
    }

    /**
     * 删除短信验证码
     * 
     * @param account
     *            手机号码
     * @return
     */
    public void delMsgValidatorCode(String account) {
        String key = SmsMessageCacheEnums.MESSAGE_VALIDATOR_BY_ACCOUNT.getKey() + account;
        CacheKit.cache().del(key);
    }

    /**
     * 获取短信验证码
     * 
     * @param account
     *            手机号码
     * @return
     */
    public String getMsgValidatorCode(String account) {
        String key = SmsMessageCacheEnums.MESSAGE_VALIDATOR_BY_ACCOUNT.getKey() + account;
        return CacheKit.cache().get(key, String.class);
    }

    /**
     * 获取单日验证码上限数
     * 
     * @param account
     *            手机号码
     * @return
     */
    public String getDayMaxTotalNum(String account) {
        String date = net.snaptag.system.sadais.util.core.ToolsKit.Date.format(new Date(), DatePattern.PURE_DATE_PATTERN);
        String key = SmsMessageCacheEnums.MESSAGE_MAX_TOTAL_BY_DAY.getKey() + date + ":" + account;
        return CacheKit.cache().get(key, String.class);
    }

    /**
     * 设置单日验证码上限数
     * 
     * @param account
     *            手机号码
     * @return
     */
    public void setDayMaxTotalNum(String account) {
        String date = net.snaptag.system.sadais.util.core.ToolsKit.Date.format(new Date(), DatePattern.PURE_DATE_PATTERN);
        String key = SmsMessageCacheEnums.MESSAGE_MAX_TOTAL_BY_DAY.getKey() + date + ":" + account;
        CacheKit.cache().incr(key, ToolsConst.DAY_SECOND);
    }

    /**
     * 获取单日ip数量上限数
     * 
     * @param ip
     * @return
     */
    public String getDayIpTotalNum(String ip) {
        String date = net.snaptag.system.sadais.util.core.ToolsKit.Date.format(new Date(), DatePattern.PURE_DATE_PATTERN);
        String key = SmsMessageCacheEnums.MESSAGE_IP_COUNT_BY_DAY.getKey() + date + ":" + ip;
        return CacheKit.cache().get(key, String.class);
    }

    /**
     * 设置单日ip数量上限数
     * 
     * @param ip
     * @return
     */
    public void setDayIpTotalNum(String ip) {
        String date = net.snaptag.system.sadais.util.core.ToolsKit.Date.format(new Date(), DatePattern.PURE_DATE_PATTERN);
        String key = SmsMessageCacheEnums.MESSAGE_IP_COUNT_BY_DAY.getKey() + date + ":" + ip;
        CacheKit.cache().incr(key, ToolsConst.DAY_SECOND);
    }

    /**
     * 设置用户短信验证码时间间隔
     *
     * @param account
     *            手机号码
     */
    public void setSmsCodeIntervalTime(String account) {
        CacheKit.cache().set(SmsMessageCacheEnums.MESSAGE_INTERVAL_TIME.getKey() + account, account, 60);
    }

    /**
     * 获取短信验证码间隔时间
     *
     * @param account
     *            手机号码
     */
    public String getSmsCodeIntervalTime(String account) {
        return CacheKit.cache().get(SmsMessageCacheEnums.MESSAGE_INTERVAL_TIME.getKey() + account, String.class);
    }

    /**
     * 获取验证码剩余间隔时间
     * 
     * @param account
     *            手机号码
     * @return
     */
    public int getIntervalTime(String account) {
        return CacheKit.cache().ttl(SmsMessageCacheEnums.MESSAGE_INTERVAL_TIME.getKey() + account).intValue();
    }

    /**
     * 获取图片验证码
     * 
     * @param account
     * @return
     */
    public String getCaptcha(String account) {
        String key = SmsMessageCacheEnums.MESSAGE_CAPTCHA_PRE.getKey() + ":" + account;
        return CacheKit.cache().get(key, String.class);
    }

    /**
     * 保存图片验证码
     * 
     * @param account
     * @param vCode
     */
    public void setCaptcha(String account, String vCode) {
        String key = SmsMessageCacheEnums.MESSAGE_CAPTCHA_PRE.getKey() + ":" + account;
        CacheKit.cache().set(key, vCode, ToolsConst.FIVE_MINUTES);
    }
}
