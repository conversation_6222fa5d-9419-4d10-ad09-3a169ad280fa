package net.snaptag.system.smscenter.controller;

import net.snaptag.system.sadais.core.common.enums.ExceptionEnums;
import net.snaptag.system.sadais.core.common.exception.ServiceException;
import net.snaptag.system.sadais.web.core.BaseController;
import net.snaptag.system.sadais.web.dto.ReturnDto;
import net.snaptag.system.smscenter.buservice.SystemStatusBuService;
import net.snaptag.system.smscenter.utils.Constant;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(Constant.PRE_MAPPING_URL + "/status")
public class SystemStatusController extends BaseController {
    @Autowired
    private SystemStatusBuService systemStatusBuService;

    /**
     * 保存状态信息
     * @return
     */
    @RequestMapping(value = "/savestatus")
    public ReturnDto saveStatus() {
        try {
            String key = this.getValue("key");
            String account = this.getValue("account");
            String seconds = this.getValue("seconds");
            systemStatusBuService.saveStatus(key, account, seconds);
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, null);
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

//    /**
//     * 获取状态信息
//     * @return
//     */
//    @RequestMapping(value = "/getstatuscount")
//    public ReturnDto getStatusCount() {
//        try {
//            String key = this.getValue("key");
//            String account = this.getValue("account");
//            return this.returnSuccessJson(ExceptionEnums.SUCCESS, systemStatusBuService.getStatusCount(key, account));
//        } catch (ServiceException e) {
//            return this.returnFailJson(e);
//        }
//    }
}
