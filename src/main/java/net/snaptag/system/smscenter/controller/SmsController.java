package net.snaptag.system.smscenter.controller;

import net.snaptag.system.sadais.core.common.enums.ExceptionEnums;
import net.snaptag.system.sadais.core.common.exception.ServiceException;
import net.snaptag.system.sadais.util.core.ToolsKit;
import net.snaptag.system.sadais.web.core.BaseController;
import net.snaptag.system.sadais.web.core.I18nUtils;
import net.snaptag.system.sadais.web.dto.HeadInfoDto;
import net.snaptag.system.sadais.web.dto.ReturnDto;
import net.snaptag.system.sadais.web.utils.Encodes;
import net.snaptag.system.sadais.web.utils.LocalTools;
import net.snaptag.system.sadais.web.utils.WebTools;
import net.snaptag.system.smscenter.buservice.SmsMessageBuService;
import net.snaptag.system.smscenter.enums.MobileRegularExp;
import net.snaptag.system.smscenter.utils.Constant;
import net.snaptag.system.smscenter.utils.ToolUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.imageio.ImageIO;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletResponse;
import java.awt.image.BufferedImage;
import java.io.IOException;
import java.util.Locale;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@RestController
@RequestMapping(Constant.PRE_MAPPING_URL + "/sms")
public class SmsController extends BaseController {

    @Autowired
    private SmsMessageBuService smsMessageBuService;
    @Autowired
    private I18nUtils i18nUtils;

    /**
     * 短信获取验证码
     * @return
     */
    @RequestMapping(value = "/validatorcode")
    public ReturnDto validatorCode() {
        HeadInfoDto headInfoDto = this.getHeadInfoDto();
        Locale locale = WebTools.getLocaleByLanguage(headInfoDto.getLanguage());
        try {
            String from = this.getValue("from");
            String captcha = this.getValue("captcha");
            String nationCode = this.getValue("nationcode");
            String phone = this.getValue("phone");

            if (ToolsKit.isEmpty(phone) || !isMobileNumber(nationCode, phone)) {
                throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage(i18nUtils.getKey(LocalTools.toast_mobile_invalid, locale));
            }
            phone = getMobileAccount(nationCode, phone);

            String userAgent2 = this.getValue("allowhost");// H5
            boolean isPhoneRequest = true;
            if (headInfoDto.getUaFlag().indexOf("(iPhone; iOS") > -1 || headInfoDto.getUaFlag().indexOf("(iPad; iOS") > -1) {
                isPhoneRequest = true;
            } else if (headInfoDto.getUaFlag().indexOf("Android") > -1) {
                isPhoneRequest = true;
            } else if (ToolsKit.isNotEmpty(userAgent2)) {
                isPhoneRequest = true;
                if (ToolsKit.isEmpty(captcha)) {
                    throw new ServiceException().setCode(ExceptionEnums.ERROR.getCode()).setMessage(i18nUtils.getKey(LocalTools.toast_vercode_notexist, locale));
                }
            }
            if (isPhoneRequest) {
                String ip = ToolUtils.getIp(this.getRequest());
                smsMessageBuService.message(phone, from, ip, captcha, headInfoDto.getOverseas(), locale);
                return this.returnSuccessJson(ExceptionEnums.SUCCESS, null);
            } else {
                throw new ServiceException().setCode(ExceptionEnums.ERROR.getCode()).setMessage(i18nUtils.getKey(LocalTools.toast_op_freq_try, locale));
            }
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        } catch (Exception e) {
            return this.returnFailJson(new ServiceException().setCode(ExceptionEnums.ERROR.getCode()).setMessage(i18nUtils.getKey(LocalTools.toast_vercode_getfail, locale)));
        }
    }

    /**
     * 验证短信验证码
     * @return
     */
    @RequestMapping(value = "/checkcode")
    public ReturnDto checkCode() {
        try {
            HeadInfoDto headInfoDto = this.getHeadInfoDto();
            Locale locale = WebTools.getLocaleByLanguage(headInfoDto.getLanguage());
            String nationCode = this.getValue("nationcode");
            String phone = this.getValue("phone");
            String code = this.getValue("code");

            if (ToolsKit.isEmpty(phone) || !isMobileNumber(nationCode, phone)) {
                throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage(i18nUtils.getKey(LocalTools.toast_mobile_invalid, locale));
            }
            phone = getMobileAccount(nationCode, phone);

            return this.returnSuccessJson(ExceptionEnums.SUCCESS, smsMessageBuService.checkCode(phone, code, locale));
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    /**
     * 验证短信验证码
     * @return
     */
    @RequestMapping(value = "/sendsmscode")
    public ReturnDto sendSmsCode() {
        try {
            HeadInfoDto headInfoDto = this.getHeadInfoDto();
            Locale locale = WebTools.getLocaleByLanguage(headInfoDto.getLanguage());
            String nationCode = this.getValue("nationcode");
            String phone = this.getValue("phone");
            String type = this.getValue("type");
            String paramMap = this.getValue("parammap");

            if (ToolsKit.isEmpty(phone) || !isMobileNumber(nationCode, phone)) {
                throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage(i18nUtils.getKey(LocalTools.toast_mobile_invalid, locale));
            }
            phone = getMobileAccount(nationCode, phone);

            return this.returnSuccessJson(ExceptionEnums.SUCCESS, smsMessageBuService.sendSmsCode(phone, type, paramMap, locale));
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    /**
     * 获取图片验证码
     */
    @RequestMapping(value = "/getcaptcha")
    public void getCaptcha() {
        HeadInfoDto headInfoDto = this.getHeadInfoDto();
        Locale locale = WebTools.getLocaleByLanguage(headInfoDto.getLanguage());
        HttpServletResponse response = this.getResponse();
        String phone = this.getValue("phone");

        if (ToolsKit.isEmpty(phone)) {
            this.returnFailJson(new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage(i18nUtils.getKey(LocalTools.toast_mobile_notblank, locale)));
            return;
        }
        BufferedImage image = new BufferedImage(Constant.WIDTH, Constant.HEIGHT, BufferedImage.TYPE_INT_RGB);
        // 生成出来的验证码(明文)
        String code = ToolUtils.drawGraphic(image);
        smsMessageBuService.setCaptcha(phone, code);
        code = Encodes.encodeHex(code.getBytes());
        Cookie cookie = new Cookie(Constant.RANDOM_CODE_KEY, code);
        cookie.setMaxAge(-1);
        cookie.setPath("/api/");
        response.addCookie(cookie);
        response.setHeader("Pragma", "no-cache");
        response.setHeader("Cache-Control", "no-cache");
        response.setDateHeader("Expires", 0);
        response.setContentType("image/jpeg");
        ServletOutputStream sos = null;
        try {
            sos = response.getOutputStream();
            ImageIO.write(image, "jpeg", sos);
        } catch (Exception e) {
            throw new RuntimeException(e);
        } finally {
            if (sos != null) try {
                sos.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    private static boolean isMobileNumber(String nationalCode, String mobileNumber) {
        boolean isMobileNumber = false;
        String natCode = "";
        if (nationalCode != null) {
            natCode = nationalCode;
        }
        for (MobileRegularExp regularExp : MobileRegularExp.values()) {
            Pattern pattern = Pattern.compile(regularExp.getRegularExp());
            Matcher matcher = pattern.matcher(new StringBuilder().append(natCode).append(mobileNumber).toString());
            if (matcher.matches()) {
                isMobileNumber = true;
                // 枚举中把最常用的国际区号拍在前面可以减少校验开销
                break;
            }
        }
        return isMobileNumber;
    }

    private String getMobileAccount(String nationCode, String account) {
        String strMobile = account;
        if (nationCode != null) {
            strMobile = nationCode + account;
        }
        if (!strMobile.matches(ToolUtils.CHINA_MOBILE_PATTERN)) {
            // 除了中国大陆外，手机号前加上区号
            account = strMobile;
        } else {
            if (account.length() > 11) {
                // 中国大陆手机去掉前面区号
                account = account.substring(account.length() - 11);
            }
        }
        return account;
    }

}
