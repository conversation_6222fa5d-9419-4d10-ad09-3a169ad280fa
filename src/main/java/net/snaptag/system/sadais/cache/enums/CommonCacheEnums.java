package net.snaptag.system.sadais.cache.enums;

import net.snaptag.system.sadais.core.common.enums.ICacheEnums;

import java.util.LinkedHashMap;

public enum CommonCacheEnums implements ICacheEnums {
    COMMON_FLAG_BY("pc:comm:flg:by:", "公共缓存标识");
    private final String                               key;
    private final String                               desc;
    private static final LinkedHashMap<String, String> map;
    static {
        map = new LinkedHashMap<String, String>();
        for (CommonCacheEnums commonCacheEnums : CommonCacheEnums.values()) {
            map.put(commonCacheEnums.getKey(), commonCacheEnums.getDesc());
        }
    }

    private CommonCacheEnums(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public String getKey() {
        return key;
    }

    public String getDesc() {
        return desc;
    }

    public static LinkedHashMap<String, String> getMap() {
        return map;
    }
}
