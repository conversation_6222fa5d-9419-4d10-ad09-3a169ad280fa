package net.snaptag.system.sadais.cache.utils;

import com.alibaba.fastjson.TypeReference;
import net.snaptag.system.sadais.web.common.JsonKit;
import java.io.UnsupportedEncodingException;
import java.nio.charset.StandardCharsets;
import java.util.List;

/**
 * SafeEncoder替代实现
 */
class SafeEncoder {
    public static byte[] encode(String str) {
        return str != null ? str.getBytes(StandardCharsets.UTF_8) : new byte[0];
    }
}

public class SerializableUtils {
    private SerializableUtils() {
    }

    public static <T> String serializeString(T obj) {
        return JsonKit.toJsonString(obj);
    }

    public static <T> byte[] serialize(T obj) {
        return SafeEncoder.encode(JsonKit.toJsonString(obj));
    }

    public static <T> T deserialize(byte[] data, Class<T> clazz) {
        try {
            return JsonKit.jsonParseObject(data, clazz);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public static <T> T deserialize(byte[] data, TypeReference<T> type) {
        try {
            return JsonKit.jsonParseObject(new String(data, "UTF-8"), type);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public static <T> List<T> deserializeArray(byte[] data, Class<T> clazz) {
        try {
            return JsonKit.jsonParseArray(new String(data, "UTF-8"), clazz);
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
            return null;
        }
    }
}
