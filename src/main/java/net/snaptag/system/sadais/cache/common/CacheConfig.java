package net.snaptag.system.sadais.cache.common;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class CacheConfig {
    private static final Logger logger   = LoggerFactory.getLogger(CacheConfig.class);
    protected String            host;
    protected String            password;
    protected int               port;
    protected int               database = 0;
    private static boolean      isCluster;
    private static CacheConfig cacheConfig;

    public static CacheConfig getInstance() {
        try {
            if (null == cacheConfig) {
                cacheConfig = new CacheConfig();
            }
        } catch (Exception e) {
            logger.warn(e.getMessage(), e);
        }
        return cacheConfig;
    }

    private CacheConfig() {
    }

    public static boolean isCluster() {
        return isCluster;
    }

    public void setCluster(boolean cluster) {
        isCluster = cluster;
    }

    public int getDatabase() {
        return database;
    }

    public void setDatabase(int database) {
        this.database = database;
    }

    public String getHost() {
        return host;
    }

    public void setHost(String host) {
        this.host = host;
        // 如果host字段里包含有,号的话，则代表有一个以上的地址，认为是一个集群环境
        setCluster(host.contains(","));
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public int getPort() {
        return port;
    }

    public void setPort(int port) {
        this.port = port;
    }
}
