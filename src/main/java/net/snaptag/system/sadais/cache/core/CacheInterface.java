package net.snaptag.system.sadais.cache.core;

import com.alibaba.fastjson.TypeReference;
import net.snaptag.system.sadais.cache.common.RedisListener;
import net.snaptag.system.sadais.cache.model.RedisMessage;
import net.snaptag.system.sadais.cache.model.TupleDto;
import redis.clients.jedis.GeoCoordinate;
import redis.clients.jedis.GeoRadiusResponse;
import redis.clients.jedis.GeoUnit;
import redis.clients.jedis.Tuple;
// import redis.clients.jedis.params.geo.GeoRadiusParam; // 在当前Jedis版本中可能不可用


import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 */
public interface CacheInterface {
    /**
     * 根据key取值
     * 
     * @param key
     * @return
     */
    public <T> T get(final String key, final Class<T> typeReference);

    /**
     * 取值
     * 
     * @param key
     * @param type
     *            泛型
     * @return
     */
    public <T> T get(final String key, final TypeReference<T> type);

    public <T> List<T> getArray(final String key, final Class<T> typeReference);

    /**
     * 按key-value方式将值保存到redis, 缓存时间为seconds, 过期后会自动将该key指向的value删除
     * 
     * @param key
     *            关键字
     * @param value
     *            值
     * @param seconds
     *            缓存时间，秒作单位
     * @return
     */
    public boolean set(final String key, final Object value, final int seconds);

    /**
     * 根据key删除指定的内容
     * 
     * @param keys
     * @return
     */
    public long del(final String... keys);

    /**
     * 将内容添加到list里的第一位
     * 
     * @param key
     *            关键字
     * @param value
     *            内容
     * @return
     */
    public long lpush(final String key, final Object value);

    /**
     * 将内容添加到list里的最后一位
     * 
     * @param key
     *            关键字
     * @param value
     *            内容
     * @return
     */
    public long rpush(final String key, final Object value);

    /**
     * 根据key取出list集合
     * 
     * @param key
     *            关键字
     * @param start
     *            开始位置(0表示第一个元素)
     * @param end
     *            结束位置(-1表示最后一个元素)
     * @return
     */
    public List<String> lrange(final String key, final int start, final int end);

    public long lrem(final String key, final int count, final Object value);

    /**
     * 向名称为key的hash中添加元素(map)
     * 
     * @param key
     * @param values
     *            map<String,?>
     * @return
     */
    public boolean hmset(final String key, final Map<String, String> values, final int seconds);

    /**
     * 返回名称为key在hash中fields对应的value
     * 
     * @param key
     *            关键字
     * @param fields
     *            hash中的field
     * @return
     */
    public List<String> hmget(final String key, final String... fields);

    /**
     * 返回名称为key的hash中fields对应的value
     * 
     * @param key
     *            关键字
     * @param fields
     *            hash中的field
     * @return
     */
    public Map<String, String> hmgetToMap(final String key, final String... fields);

    /**
     * 删除指定hash里的field
     * 
     * @param key
     * @param fields
     * @return
     */
    public long hdel(final String key, final String... fields);

    /**
     * 取出指定hash里的所有field
     * 
     * @param key
     * @return
     */
    public Set<String> hkeys(final String key);

    /**
     * 判断hashmap里面是否存在field的key
     * 
     * @param key
     * @param field
     * @return
     */
    public boolean hexists(final String key, final String field);

    /**
     * 返回名称为key的hash中fields对应的value
     * 
     * @param key
     *            关键字
     * @param field
     *            hash中的field
     * @return
     */
    public String hget(final String key, final String field);

    /**
     * key返回哈希表key中，所有的域和值
     * 
     * @param key
     * @return
     */
    public Map<String, String> hgetAll(final String key);

    /**
     * 向有序set里添加元素
     * 
     * @param key
     *            set的key
     * @param value
     *            对应的value
     * @return
     */
    public boolean sadd(final String key, final Object value, final int seconds);

    /**
     * 返回名称为key的set的基数
     * 
     * @param key
     *            set的key
     * @return
     */
    public Long scard(final String key);

    /**
     * 测试member是否是名称为key的set的元素
     * 
     * @param key
     *            Set集合的key
     * @param value
     *            值
     * @return
     */
    public boolean sismember(final String key, final Object value);

    /**
     * 慎用，会导致redis等待结果返回，若是集群模式则直接返回null
     * 
     * @param pattern
     *            正则表达式
     * @return
     */
    public Set<String> keys(final String pattern);

    /**
     * 根据标识取出redis里的集合size
     * 
     * @param type
     *            标识("list", "hash", "set")
     * @param key
     *            关键字
     * @return
     */
    public long size(final String type, final String key);

    /**
     * 根据key判断值类型
     * 
     * @param key
     *            关键字
     * @return 类型名称
     */
    public String type(final String key);

    /**
     * 判断KEY是否存在
     * 
     * @param key
     *            关键字
     * @return 存在返回true
     */
    public boolean exists(final String key);

    /**
     * 根据key设置过期时间
     * 
     * @param key
     * @param seconds
     * @return
     */
    public Long expire(final String key, final Integer seconds);

    /**
     * 保存ZSet<String>
     * 
     * @param key
     * @param sort
     * @param value
     * @return
     */
    public Boolean zadd(final String key, final double sort, final String value, final int seconds);

    /**
     * 删除ZSet元素
     * 
     * @param key
     * @param value
     * @return
     */
    public Long zrem(final String key, final String value);

    /**
     * 由小到大获取member成员在该key的位置
     * 
     * @param key
     * @param member
     * @return
     */
    public Long zrank(final String key, final String member);

    /**
     * 由大到小获取member成员在该key的位置
     * 
     * @param key
     * @param member
     * @return
     */
    public Long zrevrank(final String key, final String member);

    /**
     * 升序获取zset元素
     * 
     * @param key
     * @return
     */
    public List<String> zrevrank(final String key);

    /**
     * 升序获取zset元素
     * 
     * @param key
     * @param start
     * @param end
     * @return
     */
    public List<String> zrevrank(final String key, final int start, final int end);

    /**
     * 降序获取zset元素
     * 
     * @param key
     * @return
     */
    public List<String> zrevrange(final String key);

    /**
     * 降序获取zset元素
     * 
     * @param key
     * @param start
     * @param end
     * @return
     */
    public List<String> zrevrange(final String key, final int start, final int end);

    public List<String> zrange(final String key, final int start, final int end);

    /**
     * 根据区间段获取集合内排名成员--倒序
     * 
     * @param key
     *            分组key
     * @param start
     *            开始位
     * @param end
     *            结束位 当为-1时，为取所有值
     * @return
     */
    public Set<Tuple> zrevrangeWithScores(final String key, final int start, final int end);

    /**
     * 根据key获取list长度
     * 
     * @param key
     * @return
     */
    public Long llen(final String key);

    /**
     * 根据key删除并返回list尾元素
     * 
     * @param key
     * @returnrpop
     */
    public String rpop(final String key);

    /**
     * 将名称为key的hash中field的value增加integer
     * 
     * @param key
     * @param field
     * @param integer
     * @return
     */
    public Long hincrby(final String key, final String field, final Integer integer);

    /**
     * 向名称为key的hash中添加元素field<—>value
     * 
     * @param key
     * @param field
     * @param value
     * @return
     */
    public Long hset(final String key, final String field, final Object value, final int seconds);

    /**
     * 返回名称为key的zset中score>=min且score<=max的所有元素
     *
     * @param key
     * @param min
     * @param max
     * @return
     */
    public Set<String> zrangebyscore(final String key, final double min, final double max);

    /**
     * 返回名称为key的zset中score>=min且score<=max结果之间的区间数据 <br/>
     * offset, count就相当于sql中limit的用法 <br/>
     * select * from table where score >=min and score <=max limit offset count
     *
     * @param key
     * @param min
     * @param max
     * @param offset
     * @param count
     * @return
     */
    public Set<String> zrangebyscore(final String key, final double min, final double max, final int offset, final int count);

    /**
     * 返回名称为key的zset中score>=min且score<=max结果之间的区间数据 <br/>
     * offset, count就相当于sql中limit的用法 <br/>
     * select * from table where score >=min and score <=max limit offset count
     *
     * @param key
     * @param min
     * @param max
     * @param offset
     * @param count
     * @return
     */
    public List<TupleDto> zrangeByScoreWithScores(final String key, final double min, final double max, final int offset, final int count);

    /**
     * 删除名称为key的zset中score>=min且score<=max的所有元素
     */
    public Long zremrangebyscore(final String key, final double min, final double max);

    /**
     * 删除名称为KEY的zeset中rank>=min且rank<=max的所有元素
     * 
     * @param key
     * @param start
     * @param max
     * @return
     */
    public Long zremrangebyrank(final String key, final int start, final int max);

    /**
     * 为某个key自增1
     * 
     * @param key
     * @return
     */
    public Long incr(final String key, final int seconds);

    /**
     * 为某个key自减1
     * 
     * @param key
     * @return
     */
    public Long decr(final String key, final int seconds);

    /**
     * 获取set的基数
     * 
     * @param key
     * @return
     */
    public Long zcard(final String key);

    /**
     * 返回key的有效时间
     * 
     * @param key
     * @return
     */
    public Long ttl(final String key);

    /**
     * 删除set里面和member相同的元素
     * 
     * @param key
     * @param member
     * @return
     */
    public Long srem(final String key, final String member);

    /**
     * 获取set对象
     * 
     * @param key
     * @return
     */
    public Set<String> smembers(final String key);

    /**
     * 获取zset里面元素的soce
     * 
     * @param key
     * @return
     */
    public Double zscore(final String key, final String member);

    /**
     * 返回 key 指定的哈希集中所有字段的值
     * 
     * @param key
     * @return
     */
    public List<String> hvals(final String key);

    /**
     * 添加地理位置
     * 
     * @param key
     *            地理位置集合KEY
     * @param longitude
     *            经度
     * @param latitude
     *            纬度
     * @param member
     *            集合成员值
     * @return
     */
    public long geoadd(final String key, final double longitude, final double latitude, final String member, final int seconds);

    /**
     * 添加地理位置
     * 
     * @param key
     *            地理位置集合KEY
     * @param memberCoordinateMap
     *            成员集合值
     * @return
     */
    public long geoadd(final String key, final Map<String, GeoCoordinate> memberCoordinateMap, final int seconds);

    /**
     * 根据名称获取地理位置信息
     * 
     * @param key
     *            地理位置集合KEY
     * @param members
     *            成员值
     * @return
     */
    public List<GeoCoordinate> geopos(final String key, final String... members);

    /**
     * 计算两个位置之间的距离
     * 
     * @param key
     *            地理位置集合KEY
     * @param member1
     *            成员值
     * @param member2
     *            成员值
     * @parma unit 单位(M/KM)
     * @return
     */
    public Double geodist(final String key, final String member1, final String member2, final GeoUnit unit);

    /**
     * 获取指定范围内的位置信息
     * 
     * @param key
     *            地理位置集合KEY
     * @param longitude
     *            经度
     * @param latitude
     *            纬度
     * @param radius
     *            半径范围
     * @param unit
     *            单位(M/KM)
     * @return
     */
    public List<GeoRadiusResponse> georadius(final String key, final double longitude, final double latitude, final double radius, final GeoUnit unit);

    /**
     * 获取指定范围内的位置信息
     * 
     * @param key
     *            地理位置集合KEY
     * @param longitude
     *            经度
     * @param latitude
     *            纬度
     * @param radius
     *            半径范围
     * @param unit
     *            单位(M/KM)
     * @param param
     *            查询条件参数
     * @return
     */
    // 暂时注释掉，因为GeoRadiusParam在当前Jedis版本中可能不可用
    // public List<GeoRadiusResponse> georadius(final String key, final double longitude, final double latitude, final double radius, final GeoUnit unit,
    //         final GeoRadiusParam param);

    /**
     * 获取存储集合范围内的位置信息
     * 
     * @param key
     *            地理位置集合KEY
     * @param member
     *            成员名称
     * @param radius
     *            半径范围
     * @param unit
     *            单位(M/KM)
     * @return
     */
    public List<GeoRadiusResponse> georadiusByMember(final String key, final String member, final double radius, final GeoUnit unit);

    /**
     * 订阅消息
     * 
     * @param listener
     *            订阅监听器
     * @param channels
     *            订阅渠道
     */
    public void subscribe(final RedisListener listener, final List<String> channels);

    /**
     * 模式匹配方式订阅消息
     * 
     * @param listener
     *            订阅监听器
     * @param channels
     *            订阅渠道
     * @return
     */
    public void psubscribe(final RedisListener listener, final List<String> channels);

    /**
     * 发布消息
     * 
     * @param message
     * @return
     */
    public long publish(final RedisMessage message);
}
