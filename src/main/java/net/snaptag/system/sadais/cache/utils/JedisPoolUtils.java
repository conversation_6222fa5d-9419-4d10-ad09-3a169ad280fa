package net.snaptag.system.sadais.cache.utils;
import net.snaptag.system.sadais.cache.common.CacheConfig;
import net.snaptag.system.sadais.util.core.ToolsKit;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;
import redis.clients.jedis.JedisPoolConfig;
import redis.clients.jedis.exceptions.JedisException;

/**
 *
 */
public class JedisPoolUtils {
    private static JedisPool pool     = null;
    private static String    host;
    private static int       port;
    private static int       timeout  = 10000;  // 增加超时时间到10秒
    private static String    password;
    private static int       database = 0;

    /**
     * 建立连接池 真实环境，一般把配置参数缺抽取出来。
     */
    private static void createJedisPool() {
        // 建立连接池配置参数
        JedisPoolConfig config = new JedisPoolConfig();

        // 连接池大小配置
        config.setMaxTotal(20);                    // 最大连接数
        config.setMaxIdle(10);                     // 最大空闲连接数
        config.setMinIdle(2);                      // 最小空闲连接数
        config.setMaxWaitMillis(10000);            // 获取连接时的最大等待毫秒数

        // 连接有效性检测
        config.setTestOnBorrow(true);              // 在获取连接时检测连接有效性
        config.setTestOnReturn(true);              // 在归还连接时检测连接有效性
        config.setTestWhileIdle(true);             // 在空闲时检测连接有效性

        // 空闲连接检测配置
        config.setTimeBetweenEvictionRunsMillis(30000);  // 空闲连接检测周期(毫秒)
        config.setMinEvictableIdleTimeMillis(60000);     // 连接空闲多久后被回收(毫秒)
        config.setNumTestsPerEvictionRun(3);             // 每次检测的连接数

        // 其他配置
        config.setBlockWhenExhausted(true);        // 连接耗尽时是否阻塞
        config.setLifo(false);                     // 是否启用后进先出
        // 创建连接池
        try {
            database = CacheConfig.getInstance().getDatabase();
            host = CacheConfig.getInstance().getHost();
            password = CacheConfig.getInstance().getPassword();
            port = CacheConfig.getInstance().getPort();
            if (ToolsKit.isEmpty(password)) {
                pool = new JedisPool(config, host, port, timeout);
            } else {
                pool = new JedisPool(config, host, port, timeout, password, database);
            }
            System.out.println("Connent  " + host + ":" + port + " Redis is Success...");
        } catch (Exception e) {
            e.printStackTrace();
            throw new JedisException(e.getMessage(), e);
        }
    }

    public static boolean isSuccess() {
        return ToolsKit.isNotEmpty(pool);
    }

    /**
     * 在多线程环境同步初始化
     */
    private static synchronized void poolInit() throws JedisException {
        if (pool == null) {
            createJedisPool();
        }
    }

    /**
     * 获取一个jedis 对象
     * 
     * @return
     */
    public static Jedis getJedis() {
        if (pool == null) {
            poolInit();
        }
        return pool.getResource();
    }

    /**
     * 归还一个连接
     *
     * @param jedis
     */
    public static void returnResource(Jedis jedis) {
        if (jedis != null) {
            try {
                jedis.close();
            } catch (Exception e) {
                // 记录日志但不抛出异常，避免影响业务流程
                System.err.println("Error closing Jedis connection: " + e.getMessage());
            }
        }
    }

    /**
     * 归还一个损坏的连接
     *
     * @param jedis
     */
    public static void returnBrokenResource(Jedis jedis) {
        if (jedis != null) {
            try {
                jedis.close();
            } catch (Exception e) {
                // 记录日志但不抛出异常，避免影响业务流程
                System.err.println("Error closing broken Jedis connection: " + e.getMessage());
            }
        }
    }

    public static void close() {
        if (pool != null) {
            pool.close();
        }
    }
}