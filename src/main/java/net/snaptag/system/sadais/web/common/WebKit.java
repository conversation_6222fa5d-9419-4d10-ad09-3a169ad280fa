package net.snaptag.system.sadais.web.common;

import cn.hutool.core.date.DatePattern;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.support.spring.FastJsonHttpMessageConverter;
import net.snaptag.system.sadais.core.common.core.ToolsConst;
import net.snaptag.system.sadais.core.common.enums.ExceptionEnums;
import net.snaptag.system.sadais.core.common.enums.IEnums;
import net.snaptag.system.sadais.core.common.exception.ServiceException;
import net.snaptag.system.sadais.core.common.utils.CryptionUtil;
import net.snaptag.system.sadais.util.core.ToolsKit;
import net.snaptag.system.sadais.util.core.ToolsKit.Random;
import net.snaptag.system.sadais.web.config.CommonProperties;
import net.snaptag.system.sadais.web.core.SpringContextHolder;
import net.snaptag.system.sadais.web.dto.*;
import net.snaptag.system.sadais.web.enums.HeadParamEnums;
import net.snaptag.system.sadais.web.enums.LanguageEnums;
import net.snaptag.system.sadais.web.jwt.JWTTokenUtil;
import net.snaptag.system.sadais.web.jwt.SubjectModel;
import net.snaptag.system.sadais.web.queue.QueueKit;
import net.snaptag.system.sadais.web.queue.QueueMessage;
import net.snaptag.system.sadais.web.utils.UrlEncoderUtils;
import io.jsonwebtoken.Claims;
import org.apache.commons.codec.Charsets;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.ServletException;
import javax.servlet.ServletResponse;
//import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;

import static net.snaptag.system.sadais.core.common.enums.ExceptionEnums.NET_EXCEPTION;

public class WebKit {
    private static final Logger        log             = LoggerFactory.getLogger(WebKit.class);
    // 不校验token uri
    private static Map<String, String> noCheckUriMap   = new HashMap<String, String>();
    // 不采集日志 uri
    private static Map<String, String> noCollectUriMap = new HashMap<String, String>();
    // 同时允许userId为空和非空 uri
    private static Map<String, String> allowEmptyUserIdUriMap = new HashMap<String, String>();
    private static Map<String, String> checkUriMap = new HashMap<>();
    static {
        checkUriMap.put("/api/account/v1/login/bindingaccount","1");
        checkUriMap.put("/api/account/v1/login/unbindingaccount","1");
        checkUriMap.put("/api/account/v1/user/updateuserinfo","1");
        checkUriMap.put("/api/account/v1/user/getuserinfo","1");
        checkUriMap.put("/api/business/v1/drafts/getdraftslist","1");
//        checkUriMap.put("/api/business/v1/drafts/addorupdatedrafts","1");
        checkUriMap.put("/api/business/v1/drafts/deldrafts","1");
//        checkUriMap.put("/api/business/v1/drafts/saveresource","1");
//        checkUriMap.put("/api/business/v1/drafts/getresourcedata","1");
        checkUriMap.put("/api/business/v1/feedback/saveuserfeedback","1");
        checkUriMap.put("/api/business/v1/feedback/gethistories","1");
        checkUriMap.put("/api/business/v1/feedback/gethistories2","1");
        checkUriMap.put("/api/business/v1/feedback/getDetail","1");

        checkUriMap.put("/api/business/v1/templatedrafts/addcollect","1");
        checkUriMap.put("/api/business/v1/templatedrafts/cancelcollect","1");

        checkUriMap.put("/api/business/v1/login/unregister","1");


        // 获取最近纸张信息
        

        // 用户相关
//        noCheckUriMap.put("/account/v1/login/getaccesstoken", "1");
//        noCheckUriMap.put("/account/v1/login/login", "1");
//        noCheckUriMap.put("/account/v1/login/register", "1");
//        noCheckUriMap.put("/account/v1/login/resetpwd", "1");
//        noCheckUriMap.put("/account/v1/login/resetpwdforxc", "1");
//        // 短信相关
//        noCheckUriMap.put("/smscenter/v1/sms/validatorcode", "1");
//        noCheckUriMap.put("/smscenter/v1/sms/getcaptcha", "1");
//        // --------------- 海外版 ---------------
//        // 获取手机短信国家区域代码
//        noCheckUriMap.put("/account/v1/login/getSmsCountryCodes", "1");
//        // 获取邮箱获取验证码
//        noCheckUriMap.put("/account/v1/login/mailValidCode", "1");
//        // 邮箱注册登录
//        noCheckUriMap.put("/account/v1/login/registerByMail", "1");
//        // 重设邮箱帐号密码
//        noCheckUriMap.put("/account/v1/login/resetMailPwd", "1");
//        // AppleID登录注册接口
//        noCheckUriMap.put("/account/v1/login/appleLogin", "1");
    }

    /**
     * 打印输出返回内容
     * 
     * @param response
     * @param code
     * @param message
     */
    public static void printResult(ServletResponse response, int code, String message) {
        HttpServletResponse httpResponse = (HttpServletResponse) response;
        httpResponse.setCharacterEncoding(WebConst.ENCODING_FIELD);// 设置utf-8编码
        httpResponse.setHeader("Content-type", MediaType.APPLICATION_JSON_UTF8_VALUE);
        ReturnDto returnDto = WebKit.buildReturnDto(ExceptionEnums.SUCCESS, null);
        returnDto.getHead().setRet(code);
        returnDto.getHead().setMsg(message);
        try {
            response.getWriter().print(JsonKit.toJsonString(returnDto));
        } catch (IOException e1) {
            e1.printStackTrace();
        }
    }

    /**
     * 发送日志采集
     * 
     * @param request
     */
    public static void sendJournal(HttpServletRequest request, String result, String type) {
        try {
            CommonProperties properties = SpringContextHolder.getBean("commonProperties");
            boolean isEventService = properties.getApplicationName().equals(WebConst.SADAIS_SERVICE_EVENT);
            boolean isApiUri = isEventService && request.getRequestURI().equals(WebConst.EVENT_LOG_API_URI);
            boolean isCollect = checkUri(noCollectUriMap, request.getRequestURI());
            if (properties.getEventLogEnable() && (!isEventService || !isApiUri) && !isCollect) {
                QueueMessageDto queueMessageDto = WebKit.fillParamQueueMessage(request, result);
                queueMessageDto.setType(type);
                QueueKit.queue().addToQueue(new QueueMessage(queueMessageDto, "RequestEventLogBuService"));
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 填充请求信息到map
     * 
     * @param request
     */
    public static QueueMessageDto fillParamQueueMessage(HttpServletRequest request, String result) {
        String remoteIp = getValue(WebConst.FORWARDED_FOR, request); // 客户端真实 IP
        remoteIp = ToolsKit.isEmpty(remoteIp) ? request.getRemoteHost() : remoteIp.split(",")[0];
        String intranetIp = getValue(WebConst.REAL_IP, request); // slb的内网IP
        String host = getValue(HttpHeaders.HOST, request);
        if (ToolsKit.isEmpty(host)) host = request.getRemoteHost();
        String scheme = getValue(WebConst.FORWARDED_PROTO, request);
        if (ToolsKit.isEmpty(scheme)) scheme = request.getScheme();
        QueueMessageDto dto = new QueueMessageDto();
        dto.setMethod(request.getMethod().toUpperCase());
        dto.setHost(host);
        dto.setRemoteIp(remoteIp);
        dto.setIntranetIp(intranetIp);
        dto.setRequestDate(ToolsKit.Date.format(new Date(), DatePattern.NORM_DATETIME_MS_PATTERN));
        dto.setUri(request.getRequestURI());
        dto.setScheme(scheme);
        dto.setContentType(ToolsKit.isEmpty(request.getContentType()) ? MediaType.APPLICATION_JSON_UTF8_VALUE : request.getContentType());
        dto.setRequestId(getValue(WebConst.SADAIS_REQUEST_ID_FIELD, request));
        String jsonString = getHeadInfoDto(request);
        if (ToolsKit.isNotEmpty(jsonString)) {
            HeadInfoDto headInfoDto = JsonKit.jsonParseObject(jsonString, HeadInfoDto.class);
            dto.setHead(headInfoDto.getHeader());
            dto.setUaFlag(headInfoDto.getUaFlag());
        }
        dto.setParam(getValue(WebConst.JSON_POST_DATA, request));
        dto.setResult(result);
        if (ToolsKit.isNotEmpty(WebKit.getValue(WebConst.FEIGN_FLAG_KEY, request))) {
            dto.setSource(WebConst.REQUEST_SOURCE_FEIGN);
        } else {
            dto.setSource(WebConst.REQUEST_SOURCE_PHONE);
        }
        return dto;
    }

    /**
     * 打印请求信息
     * 
     * @param request
     */
    public static void printRequestInfo(HttpServletRequest request) {
        try {
            String remoteIp = getValue(WebConst.FORWARDED_FOR, request); // 客户端真实 IP
            remoteIp = ToolsKit.isEmpty(remoteIp) ? request.getRemoteHost() : remoteIp.split(",")[0];
            String intranetIp = getValue(WebConst.REAL_IP, request); // slb的内网IP
            String host = getValue(HttpHeaders.HOST, request);
            if (ToolsKit.isEmpty(host)) host = request.getRemoteHost();
            String scheme = getValue(WebConst.FORWARDED_PROTO, request);
            if (ToolsKit.isEmpty(scheme)) scheme = request.getScheme();
            String requestId = getValue(WebConst.SADAIS_REQUEST_ID_FIELD, request);
            String contentType = request.getContentType();
            String header = getValue(WebConst.HEAD_INFO_DATA, request);
            String method = request.getMethod();
            String url = scheme + "://" + host + request.getRequestURI();
            String date = ToolsKit.Date.format(new Date(), DatePattern.NORM_DATETIME_MS_PATTERN);
            String param = getValue(WebConst.JSON_POST_DATA, request);
            print(requestId, remoteIp, intranetIp, contentType, method, url, date, header, param);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void print(String requestId, String remoteIp, String intranetIp, String contentType, String method, String url, String date, String header,
            String param) {
        log.info("******************************************************************************");
        log.info("###########  RequestId:   " + requestId);
        log.info("###########  RequestDate:   " + date);
        log.info("###########  RemoteIp: " + remoteIp);
        log.info("###########  IntranetIp: " + intranetIp);
        log.info("###########  RequestHeader: " + header);
        log.info("###########  RequestURL:    " + url);
        log.info("###########  RemoteMethod:  " + method);
        log.info("###########  getContentType:  " + contentType);
        log.info("###########  RequestValues: " + param);
        log.info("******************************************************************************");
    }

    /**
     * 解析head请求头信息
     * 
     * @param request
     */
    public static void parseHeadInfo(HttpServletRequest request) {
        try {
            request.setAttribute(WebConst.HEAD_INFO_DATA, getHeadInfoDto(request));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 设置zuul网关过来的流参数
     * 
     * @param request
     * @throws IOException
     */
    public static void fillZuulStreamParam(HttpServletRequest request) {
        try {
            String body = IOUtils.toString(request.getInputStream(), Charsets.UTF_8.name());
            System.out.println("老body="+body);
            if (ToolsKit.isNotEmpty(body)) {
                Map<String, Object> jsonMap = null;
                if (JsonKit.isMapJsonString(body)) {
                    jsonMap = JsonKit.jsonParseObject(body, HashMap.class);
                } else {
                    String[] values = body.split("&");
                    jsonMap = new HashMap<String, Object>();
                    if (ToolsKit.isNotEmpty(values)) {
                        for (String value : values) {
                            String[] keyWords = value.split("=");
                            if (ToolsKit.isNotEmpty(keyWords) && keyWords.length == 2) {
                                jsonMap.put(keyWords[0], keyWords[1]);
                            }
                        }
                    }
                }
                if (ToolsKit.isNotEmpty(jsonMap)) {
                    for (Iterator<String> iter = jsonMap.keySet().iterator(); iter.hasNext();) {
                        String key = iter.next();
                        Object value = jsonMap.get(key);
                        request.setAttribute(key, getValue(value + StringUtils.EMPTY));
                    }
                }
            } else {
                Enumeration<String> params = request.getParameterNames();
                while (params.hasMoreElements()) {
                    String name = (String) params.nextElement();
                    if (JsonKit.isMapJsonString(name)) {
                        Map<String, Object> jsonMap = JsonKit.jsonParseObject(name, HashMap.class);
                        if (ToolsKit.isNotEmpty(jsonMap)) {
                            for (Iterator<String> iter = jsonMap.keySet().iterator(); iter.hasNext();) {
                                String key = iter.next();
                                if (JsonKit.isMapJsonString(key)) {
                                    continue;
                                }
                                Object value = jsonMap.get(key);
                                request.setAttribute(key, getValue(value + StringUtils.EMPTY));
                            }
                        }
                    } else {
                        request.setAttribute(name, getValue(name, request));
                    }
                }
            }
            Map<String, Object> map = getAllParams(request);
            request.setAttribute(WebConst.JSON_POST_DATA, JsonKit.toJsonString(map));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 海外版本映射到大陆的版本
     */
    private static final String OVERSEAS_MAP_VER = "2.9.0";

    /**
     * 获取head请求头信息
     * 
     * @param request
     * @return
     */
    public static String getHeadInfoDto(HttpServletRequest request) {
        String jsonString = getValue(WebConst.HEAD_INFO_DATA, request);
        if (ToolsKit.isNotEmpty(jsonString)) {
            return jsonString;
        } else {
            String agent = getValue(HeadParamEnums.SADAIS_AGENT.getKey(), request);
            String appId = getValue("appid", request);
            String uaFlag = getValue(HeadParamEnums.UA_FLAG.getKey(), request);
            if (ToolsKit.isEmpty(uaFlag)) {
                uaFlag = getValue(HeadParamEnums.USER_AGENT.getKey(), request);
            }
            String uniqueFlag = getValue(HeadParamEnums.UNIQUE_FLAG.getKey(), request);
            String remoteIp = getValue(WebConst.FORWARDED_FOR, request); // 客户端真实 IP
            String language = getValue(HeadParamEnums.LANGUAGE.getKey(), request);
            if (ToolsKit.isEmpty(language)) {
                language = LanguageEnums.ZH_CN.getKey();
            }
            remoteIp = ToolsKit.isEmpty(remoteIp) ? request.getRemoteHost() : remoteIp.split(",")[0];

            // app是否是海外版本，默认不是
            String strOverSeas = getValue("overseas", request);
            Boolean overseas = false;
            if (strOverSeas != null && strOverSeas.trim().equals("1")) {
                overseas = true;
            }

            HeadInfoDto headInfoDto = new HeadInfoDto();
            headInfoDto.setHeader(agent);
            headInfoDto.setUaFlag(uaFlag);
            headInfoDto.setUniqueFlag(uniqueFlag);
            headInfoDto.setIp(remoteIp);
            headInfoDto.setLanguage(language);
            headInfoDto.setOverseas(overseas);
            if (ToolsKit.isNotEmpty(agent)) {
                String[] values = agent.split("/");
                if (values.length > 1) {
                    headInfoDto.setAppName(values[0]);
                    headInfoDto.setVersion(values[1]);
                    // app是海外版本则做版本映射
                    if (overseas) {
                        headInfoDto.setVersion(OVERSEAS_MAP_VER);
                    }
                    headInfoDto.setDeviceSystem(values[2]);
                    headInfoDto.setChannel(values[3]);

                    if (values.length>=5){
                        // 记录设备型号
                        headInfoDto.setPhoneType(values[4]);
                    }

                    if (ToolsConst.DEVICE_SYSTEM_H5.equals(values[2]) && values.length == 7) {
                        headInfoDto.setStation(values[6]);
                    }
                }
            }
            if (ToolsKit.isNotEmpty(appId)) {
                headInfoDto.setChannel(appId);
            }
            return JsonKit.toJsonString(headInfoDto);
        }
    }

    /**
     * 取出所有请求的参数
     * 
     * @return
     */
    public static Map<String, Object> getAllParams(HttpServletRequest request) {
        Map<String, Object> params = new HashMap<String, Object>();
        Map<String, String[]> requestParams = request.getParameterMap();
        if (ToolsKit.isNotEmpty(requestParams)) {
            for (Iterator<String> iter = requestParams.keySet().iterator(); iter.hasNext();) {
                String name = (String) iter.next();
                if (JsonKit.isMapJsonString(name)) {
                    continue;
                }
                String[] values = (String[]) requestParams.get(name);
                String valueStr = "";
                for (int i = 0; i < values.length; i++) {
                    valueStr = (i == values.length - 1) ? valueStr + values[i] : valueStr + values[i] + ",";
                }
                params.put(name, getValue(valueStr));
            }
        }
        Enumeration<String> attributeNames = request.getAttributeNames();
        while (attributeNames.hasMoreElements()) {
            String name = attributeNames.nextElement();
            if (JsonKit.isMapJsonString(name)) {
                continue;
            }
            if (name.startsWith("org.") || name.endsWith(".FILTERED") || name.startsWith("javax.servlet.") || name.startsWith("exception")) {
                continue;
            }
            params.put(name, getValue(name, request));
        }
        if(params.containsKey("")){

        }
        return params;
    }

    /**
     * 设置post请求参数到作用域
     * 
     * @param request
     */
    public static void fillPostParams(HttpServletRequest request) {
        try {
            Map<String, Object> jsonMap = getPostJsonMap(request);
            if (ToolsKit.isNotEmpty(jsonMap)) {
                for (Iterator<String> iter = jsonMap.keySet().iterator(); iter.hasNext();) {
                    String key = iter.next();
                    Object value = jsonMap.get(key);
                    request.setAttribute(key, value);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 获取post请求map参数集合
     * 
     * @param request
     */
    public static Map<String, Object> getPostJsonMap(HttpServletRequest request) {
        try {
            String postdata = null;
            if ("POST".equalsIgnoreCase((request.getMethod()))) {
                try {
                    postdata = request.getAttribute(WebConst.JSON_POST_DATA) + "";
                    if (ToolsKit.isEmpty(postdata)) {
                        postdata = inputStream2String(request.getInputStream());
                    }
                    if (ToolsKit.isNotEmpty(postdata)) {
                        if (!JsonKit.isMapJsonString(postdata) && postdata.contains("=")) {// 如果是form表单提交过来，只是键值对，不是个json格式，需要转json格式
                            Map<String, Object> map = new HashMap<String, Object>();
                            for (String kv : postdata.split("&")) {
                                if (ToolsKit.isNotEmpty(kv) && kv.split("=").length == 2) {
                                    map.put(kv.split("=")[0], kv.split("=")[1]);
                                }
                            }
                            postdata = JsonKit.toJsonString(map);
                        }
                        request.setAttribute(WebConst.JSON_POST_DATA, postdata);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
            if (ToolsKit.isNotEmpty(postdata) && JsonKit.isMapJsonString(postdata)) {
                return JsonKit.jsonParseObject(postdata, Map.class);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public static String inputStream2String(InputStream is) {
        try {
            return IOUtils.toString(is, WebConst.ENCODING_FIELD);
        } catch (IOException e) {
            e.printStackTrace();
            return "";
        } finally {
            // 安静的关闭Stream
            IOUtils.closeQuietly(is);
        }
    }

    /**
     * 获取响应dto
     * 
     * @return
     */
    private static ReturnDto getReturnDto() {
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        ReturnDto returnDto = new ReturnDto();
        HeadDto head = new HeadDto();
        head.setRet(ExceptionEnums.SUCCESS.getCode());
        head.setMsg(ExceptionEnums.SUCCESS.getMessage());
        head.setUri(request.getRequestURI());
        head.setMethod(request.getMethod());
        head.setRequestId(getValue(WebConst.SADAIS_REQUEST_ID_FIELD, request));
        Map<String, Object> allParams = WebKit.getAllParams(request);
        allParams.remove(WebConst.JWT_SUBJECT);
        allParams.remove(WebConst.HEAD_INFO_DATA);
        allParams.remove(WebConst.JWT_USER_INFO);
        allParams.remove(WebConst.JSON_POST_DATA);
        allParams.remove(WebConst.JWT_TOKEN_PARAM_KEY);
        allParams.remove("userid");
        allParams.remove("userId");
        allParams.remove("codeId");
        allParams.remove("codeid");
        head.setParam(allParams);
        returnDto.setHead(head);
        return returnDto;
    }

    /**
     * @param enums
     * @param obj
     * @return
     */
    public static ReturnDto buildReturnDto(IEnums enums, Object obj) {
        ReturnDto returnDto = getReturnDto();
        HeadDto head = returnDto.getHead();
        if (ToolsKit.isEmpty(enums)) {
            head.setRet(ExceptionEnums.SUCCESS.getCode());
            head.setMsg(ExceptionEnums.SUCCESS.getMessage());
        } else {
            head.setRet(enums.getCode());
            head.setMsg(getNetworkErrorMsg(enums.getMessage()));
        }
        returnDto.setHead(head);
        returnDto.setData(obj);
        return returnDto;
    }

    /**
     * 返回成功信息
     * 
     * @param code
     * @param message
     * @param obj
     * @return
     */
    public static ReturnDto returnSuccessJson(int code, String message, Object obj) {
        ReturnDto returnDto = buildReturnDto(ExceptionEnums.SUCCESS, obj);
        HeadDto headDto = returnDto.getHead();
        headDto.setRet(code);
        headDto.setMsg(message);
        returnDto.setHead(headDto);
        return returnDto;
    }

    /**
     * 返回失败信息
     * 
     * @param ret
     * @param msg
     * @param dto
     * @return
     */
    public static ReturnDto returnFailJson(int ret, String msg, Object dto) {
        ReturnDto returnDto = getReturnDto();
        HeadDto head = returnDto.getHead();
        head.setMsg(msg);
        head.setRet(ret);
        returnDto.setHead(head);
        returnDto.setData(dto);
        return returnDto;
    }

    /**
     * 设置作用域信息
     * 
     * @param httpRequest
     * @param httpResponse
     * @throws IOException
     * @throws ServletException
     */
    public static void setDomainInfo(HttpServletRequest httpRequest, HttpServletResponse httpResponse) throws ServiceException {
        try {
            httpRequest.setCharacterEncoding(WebConst.ENCODING_FIELD);// 设置utf-8编码
            httpResponse.setCharacterEncoding(WebConst.ENCODING_FIELD);// 设置utf-8编码
            httpResponse.setHeader("Content-type", MediaType.APPLICATION_JSON_UTF8_VALUE);
            httpRequest.setAttribute(WebConst.SADAIS_REQUEST_ID_FIELD, WebConst.SADAIS_FIELD + Random.randomUUID());
            WebKit.fillPostParams(httpRequest);
            WebKit.parseHeadInfo(httpRequest);
            WebKit.printRequestInfo(httpRequest);
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException().setCode(ExceptionEnums.NET_EXCEPTION.getCode()).setMessage(ExceptionEnums.NET_EXCEPTION.getMessage());
        }
    }

    /**
     * 检测jwttoken
     * 
     * @param request
     * @throws IOException
     * @throws ServletException
     */
    public static void checkedJWT(HttpServletRequest request) throws ServiceException {
        try {
            if (checkUri(checkUriMap, request.getRequestURI())) {
                String jwtToken = getJwtToken(request);
                if (ToolsKit.isEmpty(jwtToken) && !checkUri(allowEmptyUserIdUriMap, request.getRequestURI())) {
                    throw new ServiceException().setCode(ExceptionEnums.TOKEN_IS_EMPTY.getCode()).setMessage("token令牌不能为空");
                }
                String feign = WebKit.getValue(WebConst.FEIGN_FLAG_KEY, request);
                Claims claims = JWTTokenUtil.validateJWT(jwtToken);
                if (ToolsKit.isEmpty(claims) && ToolsKit.isEmpty(feign)) {
                    throw new ServiceException().setCode(ExceptionEnums.TOKEN_EXPIRE.getCode()).setMessage("token令牌已过期");
                } else if (ToolsKit.isEmpty(claims) && ToolsKit.isNotEmpty(feign)) {
                    return;
                }
                SubjectModel subjectModel = JsonKit.jsonParseObject(claims.getSubject(), SubjectModel.class);
                String userInfo = getUserInfoDto(subjectModel.getUserInfo(), request);
                request.setAttribute(WebConst.JWT_SUBJECT, claims.getSubject());
                request.setAttribute(WebConst.JWT_USER_INFO, userInfo);
                // System.out.println(JsonKit.toJsonString(claims));
            } else {
                // 处理jwt，如果有就获取，如果没有，无所谓
                String jwtToken = getJwtToken(request);
//                System.out.println("-------------进来判断jwt-------------" + jwtToken);
                if (ToolsKit.isNotEmpty(jwtToken)){
                    Claims claims = JWTTokenUtil.validateJWT(jwtToken);
                    if (ToolsKit.isNotEmpty(claims)) {
                        SubjectModel subjectModel = JsonKit.jsonParseObject(claims.getSubject(), SubjectModel.class);
                        String userInfo = getUserInfoDto(subjectModel.getUserInfo(), request);
                        request.setAttribute(WebConst.JWT_SUBJECT, claims.getSubject());
                        request.setAttribute(WebConst.JWT_USER_INFO, userInfo);
                    }
                }
            }
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException().setCode(ExceptionEnums.NET_EXCEPTION.getCode()).setMessage(ExceptionEnums.NET_EXCEPTION.getMessage());
        }
    }

    /**
     * 根据加密信息解析出用户信息对象
     * 
     * @param userInfo
     * @return
     */
    public static String getUserInfoDto(String userInfo, HttpServletRequest request) {
        try {
            if (ToolsKit.isNotEmpty(userInfo)) {
                CommonProperties properties = SpringContextHolder.getBean("commonProperties");
                String userInfoString = CryptionUtil.getDecryptString(userInfo, properties.getCryptionKey());
                UserInfoDto userInfoDto = JsonKit.jsonParseObject(userInfoString, UserInfoDto.class);
                if (ToolsKit.isEmpty(getValue("userid", request)) && ToolsKit.isNotEmpty(userInfoDto)) {
                    request.setAttribute("userid", userInfoDto.getUserId());
                }
                if (ToolsKit.isEmpty(getValue("userId", request)) && ToolsKit.isNotEmpty(userInfoDto)) {
                    request.setAttribute("userId", userInfoDto.getUserId());
                }
                if (ToolsKit.isEmpty(getValue("codeid", request)) && ToolsKit.isNotEmpty(userInfoDto)) {
                    request.setAttribute("codeid", userInfoDto.getCodeId());
                }
                if (ToolsKit.isEmpty(getValue("codeId", request)) && ToolsKit.isNotEmpty(userInfoDto)) {
                    request.setAttribute("codeId", userInfoDto.getCodeId());
                }
                return userInfoString;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 获取请求host
     * 
     * @param request
     * @return
     */
    private static String getHost(HttpServletRequest request) {
        String host = getValue(HttpHeaders.HOST, request);
        if (ToolsKit.isEmpty(host)) host = request.getRemoteHost();
        String scheme = getValue(WebConst.FORWARDED_PROTO, request);
        if (ToolsKit.isEmpty(scheme)) scheme = request.getScheme();
        return scheme + "://" + host;
    }

    /**
     * 获取Token 主题模型
     * 
     * @param httpRequest
     * @return
     * @throws ServiceException
     */
    public static String getSubjectModel(HttpServletRequest httpRequest) throws ServiceException {
        if (checkUri(checkUriMap, httpRequest.getRequestURI())) {
            String jwtToken = getJwtToken(httpRequest);
            if (ToolsKit.isEmpty(jwtToken) && !checkUri(allowEmptyUserIdUriMap, httpRequest.getRequestURI())) {
                throw new ServiceException().setCode(ExceptionEnums.TOKEN_IS_EMPTY.getCode()).setMessage("token令牌不能为空");
            }
            if (ToolsKit.isEmpty(jwtToken)) {
                return null;
            }
            String feign = WebKit.getValue(WebConst.FEIGN_FLAG_KEY, httpRequest);
            Claims claims = JWTTokenUtil.validateJWT(jwtToken);
            if (ToolsKit.isEmpty(claims) && ToolsKit.isEmpty(feign)) {
                throw new ServiceException().setCode(ExceptionEnums.TOKEN_EXPIRE.getCode()).setMessage("token令牌已过期");
            } else if (ToolsKit.isEmpty(claims) && ToolsKit.isNotEmpty(feign)) {
                return null;
            }
            return claims.getSubject();
        } else {
            return null;
        }
    }

    /**
     * 是否检测uri token令牌
     * 
     * @param uri
     * @return
     */
    private static boolean checkUri(Map<String, String> uriMap, String uri) {
        boolean isCheck = false;
        if (ToolsKit.isNotEmpty(uri)) {
            for (Map.Entry<String, String> entry : uriMap.entrySet()) {
                if (entry.getKey().indexOf(uri) > -1) {
                    isCheck = true;
                    break;
                }
            }
        }
        return isCheck;
    }

    /**
     * 是否检测uri token令牌
     * 
     * @param uri
     * @return
     */
    public static boolean checkUri(String uri) {
        boolean isCheck = false;
        if (ToolsKit.isNotEmpty(uri)) {
            for (Map.Entry<String, String> entry : noCheckUriMap.entrySet()) {
                if (entry.getKey().indexOf(uri) > -1) {
                    isCheck = true;
                    break;
                }
            }
        }
        return isCheck;
    }

    /**
     * 获取jwttoken
     * 
     * @param httpRequest
     * @return
     * @throws ServiceException
     */
    public static String getJwtToken(HttpServletRequest httpRequest) {
        return getValue(WebConst.JWT_TOKEN_PARAM_KEY, httpRequest);
    }

    /**
     * 获取请求ID
     * 
     * @return
     */
    public static String newRequestId() {
        return WebConst.SADAIS_FIELD + Random.randomUUID() + "_" + System.currentTimeMillis();
    }

    /**
     * 打印到达时间
     * 
     * @param request
     */
    public static void printArrivalTime(HttpServletRequest request) {
        // for (StackTraceElement stackTraceElement : new Exception().getStackTrace()) {
        // System.out.println(stackTraceElement.getClassName() + "-->" + stackTraceElement.getMethodName() + "-->" + stackTraceElement.getLineNumber());
        // }
//        String requestId = getValue(WebConst.SADAIS_REQUEST_ID_FIELD, request);
//        if (ToolsKit.isNotEmpty(requestId)) {
//            String starTime = requestId.substring(requestId.lastIndexOf("_") + 1);
//            if (ToolsKit.isNotEmpty(starTime)) {
//                log.info("到达时间：" + new Exception().getStackTrace()[1].getClassName() + " --> " + (System.currentTimeMillis() - Long.valueOf(starTime)));
//            }
//        }
    }

    /**
     * GET请求
     * 
     * @param url
     *            请求地址
     * @return
     */
    public static String getForObject(String url) {
        try {
            RestTemplate restTemplate = new RestTemplate();
            restTemplate.getMessageConverters().clear();
            restTemplate.getMessageConverters().add(new FastJsonHttpMessageConverter());
            String result = restTemplate.getForObject(url, String.class);
            log.debug(result);
            return result;
        } catch (RestClientException e) {
            e.printStackTrace();
        }
        return StringUtils.EMPTY;
    }

    /**
     * POST请求
     * 
     * @param url
     *            请求地址
     * @param params
     *            请求参数
     * @return
     */
    public static String postJsonForObject(String url, Object params, HttpHeaders headers) {
        try {
            headers.setContentType(MediaType.APPLICATION_JSON_UTF8);
            RestTemplate restTemplate = new RestTemplate();
            restTemplate.getMessageConverters().clear();
            restTemplate.getMessageConverters().add(new FastJsonHttpMessageConverter());
            HttpEntity<String> entity = new HttpEntity<String>(JsonKit.toJsonString(params), headers);
            String result = restTemplate.postForObject(url, entity, String.class);
            log.debug(result);
            return result;
        } catch (RestClientException e) {
            e.printStackTrace();
        }
        return StringUtils.EMPTY;
    }

    /**
     * POST请求
     * 
     * @param url
     *            请求地址
     * @param params
     *            请求参数
     * @return
     */
    public static String postJsonForObject(String url, Object params) {
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON_UTF8);
            RestTemplate restTemplate = new RestTemplate();
            restTemplate.getMessageConverters().clear();
            restTemplate.getMessageConverters().add(new FastJsonHttpMessageConverter());
            HttpEntity<String> entity = new HttpEntity<String>(JsonKit.toJsonString(params), headers);
            String result = restTemplate.postForObject(url, entity, String.class);
            log.debug(result);
            return result;
        } catch (RestClientException e) {
            e.printStackTrace();
        }
        return StringUtils.EMPTY;
    }

    /**
     * POST请求
     * 
     * @param url
     *            请求地址
     * @param params
     *            请求参数
     * @return
     */
    public static String postJsonForObject(String url, Map<String, Object> params) {
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON_UTF8);
            RestTemplate restTemplate = new RestTemplate();
            restTemplate.getMessageConverters().clear();
            restTemplate.getMessageConverters().add(new FastJsonHttpMessageConverter());
            HttpEntity<String> entity = new HttpEntity<String>(JsonKit.toJsonString(params), headers);
            String result = restTemplate.postForObject(url, entity, String.class);
            log.debug(result);
            return result;
        } catch (RestClientException e) {
            e.printStackTrace();
        }
        return StringUtils.EMPTY;
    }

    /**
     * POST请求
     * 
     * @param url
     *            请求地址
     * @param params
     *            请求参数
     * @return
     */
    public static String postJsonForObject(String url, String params) {
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON_UTF8);
            RestTemplate restTemplate = new RestTemplate();
            restTemplate.getMessageConverters().clear();
            restTemplate.getMessageConverters().add(new FastJsonHttpMessageConverter());
            HttpEntity<String> entity = new HttpEntity<String>(params, headers);
            String result = restTemplate.postForObject(url, entity, String.class);
            log.debug(result);
            return result;
        } catch (RestClientException e) {
            e.printStackTrace();
        }
        return StringUtils.EMPTY;
    }

    /**
     * POST请求
     * 
     * @param url
     *            请求地址
     * @param params
     *            请求参数
     * @return
     */
    public static String postFormForObject(String url, Map<String, Object> params) {
        try {
            RestTemplate restTemplate = new RestTemplate();
            restTemplate.getMessageConverters().clear();
            restTemplate.getMessageConverters().add(new FastJsonHttpMessageConverter());
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
            String paramString = null;
            if (ToolsKit.isNotEmpty(params)) {
                StringBuilder sb = new StringBuilder();
                for (Iterator<String> iter = params.keySet().iterator(); iter.hasNext();) {
                    String key = iter.next();
                    Object value = params.get(key);
                    sb.append(key).append("=").append(value).append("&");
                }
                paramString = sb.substring(0, sb.length() - 1);
            } else {
                paramString = StringUtils.EMPTY;
            }
            HttpEntity<String> entity = new HttpEntity<String>(paramString, headers);
            String result = restTemplate.postForObject(url, entity, String.class);
            log.debug(result);
            return result;
        } catch (RestClientException e) {
            e.printStackTrace();
        }
        return StringUtils.EMPTY;
    }

    /**
     * 根据IP获取城市信息
     * 
     * @param ip
     * @return
     */
    public static CityInfo getCityInfo(String ip) {
        if (ToolsKit.isNotEmpty(ip)) {
            String url = WebConst.TAOBAO_GETIP_URL + "?ip=" + ip;
            String result = getForObject(url);
            if (ToolsKit.isNotEmpty(result)) {
                JSONObject JSONObject = JsonKit.jsonParseObject(result);
                if (ToolsKit.isNotEmpty(JSONObject.getString("data"))) {
                    CityInfo cityInfo = (CityInfo) JsonKit.jsonParseObject(JSONObject.getString("data"), CityInfo.class);
                    return cityInfo;
                }
            }
        }
        return null;
    }

    /**
     * 获取图片信息
     * 
     * @param pic
     *            图片地址
     * @return
     */
    public static PicInfoDto getPicInfo(String pic) {
        PicInfoDto picInfoDto = new PicInfoDto();
        try {
            if (ToolsKit.isNotEmpty(pic) && ToolsKit.URL.isUrl(pic)) {
                String url = pic + "?x-oss-process=image/info";
                String result = getForObject(url);
                if (ToolsKit.isNotEmpty(result)) {
                    JSONObject JSONObject = JsonKit.jsonParseObject(result);
                    if (ToolsKit.isNotEmpty(JSONObject)) {
                        picInfoDto.setFileSize(JSONObject.getJSONObject("FileSize").getLongValue("value"));
                        picInfoDto.setFormat(JSONObject.getJSONObject("Format").getString("value"));
                        picInfoDto.setImageHeight(JSONObject.getJSONObject("ImageHeight").getIntValue("value"));
                        picInfoDto.setImageWidth(JSONObject.getJSONObject("ImageWidth").getIntValue("value"));
                    }
                }
            }
        } catch (Exception e) {
        }
        return picInfoDto;
    }

    /**
     * 根据IP获取城市信息
     * 
     * @param ip
     * @return
     */
    public static String getCity(String ip) {
        if (ToolsKit.isNotEmpty(ip)) {
            String url = WebConst.TAOBAO_GETIP_URL + "?ip=" + ip;
            String result = getForObject(url);
            if (ToolsKit.isNotEmpty(result)) {
                JSONObject JSONObject = JsonKit.jsonParseObject(result);
                if (ToolsKit.isNotEmpty(JSONObject.getString("data"))) {
                    CityInfo cityInfo = (CityInfo) JsonKit.jsonParseObject(JSONObject.getString("data"), CityInfo.class);
                    return "XX".equals(cityInfo.getCity()) ? "" : cityInfo.getCity();
                }
            }
        }
        return StringUtils.EMPTY;
    }

    /**
     * 取出请求值
     *
     * @param key
     *            请求参数的key
     * @return 如果存在则返回字符串内容,不存在则返回""
     */
    public static String getValue(String key, HttpServletRequest request) {
//        System.out.println("================获取【"+key+"】==============");
        String values = request.getHeader(key);
//        System.out.println(values);
//        System.out.println("=============================");
        if (ToolsKit.isEmpty(values)) {
            values = request.getParameter(key);
            if (ToolsKit.isEmpty(values)) {
                values = request.getAttribute(key) + StringUtils.EMPTY;
            }
        }
        return getValue(values);
    }

    private static String getValue(String values) {
        if (ToolsKit.isEmpty(values)) {
            return StringUtils.EMPTY;
        } else {
            if (UrlEncoderUtils.hasUrlEncoded(values)) {
                return ToolsKit.URL.decode(values, WebConst.ENCODING_FIELD);
            } else {
                return values;
            }
        }
    }

    /**
     * 取出请求值
     *
     * @param key
     *            请求参数的key
     * @return 如果存在则返回字符串内容,不存在则返回null
     */
    public static Object getValueObj(String key, HttpServletRequest request) {
        Object values = request.getHeader(key);
        if (ToolsKit.isEmpty(values)) {
            values = request.getParameter(key);
            if (ToolsKit.isEmpty(values)) {
                values = request.getAttribute(key);
            }
        }
        return getValue(values + StringUtils.EMPTY);
    }

    /**
     * 设置 Header信息
     *
     * @param response
     * @param key
     * @param value
     */
    public static void setHeader(HttpServletResponse response, String key, String value) {
        response.setHeader(key, value);
    }

    /**
     * 获取Hearder信息
     * 
     * @param response
     * @return
     */
    public static Map<String, Object> getHearderMap(HttpServletResponse response) {
        Map<String, Object> map = new HashMap<String, Object>();
        Set<String> nameList = (Set) response.getHeaderNames();
        if (nameList.size() > 0) {
            for (String name : nameList) {
                map.put(name, response.getHeader(name));
            }
        }
        return map;
    }

    private static String getNetworkErrorMsg(String msg) {
        if (msg == null) {
            return msg;
        }
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        String language = getValue("language", request);
        // com.regardlabel.sadais.core.common.enums.ExceptionEnums.NET_EXCEPTION
        if (!msg.equals(NET_EXCEPTION.getMessage())) {
            return msg;
        }
        if (language.equals(LanguageEnums.EN_US.getKey())) {
            return "The network is abnormal, try again later";
        } else if (language.equals(LanguageEnums.JA_JP.getKey())) {
            return "ネットワーク異常";
        } else if (language.equals(LanguageEnums.KO_KR.getKey())) {
            return "네트워크 이상";
        } else if (language.equals(LanguageEnums.ZH_TW.getKey())) {
            return "網絡异常，稍後再試";
        } else if (language.equals(LanguageEnums.ZH_CN.getKey())) {
            return msg;
        } else {
            return "The network is abnormal, try again later";
        }
    }
}
