package net.snaptag.system.sadais.web.config;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
public class MqttProperties {
    @Value("${accessKey:}")
    private String accessKey;    // 密匙
    @Value("${accessSecret:}")
    private String accessSecret; // 密钥
    @Value("${uid:}")
    private String uid;          // 阿里云uid
    @Value("${endpointName:}")
    private String endpointName; // 节点名称
    @Value("${regionId:}")
    private String regionId;     // regionId
    @Value("${product:}")
    private String product;      // product
    @Value("${domain:}")
    private String domain;       // 节点
    @Value("${productKey:}")
    private String productKey;   // 产品key

    public String getAccessKey() {
        return accessKey;
    }

    public void setAccessKey(String accessKey) {
        this.accessKey = accessKey;
    }

    public String getAccessSecret() {
        return accessSecret;
    }

    public void setAccessSecret(String accessSecret) {
        this.accessSecret = accessSecret;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public String getEndpointName() {
        return endpointName;
    }

    public void setEndpointName(String endpointName) {
        this.endpointName = endpointName;
    }

    public String getRegionId() {
        return regionId;
    }

    public void setRegionId(String regionId) {
        this.regionId = regionId;
    }

    public String getProduct() {
        return product;
    }

    public void setProduct(String product) {
        this.product = product;
    }

    public String getDomain() {
        return domain;
    }

    public void setDomain(String domain) {
        this.domain = domain;
    }

    public String getProductKey() {
        return productKey;
    }

    public void setProductKey(String productKey) {
        this.productKey = productKey;
    }
}