package net.snaptag.system.sadais.web.enums;

import net.snaptag.system.sadais.core.common.core.ToolsConst;

import java.util.LinkedHashMap;

/**
 * 快递公司信息
 * <AUTHOR> 2018年11月24日
 *
 */
public enum LogisticsCompanyEnum {
    /**
     * value 存储值
     * name 快递名称
     * com 快递公司代码 https://www.kuaidi100.com/download/api_kuaidi100_com(20140729).doc
     * icon 快递公司图标
     * tel 快递公司电话
     */

    EMS("1", "邮政EMS", "ems", "11183", ToolsConst.EX_LOGO_PATH + "ems.png"),
    YZXB("14", "邮政小包", "youzhengguonei", "11183", ToolsConst.EX_LOGO_PATH + "youzhengguonei.png"),
    STKD("2", "申通快递", "shentong", "95543", ToolsConst.EX_LOGO_PATH + "shentong.png"),
    ZTSD("3", "中通速递", "zhongtong", "95311", ToolsConst.EX_LOGO_PATH + "zhongtong.png"),
    YTSD("4", "圆通速递", "yuantong", "95554", ToolsConst.EX_LOGO_PATH + "yuantong.png"),
    TTKD("5", "天天快递", "tiantian", "************", ToolsConst.EX_LOGO_PATH + "tiantian.png"),
    SFSY("6", "顺丰速运", "shunfeng", "95338", ToolsConst.EX_LOGO_PATH + "shunfeng.png"),
    YDKY("7", "韵达快运", "yunda", "95546", ToolsConst.EX_LOGO_PATH + "yunda.png"),
    ZJS("8", "宅急送", "zhaijisong", "400-6789-000", ToolsConst.EX_LOGO_PATH + "zhaijisong.png"),
    HTKY("9", "汇通快运", "huitongkuaidi", "************", ToolsConst.EX_LOGO_PATH + "huitongkuaidi.png"),
    DBWL("13", "德邦物流", "debangwuliu", "95353", ToolsConst.EX_LOGO_PATH + "debangwuliu.png"),
    QFKD("15", "全峰快递", "quanfengkuaidi", "4001000001", ToolsConst.EX_LOGO_PATH + "quangfeng.jpg"),
	YSWL("16", "优速物流", "youshuwuliu", "4001111119", ToolsConst.EX_LOGO_PATH + "uc56.jpg"),
	ZTKY("17", "中铁快运", "zhongtiekuaiyun", "95572", ToolsConst.EX_LOGO_PATH + "uc56.jpg"),
	JDWL("19", "京东物流", "jingdongkuaidi", " ", ToolsConst.EX_LOGO_PATH + "uc56.jpg"),
    CJPS("18", "厂家配送", "changjiapeisong", "95572", ToolsConst.EX_LOGO_PATH + "uc56.jpg");

    private final String value;
    private final String desc;
    private final String com;
    private final String tel;
    private final String icon;


    private static final LinkedHashMap<String, String> map = new LinkedHashMap<String, String>();
    private static final LinkedHashMap<String, LogisticsCompanyEnum> detail = new LinkedHashMap<String, LogisticsCompanyEnum>();

    static {
        for (LogisticsCompanyEnum logisticsCompany : LogisticsCompanyEnum.values()) {
            map.put(logisticsCompany.getValue(), logisticsCompany.getDesc());
            detail.put(logisticsCompany.getValue(), logisticsCompany);
        }
    }

    LogisticsCompanyEnum(String value, String desc, String com, String tel, String icon) {
        this.value = value;
        this.desc = desc;
        this.com = com;
        this.tel = tel;
        this.icon = icon;
    }

    public String getDesc() {
        return desc;
    }

    public String getValue() {
        return value;
    }

    public String getIcon() {
        return icon;
    }

    public String getCom() {
        return com;
    }

    public String getTel() {
        return tel;
    }

    public static LinkedHashMap<String, String> getMap() {
        return map;
    }

    public static LogisticsCompanyEnum getDetail(String value) {
        return detail.get(value);
    }
}
