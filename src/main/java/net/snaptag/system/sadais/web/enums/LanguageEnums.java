package net.snaptag.system.sadais.web.enums;

import java.util.LinkedHashMap;

/**
 * 语言枚举
 */
public enum LanguageEnums {
    ZH_CN("zh_CN", "中文简体"),
    ZH_TW("zh_TW", "中文繁体"),
    EN_US("en_US", "英语"),
    KO_KR("ko_KR", "韩国"),
    JA_JP("ja_JP", "日本"),
    ES_ES("es_ES", "西班牙（欧）"),
    ES_LA("es_LA", "西班牙（拉美）"),
    FR_FR("fr_FR", "法语"),
    RU_RU("ru_RU", "俄语"),
    AR_AE("ar_AE", "阿拉伯语"),
    DE_DE("de_DE", "德语"),
    IT_IT("it_IT", "意大利"),
    PT_PT("pt_PT", "葡萄牙"),
    TH_TH("th_TH", "泰语"),
    VI_VI("vi_VI", "越南语"),
    FI_FI("fi_FI", "芬兰语"),
    PL_PL("pl_PL", "波兰语"),
    NL_NL("nl_NL", "荷兰语"),
    BN_BD("bn_BD", "孟加拉语");
        
    public String                                      key;
    public String                                      desc;
    private static final LinkedHashMap<String, String> map;
    static {
        map = new LinkedHashMap<String, String>();
        for (LanguageEnums languageEnums : LanguageEnums.values()) {
            map.put(languageEnums.getKey(), languageEnums.getDesc());
        }
    } 
 
    LanguageEnums(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }


    public String getKey() {
        return key;
    }

    public String getDesc() {
        return desc;
    }

    public static LinkedHashMap<String, String> getMap() {
        return map;
    }
}
