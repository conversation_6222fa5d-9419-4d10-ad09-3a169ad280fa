package net.snaptag.system.sadais.web.enums;

import java.util.LinkedHashMap;

/**
 * 支撑平台查询参数类型枚举
 */
public enum SearchValueKeyEnums {
    ORDER_NO("orderNo", "订单号"), 
    CODE_ID("codeId", "用户短ID"), 
    PHONE("phone", "手机号"), 
    USER_NAME("userName", "客户姓名"),;
    
    public String                                      key;
    public String                                      desc;
    private static final LinkedHashMap<String, String> map;
    static {
        map = new LinkedHashMap<String, String>();
        for (SearchValueKeyEnums searchValueKeyEnums : SearchValueKeyEnums.values()) {
            map.put(searchValueKeyEnums.getKey(), searchValueKeyEnums.getDesc());
        }
    } 
  
    SearchValueKeyEnums(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }


    public String getKey() {
        return key;
    }

    public String getDesc() {
        return desc;
    }

    public static LinkedHashMap<String, String> getMap() {
        return map;
    }
}
