package net.snaptag.system.sadais.web.queue;

import net.snaptag.system.sadais.core.common.core.IPlugin;
import net.snaptag.system.sadais.core.common.enums.ExceptionEnums;
import net.snaptag.system.sadais.core.common.exception.ServiceException;
import net.snaptag.system.sadais.util.core.ToolsKit;

import java.util.ArrayList;
import java.util.List;

public class QueuePlugin implements IPlugin {
    private int          queueSize = 300;
    private List<String> classList = new ArrayList<String>();

    /**
     * 消费队列构造函数
     * 
     * @param queueSize
     *            队列大小
     * @param classList
     *            消费类集合
     */
    public QueuePlugin(int queueSize, List<Class<?>> classList) throws ServiceException {
        if (ToolsKit.isNotEmpty(classList)) {
            for (Class<?> clazz : classList) {
                System.out.println(clazz.getSimpleName());
                this.classList.add(clazz.getSimpleName());
            }
        } else {
            throw new ServiceException().setCode(ExceptionEnums.ERROR.getCode()).setMessage("消费类不能为空");
        }
        this.queueSize = queueSize;
    }

    @Override
    public void init() throws Exception {
    }

    @Override
    public void start() throws Exception {
        ConsumeQueueService consumeQueueService = ConsumeQueueService.getInstance();
        consumeQueueService.setConsumeQueue(queueSize);
        consumeQueueService.start();
    }

    @Override
    public void stop() throws Exception {
    }
}
