package net.snaptag.system.sadais.web.enums;

import java.util.LinkedHashMap;

/**
 * head请求头参数key
 */
public enum FeignHeadParamEnums {
    ACCEPT("Accept", ""), 
    REQUEST_ID("sadais_request_id", ""), 
    USER_AGENT("User-Agent", ""), 
    X_FORWARDED_HOST("X-Forwarded-Host", ""), 
    CONNECTION("Connection", ""),
    X_FORWARDED_PORT("x-forwarded-port", ""),
    HOST("Host", ""),
    ACCEPT_ENCODING("Accept-Encoding", ""),
    HEAD_INFO_DATA("head_info_data", ""),
    X_FORWARDED_FOR("X-Forwarded-For", ""),
    ACCEPT_LANGUAGE("Accept-Language", ""),
    CONTENT_TYPE("Content-Type", ""),
    JWT_TOKEN("jwttoken", ""),
    LANGUAGE("language", ""),;
    public String                                      key;
    public String                                      desc;
    private static final LinkedHashMap<String, String> map;
    static {
        map = new LinkedHashMap<String, String>();
        for (FeignHeadParamEnums feignHeadParamEnums : FeignHeadParamEnums.values()) {
            map.put(feignHeadParamEnums.getKey(), feignHeadParamEnums.getDesc());
        }
    } 
 
    FeignHeadParamEnums(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }


    public String getKey() {
        return key;
    }

    public String getDesc() {
        return desc;
    }

    public static LinkedHashMap<String, String> getMap() {
        return map;
    }
}
