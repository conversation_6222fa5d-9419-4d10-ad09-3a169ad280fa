package net.snaptag.system.sadais.web.jwt;

import net.snaptag.system.sadais.core.common.utils.Base64Helper;
import net.snaptag.system.sadais.web.common.WebConst;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.JwtBuilder;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;

import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.util.UUID;

/**
 * jwt token工具类
 */
public class JWTTokenUtil {
    /**
     * 获取密钥
     * 
     * @return
     * @throws UnsupportedEncodingException
     */
    public static SecretKey generalKey() throws UnsupportedEncodingException {
        byte[] encodedKey = Base64Helper.decode(WebConst.JWT_SECERT, WebConst.ENCODING_FIELD).getBytes();
        SecretKey key = new SecretKeySpec(encodedKey, 0, encodedKey.length, "AES");
        return key;
    }

    /**
     * 签发JWT
     * 
     * @param subject
     * @param ttlMillis
     * @return
     * @throws Exception
     */
    public static String createJWT(String subject, long ttlMillis) {
        SignatureAlgorithm signatureAlgorithm = SignatureAlgorithm.HS256;
        Date now = new Date();
        long nowMillis = now.getTime();
        SecretKey secretKey = null;
        try {
            secretKey = generalKey();
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        JwtBuilder builder = Jwts.builder().setId(UUID.randomUUID().toString()).setSubject(subject).setIssuedAt(now).signWith(signatureAlgorithm, secretKey);
        if (ttlMillis >= 0) {
            long expMillis = nowMillis + ttlMillis;
            Date expDate = new Date(expMillis);
            builder.setExpiration(expDate);
        }
        return builder.compact();
    }

    /**
     * 验证JWT
     * 
     * @param jwtStr
     * @return
     */
    public static Claims validateJWT(String jwtStr) {
        Claims claims = null;
        try {
            SecretKey secretKey = generalKey();
            claims = Jwts.parser().setSigningKey(secretKey).parseClaimsJws(jwtStr).getBody();
        } catch (Exception e) {
            // e.printStackTrace();
        }
        return claims;
    }
}
