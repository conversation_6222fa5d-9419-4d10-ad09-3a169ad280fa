package net.snaptag.system.sadais.web.enums;
import java.util.LinkedHashMap;

/**
 * 系统状态信息枚举
 */
public enum SystemStatusEnums {
    REGISTER_COUNT("pc:register:count:", "注册数", 10),
    PWD_ERROR_COUNT("pc:pwd:error:count:", "密码错误数", 6),
    LOGIN_IP_COUNT("pc:login:ip:count:", "登录IP数量", 150);
    public String                                      key;
    public String                                      desc;
    public int                                         num;
    private static final LinkedHashMap<String, String> map;
    static {
        map = new LinkedHashMap<String, String>();
        for (SystemStatusEnums systemStatusEnums : SystemStatusEnums.values()) {
            map.put(systemStatusEnums.getKey(), systemStatusEnums.getDesc());
        }
    }

    SystemStatusEnums(String key, String desc, int num) {
        this.key = key;
        this.desc = desc;
        this.num = num;
    }

    public String getKey() {
        return key;
    }

    public String getDesc() {
        return desc;
    }

    public int getNum() {
        return num;
    }

    public static LinkedHashMap<String, String> getMap() {
        return map;
    }

    public static int get(String key) {
        for (SystemStatusEnums systemStatusEnums : SystemStatusEnums.values()) {
            if (systemStatusEnums.getKey().equals(key)) {
                return systemStatusEnums.getNum();
            }
        }
        return 0;
    }
}
