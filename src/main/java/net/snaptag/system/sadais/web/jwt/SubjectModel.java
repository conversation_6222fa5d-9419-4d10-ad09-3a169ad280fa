package net.snaptag.system.sadais.web.jwt;

import net.snaptag.system.sadais.web.dto.AuthorityDto;
import net.snaptag.system.sadais.web.dto.UserInfoDto;

import java.io.Serializable;

/**
 * Token 主题模型
 */
public class SubjectModel implements Serializable {
    /**
     * 
     */
    private static final long serialVersionUID = 1L;
    private String            userInfo;             // 用户信息
    private UserInfoDto       userInfoDto;
    private AuthorityDto      authority;            // 用户权限

    public UserInfoDto getUserInfoDto() {
        return userInfoDto;
    }

    public void setUserInfoDto(UserInfoDto userInfoDto) {
        this.userInfoDto = userInfoDto;
    }

    public String getUserInfo() {
        return userInfo;
    }

    public void setUserInfo(String userInfo) {
        this.userInfo = userInfo;
    }

    public AuthorityDto getAuthority() {
        return authority;
    }

    public void setAuthority(AuthorityDto authority) {
        this.authority = authority;
    }
}
