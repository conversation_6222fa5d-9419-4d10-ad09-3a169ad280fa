package net.snaptag.system.sadais.web.dto;
import java.io.Serializable;
import java.util.List;
import java.util.Map;

public class UserInfoDto implements Serializable {
    /**
     * 用户信息
     */
    private static final long    serialVersionUID = 1L;
    private String               userId;               // 用户ID
    private String               account;              // 账号名称
    private String               name;                 // 名称
    private int                  codeId;               // 用户codeid
    private String               ip;                   // 用户请求IP
    private String               ua;                   // ua信息
    private Map<String, String>  resource;             // 资源信息
    private boolean              superAdmin;           // 是否超级管理员，可以查看所有数据级别
    private String               appId;                // 渠道ID
    private List<ProjectInfoDto> projectInfoDto;       // 项目信息
    private String            mobile;               // 手机号
    private String            email;                // 邮箱

    public UserInfoDto(String account, String name) {
        super();
        this.account = account;
        this.name = name;
    }

    public UserInfoDto() {
        super();
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public boolean getSuperAdmin() {
        return superAdmin;
    }

    public void setSuperAdmin(boolean superAdmin) {
        this.superAdmin = superAdmin;
    }

    public List<ProjectInfoDto> getProjectInfoDto() {
        return projectInfoDto;
    }

    public void setProjectInfoDto(List<ProjectInfoDto> projectInfoDto) {
        this.projectInfoDto = projectInfoDto;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public int getCodeId() {
        return codeId;
    }

    public void setCodeId(int codeId) {
        this.codeId = codeId;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public String getUa() {
        return ua;
    }

    public void setUa(String ua) {
        this.ua = ua;
    }

    public Map<String, String> getResource() {
        return resource;
    }

    public void setResource(Map<String, String> resource) {
        this.resource = resource;
    }

    public String getAccount() {
        return account;
    }

    public void setAccount(String account) {
        this.account = account;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

}
