package net.snaptag.system.sadais.web.dto;

import net.snaptag.system.sadais.web.jwt.SubjectModel;

import java.io.Serializable;

public class TokenDto implements Serializable {
    /**
     * token dto
     */
    private static final long serialVersionUID = 1L;
    private String            accesstoken;
    private long              timeOut;
    private SubjectModel subjectModel;

    public String getAccesstoken() {
        return accesstoken;
    }

    public void setAccesstoken(String accesstoken) {
        this.accesstoken = accesstoken;
    }

    public SubjectModel getSubjectModel() {
        return subjectModel;
    }

    public void setSubjectModel(SubjectModel subjectModel) {
        this.subjectModel = subjectModel;
    }

    public long getTimeOut() {
        return timeOut;
    }

    public void setTimeOut(long timeOut) {
        this.timeOut = timeOut;
    }
}
