package net.snaptag.system.sadais.web.core;

import net.snaptag.system.sadais.core.common.enums.ExceptionEnums;
import net.snaptag.system.sadais.core.common.exception.ServiceException;
import net.snaptag.system.sadais.web.common.WebKit;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/***
 * 统一处理过滤器
 */
public class VerificationFilterChain implements Filter {
    /**
     * 初始化
     */
    public void init(FilterConfig filterConfig) throws ServletException {
    }

    /**
     * 执行部分
     */
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws ServiceException {
        try {
//            System.out.println("============> VerificationFilterChain");
            HttpServletRequest httpRequest = (HttpServletRequest) request;
            HttpServletResponse httpResponse = (HttpServletResponse) response;

            ServletRequest requestWrapper = new RequestWrapper(httpRequest);

            WebKit.setDomainInfo((HttpServletRequest) requestWrapper, httpResponse);
            chain.doFilter(request, response);
        } catch (ServiceException e) {
            e.printStackTrace();
            WebKit.printResult(response, e.getCode(), e.getMessage());
            return;
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException().setCode(ExceptionEnums.NET_EXCEPTION.getCode()).setMessage(ExceptionEnums.NET_EXCEPTION.getMessage());
        }
    }

    /**
     * 销毁
     */
    public void destroy() {
    }
}