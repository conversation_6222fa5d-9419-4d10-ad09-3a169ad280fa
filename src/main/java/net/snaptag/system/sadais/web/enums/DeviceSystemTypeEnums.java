package net.snaptag.system.sadais.web.enums;

import java.util.LinkedHashMap;

/**
 * 系统设备类型枚举
 */
public enum DeviceSystemTypeEnums {
    ANDROID("ANDROID", "安卓"), 
    IOS("IOS", "苹果"), 
    H5("H5", "H5网页"), 
    MA("MA", "小程序"), 
    WEB("WEB", "支撑平台");
        
    public String                                      key;
    public String                                      desc;
    private static final LinkedHashMap<String, String> map;
    static {
        map = new LinkedHashMap<String, String>();
        for (DeviceSystemTypeEnums deviceSystemTypeEnums : DeviceSystemTypeEnums.values()) {
            map.put(deviceSystemTypeEnums.getKey(), deviceSystemTypeEnums.getDesc());
        }
    }
 
    DeviceSystemTypeEnums(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }


    public String getKey() {
        return key;
    }

    public String getDesc() {
        return desc;
    }

    public static LinkedHashMap<String, String> getMap() {
        return map;
    }
}
