package net.snaptag.system.sadais.web.core;

import net.snaptag.system.sadais.core.common.enums.ExceptionEnums;
import net.snaptag.system.sadais.core.common.exception.ServiceException;
import net.snaptag.system.sadais.util.core.ToolsKit;
import net.snaptag.system.sadais.web.common.JsonKit;
import net.snaptag.system.sadais.web.common.WebConst;
import net.snaptag.system.sadais.web.common.WebKit;
import net.snaptag.system.sadais.web.jwt.SubjectModel;
import org.apache.commons.lang3.StringUtils;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;

public class HeaderFilter implements Filter {

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {

    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {
        try {
            HttpServletRequest httpRequest = (HttpServletRequest) request;
            WebKit.printArrivalTime(httpRequest);
            if (ToolsKit.isNotEmpty(WebKit.getJwtToken(httpRequest))) {
                String jsonString = WebKit.getSubjectModel(httpRequest);
                if (ToolsKit.isNotEmpty(jsonString)) {
                    SubjectModel subjectModel = JsonKit.jsonParseObject(jsonString, SubjectModel.class);
                    request.setAttribute(WebConst.JWT_SUBJECT, subjectModel);
                    request.setAttribute(WebConst.JWT_USER_INFO, WebKit.getUserInfoDto(subjectModel.getUserInfo(), httpRequest));
                }
            }
            String headJsonString = WebKit.getHeadInfoDto(httpRequest);
            httpRequest.setAttribute(WebConst.HEAD_INFO_DATA, headJsonString);// head头信息

            ServletRequest requestWrapper = new RequestWrapper(httpRequest);
            WebKit.fillZuulStreamParam((HttpServletRequest) requestWrapper);
            WebKit.printRequestInfo((HttpServletRequest) requestWrapper);
            chain.doFilter(requestWrapper, response);
            WebKit.sendJournal((HttpServletRequest) requestWrapper, StringUtils.EMPTY, WebConst.QUEUE_MSG_TYPE_REQUEST);
        } catch (ServiceException e) {
            e.printStackTrace();
            WebKit.printResult(response, e.getCode(), e.getMessage());
            return;
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException().setCode(ExceptionEnums.NET_EXCEPTION.getCode()).setMessage(ExceptionEnums.NET_EXCEPTION.getMessage());
        }
    }

    @Override
    public void destroy() {

    }
}
