package net.snaptag.system.sadais.web.core;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Component
@ConfigurationProperties(prefix="sadais")
public class ConnectionSettings {
    private boolean useAccessFilter;

    public boolean getUseAccessFilter() {
        return useAccessFilter;
    }

    public void setUseAccessFilter(boolean useAccessFilter) {
        this.useAccessFilter = useAccessFilter;
    }
}
