package net.snaptag.system.sadais.web.enums;
/**
 * 消息类型枚举
 */
import java.util.LinkedHashMap;

public enum MsgTypeEnums {
    NO_JUMP(100, "表示无跳转"), 
    H5(101, "表示h5页"), 
    MSG(102, "表示消息中心"), 
    USER_NOTE(103, "表示纸条消息"), 
    SHARE_PRINT(104, "表示共享打印"), 
    CATE(105, "表示类目"), 
    ITEM(106, "表示商品");
    
    public int                                          type;
    public String                                       desc;
    private static final LinkedHashMap<Integer, String> map;
    static {
        map = new LinkedHashMap<Integer, String>();
        for (MsgTypeEnums msgTypeEnums : MsgTypeEnums.values()) {
            map.put(msgTypeEnums.getType(), msgTypeEnums.getDesc());
        }
    } 
 
    private MsgTypeEnums(int type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public int getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }

    public static LinkedHashMap<Integer, String> getMap() {
        return map;
    }
}
