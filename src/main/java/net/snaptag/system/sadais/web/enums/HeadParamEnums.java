package net.snaptag.system.sadais.web.enums;

import java.util.LinkedHashMap;

/**
 * head请求头参数key
 */
public enum HeadParamEnums {
    SADAIS_AGENT("sadais-agent", "自定义ua信息"), 
    UA_FLAG("ua-flag", "ua"), 
    USER_AGENT("User-Agent", "ua"), 
    UNIQUE_FLAG("unique-flag", "唯一标识"), 
    JWT_TOKEN("jwttoken", "token"), 
    LANGUAGE("language", "语言");
    public String                                      key;
    public String                                      desc;
    private static final LinkedHashMap<String, String> map;
    static {
        map = new LinkedHashMap<String, String>();
        for (HeadParamEnums headParamEnums : HeadParamEnums.values()) {
            map.put(headParamEnums.getKey(), headParamEnums.getDesc());
        }
    }
 
    HeadParamEnums(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }


    public String getKey() {
        return key;
    }

    public String getDesc() {
        return desc;
    }

    public static LinkedHashMap<String, String> getMap() {
        return map;
    }
}
