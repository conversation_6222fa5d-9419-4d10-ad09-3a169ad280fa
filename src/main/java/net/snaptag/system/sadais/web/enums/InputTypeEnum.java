package net.snaptag.system.sadais.web.enums;
import java.util.LinkedHashMap;

/**
 * 输入类型枚举
 * 
 * <AUTHOR> 2018年11月15日
 */
public enum InputTypeEnum {
    SELECT("1", "select"),
    CHECK_BOX("2", "checkbox"),
    INPUT("3", "input"),
    RADIO("4", "radio"),
    COMPOSITE("5", "composite");
    private final String                                value;
    private final String                                desc;
    private static final LinkedHashMap<String, String> map;
    static {
        map = new LinkedHashMap<>();
        for (InputTypeEnum inputTypeEnum : InputTypeEnum.values()) {
            map.put(inputTypeEnum.getValue(), inputTypeEnum.getDesc());
        }
    }

    InputTypeEnum(String value, String desc) {
        this.value = value;
        this.desc = desc;
    } 

    public String getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public static LinkedHashMap<String, String> getMap() {
        return map;
    }
}
