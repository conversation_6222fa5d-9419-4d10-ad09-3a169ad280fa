package net.snaptag.system.sadais.web.dto;
import java.io.Serializable;

public class ProjectInfoDto implements Serializable {
    /**
     * 用户信息
     */
    private static final long serialVersionUID = 1L;
    private String            id;                   // 记录ID
    private String            projectName;          // 项目名称
    private String            projectCode;          // 项目编号
    private String            enterpriseCode;       // 企业编号
    private int               isUser;               // 是否能创建用户
    private int               isRole;               // 是否能创建角色
    private int               isResource;           // 是否能创建资源
    private String            assAttribute;         // 关联属性

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getAssAttribute() {
        return assAttribute;
    }

    public void setAssAttribute(String assAttribute) {
        this.assAttribute = assAttribute;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public String getProjectCode() {
        return projectCode;
    }

    public void setProjectCode(String projectCode) {
        this.projectCode = projectCode;
    }

    public String getEnterpriseCode() {
        return enterpriseCode;
    }

    public void setEnterpriseCode(String enterpriseCode) {
        this.enterpriseCode = enterpriseCode;
    }

    public int getIsUser() {
        return isUser;
    }

    public void setIsUser(int isUser) {
        this.isUser = isUser;
    }

    public int getIsRole() {
        return isRole;
    }

    public void setIsRole(int isRole) {
        this.isRole = isRole;
    }

    public int getIsResource() {
        return isResource;
    }

    public void setIsResource(int isResource) {
        this.isResource = isResource;
    }
}
