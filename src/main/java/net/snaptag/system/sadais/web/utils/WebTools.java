package net.snaptag.system.sadais.web.utils;

import net.snaptag.system.sadais.util.core.ToolsKit;
import net.snaptag.system.sadais.web.enums.LanguageEnums;

import java.util.Locale;

public class WebTools {
    /**
     * 设定安全密码的Salt
     */
    public static byte[] buildEntryptSalt() {
        return Digests.generateSalt(8);
    }

    /**
     * 设定安全的密码，生成随机的salt并经过1024次 sha-1 hash
     */
    public static String buildEntryptPassword(String password, byte[] salt) {
        byte[] hashPassword = Digests.sha1(password.getBytes(), salt, 1024);
        return Encodes.encodeHex(hashPassword);
    }

    /**
     * 获取语言
     * 
     * @param language
     * @return
     */
    public static Locale getLocaleByLanguage(String language) {
        if (ToolsKit.isNotEmpty(language)) {
            if (language.equals(LanguageEnums.ZH_CN.getKey())) {
                return Locale.SIMPLIFIED_CHINESE;
            } else if (language.equals(LanguageEnums.EN_US.getKey())) {
                return Locale.US;
            } else if (language.equals(LanguageEnums.JA_JP.getKey())) {
                return Locale.JAPAN;
            } else if (language.equals(LanguageEnums.KO_KR.getKey())) {
                return Locale.KOREA;
            } else if (language.equals(LanguageEnums.ZH_TW.getKey())) {
                return Locale.TRADITIONAL_CHINESE;
            } else if (language.equals(LanguageEnums.ES_ES.getKey())) {
                return ES_ES;
            } else if (language.equals(LanguageEnums.ES_LA.getKey())) {
                return ES_LA;
            } else if (language.equals(LanguageEnums.FR_FR.getKey())) {
                return FR_FR;
            } else if (language.equals(LanguageEnums.RU_RU.getKey())) {
                return RU_RU;
            } else if (language.equals(LanguageEnums.AR_AE.getKey())) {
                return AR_AE;
            } else if (language.equals(LanguageEnums.DE_DE.getKey())) {
                return DE_DE;
            } else if (language.equals(LanguageEnums.IT_IT.getKey())) {
                return IT_IT;
            } else if (language.equals(LanguageEnums.PT_PT.getKey())) {
                return PT_PT;
            } else if (language.equals(LanguageEnums.TH_TH.getKey())) {
                return TH_TH;
            } else if (language.equals(LanguageEnums.VI_VI.getKey())) {
                return VI_VI;
            } else if (language.equals(LanguageEnums.FI_FI.getKey())) {
                return FI_FI;
            } else if (language.equals(LanguageEnums.PL_PL.getKey())) {
                return PL_PL;
            } else if (language.equals(LanguageEnums.NL_NL.getKey())) {
                return NL_NL;
            } else if (language.equals(LanguageEnums.BN_BD.getKey())) {
                return BN_BD;
            } else {
                // 其他情况返回英文
                return Locale.US;
            }
        }
        return Locale.SIMPLIFIED_CHINESE;
    }

    private final static Locale ES_ES = new Locale("es","ES");
    private final static Locale ES_LA = new Locale("es","LA");
    private final static Locale FR_FR = new Locale("fr","FR");
    private final static Locale RU_RU = new Locale("ru","RU");
    private final static Locale AR_AE = new Locale("ar","AE");
    private final static Locale DE_DE = new Locale("de","DE");

    private final static Locale  IT_IT= new Locale("it","IT");
    private final static Locale  PT_PT= new Locale("pt","PT");
    private final static Locale  TH_TH= new Locale("th","TH");
    private final static Locale  VI_VI= new Locale("vi","VI");

    private final static Locale  FI_FI= new Locale("fi","FI");
    private final static Locale  PL_PL= new Locale("pl","PL");
    private final static Locale  NL_NL= new Locale("nl","NL");
    private final static Locale  BN_BD= new Locale("bn","BD");
}
