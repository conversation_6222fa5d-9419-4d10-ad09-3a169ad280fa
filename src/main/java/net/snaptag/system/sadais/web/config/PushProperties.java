package net.snaptag.system.sadais.web.config;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
public class PushProperties {
    @Value("${androidAccessId:0}")
    private long   androidAccessId;
    @Value("${androidSecretKey:}")
    private String androidSecretKey;
    @Value("${iosAccessId:0}")
    private long   iosAccessId;
    @Value("${iosSecretKey:}")
    private String iosSecretKey;
    @Value("${expireTime:259200}")
    private int    expireTime;
    @Value("${iosPushType:2}")
    private int    iosPushType;
    @Value("${accessKeyId:}")
    private String accessKeyId;
    @Value("${accessKeySecret:}")
    private String accessKeySecret;
    @Value("${useType:XG}")
    private String useType;

    @Value("${ymAndroidAccessId:}")
    private String ymAndroidAccessId;
    private String ymAndroidSecretKey;
    private String ymIosAccessId;

    public String getYmAndroidAccessId() {
        return ymAndroidAccessId;
    }

    public void setYmAndroidAccessId(String ymAndroidAccessId) {
        this.ymAndroidAccessId = ymAndroidAccessId;
    }

    public String getYmAndroidSecretKey() {
        return ymAndroidSecretKey;
    }

    public void setYmAndroidSecretKey(String ymAndroidSecretKey) {
        this.ymAndroidSecretKey = ymAndroidSecretKey;
    }

    public String getYmIosAccessId() {
        return ymIosAccessId;
    }

    public void setYmIosAccessId(String ymIosAccessId) {
        this.ymIosAccessId = ymIosAccessId;
    }

    public String getYmIosSecretKey() {
        return ymIosSecretKey;
    }

    public void setYmIosSecretKey(String ymIosSecretKey) {
        this.ymIosSecretKey = ymIosSecretKey;
    }

    private String ymIosSecretKey;

    public long getAndroidAccessId() {
        return androidAccessId;
    }

    public void setAndroidAccessId(long androidAccessId) {
        this.androidAccessId = androidAccessId;
    }

    public String getAndroidSecretKey() {
        return androidSecretKey;
    }

    public void setAndroidSecretKey(String androidSecretKey) {
        this.androidSecretKey = androidSecretKey;
    }

    public long getIosAccessId() {
        return iosAccessId;
    }

    public void setIosAccessId(long iosAccessId) {
        this.iosAccessId = iosAccessId;
    }

    public String getIosSecretKey() {
        return iosSecretKey;
    }

    public void setIosSecretKey(String iosSecretKey) {
        this.iosSecretKey = iosSecretKey;
    }

    public int getExpireTime() {
        return expireTime;
    }

    public void setExpireTime(int expireTime) {
        this.expireTime = expireTime;
    }

    public int getIosPushType() {
        return iosPushType;
    }

    public void setIosPushType(int iosPushType) {
        this.iosPushType = iosPushType;
    }

    public String getAccessKeyId() {
        return accessKeyId;
    }

    public void setAccessKeyId(String accessKeyId) {
        this.accessKeyId = accessKeyId;
    }

    public String getAccessKeySecret() {
        return accessKeySecret;
    }

    public void setAccessKeySecret(String accessKeySecret) {
        this.accessKeySecret = accessKeySecret;
    }

    public String getUseType() {
        return useType;
    }

    public void setUseType(String useType) {
        this.useType = useType;
    }
}