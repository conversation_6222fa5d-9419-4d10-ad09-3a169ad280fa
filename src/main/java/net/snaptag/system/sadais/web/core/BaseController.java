package net.snaptag.system.sadais.web.core;

import cn.hutool.core.date.DatePattern;
import com.alibaba.fastjson.JSONArray;
import net.snaptag.system.sadais.core.common.enums.ExceptionEnums;
import net.snaptag.system.sadais.core.common.enums.IEnums;
import net.snaptag.system.sadais.core.common.exception.ServiceException;
import net.snaptag.system.sadais.util.core.ToolsKit;
import net.snaptag.system.sadais.web.common.JsonKit;
import net.snaptag.system.sadais.web.common.WebConst;
import net.snaptag.system.sadais.web.common.WebKit;
import net.snaptag.system.sadais.web.dto.*;
import net.snaptag.system.sadais.web.jwt.SubjectModel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Field;
import java.util.*;

/**
 * Controller基类，封装公用方法
 */
public abstract class BaseController {
    private static Logger logger = LoggerFactory.getLogger(BaseController.class);

    public HttpServletRequest getRequest() {
        return ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
    }

    public HttpServletResponse getResponse() {
        return ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getResponse();
    }

    public BaseController setValue(String key, Object obj) {
        this.getRequest().setAttribute(key, obj);
        return this;
    }

    /**
     * 取出请求值
     *
     * @param key
     *            请求参数的key
     * @return 如果存在则返回字符串内容,不存在则返回""
     */
    public String getValue(String key) {
        return WebKit.getValue(key, this.getRequest());
    }

    @SuppressWarnings("unchecked")
    public <T> T getValueObj(String key, Class<?> cls) {
        Object values = WebKit.getValueObj(key, this.getRequest());
        if (ToolsKit.isEmpty(values)) {
            return null;
        }
        String jsonText = JsonKit.toJsonString(values);
        if (ToolsKit.isEmpty(jsonText)) {
            try {
                return this.newInstance(cls);
            } catch (Exception e) {
                return null;
            }
        }
        return (T) JsonKit.jsonParseObject(jsonText, cls);
    }

    /**
     * 通过反射创建实例
     */
    @SuppressWarnings("unchecked")
    public <T> T newInstance(Class<?> commandClass) {
        T instance;
        try {
            instance = (T) commandClass.newInstance();
        } catch (Exception e) {
            logger.error("创建实例出错！", e);
            throw new RuntimeException(e);
        }
        return instance;
    }

    /**
     * 取出请求值
     *
     * @param key
     *            请求参数的key
     * @return 如果存在则返回字符串内容,不存在则返回null
     */
    public String[] getValues(String key) {
        String[] values = null;
        String errorMsg = "";
        try {
            values = this.getRequest().getParameterValues(key);
            if (ToolsKit.isEmpty(values)) {
                values = ToolsKit.isEmpty(this.getRequest().getAttribute(key)) ? null : (String[]) this.getRequest().getAttribute(key);
            }
        } catch (Exception e) {
            errorMsg = e.getMessage();
            try {
                Object valObj = this.getRequest().getAttribute(key);
                if (valObj instanceof JSONArray) {
                    JSONArray array = (JSONArray) valObj;
                    values = array.toArray(new String[] {});
                }
            } catch (Exception e1) {
                errorMsg = e1.getMessage();
            }
        }
        if (ToolsKit.isEmpty(values)) {
            logger.warn(errorMsg);
        }
        return values;
    }

    /**
     * 根据key取请求值，并将数据转换为int返回
     *
     * @param key
     *            请求参数的key
     * @return 如果值为空或转换异常时，返回-1
     */
    protected int getIntValue(String key) {
        String value = getValue(key);
        if (ToolsKit.isNotEmpty(value)) {
            try {
                return Integer.parseInt(value);
            } catch (Exception e) {
                try {
                    logger.warn(e.getMessage(), e);
                    return getNumberValue(value).intValue();
                } catch (Exception e1) {
                    throw new ServiceException(e1.getMessage(), e1);
                }
            }
        }
        return -1;
    }

    /**
     * 根据key取请求值，并将数据转换为long返回
     *
     * @param key
     *            请求参数的key
     * @return 如果值为空或转换异常时，返回-1
     */
    protected long getLongValue(String key) {
        String value = getValue(key);
        if (ToolsKit.isNotEmpty(value)) {
            try {
                return Long.parseLong(value);
            } catch (Exception e) {
                try {
                    logger.warn(e.getMessage(), e);
                    return getNumberValue(value).longValue();
                } catch (Exception e1) {
                    throw new ServiceException(e1.getMessage(), e1);
                }
            }
        }
        return -1L;
    }

    /**
     * 根据key取请求值，并将数据转换为float返回
     *
     * @param key
     *            请求参数的key
     * @return 如果值为空或转换异常时，返回-1
     */
    protected float getFloatValue(String key) {
        String value = getValue(key);
        if (ToolsKit.isNotEmpty(value)) {
            try {
                return Float.parseFloat(value);
            } catch (Exception e) {
                try {
                    logger.warn(e.getMessage(), e);
                    return getNumberValue(value).floatValue();
                } catch (Exception e1) {
                    throw new ServiceException(e1.getMessage(), e1);
                }
            }
        }
        return -1f;
    }

    /**
     * 根据key取请求值，并将数据转换为double返回
     *
     * @param key
     *            请求参数的key
     * @return 如果值为空或转换异常时，返回-1
     */
    protected double getDoubleValue(String key) {
        String value = getValue(key);
        if (ToolsKit.isNotEmpty(value)) {
            try {
                return Double.parseDouble(value);
            } catch (Exception e) {
                throw new ServiceException(e.getMessage(), e);
            }
        }
        return -1d;
    }

    private Double getNumberValue(String key) {
        try {
            return getDoubleValue(key);
        } catch (Exception e) {
            throw new ServiceException(e.getMessage(), e);
        }
    }

    /**
     * 根据key取请求值，并将数据转换为Boolean返回
     *
     * @param key
     *            请求参数的key
     * @return 如果值为空或转换异常时，返回false
     */
    protected Boolean getBooleanValue(String key) {
        String value = getValue(key);
        if (ToolsKit.isNotEmpty(value)) {
            try {
                return Boolean.parseBoolean(value);
            } catch (Exception e) {
                throw new ServiceException(e.getMessage(), e);
            }
        }
        return false;
    }

    /**
     * 根据key取请求值，并将数据转换为Date返回
     *
     * @param key
     *            请求参数的key
     * @return 如果值为空或转换异常时，返回null
     */
    protected Date getDateValue(String key) {
        String value = getValue(key);
        if (ToolsKit.isNotEmpty(value)) {
            try {
                long millisecond = Long.parseLong(value);
                return new Date(millisecond);
            } catch (Exception ex) {
                try {
                    return net.snaptag.system.sadais.util.core.ToolsKit.Date.parse(value, DatePattern.NORM_DATETIME_PATTERN);
                } catch (Exception e) {
                    return null;
                }
            }
        }
        return null;
    }

    /**
     * 根据类，取出请求参数并将其转换为Bean对象返回 默认验证
     *
     * @param tClass
     *            要转换的类
     * @return
     */
    protected <T> T getBean(Class<T> tClass) {
        String jsonString = this.getValue(WebConst.JSON_POST_DATA);
        if (ToolsKit.isNotEmpty(jsonString)) {
            Map<String, Object> jsonMap = JsonKit.jsonParseObject(jsonString, Map.class);
            T t = ToolsKit.Bean.mapToBean(jsonMap, tClass, true);
            List<Field> fieldList = new ArrayList<Field>();
            fieldList.addAll(Arrays.asList(tClass.getDeclaredFields()));
            Class<?> superclass = tClass.getSuperclass();
            if (ToolsKit.isNotEmpty(superclass) && ToolsKit.isNotEmpty(superclass.getDeclaredFields())) {
                fieldList.addAll(Arrays.asList(superclass.getDeclaredFields()));
            }
            for (Field field : fieldList) {
                Class<?> typeCls = field.getType();
                Object value = jsonMap.get(field.getName());
                if (ToolsKit.isNotEmpty(value)) {
                    if (JsonKit.isMapJsonString(value.toString()) || JsonKit.isArrayJsonString(value.toString())) {
                        ToolsKit.Bean.setFieldValue(t, field.getName(), JsonKit.jsonParseObject(value.toString(), typeCls));
                    }
                }
            }
            return JsonKit.jsonParseObject(JsonKit.toJsonString(t), tClass);
        }
        return null;
    }

    protected ReturnDto returnSuccessJson(Object obj) {
        return returnSuccessJson(ExceptionEnums.SUCCESS, obj);
    }

    protected ReturnDto returnSuccessJson(IEnums enums, Object obj) {
        return WebKit.buildReturnDto(enums, obj);
    }

    protected ReturnDto returnSuccessJson(int code, String message, Object obj) {
        return WebKit.returnSuccessJson(code, message, obj);
    }

    /**
     * 返回错误信息到客户端
     *
     * @param ex
     *            自定义ServiceException异常
     */
    protected ReturnDto returnFailJson(Exception ex) {
        String message = ex.getMessage();
        int code = 1;
        Map<String, Object> allParams = WebKit.getAllParams(this.getRequest());
        if (ex instanceof ServiceException) {
            ServiceException se = (ServiceException) ex;
            IEnums enums = se.getEnums();
            if (ToolsKit.isEmpty(enums)) {
                code = ToolsKit.isEmpty(se.getCode()) ? ExceptionEnums.ERROR.getCode() : se.getCode();
                message = ToolsKit.isEmpty(se.getMessage()) ? ExceptionEnums.ERROR.getMessage() : se.getMessage();
            } else {
                code = enums.getCode();
                message = enums.getMessage();
            }
            System.out.println("returnFail：" + se.getStackTrace()[0].getClassName() + "-->" + se.getStackTrace()[0].getMethodName() + "-->"
                    + se.getStackTrace()[0].getLineNumber() + "：" + message + "：" + this.getRequest().getRequestURI() + "：" + JsonKit.toJsonString(allParams));
        } else {
            logger.warn(ex.getMessage(), ex);
        }
        ReturnDto returnDto = WebKit.buildReturnDto(ExceptionEnums.ERROR, null);
        HeadDto head = returnDto.getHead();
        head.setRet(code);
        head.setMsg(message);
        returnDto.setHead(head);
        return returnDto;
    }

    /**
     * 返回失败信息
     * 
     * @param ret
     * @param msg
     * @param dto
     * @return
     */
    protected ReturnDto returnFailJson(int ret, String msg, Object dto) {
        return WebKit.returnFailJson(ret, msg, dto);
    }

    /**
     * 获取请求头信息
     * 
     * @return
     */
    protected HeadInfoDto getHeadInfoDto() {
        String jsonString = WebKit.getValue(WebConst.HEAD_INFO_DATA, this.getRequest());
        if (ToolsKit.isNotEmpty(jsonString)) {
            return JsonKit.jsonParseObject(jsonString, HeadInfoDto.class);
        }
        return null;
    }

    /**
     * 获取JWT用户信息
     * 
     * @return
     */
    protected UserInfoDto getUserInfoDto() {
        String jsonString = WebKit.getValue(WebConst.JWT_USER_INFO, this.getRequest());
        if (ToolsKit.isNotEmpty(jsonString)) {
            return JsonKit.jsonParseObject(jsonString, UserInfoDto.class);
        }
        return null;
    }

    /**
     * 获取JWT模型信息
     * 
     * @return
     */
    protected SubjectModel getSubjectModelDto() {
        String jsonString = WebKit.getValue(WebConst.JWT_SUBJECT, this.getRequest());
        if (ToolsKit.isNotEmpty(jsonString)) {
            return JsonKit.jsonParseObject(jsonString, SubjectModel.class);
        }
        return null;
    }

    /**
     * 获取渠道APPID
     * 
     * @return
     */
    protected String getPlatFormAppId() throws ServiceException {
        HeadInfoDto headInfoDto = this.getHeadInfoDto();
        UserInfoDto userInfoDto = this.getUserInfoDto();
        String appId = null;
        if (ToolsKit.isEmpty(userInfoDto)) {
            throw new ServiceException().setCode(ExceptionEnums.ERROR.getCode()).setMessage("校验信息不能为空");
        }
        if (ToolsKit.isEmpty(headInfoDto) || ToolsKit.isEmpty(headInfoDto.getChannel())) {
            throw new ServiceException().setCode(ExceptionEnums.ERROR.getCode()).setMessage("校验信息不能为空");
        }
        if (!userInfoDto.getSuperAdmin()) {
            List<ProjectInfoDto> dtoList = userInfoDto.getProjectInfoDto();
            if (ToolsKit.isEmpty(dtoList)) {
                throw new ServiceException().setCode(ExceptionEnums.ERROR.getCode()).setMessage("未分配项目信息");
            } else {
                for (ProjectInfoDto dto : dtoList) {
                    if (dto.getProjectCode().equals(headInfoDto.getChannel())) {
                        appId = dto.getAssAttribute();
                        break;
                    }
                }
            }
        }
        return appId;
    }
}
