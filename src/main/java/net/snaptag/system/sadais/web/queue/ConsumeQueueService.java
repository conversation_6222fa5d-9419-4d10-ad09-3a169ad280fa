package net.snaptag.system.sadais.web.queue;

import net.snaptag.system.sadais.util.core.ToolsKit;
import net.snaptag.system.sadais.web.common.JsonKit;
import net.snaptag.system.sadais.web.core.SpringContextHolder;
import org.springframework.context.support.ApplicationObjectSupport;

import java.util.concurrent.BlockingQueue;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

/**
 * 消费者队列服务
 * 
 * <AUTHOR> 2018年6月24日
 */
public class ConsumeQueueService extends ApplicationObjectSupport implements Runnable, QueueInterface {
    private BlockingQueue<QueueMessage> blockingQueue;
    private static ConsumeQueueService ourInstance;
    private static Lock                 cacheKitLock = new ReentrantLock();

    public static ConsumeQueueService getInstance() {
        try {
            cacheKitLock.lock();
            if (null == ourInstance) {
                ourInstance = new ConsumeQueueService();
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            cacheKitLock.unlock();
        }
        return ourInstance;
    }

    private ConsumeQueueService() {
    }

    public void start() {
        Thread thread = new Thread(this);
        thread.setName("ConsumeQueueService-Thread");
        thread.start();
    }

    @Override
    public void run() {
        while (true) {
            try {
                QueueMessage queueMessage = blockingQueue.take();
                if (ToolsKit.isNotEmpty(queueMessage)) {
                    // System.out.println(JsonKit.toJsonString(queueMessage));
                    String beanName = ToolsKit.String.toLowerCaseFirstOne(queueMessage.getTags());
                    if (SpringContextHolder.containsBean(beanName)) {
                        ConsumeInterface consumeInterface = SpringContextHolder.getBean(beanName);
                        consumeInterface.consumeQueue(queueMessage);
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    public void setConsumeQueue(int queueSize) {
        if (queueSize > 0 && ToolsKit.isEmpty(blockingQueue)) {
            this.blockingQueue = new LinkedBlockingQueue<QueueMessage>(queueSize);
        }
    }

    /**
     * 添加消费队列信息对象到队列
     * 
     * @param queueMessage
     *            消费队列信息对象
     * @return
     */
    @Override
    public boolean addToQueue(QueueMessage queueMessage) {
        if (ToolsKit.isNotEmpty(queueMessage) && ToolsKit.isNotEmpty(queueMessage.getTags())) {
            String beanName = ToolsKit.String.toLowerCaseFirstOne(queueMessage.getTags());
            if (SpringContextHolder.containsBean(beanName)) {// 判断插件是否注册了对应的处理服务
                // System.out.println("====addToCustomizeQueue====" + " --- " + blockingQueue.size());
                if (ToolsKit.isNotEmpty(queueMessage.getBody())) {
                    queueMessage.setBody(JsonKit.toJsonString(queueMessage.getBody()));
                }
                return blockingQueue.offer(queueMessage);
            }
        }
        return false;
    }
}
