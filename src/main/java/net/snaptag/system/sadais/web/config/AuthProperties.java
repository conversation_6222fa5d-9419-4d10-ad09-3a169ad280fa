package net.snaptag.system.sadais.web.config;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
public class AuthProperties {
    @Value("${clientId:}")
    private String clientId;    // 第三方授权id
    @Value("${clientSecret:}")
    private String clientSecret;// 第三方授权密钥

    public String getClientId() {
        return clientId;
    }

    public void setClientId(String clientId) {
        this.clientId = clientId;
    }

    public String getClientSecret() {
        return clientSecret;
    }

    public void setClientSecret(String clientSecret) {
        this.clientSecret = clientSecret;
    }
}