package net.snaptag.system.sadais.web.queue;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class QueueKit {
    private static Logger         logger = LoggerFactory.getLogger(QueueKit.class);
    private static QueueInterface queueInterface;

    public static QueueInterface queue() {
        try {
            if (queueInterface == null) {
                queueInterface = ConsumeQueueService.getInstance();
            }
        } catch (Exception e) {
            logger.warn(e.getMessage(), e);
        }
        return queueInterface;
    }
}
