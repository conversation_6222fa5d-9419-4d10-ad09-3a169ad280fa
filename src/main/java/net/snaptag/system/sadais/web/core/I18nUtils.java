package net.snaptag.system.sadais.web.core;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Locale;

/**
 * code：信息的键，properties中的key 默认找mess.properties，找不到去其他两个当中找直到找到为止 注意:当三个文件当中都有同一个key对应不同的value时 执行顺序： mess_zh_CN.properties > mess.properties >
 * mess_en_US.properties 所以禁止写入同一个key对应不同value
 */
@Component
public class I18nUtils {
    @Autowired
    private LocaleMessage localeMessage;

    /**
     * 获取key
     * 
     * @param key
     * @return
     */
    public String getKey(String key) {
        String name = localeMessage.getMessage(key);
        return name;
    }

    /**
     * 获取指定哪个配置文件下的key
     * 
     * @param key
     * @param local
     * @return
     */
    public String getKey(String key, Locale local) {
        String name = localeMessage.getMessage(key, local);
        return name;
    }
}
