package net.snaptag.system.sadais.web.core;

import net.snaptag.system.sadais.core.common.enums.ExceptionEnums;
import net.snaptag.system.sadais.web.dto.ReturnDto;
import org.springframework.boot.web.servlet.error.ErrorController;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * 基础的错误异常处理器
 * 
 * <AUTHOR>
 */
@Controller
@RequestMapping("${server.error.path:${error.path:/error}}")
public class FundaErrorController extends BaseController implements ErrorController {
    public String getErrorPath() {
        return "/error";
    }

    @RequestMapping
    @ResponseBody
    public ReturnDto doHandleError() {
        return this.returnFailJson(ExceptionEnums.ERROR.getCode(), ExceptionEnums.NET_EXCEPTION.getMessage(), null);
    }
}
