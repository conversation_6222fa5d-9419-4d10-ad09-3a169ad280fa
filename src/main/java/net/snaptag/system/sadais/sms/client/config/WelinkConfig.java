package net.snaptag.system.sadais.sms.client.config;
public class WelinkConfig {
    private String              welinkName;
    private String              welinkPwd;
    private String              welinkUrl;
    private String              scorpid;
    private String              sprdid;
    private int                 maxRecNum;
    private String              signName;
    private static WelinkConfig welinkConfig;

    public static WelinkConfig getInstance() {
        try {
            if (null == welinkConfig) {
                welinkConfig = new WelinkConfig();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return welinkConfig;
    }

    private WelinkConfig() {
    }

    public String getWelinkName() {
        return welinkName;
    }

    public void setWelinkName(String welinkName) {
        this.welinkName = welinkName;
    }

    public String getWelinkPwd() {
        return welinkPwd;
    }

    public void setWelinkPwd(String welinkPwd) {
        this.welinkPwd = welinkPwd;
    }

    public String getWelinkUrl() {
        return welinkUrl;
    }

    public void setWelinkUrl(String welinkUrl) {
        this.welinkUrl = welinkUrl;
    }

    public String getScorpid() {
        return scorpid;
    }

    public void setScorpid(String scorpid) {
        this.scorpid = scorpid;
    }

    public String getSprdid() {
        return sprdid;
    }

    public void setSprdid(String sprdid) {
        this.sprdid = sprdid;
    }

    public int getMaxRecNum() {
        return maxRecNum;
    }

    public void setMaxRecNum(int maxRecNum) {
        this.maxRecNum = maxRecNum;
    }

    public String getSignName() {
        return signName;
    }

    public void setSignName(String signName) {
        this.signName = signName;
    }
}
