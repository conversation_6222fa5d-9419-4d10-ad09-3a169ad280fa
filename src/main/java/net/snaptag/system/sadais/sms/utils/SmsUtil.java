package net.snaptag.system.sadais.sms.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import net.snaptag.system.sadais.sms.client.config.AldyConfig;
import net.snaptag.system.sadais.sms.core.SmsMessage;
import net.snaptag.system.sadais.util.core.ToolsKit;
import org.apache.commons.io.IOUtils;

import java.io.InputStream;
import java.net.*;
import java.util.*;
import java.util.Map.Entry;

/**
 * 1、日期 2、淘宝帐号 3、公司名 4、用户邮箱 5、核对数据日期范围 6、模板ID 7、接收手机号 8、短信条数 9成功/失败（6~9不用填）10、用户联系电话号码（走工单系统需要） 11、需要您提交登录大于管理中心的截图 包含右上角的用户类型和用户名 ， 开发处理后邮件通知给您结果
 * 麻烦您提交相关材料，发送至邮箱：<EMAIL>
 */
public class SmsUtil {
    private final static String                  QUOTATION   = "\"";
    private final static Map<String, SmsMessage> templateMap = new HashMap<String, SmsMessage>();

    /**
     * 通过反射创建实例
     */
    @SuppressWarnings("unchecked")
    public static <T> T newInstance(String className) {
        T instance;
        try {
            Class<?> commandClass = Class.forName(className);
            instance = (T) commandClass.newInstance();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return instance;
    }

    public static String toJsonString(Map<String, String> map) {
        if (null == map) {
            throw new NullPointerException("map is null");
        }
        StringBuilder sb = new StringBuilder();
        sb.append("{");
        for (Iterator<Entry<String, String>> it = map.entrySet().iterator(); it.hasNext();) {
            Entry<String, String> entry = it.next();
            sb.append(QUOTATION).append(entry.getKey()).append(QUOTATION).append(":").append(QUOTATION).append(entry.getValue()).append(QUOTATION).append(",");
        }
        if (sb.length() > 1) {
            sb.deleteCharAt(sb.length() - 1);
        }
        sb.append("}");
        return sb.toString();
    }

    public static String builderPhones(List<String> phoneList, int max) {
        if (null == phoneList) {
            throw new NullPointerException("phoneList is null");
        }
        if (phoneList.size() > max) {
            throw new IndexOutOfBoundsException();
        }
        StringBuilder sb = new StringBuilder();
        for (Iterator<String> it = phoneList.iterator(); it.hasNext();) {
            sb.append(it.next()).append(",");
        }
        if (sb.length() > 1) {
            sb.deleteCharAt(sb.length() - 1);
        }
        return sb.toString();
    }

    private static String getSmsTemplateJson() {
        String urlStr = AldyConfig.getInstance().getSmsTemplateUrl();
        InputStream input = null;
        HttpURLConnection connection = null;
        String json = "";
        try {
            URL url = new URL(urlStr);
            connection = (HttpURLConnection) url.openConnection();
            connection.connect();
            input = connection.getInputStream();
            json = IOUtils.toString(input, AldyConfig.getInstance().getCharEncode());
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            IOUtils.closeQuietly(input);
            if (connection != null) {
                connection.disconnect();
            }
        }
        return json;
    }

    @SuppressWarnings("rawtypes")
    private static Map<String, SmsMessage> getTemplateCodeMap() {
        try {
            String json = getSmsTemplateJson();
            if (json.isEmpty()) {
                throw new NullPointerException("sms_template.json is null");
            }
            Map<String, SmsMessage> tempMap = new HashMap<String, SmsMessage>();
            JSONArray array = JSON.parseArray(json);
            for (Iterator it = array.listIterator(); it.hasNext();) {
                JSONObject obj = (JSONObject) it.next();
                String type = obj.getString("type");
                String tplCode = obj.getString("tplcode");
                String text = obj.getString("text");
                String name = obj.getString("name");
                SmsMessage message = new SmsMessage();
                message.setText(text);
                message.setTplCode(tplCode);
                message.setName(name);
                message.setType(type);
                tempMap.put(type, message);
            }
            return tempMap;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public static SmsMessage builderSmsMessage(String type) {
        if (ToolsKit.isEmpty(templateMap) || ToolsKit.isEmpty(templateMap.get(type))) {
            Map<String, SmsMessage> map = getTemplateCodeMap();
            if (null != map) {
                templateMap.putAll(map);
            }
        }
        return templateMap.get(type);
    }

    /**
     * 重载模板数据
     * 
     * @throws Exception
     */
    public static void reloadTemplate() throws Exception {
        Map<String, SmsMessage> map = getTemplateCodeMap();
        if (null != map) {
            templateMap.clear();
            templateMap.putAll(map);
        }
    }

    public static String builderContent(SmsMessage message) {
        String content = message.getText();
        for (Iterator<Entry<String, String>> it = message.getParams().entrySet().iterator(); it.hasNext();) {
            Entry<String, String> entry = it.next();
            content = content.replace("${" + entry.getKey() + "}", entry.getValue());
        }
        return content;
    }

    public static String replaceContent(String from, String to, String source) {
        if (source == null || from == null || to == null) return null;
        return source.replace(from, to);
    }

    /**
     * @param content
     * @param charset
     * @return
     */
    public static String encode(String content, String charset) {
        try {
            return URLEncoder.encode(content, charset);
        } catch (Exception e) {
            throw new IllegalArgumentException(e);
        }
    }

    /**
     * 如果是10开头的IP，统统默认为阿里云的机器
     * 
     * @return
     */
    public static boolean isAliyunHost() {
        String clientIp = getLocalHostIP(false).trim();
        // System.out.println("clientIp: " + clientIp);
        if (ToolsKit.isEmpty(clientIp)) throw new NullPointerException("getLocalHostIP Fail: Ip is Empty!");
        // if (clientIp.startsWith("127.0") || clientIp.startsWith("192.168") || "*************".equals(clientIp) || "*************".equals(clientIp)) {
        return clientIp.startsWith("10") ? true : false;
    }

    /**
     * 取本机IP地址
     * 
     * @param isPublicIp
     *            是否取公网IP 为true时取公网IP，为false时取私网IP
     * @return
     */
    public static String getLocalHostIP(boolean isPublicIp) {
        try {
            return getLocalMachineIp(isPublicIp);
        } catch (Exception e) {
            e.printStackTrace();
            return "";
        }
    }

    /**
     * 阿里云的ECS内网IP都是以10开头
     * 
     * @param isPublicIp
     * @return
     */
    private static String getLocalMachineIp(boolean isPublicIp) {
        InetAddressValidator validator = new InetAddressValidator();
        String candidate = new String();
        try {
            Enumeration<NetworkInterface> ifaces = NetworkInterface.getNetworkInterfaces();
            while (ifaces.hasMoreElements()) {
                NetworkInterface iface = (NetworkInterface) ifaces.nextElement();
                if (iface.isUp()) {
                    Enumeration<InetAddress> addresses = iface.getInetAddresses();
                    while (addresses.hasMoreElements()) {
                        InetAddress address = (InetAddress) addresses.nextElement();
                        if ((!address.isLinkLocalAddress()) && (address.getHostAddress() != null)) {
                            String ipAddress = address.getHostAddress();
                            if (isPublicIp) {
                                if (!ipAddress.equals("127.0.0.1") && !ipAddress.startsWith("10") && !ipAddress.startsWith("0")) {
                                    if (validator.isValidInet4Address(ipAddress)) {
                                        return ipAddress;
                                    }
                                }
                            } else {
                                if (!ipAddress.equals("127.0.0.1") && ipAddress.startsWith("10")) {
                                    if (validator.isValidInet4Address(ipAddress)) {
                                        return ipAddress;
                                    }
                                }
                            }
                            if (validator.isValid(ipAddress)) {
                                candidate = ipAddress;
                            }
                        }
                    }
                }
            }
        } catch (SocketException localSocketException) {
        }
        return candidate;
    }
}
