package net.snaptag.system.sadais.sms.client.welink;

import net.snaptag.system.sadais.sms.client.config.WelinkConfig;
import net.snaptag.system.sadais.sms.core.SmsClientInterface;
import net.snaptag.system.sadais.sms.core.SmsMessage;
import net.snaptag.system.sadais.sms.utils.SmsUtil;
import net.snaptag.system.sadais.util.core.ToolsKit;

import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

public class WelinkSmsUtils implements SmsClientInterface<WelinkClient> {
    private static WelinkClient   client;
    private static WelinkSmsUtils welinkSmsUtils;
    private static Lock           smsKitLock    = new ReentrantLock();
    private static Lock           smsClientLock = new ReentrantLock();

    public static WelinkSmsUtils getInstance() {
        try {
            smsKitLock.lock();
            if (welinkSmsUtils == null) {
                welinkSmsUtils = new WelinkSmsUtils();
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            smsKitLock.unlock();
        }
        return welinkSmsUtils;
    }

    @Override
    public void initClient() {
        try {
            smsClientLock.lock();
            if (client == null) {
                String sname = WelinkConfig.getInstance().getWelinkName();
                String spwd = WelinkConfig.getInstance().getWelinkPwd();
                String smsurl = WelinkConfig.getInstance().getWelinkUrl();
                String scorpid = WelinkConfig.getInstance().getScorpid();
                String sprdid = WelinkConfig.getInstance().getSprdid();
                if (sname.isEmpty() || spwd.isEmpty() || smsurl.isEmpty()) {
                    throw new NullPointerException("get welink sms client params is error!");
                }
                client = new WelinkClient(smsurl, sname, spwd, scorpid, sprdid);
                // client.setUseSimplifyJson(true);
                // client.setNeedEnableLogger(false);
                // client.setIgnoreSSLCheck(true);
                // client.setUseGzipEncoding(false);
                // client.setMaxRetryCount(3);
                // client.setRetryWaitTime(100L);
            }
            if (client == null) {
                throw new NullPointerException("create AcsClient fail!");
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            smsClientLock.unlock();
        }
    }

    @Override
    public WelinkClient getSmsClient() {
        if (null == client) {
            initClient();
        }
        return client;
    }

    @Override
    public SmsMessage sendSms(SmsMessage message) {
        if (null == client) {
            throw new NullPointerException("client is null");
        }
        WelinkSendRequest req = new WelinkSendRequest();
        req.setSmsType(message.getType());
        req.setSmsParam(message.getParams());
        req.setRecNum(SmsUtil.builderPhones(message.getPhones(), WelinkConfig.getInstance().getMaxRecNum()));
        req.setSmsContent(message.getText());
        try {
            WelinkSendResponse response = client.execute(req);
            if (null == response || null == response.getResult()) {
                throw new NullPointerException("WelinkSendRequest is null");
            }
            message.setSuccess(response.isSuccess());
            message.setContent(response.getResult());
        } catch (Exception e) {
            e.printStackTrace();
            message.setSuccess(false);
            message.setContent(e.getMessage());
        }
        return message;
    }

    @Override
    public SmsMessage showSms(SmsMessage message) {
        if (ToolsKit.isEmpty(message)) return null;
        if (ToolsKit.isNotEmpty(message.getParams())) {
            message.setContent(SmsUtil.builderContent(message));
        }
        return message;
    }
}
