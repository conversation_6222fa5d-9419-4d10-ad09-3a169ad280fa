package net.snaptag.system.sadais.sms.plugin;
import net.snaptag.system.sadais.core.common.core.IPlugin;
import net.snaptag.system.sadais.sms.client.config.AldyConfig;
import net.snaptag.system.sadais.sms.kit.SmsKit;
import net.snaptag.system.sadais.sms.utils.SmsChanelEnum;

public class AldyPlugin implements IPlugin {
    public AldyPlugin(String regionId, String accessKey, String accessKeySecret, String endpointName, String product, String domain, int maxRecNum,
            String signName, String charEncode, String smsTemplateUrl) {
        AldyConfig.getInstance().setRegionId(regionId);
        AldyConfig.getInstance().setAccessKey(accessKey);
        AldyConfig.getInstance().setAccessKeySecret(accessKeySecret);
        AldyConfig.getInstance().setEndpointName(endpointName);
        AldyConfig.getInstance().setProduct(product);
        AldyConfig.getInstance().setDomain(domain);
        AldyConfig.getInstance().setMaxRecNum(maxRecNum);
        AldyConfig.getInstance().setSignName(signName);
        AldyConfig.getInstance().setCharEncode(charEncode);
        AldyConfig.getInstance().setSmsTemplateUrl(smsTemplateUrl);
    }

    @Override
    public void init() throws Exception {
    }

    @Override
    public void start() throws Exception {
        SmsKit.getInstance().init(SmsChanelEnum.ALIYUN);
    }

    @Override
    public void stop() throws Exception {
    }
}
