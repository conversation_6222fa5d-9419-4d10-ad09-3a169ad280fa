package net.snaptag.system.sadais.sms.client.welink;

import net.snaptag.system.sadais.sms.client.config.WelinkConfig;
import net.snaptag.system.sadais.sms.utils.SmsUtil;
import net.snaptag.system.sadais.util.core.ToolsKit;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.io.OutputStreamWriter;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.Iterator;
import java.util.Map;

/**
 * 微网联通客户端
 */
public class WelinkClient {
    private URL    postUrl; // 请求对象
    private String smsUrl;  // 请求地址
    private String sname;   // 用户名
    private String spwd;    // 密码
    private String scorpid;
    private String sprdid;

    /**
     * 构建函数
     * 
     * @param smsurl
     *            请求地址
     * @param sname
     *            用户名
     * @param spwd
     *            密码
     * @param scorpid
     *            微网需要的ID值
     * @param sprdid
     *            通道ID
     */
    public WelinkClient(String smsurl, String sname, String spwd, String scorpid, String sprdid) {
        this.smsUrl = smsurl;
        this.sname = sname;
        this.spwd = spwd;
        this.scorpid = scorpid;
        this.sprdid = sprdid;
        initURL();
    }

    /**
     * 初始化URL对象
     */
    private void initURL() {
        try {
            postUrl = new URL(smsUrl);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 构建请求发送数据
     * 
     * @param sdst
     *            短信接收人
     * @param smsg
     *            短信内容
     * @return
     */
    private String builderPostData(String sdst, String smsg) {
        StringBuilder postData = new StringBuilder();
        postData.append("sname=").append(ToolsKit.isEmpty(sname) ? "" : sname).append("&").append("spwd=").append(ToolsKit.isEmpty(spwd) ? "" : spwd)
                .append("&").append("scorpid=").append(ToolsKit.isEmpty(scorpid) ? "" : scorpid).append("&").append("sprdid=")
                .append(ToolsKit.isEmpty(sprdid) ? "" : sprdid).append("&").append("sdst=").append(ToolsKit.isEmpty(sdst) ? "" : sdst).append("&")
                .append("smsg=").append(ToolsKit.isEmpty(smsg) ? "" : smsg);
        return postData.toString();
    }

    /**
     * 执行发送
     * 
     * @param request
     *            请求对象
     * @return
     * @throws Exception
     */
    public WelinkSendResponse execute(WelinkSendRequest request) throws Exception {
        Map<String, String> paramsMap = (Map) request.getSmsParam();
        String content = request.getSmsContent();
        for (Iterator<Map.Entry<String, String>> it = paramsMap.entrySet().iterator(); it.hasNext();) {
            Map.Entry<String, String> entry = it.next();
            String key = "${" + entry.getKey() + "}";
            content = SmsUtil.replaceContent(key, entry.getValue(), content);
        }
        content = "【" + WelinkConfig.getInstance().getSignName() + "】" + content + "\n回复T退订";
        // String[] recArray = request.getRecNum().split(",");
        // WelinkSendResponse response = null;
        // try {
        // for (String rec : recArray) {
        // String data = builderPostData(rec, SmsUtil.encode(content, "utf-8"));
        // response = sendSms(data);
        // }
        // }catch (Exception e) {
        // e.printStackTrace();
        // }
        String data = builderPostData(request.getRecNum(), SmsUtil.encode(content, "utf-8"));
        return sendSms(data);
    }

    /**
     * 以POST方式发送请求到微网联通服务器
     * 
     * @param postData
     *            要发送的内容参数
     * @return
     */
    private WelinkSendResponse sendSms(String postData) {
        WelinkSendResponse response = new WelinkSendResponse();
        try {
            // 发送POST请求
            HttpURLConnection conn = (HttpURLConnection) postUrl.openConnection();
            conn.setRequestMethod("POST");
            conn.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");
            conn.setRequestProperty("Connection", "Keep-Alive");
            conn.setUseCaches(false);
            conn.setDoOutput(true);
            conn.setRequestProperty("Content-Length", "" + postData.length());
            OutputStreamWriter out = new OutputStreamWriter(conn.getOutputStream(), "UTF-8");
            out.write(postData);
            out.flush();
            out.close();
            response.setSuccess(false);
            // 获取响应状态
            if (conn.getResponseCode() != HttpURLConnection.HTTP_OK) {
                response.setCode(conn.getResponseCode());
            }
            // 获取响应内容体
            String line, result = "";
            BufferedReader in = new BufferedReader(new InputStreamReader(conn.getInputStream(), "utf-8"));
            while ((line = in.readLine()) != null) {
                result += line + "\n";
            }
            in.close();
            if (result.length() > 0) {
                response.setSuccess(true);
                response.setResult(result);
            }
            return response;
        } catch (Exception e) {
            // e.printStackTrace(System.out);
            response.setResult(e.getMessage());
        }
        return null;
    }
}
