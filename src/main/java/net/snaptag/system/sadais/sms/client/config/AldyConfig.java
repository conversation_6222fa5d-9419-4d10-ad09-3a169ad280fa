package net.snaptag.system.sadais.sms.client.config;
public class AldyConfig {
    private String            regionId;
    private String            accessKey;
    private String            accessKeySecret;
    private String            endpointName;
    private String            product;
    private String            domain;
    private int               maxRecNum;
    private String            signName;
    private String            charEncode;
    private String            smsTemplateUrl;
    private static AldyConfig aldyConfig;

    public static AldyConfig getInstance() {
        try {
            if (null == aldyConfig) {
                aldyConfig = new AldyConfig();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return aldyConfig;
    }

    private AldyConfig() {
    }

    public String getRegionId() {
        return regionId;
    }

    public void setRegionId(String regionId) {
        this.regionId = regionId;
    }

    public String getAccessKey() {
        return accessKey;
    }

    public void setAccessKey(String accessKey) {
        this.accessKey = accessKey;
    }

    public String getAccessKeySecret() {
        return accessKeySecret;
    }

    public void setAccessKeySecret(String accessKeySecret) {
        this.accessKeySecret = accessKeySecret;
    }

    public String getEndpointName() {
        return endpointName;
    }

    public void setEndpointName(String endpointName) {
        this.endpointName = endpointName;
    }

    public String getProduct() {
        return product;
    }

    public void setProduct(String product) {
        this.product = product;
    }

    public String getDomain() {
        return domain;
    }

    public void setDomain(String domain) {
        this.domain = domain;
    }

    public int getMaxRecNum() {
        return maxRecNum;
    }

    public void setMaxRecNum(int maxRecNum) {
        this.maxRecNum = maxRecNum;
    }

    public String getSignName() {
        return signName;
    }

    public void setSignName(String signName) {
        this.signName = signName;
    }

    public String getCharEncode() {
        return charEncode;
    }

    public void setCharEncode(String charEncode) {
        this.charEncode = charEncode;
    }

    public String getSmsTemplateUrl() {
        return smsTemplateUrl;
    }

    public void setSmsTemplateUrl(String smsTemplateUrl) {
        this.smsTemplateUrl = smsTemplateUrl;
    }
}
