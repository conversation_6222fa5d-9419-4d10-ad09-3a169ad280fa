package net.snaptag.system.sadais.sms.plugin;
import net.snaptag.system.sadais.core.common.core.IPlugin;
import net.snaptag.system.sadais.sms.client.config.WelinkConfig;
import net.snaptag.system.sadais.sms.kit.SmsKit;
import net.snaptag.system.sadais.sms.utils.SmsChanelEnum;

public class WelinkPlugin implements IPlugin {
    public WelinkPlugin(String welinkName, String welinkPwd, String welinkUrl, String scorpid, String sprdid, int maxRecNum) {
        WelinkConfig.getInstance().setWelinkName(welinkName);
        WelinkConfig.getInstance().setWelinkPwd(welinkPwd);
        WelinkConfig.getInstance().setWelinkUrl(welinkUrl);
        WelinkConfig.getInstance().setScorpid(scorpid);
        WelinkConfig.getInstance().setSprdid(sprdid);
        WelinkConfig.getInstance().setMaxRecNum(maxRecNum);
    }

    @Override
    public void init() throws Exception {
    }

    @Override
    public void start() throws Exception {
        SmsKit.getInstance().init(SmsChanelEnum.WELINK);
    }

    @Override
    public void stop() throws Exception {
    }
}
