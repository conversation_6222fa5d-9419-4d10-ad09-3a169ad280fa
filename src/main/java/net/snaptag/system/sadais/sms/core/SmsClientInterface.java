package net.snaptag.system.sadais.sms.core;
public interface SmsClientInterface<T> {
    /**
     * 初始化
     */
    public void initClient();

    /**
     * 取短信客户端
     * 
     * @return
     */
    public abstract T getSmsClient();

    /**
     * 发送短信
     * 
     * @param message
     * @return
     */
    public SmsMessage sendSms(SmsMessage message);

    /**
     * 发送前查看内容
     * 
     * @param message
     * @return
     */
    public SmsMessage showSms(SmsMessage message);
}
