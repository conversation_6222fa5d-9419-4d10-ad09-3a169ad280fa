package net.snaptag.system.sadais.sms.client.welink;
/**
 * 微网联通的返回结果
 */
public class WelinkSendResponse {
    private int     code;
    private String  result;
    private boolean success;

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getResult() {
        return result;
    }

    public void setResult(String result) {
        this.result = result;
    }

    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public WelinkSendResponse() {
    }

    public WelinkSendResponse(int code, String result, boolean success) {
        this.code = code;
        this.result = result;
        this.success = success;
    }
}
