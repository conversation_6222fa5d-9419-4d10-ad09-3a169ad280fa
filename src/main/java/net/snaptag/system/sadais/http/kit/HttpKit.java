package net.snaptag.system.sadais.http.kit;

import net.snaptag.system.sadais.http.common.ContentType;
import net.snaptag.system.sadais.http.common.HttpMethod;
import net.snaptag.system.sadais.http.core.HttpClient;
import net.snaptag.system.sadais.http.core.response.HttpResponse;
import net.snaptag.system.sadais.http.utils.HttpUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

/**
 * Created by laotang on 2017/8/14.
 */
public class HttpKit {
    private static final Logger        logger = LoggerFactory.getLogger(HttpKit.class);
    private static Map<String, Object> params;
    private static Map<String, File>   files;
    private static Map<String, String> headers;
    private static String              url    = "";
    private static boolean             security;
    private static byte[]              body;
    private static ContentType contentType;
    private static ContentType         bodyCntentType;

    public static HttpKit http() {
        return new HttpKit();
    }

    private HttpKit() {
        url = "";
        security = false;
        params = new HashMap<String, Object>();
        files = new HashMap<String, File>();
        headers = new HashMap<String, String>();
        body = null;
        contentType = null;
        bodyCntentType = null;
    }

    public HttpKit url(String requestUrl) {
        url = requestUrl;
        return this;
    }

    public HttpKit param(String key, Object value) {
        if (value instanceof File) {
            files.put(key, (File) value);
        } else {
            params.put(key, value);
        }
        return this;
    }

    public HttpKit param(Object paramObj) {
        if (paramObj instanceof Map) {
            Map<String, Object> tmpMap = (Map) paramObj;
            for (Iterator<Map.Entry<String, Object>> it = tmpMap.entrySet().iterator(); it.hasNext();) {
                Map.Entry<String, Object> entry = it.next();
                param(entry.getKey(), entry.getValue());
            }
        } else {
            body(paramObj);
        }
        return this;
    }

    private void body(Object paramObj) {
        if (paramObj instanceof String) {
            String bodyString = paramObj + "";
            bodyCntentType = HttpUtils.getContentType(bodyString);
            body = bodyString.getBytes();
        } else if (paramObj instanceof byte[]) {
            body = (byte[]) paramObj;
            bodyCntentType = ContentType.STREAM;
        }
    }

    public HttpKit header(String key, String value) {
        headers.put(key, value);
        return this;
    }

    public HttpKit header(Map<String, String> headerMap) {
        headers.putAll(headerMap);
        return this;
    }

    public HttpKit security(boolean reqSecurity) {
        security = reqSecurity;
        return this;
    }

    public HttpKit contentType(String reqContentType) {
        contentType = ContentType.parse(reqContentType);
        return this;
    }

    public HttpKit contentType(ContentType reqContentType) {
        contentType = reqContentType;
        return this;
    }

    public HttpResponse get() {
        return request(HttpMethod.GET);
    }

    public HttpResponse options() {
        return request(HttpMethod.OPTIONS);
    }

    public HttpResponse post() {
        return request(HttpMethod.POST);
    }

    /**
     * 最终根据提交的参数值确定ContentType
     *
     * @param method
     *            请求方式
     */
    private void ensureContentType(HttpMethod method) {
        if (HttpUtils.isEmpty(contentType)) {
            // 如果是GET请求, 则设置为null后直接退出
            if (HttpMethod.GET.name().equalsIgnoreCase(method.name())) {
                contentType = null;
                return;
            }
            if (HttpUtils.isNotEmpty(params)) {
                contentType = ContentType.FORM;
            }
            // 如果有文件提交的话，就强制将类型更改为multipart/form-data
            if (HttpUtils.isNotEmpty(files)) {
                contentType = ContentType.MULTIPART;
            }
            if (HttpUtils.isEmpty(params) && HttpUtils.isEmpty(files) && HttpUtils.isNotEmpty(bodyCntentType)) {
                contentType = bodyCntentType;
            }
        }
    }

    public HttpResponse request(HttpMethod method) {
        ensureContentType(method);
        HttpClient client = new HttpClient(method, headers, url, params, files, security, contentType, body);
        return client.send();
    }
}
