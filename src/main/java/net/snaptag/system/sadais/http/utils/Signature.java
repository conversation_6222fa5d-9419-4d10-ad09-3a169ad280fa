package net.snaptag.system.sadais.http.utils;

import net.snaptag.system.sadais.http.common.Consts;
import net.snaptag.system.sadais.http.exception.HttpClientException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.util.Map;

/**
 * Created by laotang on 2017/8/25.
 */
public class Signature {
    private static Logger       logger                  = LoggerFactory.getLogger(Signature.class);
    public final static String  DUANG_SGIN_KEY_FIELD    = "duangkey";
    public final static String  DUANG_SGIN_RESULT_FIELD = "duangsgin";
    private final static String DUANG_SGIN_VALUE_FIELD  = "duangduangduang";

    /**
     * 根据paramMap里的内容值及密钥，生成MD5加密字符串
     * 
     * @param params
     * @return 签名后的字符串
     */
    public static String getSign(Map<String, Object> params) {
        StringBuilder sb = new StringBuilder(createParamString(params));
        InputStream input = HttpUtils.string2InputStream(sb.toString(), Consts.UTF_8_ENCODING);
        if (HttpUtils.isNotEmpty(input)) {
            try {
                String result = Digests.md5toString(input);
                sb.append("&").append(DUANG_SGIN_RESULT_FIELD).append("=").append(result);
                return sb.toString();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return "";
    }

    /**
     * 只返回加密后的字符串
     * 
     * @param paramMap
     * @return 签名后的字符串
     */
    public static String getSignResult(Map<String, Object> paramMap) {
        StringBuilder sb = new StringBuilder(createParamString(paramMap));
        InputStream input = HttpUtils.string2InputStream(sb.toString(), Consts.UTF_8_ENCODING);
        if (HttpUtils.isNotEmpty(input)) {
            try {
                return Digests.md5toString(input);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return "";
    }

    /**
     * 返回加密后的字符串
     * 
     * @param content
     * @return 签名后的字符串
     */
    public static String getSignResult(InputStream content) {
        if (HttpUtils.isNotEmpty(content)) {
            try {
                return Digests.md5toString(content);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return "";
    }

    /**
     * 返回加密后的字符串
     * 
     * @param body
     * @return 签名后的字符串
     */
    public static String getSignResult(byte[] body) {
        if (HttpUtils.isNotEmpty(body)) {
            try {
                return getSignResult(HttpUtils.byte2InputStream(body));
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return "";
    }

    /**
     * 根据参数集合，创建要进行签名的字符串
     * 
     * @param paramMap
     *            参数集合
     * @return 待签名的字符串
     */
    private static String createParamString(Map<String, Object> paramMap) {
        if (HttpUtils.isEmpty(paramMap)) throw new NullPointerException("Signature getSign paramMap is null!");
        StringBuilder sb = new StringBuilder();
        try {
            sb.append(HttpUtils.paramToQueryString(paramMap));
        } catch (UnsupportedEncodingException e) {
            logger.warn(e.getMessage(), e);
        }
        if (sb.length() > 0) {
            sb.append("&").append(DUANG_SGIN_KEY_FIELD).append("=").append(getSignKey());
        }
        return sb.toString();
    }

    /**
     * 根据参数验证签名是否一致
     * 
     * @param paramMap
     *            参数
     * @return 一致返回true
     */
    public static boolean validateSign(Map<String, Object> paramMap) {
        if (HttpUtils.isEmpty(paramMap)) throw new HttpClientException("Signature validateSign paramMap is null!");
        String signKeyByResponse = paramMap.get(DUANG_SGIN_RESULT_FIELD) + "";
        if (HttpUtils.isEmpty(signKeyByResponse)) {
            // 返回的加密字符串为空,表示这个API返回的数据有可能已经被篡改
            logger.warn("response sign data is not exite!");
            return false;
        }
        paramMap.remove(DUANG_SGIN_RESULT_FIELD);
        String signKeyByFuncion = getSign(paramMap);
        if (!signKeyByResponse.equals(signKeyByFuncion)) {
            // 返回的加密字符串与生成的加密字符串不一致，表示这个API返回的数据有可能已经被篡改
            logger.warn("response sign data is not equal!");
            return false;
        }
        return true;
    }

    private static String getSignKey() {
        return DUANG_SGIN_VALUE_FIELD;
    }

    public static boolean validateRequestUserAgent() {
        return false;
    }
}
