package net.snaptag.system.sadais.http.exception;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.io.Writer;

/**
 * 关于异常的工具类.
 */
public class Exceptions {
    /**
     * 将CheckedException转换为UncheckedException.
     */
    public static RuntimeException unchecked(Exception e) {
        if (e instanceof RuntimeException) {
            return (RuntimeException) e;
        } else {
            return new RuntimeException(e);
        }
    }

    /**
     * 将ErrorStack转化为String.
     */
    public static String getStackTraceAsString(Exception e) {
        StringWriter stringWriter = new StringWriter();
        e.printStackTrace(new PrintWriter(stringWriter));
        String resultString = stringWriter.toString();
        return (resultString.endsWith("\n")) ? resultString.substring(0, resultString.length() - 2) : resultString;
    }

    public static String getStackTraceAsString(Throwable aThrowable) {
        Writer result = new StringWriter();
        aThrowable.printStackTrace(new PrintWriter(result));
        String resultString = result.toString();
        return (resultString.endsWith("\n")) ? resultString.substring(0, resultString.length() - 2) : resultString;
    }

    /**
     * 判断异常是否由某些底层的异常引起.
     */
    public static boolean isCausedBy(Exception ex, Class<? extends Exception>... causeExceptionClasses) {
        Throwable cause = ex.getCause();
        while (cause != null) {
            for (Class<? extends Exception> causeClass : causeExceptionClasses) {
                if (causeClass.isInstance(cause)) {
                    return true;
                }
            }
            cause = cause.getCause();
        }
        return false;
    }
}
