package net.snaptag.system.sadais.http.common;
/**
 * 常量类 Created by laotang on 2017/8/14.
 */
public class Consts {
    // 编码格式
    public static final String UTF_8_ENCODING  = "UTF-8";
    // 连接超时，默认为10000毫秒
    public static final long   CONNECT_TIMEOUT = 10000;
    // 读超时，默认为10000毫秒
    public static final long   READ_TIMEOUT    = 10000;
    // 写超时，默认为10000毫秒
    public static final long   WRITE_TIMEOUT   = 10000;
}
