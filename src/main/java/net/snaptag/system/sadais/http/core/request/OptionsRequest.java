package net.snaptag.system.sadais.http.core.request;
import net.snaptag.system.sadais.http.common.HttpMethod;
import net.snaptag.system.sadais.http.core.entity.RequestEntity;
import okhttp3.HttpUrl;
import okhttp3.Request;

/**
 * Created by laotang on 2017/8/14.
 */
public class OptionsRequest extends AbsRequest {
    public OptionsRequest(RequestEntity requestEntity) {
        super(requestEntity);
    }

    @Override
    public Request buildRequest() {
        Request.Builder builder = new Request.Builder();
        HttpUrl httpUrl = HttpUrl.parse(buildUrl());
        builder.url(httpUrl);
        this.putHeaders2Builder(builder, httpUrl); // 设置header
        return builder.method(HttpMethod.OPTIONS.name(), null).build();
    }
}
