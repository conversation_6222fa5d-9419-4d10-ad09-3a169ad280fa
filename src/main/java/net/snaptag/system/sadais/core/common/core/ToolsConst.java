package net.snaptag.system.sadais.core.common.core;
import java.net.InetAddress;
import java.net.NetworkInterface;
import java.net.SocketException;
import java.net.UnknownHostException;
import java.util.Enumeration;

public class ToolsConst {
    /**
     * 正式API地址
     */
    public static final String ONLINE_API_HOST = "https://api.sadais.com";
    /**
     * 正式H5地址
     */
    public static final String ONLINE_H5_HOST  = "https://m.sadais.com";

    public static boolean pingIp(String ipStr) {
        try {
            long start = System.currentTimeMillis();
            final int timeOutMilliseconds = 1500;
            InetAddress inetAddress = InetAddress.getByName(ipStr);
            System.out.println("#########Sending Ping Request to " + ipStr);
            if (inetAddress.isReachable(timeOutMilliseconds)) {
                System.out.println("######### ping " + ipStr + " is reachable. time =" + (System.currentTimeMillis() - start));
                return true;
            } else {
                System.out.println("######### ping " + ipStr + " NOT reachable.");
            }
        } catch (Exception e) {
            System.out.println("Exception:" + e.getMessage());
        }
        return false;
    }

    /**
     * 获取本机网卡IP地址，这个地址为所有网卡中非回路地址的第一个<br>
     * 如果获取失败调用 {@link InetAddress#getLocalHost()}方法获取。<br>
     * 此方法不会抛出异常，获取失败将返回<code>null</code><br>
     * <p>
     * 参考：http://stackoverflow.com/questions/9481865/getting-the-ip-address-of-the-current-machine-using-java
     *
     * @return 本机网卡IP地址，获取失败返回<code>null</code>
     * @since 3.0.1
     */
    public static InetAddress getLocalhost() {
        InetAddress candidateAddress = null;
        NetworkInterface iface;
        InetAddress inetAddr;
        try {
            for (Enumeration<NetworkInterface> ifaces = NetworkInterface.getNetworkInterfaces(); ifaces.hasMoreElements();) {
                iface = ifaces.nextElement();
                if (iface.getDisplayName().contains("VMware") || iface.getDisplayName().contains("VirtualBox")) {
                    continue;
                }
                for (Enumeration<InetAddress> inetAddrs = iface.getInetAddresses(); inetAddrs.hasMoreElements();) {
                    inetAddr = inetAddrs.nextElement();
                    if (false == inetAddr.isLoopbackAddress()) {
                        if (inetAddr.isSiteLocalAddress()) {
                            return inetAddr;
                        } else if (null == candidateAddress) {
                            // 非site-local地址做为候选地址返回
                            candidateAddress = inetAddr;
                        }
                    }
                }
            }
        } catch (SocketException e) {
            // ignore socket exception, and return null;
        }
        if (null == candidateAddress) {
            try {
                candidateAddress = InetAddress.getLocalHost();
            } catch (UnknownHostException e) {
                // ignore
            }
        }
        return candidateAddress;
    }

    public static String getLocalhostStr() {
        InetAddress localhost = getLocalhost();
        if (null != localhost) {
            return localhost.getHostAddress();
        }
        return null;
    }
    /**
     * 微信登陆
     */
    public static final String LOGIN_BY_WEIXIN       = "byweixin";
    /**
     * 新浪登陆
     */
    public static final String LOGIN_BY_SINA         = "bysina";
    /**
     * qq登陆
     */
    public static final String LOGIN_BY_QQ           = "byqq";
    /**
     * 手机登陆
     */
    public static final String LOGIN_BY_MOBILE       = "bymobile";
    /**
     * facebook登陆
     */
    public static final String LOGIN_BY_FACEBOOK     = "byfacebook";
    /**
     * twitter登陆
     */
    public static final String LOGIN_BY_TWITTER      = "bytwitter";
    /**
     * whatsapp登陆
     */
    public static final String LOGIN_BY_WHATSAPP      = "bywhatsapp";
    /**
     * google 登陆
     */
    public static final String LOGIN_BY_GOOGLE      = "bygoogle";
    /**
     * AppleID登陆
     */
    public static final String LOGIN_BY_APPLEID       = "byappleid";
    public static final String LOGIN_BY_APPLEID2       = "byappleid2";
    /**
     * 友盟-本机号码一键登录
     */
    public static final String LOGIN_BY_YM_ONEKEY    = "byymonekey";
    /**
     * 一天的时间秒数
     */
    public static final int    DAY_SECOND            = 86400;
    /**
     * 一周的时间秒数
     */
    public static final int    WEEK_SECOND           = 86400 * 7;
    /**
     * 一个月的时间秒数
     */
    public static final int    MONTH_SECOND          = 86400 * 30;
    /**
     * 5分钟的时间秒数
     */
    public static final int    FIVE_MINUTES          = 5 * 60;
    /**
     * 15分钟的时间秒数
     */
    public static final int    FIFTEEN_MINUTES       = 15 * 60;
    /**
     * 30分钟的时间秒数
     */
    public static final int    THIRTY_MINUTES        = 30 * 60;
    /**
     * 手机端列表默认的行数
     */
    public static Integer      PHONEPAGESIZE         = 20;
    /**
     * 允许查询记录的状态
     */
    public static String       DATA_SUCCESS_STATUS   = "审核通过";
    /**
     * 删除记录的状态
     */
    public static String       DATA_DELETE_STATUS    = "已删除";
    /**
     * 待审核记录的状态
     */
    public static String       DATA_PENDING_STATUS   = "待审核";
    /**
     * 已屏蔽记录的状态
     */
    public static String       DATA_SHIELD_STATUS    = "已屏蔽";
    /**
     * 数据库排序--asc
     */
    public static String       MONGODB_SORT_ASC      = "asc";
    /**
     * 数据库排序--desc
     */
    public static String       MONGODB_SORT_DESC     = "desc";
    /**
     * 是否启用状态--启用
     */
    public static final String STATUS_Y              = "Y";
    /**
     * 是否启用状态--禁用
     */
    public static final String STATUS_N              = "N";
    /**
     * 是否状态--否
     */
    public static final byte   STATUS_0              = 0;
    /**
     * 是否状态--是
     */
    public static final byte   STATUS_1              = 1;
    /**
     * 状态为2
     */
    public static final byte   STATUS_2              = 2;
    /**
     * 系统默认编码格式(UTF-8)
     */
    public static final String DEFAULT_ENCODING      = "UTF-8";
    /**
     * 系统版本--ios
     */
    public static final String DEVICE_SYSTEM_IOS     = "IOS";
    /**
     * 系统版本--android
     */
    public static final String DEVICE_SYSTEM_ANDROID = "ANDROID";
    /**
     * 系统版本--H5
     */
    public static final String DEVICE_SYSTEM_H5      = "H5";
    /**
     * 系统版本--小程序
     */
    public static final String DEVICE_SYSTEM_MA      = "MA";
    /**
     * 系统版本--支撑平台
     */
    public static final String DEVICE_SYSTEM_WEB     = "WEB";
    /**
     * H5网页--站内
     */
    public static final String H5_IS                 = "IS";
    /**
     * H5网页--站外商城那种，需要跟用户发生关系的
     */
    public static final String H5_OS                 = "OS";
    /**
     * H5网页--站外分享、推广那种，不需要跟用户发生关系的
     */
    public static final String H5_NOS                = "NOS";
    /**
     * 默认生日
     */
    public static final String DEFAULT_USER_BIRTHDAY = "1990-01-01";
    /**
     * 默认高度
     */
    public static final int    DEFAULT_USER_HEIGHT   = 165;
    /**
     * 默认重量
     */
    public static final double DEFAULT_USER_WEIGHT   = 58.0;
    /**
     * 性别-男
     */
    public static final String DEFAULT_M_SEX         = "男";
    /**
     * 性别-女
     */
    public static final String DEFAULT_W_SEX         = "女";
    /**
     * 成功
     */
    public static final String SUCCESS               = "SUCCESS";
    /**
     * 失败
     */
    public static final String FAIL                  = "FAIL";
    /**
     * 系统默认userid
     */
    public static final String SYSTEM_USER_ID        = "57bead16c6396d11707ee174";
    /**
     * 开始时间时分秒
     */
    public static final String START_TIME            = " 00:00:00";
    /**
     * 结束时间时分秒
     */
    public static final String END_TIME              = " 23:59:59";
    /**
     * 默认应用版本
     */
    public static final String DEFAULT_APP_VERSION   = "1.0.0";
    /**
     * 默认渠道
     */
    public static final String DEFAULT_CHANNEL       = "oppo";
    /**
     * 快递公司logo地址
     */
    public static final String EX_LOGO_PATH          = ONLINE_H5_HOST + "/common/logistics/logo/";
    /**
     * 请求方式-GET
     */
    public static final String REQUEST_MODE_GET      = "GET";
    /**
     * 请求方式-POST
     */
    public static final String REQUEST_MODE_POST     = "POST";
    /**
     * 中国顶级区域ID
     */
    public static final String CHINA_AREA_ID         = "5aa8ddea9c29e627a8e1e9a0";
    /**
     * comma ,
     */
    public static final String COMMA                 = ",";
    /**
     * COLON :
     */
    public static final String COLON                 = ":";
    /**
     * SINGLE_SLASH /
     */
    public static final String SINGLE_SLASH          = "/";
    /**
     * DOUBLE_SLASH //
     */
    public static final String DOUBLE_SLASH          = "//";
    /**
     * SEMICOLON ;
     */
    public static final String SEMICOLON             = ";";
    /**
     * EQUAL SIGN
     */
    public static final String EQUAL_SIGN            = "=";
    /**
     * underline "_"
     */
    public static final String UNDERLINE             = "_";
}
