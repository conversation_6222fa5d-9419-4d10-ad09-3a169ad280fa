package net.snaptag.system.sadais.core.common.enums;

public enum ExceptionEnums implements IEnums {

    SUCCESS(0, "成功"),
    ERROR(1, "错误"),
    TOKEN_EXPIRE(2, "会话过期,请重新登录"),        // 手机TOKENID过期
    TOKEN_IS_EMPTY(3, "token令牌不能为空"),
    NET_EXCEPTION(4, "网络异常，请稍后重试"),
    INVALID_ACCESS_TOKENID(5, "AccessTokenId不存在"),
    RESULT_NOT_FIND(6, "抱歉!没有查到符合条件的数据"),
    INVALID_SERVER_ERROR(7, "服务器内部错误"),
    IMAGE_DETECT_FAILED(8, "图片格式可能不支持"),
    TRANSFORMEXCEPTION(9, "转换异常"),
    MATURITY(10, "使用期限到期"),
    REQUEST_TIMEOUT(11, "请求超时"),
    SIGNATURE_DOES_NOTMATCH(12, "签名错误"),
    PARAM_ERROR(13, "传递的参数有误!"),
    MSG_MAX_ERROR(14,"超出每日接收短信上限"),
    SMS_RETRY_COUNT_MUCH(15,"短信获取重启次数太多，弹出验证码"),
    SMS_MUCH_TIME_OUT(16,"图片验证码超时或已过期"),
    RECORD_IS_NOT_FOUND(17,"该记录不存在或已被删除"),
    SERVER_BUSY(20, "服务器正忙，请稍后再试"),
    API_REQUEST_LIMIT_REACHED(21, "接口调用次数已达到设定的上限"),
    UNAUTHORIZED_CLIENT_IP(22, "请求来自未经授权的IP地址"),
    TOO_MANY_PARAM(23, "请求参数过多"),
    ACCOUNT_EXPIRED(24, "账户已失效"),
    COOKIE_EXPIRED(25, "cookie已失效"),
    ACCESS_DENIED(403, "拒绝访问"),
    DATABASE_ERROR(550, "数据库错误"),
    ORDER_TIME_OUT(10003, "订单已自动取消!"),
    DTO_ERROR(10004, "dto生成失败!"),
    GET_USER_FAIL(10005, "查询用户失败!"),
    CREATE_ORDER_TIME(10006, "你手太快了!"),
    BUY_ERROR_RETURN(10008, "购买时出错，返回上页!"),
    NO_ACCOUNT_EXIST(2005,"账号不存在!"),
    PAY_CALLBACK_SUCCESS(30001, "支付回调成功");


    private final int code;
    private final String message;

    /**
     * Constructor.
     */
    private ExceptionEnums(int code, String message) {
        this.code = code;
        this.message = message;
    }

    /**
     * Get the value.
     *
     * @return the value
     */
    public int getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }
}
