package net.snaptag.system.sadais.core.common.utils;

import org.apache.commons.codec.binary.Base64;
//import org.bouncycastle.jce.provider.BouncyCastleProvider;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.security.*;

public class AESUtil {
    /**
     * AES解密
     * 
     * @param content
     *            密文
     * @return
     * @throws InvalidAlgorithmParameterException
     * @throws NoSuchProviderException
     */
    public String decrypt(String encryptedData, String sessionKey, String iv) {
        try {
            // 被加密的数据
            byte[] dataByte = Base64.decodeBase64(encryptedData.getBytes());
            // 加密秘钥
            byte[] keyByte = Base64.decodeBase64(sessionKey.getBytes());
            // 偏移量
            byte[] ivByte = Base64.decodeBase64(iv.getBytes());
            Cipher cipher = Cipher.getInstance("AES/CBC/PKCS7Padding");
            Key sKeySpec = new SecretKeySpec(keyByte, "AES");
            cipher.init(Cipher.DECRYPT_MODE, sKeySpec, generateIV(ivByte));// 初始化
            byte[] resultByte = cipher.doFinal(dataByte);
            String result = null;
            if (null != resultByte && resultByte.length > 0) {
                result = new String(resultByte, "UTF-8");
            }
            return result;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }
    static {
//        Security.addProvider(new BouncyCastleProvider());
    }

    // 生成iv
    public static AlgorithmParameters generateIV(byte[] iv) throws Exception {
        AlgorithmParameters params = AlgorithmParameters.getInstance("AES");
        params.init(new IvParameterSpec(iv));
        return params;
    }
}
