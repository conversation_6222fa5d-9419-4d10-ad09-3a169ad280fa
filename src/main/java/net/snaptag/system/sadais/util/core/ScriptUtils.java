package net.snaptag.system.sadais.util.core;
import javax.script.ScriptEngine;
import javax.script.ScriptEngineManager;
import javax.script.ScriptException;

/**
 * Created by brook on 2017/9/6.
 *
 * <AUTHOR>
 */
class ScriptUtils {
    public static final ScriptEngineManager SCRIPT_ENGINE_MANAGER = new ScriptEngineManager();
    public static final ScriptEngine        SCRIPT_ENGINE         = SCRIPT_ENGINE_MANAGER.getEngineByName("JavaScript");

    public static boolean scriptEval(String expression) throws ScriptException {
        return "true".equals(SCRIPT_ENGINE.eval(expression).toString());
    }
}
