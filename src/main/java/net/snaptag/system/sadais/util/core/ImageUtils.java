package net.snaptag.system.sadais.util.core;

// import cn.hutool.extra.img.ImgUtil; // 暂时注释掉，因为新版本hutool中路径可能不同

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.color.ColorSpace;
import java.awt.image.BufferedImage;
import java.awt.image.ColorConvertOp;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;

/**
 * Created by brook on 2017/7/10.
 *
 * <AUTHOR>
 */
class ImageUtils {
    // 全流程
    public static void main(String[] args) throws IOException {
        int[] a = getImagePixels("C:\\Users\\<USER>\\Desktop\\u=2014697271,373782421&fm=26&gp=0.jpg");
        int[] b = getImagePixels("C:\\Users\\<USER>\\Desktop\\u=711390311,1281309084&fm=26&gp=0.jpg");
        // int[] b = getImagePixels("C:\\\\Users\\\\<USER>\\\\Desktop\\IsvPluginIcon1.26.399.png");
        // 获取两个图的汉明距离（假设另一个图也已经按上面步骤得到灰度比较数组）
        int hammingDistance = getHammingDistance(a, b);
        // 通过汉明距离计算相似度，取值范围 [0.0, 1.0]
        double similarity = calSimilarity(hammingDistance);
        System.out.println(similarity);
    }

    /**
     * 获取图片宽高
     * 
     * @param url
     * @return
     * @throws IOException
     */
    public static int[] getImageInfo(String url) throws IOException {
        try {
            InputStream murl = new URL(url).openStream();
            BufferedImage sourceImg = ImageIO.read(murl);
            int[] img = new int[] { sourceImg.getWidth(), sourceImg.getHeight()};
            murl.close();
            return img;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return new int[] { 0, 0};
    }

    public static int[] getImagePixels(String imagePath) throws IOException {
        // 获取图像
        File imageFile = new File(imagePath);
        Image image = ImageIO.read(imageFile);
        // 转换至灰度
        image = toGrayscale(image);
        // 缩小成32x32的缩略图
        image = scale(image);
        // 获取灰度像素数组
        int[] pixels = getPixels(image);
        // 获取平均灰度颜色
        int averageColor = getAverageOfPixelArray(pixels);
        // 获取灰度像素的比较数组（即图像指纹序列）
        return getPixelDeviateWeightsArray(pixels, averageColor);
    }

    /**
     * 将任意Image类型图像转换为BufferedImage类型，方便后续操作
     * 
     * @param srcImage
     * @return
     */
    public static BufferedImage convertToBufferedFrom(Image srcImage) {
        BufferedImage bufferedImage = new BufferedImage(srcImage.getWidth(null), srcImage.getHeight(null), BufferedImage.TYPE_INT_ARGB);
        Graphics2D g = bufferedImage.createGraphics();
        g.drawImage(srcImage, null, null);
        g.dispose();
        return bufferedImage;
    }

    /**
     * 转换至灰度图
     * 
     * @param image
     * @return
     */
    public static BufferedImage toGrayscale(Image image) {
        BufferedImage sourceBuffered = convertToBufferedFrom(image);
        ColorSpace cs = ColorSpace.getInstance(ColorSpace.CS_GRAY);
        ColorConvertOp op = new ColorConvertOp(cs, null);
        return op.filter(sourceBuffered, null);
    }

    /**
     * 缩放至32x32像素缩略图
     * 
     * @param image
     * @return
     */
    public static Image scale(Image image) {
        return image.getScaledInstance(32, 32, Image.SCALE_SMOOTH);
    }

    /**
     * 获取像素数组
     * 
     * @param image
     * @return
     */
    public static int[] getPixels(Image image) {
        int width = image.getWidth(null);
        int height = image.getHeight(null);
        return convertToBufferedFrom(image).getRGB(0, 0, width, height, null, 0, width);
    }

    /**
     * 获取灰度图的平均像素颜色值
     * 
     * @param pixels
     * @return
     */
    public static int getAverageOfPixelArray(int[] pixels) {
        long sumRed = 0;
        for (int i = 0; i < pixels.length; i++) {
            Color color = new Color(pixels[i], true);
            sumRed += color.getRed();
        }
        return (int) (sumRed / pixels.length);
    }

    /**
     * 获取灰度图的像素比较数组（平均值的离差）
     * 
     * @param pixels
     * @param averageColor
     * @return
     */
    public static int[] getPixelDeviateWeightsArray(int[] pixels, final int averageColor) {
        int[] dest = new int[pixels.length];
        for (int i = 0; i < pixels.length; i++) {
            Color color = new Color(pixels[i], true);
            dest[i] = color.getRed() - averageColor > 0 ? 1 : 0;
        }
        return dest;
    }

    /**
     * 获取两个缩略图的平均像素比较数组的汉明距离（距离越大差异越大）
     * 
     * @param a
     * @param b
     * @return
     */
    public static int getHammingDistance(int[] a, int[] b) {
        int sum = 0;
        for (int i = 0; i < a.length; i++) {
            sum += a[i] == b[i] ? 0 : 1;
        }
        return sum;
    }

    /**
     * 通过汉明距离计算相似度
     * 
     * @param hammingDistance
     * @return
     */
    public static double calSimilarity(int hammingDistance) {
        int length = 32 * 32;
        double similarity = (length - hammingDistance) / (double) length;
        // 使用指数曲线调整相似度结果
        similarity = Math.pow(similarity, 2);
        return similarity;
    }

    /**
     * 从输入流中获取字节数组
     * 
     * @param inputStream
     * @return
     * @throws IOException
     */
    public static byte[] readInputStream(InputStream inputStream) throws IOException {
        byte[] buffer = new byte[1024];
        int len = 0;
        ByteArrayOutputStream bos = new ByteArrayOutputStream();
        while ((len = inputStream.read(buffer)) != -1) {
            bos.write(buffer, 0, len);
        }
        bos.close();
        return bos.toByteArray();
    }

    /**
     * 根据图片地址获取字节数组
     * 
     * @param url
     *            图片地址
     * @return
     * @throws IOException
     */
    public static byte[] getByteImage(String url) throws IOException {
        InputStream inputStream = new URL(url).openStream();
        // // 获取自己数组
        byte[] imgData = readInputStream(inputStream);
        inputStream.close();
        return imgData;
    }
}
