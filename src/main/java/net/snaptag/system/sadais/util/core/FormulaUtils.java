package net.snaptag.system.sadais.util.core;
/**
 * Created by brook on 2017/7/11. 常用公式
 *
 * <AUTHOR>
 */
class FormulaUtils {
    /**
     * 计算BMI值 BMI = 体重(千克) / 身高*2(米*2)
     *
     * @param height
     *            身高 cm
     * @param weight
     *            体重 kg
     */
    public static double getBMI(int height, double weight) {
        return ToolsKit.Number.round(weight / height / height * 10000, 2).doubleValue();
    }

    /**
     * 获取最佳的HashMap初始化大小
     *
     * @param size
     *            大小
     * @return 最佳大小
     */
    public static int getHashMapInitialCapacity(int size) {
        return Math.max((int) (size / 0.75F) + 1, 16);
    }
}
