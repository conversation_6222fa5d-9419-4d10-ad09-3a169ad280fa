package net.snaptag.system.sadais.util.core;

import cn.hutool.core.util.StrUtil;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Created by brook on 2017/7/3.
 *
 * <AUTHOR>
 */
class StringUtils extends StrUtil {
    /**
     * 判断是否为ObjectId 简单判断
     *
     * @param objectId
     *            objectId
     * @return 布尔型
     */
    public static boolean isObjectId(String objectId) {
        return ToolsKit.isNotEmpty(objectId) && objectId.length() == 24;
    }

    /**
     * 判断是否在微信环境
     *
     * @param ua
     *            user-agent
     */
    public static boolean isWeChat(String ua) {
        return ToolsKit.isNotEmpty(ua) && (ua.contains("MicroMessenger") || ua.contains("wechat"));
    }

    /**
     * 验证电话号码
     */
    public static boolean isMobilePhoneNumber(String phone) {
        return Pattern.matches("^1+(2|3|4|5|6|7|8|9)\\d{9}$", phone);
    }

    /**
     * @param params
     *            拼接元素
     */
    public static String joinByComma(Object... params) {
        return StrUtil.join(COMMA, params);
    }

    /**
     * @param params
     *            拼接元素
     */
    public static String joinByComma(List<String> params) {
        if (params == null) {
            return null;
        }
        return StrUtil.join(COMMA, params.toArray());
    }

    /**
     * @param params
     *            拼接元素
     */
    public static String joinByComma(Set<String> params) {
        if (params == null) {
            return null;
        }
        return StrUtil.join(COMMA, params.toArray());
    }

    /**
     * @param str
     *            分隔
     */
    public static String[] splitByComma(String str) {
        List<String> list = StrUtil.split(str, COMMA);
        return list.toArray(new String[0]);
    }

    /**
     * 应用版本比较 "6.1.0", "6.1.0" = 0 "6.0.0", "6.1.0" = -1 "6.1.0", "6.0.0" = 1
     */
    public static int appVersionCompare(String current, String version) {
        return current.compareTo(version);
    }

    /**
     * 类似js math方法
     *
     * @param regex
     *            正则
     * @param input
     *            字符串
     */
    public static List<String> match(String regex, String input) {
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(input);
        List<String> list = new ArrayList<>();
        while (matcher.find()) {
            list.add(matcher.group());
        }
        return list;
    }

    /**
     * 类似js math方法 返回匹配的第一个
     *
     * @param regex
     *            正则
     * @param input
     *            字符串
     */
    public static String matchFirst(String regex, String input) {
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(input);
        while (matcher.find()) {
            return matcher.group();
        }
        return ToolsKit.String.EMPTY;
    }

    // Defaults
    // -----------------------------------------------------------------------
    /**
     * <p>
     * Returns either the passed in String, or if the String is {@code null}, an empty String ("").
     * </p>
     * <p>
     * 
     * <pre>
     * StringUtils.defaultString(null)  = ""
     * StringUtils.defaultString("")    = ""
     * StringUtils.defaultString("bat") = "bat"
     * </pre>
     *
     * @param str
     *            the String to check, may be null
     * @return the passed in String, or the empty String if it was {@code null}
     * @see String#valueOf(Object)
     */
    public static String defaultString(final String str) {
        return str == null ? EMPTY : str;
    }

    /**
     * <p>
     * Returns either the passed in String, or if the String is {@code null}, the value of {@code
     * defaultStr}.
     * </p>
     * <p>
     * 
     * <pre>
     * StringUtils.defaultString(null, "NULL")  = "NULL"
     * StringUtils.defaultString("", "NULL")    = ""
     * StringUtils.defaultString("bat", "NULL") = "bat"
     * </pre>
     *
     * @param str
     *            the String to check, may be null
     * @param defaultStr
     *            the default String to return if the input is {@code null}, may be null
     * @return the passed in String, or the default if it was {@code null}
     * @see String#valueOf(Object)
     */
    public static String defaultString(final String str, final String defaultStr) {
        return str == null ? defaultStr : str;
    }

    /**
     * <p>
     * Returns either the passed in CharSequence, or if the CharSequence is whitespace, empty ("") or {@code null}, the value of {@code defaultStr}.
     * </p>
     * <p>
     * 
     * <pre>
     * StringUtils.defaultIfBlank(null, "NULL")  = "NULL"
     * StringUtils.defaultIfBlank("", "NULL")    = "NULL"
     * StringUtils.defaultIfBlank(" ", "NULL")   = "NULL"
     * StringUtils.defaultIfBlank("bat", "NULL") = "bat"
     * StringUtils.defaultIfBlank("", null)      = null
     * </pre>
     *
     * @param <T>
     *            the specific kind of CharSequence
     * @param str
     *            the CharSequence to check, may be null
     * @param defaultStr
     *            the default CharSequence to return if the input is whitespace, empty ("") or {@code null}, may be null
     * @return the passed in CharSequence, or the default
     * @see StringUtils#defaultString(String, String)
     */
    public static <T extends CharSequence> T defaultIfBlank(final T str, final T defaultStr) {
        return isBlank(str) ? defaultStr : str;
    }

    /**
     * <p>
     * Returns either the passed in CharSequence, or if the CharSequence is empty or {@code null}, the value of {@code defaultStr}.
     * </p>
     * <p>
     * 
     * <pre>
     * StringUtils.defaultIfEmpty(null, "NULL")  = "NULL"
     * StringUtils.defaultIfEmpty("", "NULL")    = "NULL"
     * StringUtils.defaultIfEmpty(" ", "NULL")   = " "
     * StringUtils.defaultIfEmpty("bat", "NULL") = "bat"
     * StringUtils.defaultIfEmpty("", null)      = null
     * </pre>
     *
     * @param <T>
     *            the specific kind of CharSequence
     * @param str
     *            the CharSequence to check, may be null
     * @param defaultStr
     *            the default CharSequence to return if the input is empty ("") or {@code
     *                   null}, may be null
     * @return the passed in CharSequence, or the default
     * @see StringUtils#defaultString(String, String)
     */
    public static <T extends CharSequence> T defaultIfEmpty(final T str, final T defaultStr) {
        return isEmpty(str) ? defaultStr : str;
    }

    /**
     * 首字母转小写
     * 
     * @param text
     * @return
     */
    public static String toLowerCaseFirstOne(String text) {
        if (Character.isLowerCase(text.charAt(0)))
            return text;
        else
            return (new StringBuilder()).append(Character.toLowerCase(text.charAt(0))).append(text.substring(1)).toString();
    }

    /**
     * 首字母转大写
     * 
     * @param text
     * @return
     */
    public static String toUpperCaseFirstOne(String text) {
        if (Character.isUpperCase(text.charAt(0)))
            return text;
        else
            return (new StringBuilder()).append(Character.toUpperCase(text.charAt(0))).append(text.substring(1)).toString();
    }

    public static String unicodeToCn(String unicode) {
        /** 以 \ u 分割，因为java注释也能识别unicode，因此中间加了一个空格 */
        String[] strs = unicode.split("\\\\u");
        String returnStr = "";
        // 由于unicode字符串以 \ u 开头，因此分割出的第一个字符是""。
        for (int i = 1; i < strs.length; i++) {
            returnStr += (char) Integer.valueOf(strs[i], 16).intValue();
        }
        return returnStr;
    }

    public static String cnToUnicode(String cn) {
        char[] chars = cn.toCharArray();
        String returnStr = "";
        for (int i = 0; i < chars.length; i++) {
            returnStr += "\\u" + Integer.toString(chars[i], 16);
        }
        return returnStr;
    }

    /**
     * 获取替换后的字符串
     * 
     * @param value
     * @return
     */
    public static String getReplaceStr(String value) {
        if (ToolsKit.isNotEmpty(value)) {
            if (value.length() == 11 || value.contains(".com")) {
                return value.replaceAll("(?<=.{3}).(?=.{4})", "*");
            } else if (value.length() == 2) {
                return value.replaceAll("(?<=.{1}).(?=.{0})", "*");
            } else {
                return value.replaceAll("(?<=.{1}).(?=.{1})", "*");
            }
        }
        return "";
    }
}
