package net.snaptag.system.sadais.util.core;

import cn.hutool.core.util.URLUtil;
import net.snaptag.system.sadais.util.core.ToolsKit.Charset;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * Created by brook on 2017/7/10.
 * <p>
 * URL 处理工具类
 *
 * <AUTHOR>
 */
class URLUtils extends URLUtil {
    /**
     * 获取微信小程序H5跳转地址
     *
     * @param h5Url
     *            H5地址
     * @return
     */
    public static String getWeChatAppH5Url(String h5Url) {
        if (ToolsKit.isEmpty(h5Url)) {
            return ToolsKit.String.EMPTY;
        }
        return addParameter("/components/webview/index", "_url", h5Url);
    }

    /**
     * 处理图片提交结合，过滤无效url地址
     *
     * @param urls
     *            url集合
     * @return 处理完的新集合
     */
    public static List<String> replaceAndTrimUrls(String domain, List<String> urls) {
        List<String> newUrls = new ArrayList<>();
        if (ToolsKit.isNotEmpty(urls)) {
            for (String url : urls) {
                if (ToolsKit.isNotEmpty(url)) {
                    String newUrl = replaceUrl(domain, url);
                    if (ToolsKit.isNotEmpty(newUrl)) {
                        newUrls.add(newUrl);
                    }
                }
            }
        }
        return newUrls;
    }

    /**
     * 获取图片地址
     *
     * @param serverHost
     *            服务器地址
     * @param url
     *            原始图片url
     * @param suffix
     *            后缀
     */
    public static String getUrlByServer(String serverHost, String url, String suffix) {
        if (ToolsKit.isNotEmpty(url)) {
            if (isUrl(url)) {
                return url;
            } else {
                if (!url.startsWith("/")) {
                    url = "/" + url;
                }
                if (ToolsKit.isEmpty(suffix)) {
                    return serverHost + url;
                } else {
                    return serverHost + url + suffix;
                }
            }
        }
        return StringUtils.EMPTY;
    }

    /**
     * 获取图片地址
     *
     * @param serverHost
     *            服务器地址
     * @param url
     *            原始图片url
     * @return 图片地址
     */
    public static String getUrlByServer(String serverHost, String url) {
        return getUrlByServer(serverHost, url, null);
    }

    /**
     * 批量获取图片地址
     *
     * @param serverHost
     *            服务器地址
     * @param urls
     *            图片相对路径集合
     */
    public static List<String> getUrlsByServer(String serverHost, Collection<String> urls) {
        List<String> resultUrls = new ArrayList<String>();
        if (ToolsKit.isNotEmpty(urls)) {
            for (String url : urls) {
                resultUrls.add(getUrlByServer(serverHost, url));
            }
        }
        return resultUrls;
    }

    /**
     * 替换去除自己的域名
     *
     * @param url
     *            url地址
     * @return 处理后的url
     */
    public static String replaceUrl(String domain, String url) {
        if (ToolsKit.isNotEmpty(url)) {
            // return url.replaceFirst("^(http|https)\\:\\/\\/.+\\." + domain + "\\.com", StringUtils.EMPTY);
            String tmp = StringUtils.EMPTY;
            if (domain.startsWith("https://")) {
                tmp = domain.replace("https://", "http://");
            } else {
                tmp = domain.replace("http://", "https://");
            }
            try {
                url = URLDecoder.decode(url, "UTF-8");
            } catch (UnsupportedEncodingException e) {
                throw new RuntimeException(e);
            }
            return url.replace(domain, StringUtils.EMPTY).replace(tmp, StringUtils.EMPTY);
        }
        return StringUtils.EMPTY;
    }

    /**
     * url增加参数
     *
     * @param url
     *            url地址
     * @param name
     *            参数key
     * @param value
     *            参数值
     */
    public static String addParameter(String url, String name, String value) {
        if (ToolsKit.isNotEmpty(url)) {
            String encodeValue = ToolsKit.String.nullToEmpty(value);
            if (ToolsKit.isNotEmpty(encodeValue)) {
                encodeValue = encode(encodeValue, java.nio.charset.StandardCharsets.UTF_8);
            }
            if (url.contains("?")) {
                return url + "&" + name + "=" + encodeValue;
            } else {
                return url + "?" + name + "=" + encodeValue;
            }
        }
        return url;
    }

    /**
     * url增加参数
     *
     * @param url
     *            地址
     * @param params
     *            多个参数
     */
    public static String addParameters(String url, Map<String, Object> params) {
        if (ToolsKit.isNotEmpty(url) && ToolsKit.isNotEmpty(params)) {
            for (Map.Entry<String, Object> entry : params.entrySet()) {
                url = addParameter(url, entry.getKey(), entry.getValue().toString());
            }
        }
        return url;
    }

    /**
     * h5页面根据环境自动拼接服务器地址
     *
     * @param url
     *            url地址
     * @param envDomainPrefix
     *            url前缀
     */
    @Deprecated
    public static String getEnvUrl(String url, String envDomainPrefix) {
        final String parameterName = "apiHost";
        if (ToolsKit.hasEmpty(url, envDomainPrefix)) {
            return url;
        }
        return addParameter(url, parameterName, envDomainPrefix);
    }

    /**
     * 判断地址是否为绝对路径url地址
     *
     * @param url
     *            url地址
     * @return boolean
     */
    public static boolean isUrl(String url) {
        if (ToolsKit.isNotEmpty(url) && (url.startsWith("http://") || url.startsWith("https://") || url.startsWith("fitness://"))) {
            return true;
        } else {
            return false;
        }
    }
}
