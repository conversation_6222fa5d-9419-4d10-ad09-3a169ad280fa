package net.snaptag.system.sadais.util.core;

import cn.hutool.core.util.PageUtil;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Iterator;
import java.util.List;

/**
 * 分页工具类
 * <p>
 * Created by brook on 2017/8/15.
 *
 * <AUTHOR>
 */
class PageUtils extends PageUtil {
    /**
     * list分页计算
     *
     * @param collection
     *            列表
     * @param numPerPage
     *            每页数据
     * @param <T>
     *            类型
     * @return input ([1,2,3,4,5,6,7],3) output [[1,2,3],[4,5,6],[7]]
     */
    public static <T> List<List<T>> listToPage(final Collection<T> collection, final int numPerPage) {
        List<List<T>> pageList = new ArrayList<>();
        if (ToolsKit.isNotEmpty(collection) && numPerPage > 0) {
            boolean isList = collection instanceof List;
            final int totalCount = collection.size();
            if (totalCount > 0) {
                final int totalPage = totalPage(totalCount, numPerPage);
                Iterator<T> iterator = isList ? null : collection.iterator();
                for (int i = 0; i < totalPage; ++i) {
                    int start = numPerPage * i;
                    int end = start + numPerPage;
                    if (end > totalCount) {
                        end = totalCount;
                    }
                    if (isList) {
                        pageList.add(((List) collection).subList(start, end));
                    } else {
                        List<T> prePage = new ArrayList<>(numPerPage);
                        for (int j = 0; j < numPerPage && iterator.hasNext(); j++) {
                            prePage.add(iterator.next());
                        }
                        pageList.add(prePage);
                    }
                }
            }
        }
        return pageList;
    }
}
