package net.snaptag.system.business.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import net.snaptag.system.common.BaseEntity;

/**
 * 系统帮助项
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("v1_system_help_item")
public class SystemHelpItem extends BaseEntity {

    private static final long  serialVersionUID = 1L;
    public static final String COLL = "V1_SystemHelpItem";
    public static final String COLL_TYPE       = "type";

    /**
     * 标题
     */
    @TableField("title")
    private String title;

    /***
     * 国际化编码
     */
    @TableField("local_language_code")
    private String localLanguageCode;

    /**
     * 链接地址
     */
    @TableField("url")
    private String url;

    /***
     * 封面地址
     */
    @TableField("cover_url")
    private String coverUrl;

    /**
     * 帮助文档的类型
     */
    @TableField("type")
    private String type;

    /**
     * 打印机类型
     */
    @TableField("printer_type")
    private String printerType;

    @TableField("sort_num")
    private int sortNum;

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getCoverUrl() {
        return coverUrl;
    }

    public void setCoverUrl(String coverUrl) {
        this.coverUrl = coverUrl;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public int getSortNum() {
        return sortNum;
    }

    public void setSortNum(int sortNum) {
        this.sortNum = sortNum;
    }

    public String getLocalLanguageCode() {
        return localLanguageCode;
    }

    public void setLocalLanguageCode(String localLanguageCode) {
        this.localLanguageCode = localLanguageCode;
    }

    public String getPrinterType() {
        return printerType;
    }

    public void setPrinterType(String printerType) {
        this.printerType = printerType;
    }
}
