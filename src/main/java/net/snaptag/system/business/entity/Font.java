package net.snaptag.system.business.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 字体实体类
 *
 * <AUTHOR>
 * @since 2025-01-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("v1_font")
public class Font implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "fontId", type = IdType.ASSIGN_ID)
    private String fontId;

    @TableField("fontName")
    private String fontName;

    @TableField("fontEnglishName")
    private String fontEnglishName;

    @TableField("fontTraditionalName")
    private String fontTraditionalName;

    @TableField("fontKoreanName")
    private String fontKoreanName;

    @TableField("fontGermanyName")
    private String fontGermanyName;

    @TableField("fontItalyName")
    private String fontItalyName;

    @TableField("fontSpainName")
    private String fontSpainName;

    @TableField("fontFranceName")
    private String fontFranceName;

    @TableField("fontKind")
    private String fontKind;

    @TableField("fontUrl")
    private String fontUrl;

    @TableField("fontCover")
    private String fontCover;

    @TableField("fontValue")
    private String fontValue;

    @TableField("sysUserId")
    private Integer sysUserId;

    @TableField(value = "createTime", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    // 非数据库字段
    @TableField(exist = false)
    private String locale;
}
