package net.snaptag.system.business.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import net.snaptag.system.common.BaseEntity;

/**
 * 草稿箱
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("v1_drafts")
public class Drafts extends BaseEntity {
    private static final long serialVersionUID = 1L;
    public static final String COLL = "v1_drafts";
    public static final String USER_ID_FIELD = "user_id";
    public static final String TYPE_FIELD = "type";
    public static final String SUB_TYPE_FIELD = "sub_type";
    public static final String LENGTH_FIELD = "length";

    /**
     * 资源图片ID
     */
    @TableField("res_pic_id")
    private String resPicId;

    /**
     * 资源数据ID
     */
    @TableField("res_data_id")
    private String resDataId;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private String userId;

    /**
     * 草稿箱类型 0--草稿箱 1--我的历史
     */
    @TableField("type")
    private int type;

    /**
     * 草稿箱副类型 0编辑纸条 1清单 2大字横幅 3便利贴 4网页打印
     */
    @TableField("sub_type")
    private int subType;

    /**
     * 类型的ID
     */
    @TableField("material_column_id")
    private String materialColumnId;

    /**
     * 纸张长度
     */
    @TableField("length")
    private int length;

    /**
     * 草稿箱参数 - JSON格式字符串
     * 例如: {"param1": "value1", "param2": "value2"}
     */
    @TableField(value = "drafts_param")
    private String draftsParam;

    /**
     * 编辑方向，可选，默认0，打竖，1为横向
     */
    @TableField("place_type")
    private int placeType;

    /**
     * 纸张类型
     */
    @TableField("paper_type")
    private int paperType;

    /**
     * 纸张长度
     */
    @TableField("paper_length")
    private float paperLength;

    /**
     * 纸张宽度
     */
    @TableField("paper_width")
    private float paperWidth;

    /**
     * 纸张颜色
     */
    @TableField("paper_color")
    private int paperColor;

    /**
     * 打印机型号
     */
    @TableField("printer_type")
    private String printerType;

    /**
     * 直接打印的url地址
     */
    @TableField("pic_print_url")
    private String picPrintUrl;

    /**
     * 标题
     */
    @TableField("title")
    private String title;

    /**
     * 是否镜像，0：非，1：是
     */
    @TableField("is_mirror")
    private int isMirror;

    /**
     * 自定义纸张名称
     */
    private String paperName;

    /**
     * 是否黑边
     */
    @TableField("paper_is_black_flag")
    private boolean paperIsBlackFlag;

    /**
     * 是否用户自定义
     */
    @TableField("paper_is_custom")
    private boolean paperIsCustom;

    /**
     * 预览中心点
     */
    @TableField("preview_point")
    private String previewPoint;

    /**
     * 标签名称
     */
    @TableField("name")
    private String name;

}
