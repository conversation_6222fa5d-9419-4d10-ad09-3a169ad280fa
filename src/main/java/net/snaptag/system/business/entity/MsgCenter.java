package net.snaptag.system.business.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import net.snaptag.system.common.BaseEntity;

import java.util.Date;

/**
 * 消息中心表
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("v1_msg_center")
public class MsgCenter extends BaseEntity {
    private static final long serialVersionUID = 1L;
    public static final String COLL = "v1_msg_center";
    public static final String USERID_FIELD = "user_id";
    public static final String MSG_TYPE_FIELD = "msg_type";
    public static final String READ_FIELD = "is_read";

    /**
     * 用户ID
     */
    @TableField("user_id")
    private String userId;

    /**
     * 消息标题
     */
    @TableField("msg_title")
    private String msgTitle;

    /**
     * 消息内容
     */
    @TableField("msg_content")
    private String msgContent;

    /**
     * 消息主类型
     */
    @TableField("msg_type")
    private int msgType;

    /**
     * 消息副类型
     */
    @TableField("msg_sub_type")
    private int msgSubType;

    /**
     * 消息时间
     */
    @TableField("msg_time")
    private Date msgTime;

    /**
     * 图片地址
     */
    @TableField("pic")
    private String pic;

    /**
     * 消息发起者ID
     */
    @TableField("sender_user_id")
    private String senderUserId;

    /**
     * 参数 - JSON格式字符串
     * 例如: {"key1": "value1", "key2": "value2"}
     */
    @TableField(value = "param")
    private String param;

    /**
     * 消息已读状态
     */
    @TableField("read")
    private int            read;

    public String getPic() {
        return pic;
    }

    public void setPic(String pic) {
        this.pic = pic;
    }

    public int getMsgSubType() {
        return msgSubType;
    }

    public void setMsgSubType(int msgSubType) {
        this.msgSubType = msgSubType;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getMsgTitle() {
        return msgTitle;
    }

    public void setMsgTitle(String msgTitle) {
        this.msgTitle = msgTitle;
    }

    public String getMsgContent() {
        return msgContent;
    }

    public void setMsgContent(String msgContent) {
        this.msgContent = msgContent;
    }

    public int getMsgType() {
        return msgType;
    }

    public void setMsgType(int msgType) {
        this.msgType = msgType;
    }

    public Date getMsgTime() {
        return msgTime;
    }

    public void setMsgTime(Date msgTime) {
        this.msgTime = msgTime;
    }

    public String getSenderUserId() {
        return senderUserId;
    }

    public void setSenderUserId(String senderUserId) {
        this.senderUserId = senderUserId;
    }

    public String getParam() {
        return param;
    }

    public void setParam(String param) {
        this.param = param;
    }

    public int getRead() {
        return read;
    }

    public void setRead(int read) {
        this.read = read;
    }

}
