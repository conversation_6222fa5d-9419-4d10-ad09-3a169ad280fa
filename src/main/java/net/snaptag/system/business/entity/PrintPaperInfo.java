package net.snaptag.system.business.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import net.snaptag.system.common.BaseEntity;

import java.util.Date;

/**
 * 打印耗材信息
 * <AUTHOR>
 * @date ：Created in 2023/7/19 10:17
 * @description：
 * @modified By：
 * @version: $
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("v1_print_paper_info")
public class PrintPaperInfo extends BaseEntity {

    /**
     * 纸张id
     */
    @TableField("paper_id")
    private String paperId;

    /**
     * 1为连续纸，2为间隙纸
     */
    @TableField("type")
    private int type;

    /**
     * 介质，1为合成纸面材；2未普通纸面材
     */
    @TableField("material")
    private int material;

    /**
     * 纸张高度
     */
    @TableField("height")
    private float height;

    /**
     * 纸张长度，连续纸统一：1
     */
    @TableField("width")
    private float width;

    /**
     * 间隙长度
     */
    @TableField("gap_width")
    private float gapWidth;

    /**
     * 0:不带rfid，1：带rfid
     */
    @TableField("rfid_flag")
    private int rfidFlag;

    /**
     * 颜色代码
     */
    @TableField("color_code")
    private String colorCode;

    /**
     * 名称
     */
    @TableField("zh_title")
    private String zhTitle;

    /**
     * 名称-国际代码
     */
    @TableField("en_title")
    private String enTitle;

    /**
     * 列表前缀名
     */
    @TableField("list_name")
    private String listName;

    /**
     * 列表图
     */
    @TableField("res_url")
    private String resUrl;

    /**
     * 资源图
     */
    @TableField("image_url")
    private String imageUrl;

    /**
     * 适合打印机，分号分隔
     */
    @TableField("printer_type")
    private String printerType;

    /**
     * 是否展示，0表示不显示，1表示显示
     */
    @TableField("is_new")
    private int isNew;

    /**
     * 选择展示期限
     */
    @TableField("showtime_end")
    private Date showtimeEnd;

    /**
     * 方向  0为 横，1为竖
     */
    @TableField("direction")
    private int direction;

    /**
     * 是否隐藏，0为不隐藏，1为隐藏
     */
    @TableField("is_hide")
    private int isHide;

    /**
     * 排序字段
     */
    @TableField("sort_num")
    private int sortNum;

    /**
     * 打印色值
     */
    @TableField("print_color_code")
    private String printColorCode = "#000000";

    /**
     * 纸张方向
     */
    @TableField("rotate")
    private int rotate;

    /**
     * 打印范围X
     */
    @TableField("print_x")
    private float printX;

    /**
     * 打印范围Y
     */
    @TableField("print_y")
    private float printY;

    /**
     * 打印范围H
     */
    @TableField("print_h")
    private float printH;

    /**
     * 打印范围W
     */
    @TableField("print_w")
    private float printW;

    /**
     * 排废
     */
    @TableField("die_cutting")
    private float dieCutting=1.5f;

    /**
     * 是否黑标，0：否； 1：是
     */
    @TableField("is_black_flag")
    private int isBlackFlag =0;


    public String getPaperId() {
        return paperId;
    }

    public void setPaperId(String paperId) {
        this.paperId = paperId;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public int getMaterial() {
        return material;
    }

    public void setMaterial(int material) {
        this.material = material;
    }

    public float getHeight() {
        return height;
    }

    public void setHeight(float height) {
        this.height = height;
    }

    public float getWidth() {
        return width;
    }

    public void setWidth(float width) {
        this.width = width;
    }

    public float getGapWidth() {
        return gapWidth;
    }

    public void setGapWidth(float gapWidth) {
        this.gapWidth = gapWidth;
    }

    public String getColorCode() {
        return colorCode;
    }

    public void setColorCode(String colorCode) {
        this.colorCode = colorCode;
    }

    public String getZhTitle() {
        return zhTitle;
    }

    public void setZhTitle(String zhTitle) {
        this.zhTitle = zhTitle;
    }

    public String getEnTitle() {
        return enTitle;
    }

    public void setEnTitle(String enTitle) {
        this.enTitle = enTitle;
    }

    public String getResUrl() {
        return resUrl;
    }

    public void setResUrl(String resUrl) {
        this.resUrl = resUrl;
    }

    public String getImageUrl() {
        return imageUrl;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    public String getPrinterType() {
        return printerType;
    }

    public void setPrinterType(String printerType) {
        this.printerType = printerType;
    }

    public int getIsNew() {
        return isNew;
    }

    public void setIsNew(int isNew) {
        this.isNew = isNew;
    }

    public Date getShowtimeEnd() {
        return showtimeEnd;
    }

    public void setShowtimeEnd(Date showtimeEnd) {
        this.showtimeEnd = showtimeEnd;
    }

    public int getRfidFlag() {
        return rfidFlag;
    }

    public void setRfidFlag(int rfidFlag) {
        this.rfidFlag = rfidFlag;
    }

    public int getSortNum() {
        return sortNum;
    }

    public void setSortNum(int sortNum) {
        this.sortNum = sortNum;
    }

    public String getPrintColorCode() {
        return printColorCode;
    }

    public void setPrintColorCode(String printColorCode) {
        this.printColorCode = printColorCode;
    }

    public int getDirection() {
        return direction;
    }

    public void setDirection(int direction) {
        this.direction = direction;
    }

    public float getPrintX() {
        return printX;
    }

    public void setPrintX(float printX) {
        this.printX = printX;
    }

    public float getPrintY() {
        return printY;
    }

    public void setPrintY(float printY) {
        this.printY = printY;
    }

    public float getPrintH() {
        return printH;
    }

    public void setPrintH(float printH) {
        this.printH = printH;
    }

    public float getPrintW() {
        return printW;
    }

    public void setPrintW(float printW) {
        this.printW = printW;
    }

    public int getIsHide() {
        return isHide;
    }

    public void setIsHide(int isHide) {
        this.isHide = isHide;
    }

    public int getRotate() {
        return rotate;
    }

    public void setRotate(int rotate) {
        this.rotate = rotate;
    }

    public String getListName() {
        return listName;
    }

    public void setListName(String listName) {
        this.listName = listName;
    }

    public float getDieCutting() {
        return dieCutting;
    }

    public void setDieCutting(float dieCutting) {
        this.dieCutting = dieCutting;
    }

    public int getIsBlackFlag() {
        return isBlackFlag;
    }

    public void setIsBlackFlag(int isBlackFlag) {
        this.isBlackFlag = isBlackFlag;
    }
}
