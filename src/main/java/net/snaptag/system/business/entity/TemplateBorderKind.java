package net.snaptag.system.business.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 模板边框分类实体类
 *
 * <AUTHOR>
 * @since 2025-08-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("v1_template_border_kind")
public class TemplateBorderKind implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "borderKindId", type = IdType.ASSIGN_ID)
    private String borderKindId;

    @TableField("borderKindName")
    private String borderKindName;

    @TableField("englishName")
    private String englishName;

    @TableField("traditionalName")
    private String traditionalName;

    @TableField("koreanName")
    private String koreanName;

    @TableField("russianName")
    private String russianName;

    @TableField("frenchName")
    private String frenchName;

    @TableField("spanishName")
    private String spanishName;

    @TableField("germanyName")
    private String germanyName;

    @TableField("italyName")
    private String italyName;

    @TableField("sysUserId")
    private Integer sysUserId;

    @TableField(value = "createTime", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    // 非数据库字段 - 用于多语言显示
    @TableField(exist = false)
    private String displayName;

    /**
     * 根据语言类型获取对应的名称
     *
     * @param language 语言类型
     * @return 对应语言的名称
     */
    public String getNameByLanguage(Integer language) {
        if (language == null) {
            return this.borderKindName;
        }
        
        switch (language) {
            case 2: // 英文
                return this.englishName != null ? this.englishName : this.borderKindName;
            case 3: // 繁体中文
                return this.traditionalName != null ? this.traditionalName : this.borderKindName;
            case 4: // 韩文
                return this.koreanName != null ? this.koreanName : this.borderKindName;
            case 5: // 俄语
                return this.russianName != null ? this.russianName : this.borderKindName;
            default:
                return this.borderKindName;
        }
    }

    /**
     * 根据语言标识获取对应的名称
     *
     * @param lang 语言标识 (english, traditional, korean, russian, french, spanish, germany, italy)
     * @return 对应语言的名称
     */
    public String getNameByLang(String lang) {
        if (lang == null || lang.trim().isEmpty()) {
            return this.borderKindName;
        }
        
        switch (lang.toLowerCase()) {
            case "english":
                return this.englishName != null ? this.englishName : this.borderKindName;
            case "traditional":
                return this.traditionalName != null ? this.traditionalName : this.borderKindName;
            case "korean":
                return this.koreanName != null ? this.koreanName : this.borderKindName;
            case "russian":
                return this.russianName != null ? this.russianName : this.borderKindName;
            case "french":
                return this.frenchName != null ? this.frenchName : this.borderKindName;
            case "spanish":
                return this.spanishName != null ? this.spanishName : this.borderKindName;
            case "germany":
                return this.germanyName != null ? this.germanyName : this.borderKindName;
            case "italy":
                return this.italyName != null ? this.italyName : this.borderKindName;
            default:
                return this.borderKindName;
        }
    }
}
