package net.snaptag.system.business.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import net.snaptag.system.common.BaseEntity;

/**
 * 版本更新信息
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("v1_update_info")
public class UpdateInfo extends BaseEntity {
    private static final long  serialVersionUID = 1L;
    public static final String COLL             = "V1_UpdateInfo";

    /**
     * 渠道
     */
    @TableField("channel")
    private String             channel;

    /**
     * 版本号
     */
    @TableField("version")
    private String             version;

    /**
     * 下载地址
     */
    @TableField("url")
    private String             url;

    /**
     * 参数
     */
    @TableField("param")
    private String             param;

    /**
     * 版本描述
     */
    @TableField("remark")
    private String             remark;

    /**
     * 是否强制升级
     */
    @TableField("need_force_update")
    private int                needForceUpdate;

    /**
     * 是否首页显示
     */
    @TableField("need_index_show")
    private int                needIndexShow;

    /**
     * 标题
     */
    @TableField("title")
    private String             title;

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getParam() {
        return param;
    }

    public void setParam(String param) {
        this.param = param;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public int getNeedForceUpdate() {
        return needForceUpdate;
    }

    public void setNeedForceUpdate(int needForceUpdate) {
        this.needForceUpdate = needForceUpdate;
    }

    public int getNeedIndexShow() {
        return needIndexShow;
    }

    public void setNeedIndexShow(int needIndexShow) {
        this.needIndexShow = needIndexShow;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }
}
