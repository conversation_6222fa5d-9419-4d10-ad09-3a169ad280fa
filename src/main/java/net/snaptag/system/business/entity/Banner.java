package net.snaptag.system.business.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import net.snaptag.system.common.BaseEntity;

import java.util.Date;

/**
 * 顶部广告信息表
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("v1_banner")
public class Banner extends BaseEntity {
    private static final long serialVersionUID = 1L;
    public static final String COLL = "v1_banner";
    public static final String SORT_FIELD = "sort";

    /**
     * 名称
     */
    @TableField("name")
    private String name;

    /**
     * 图片map - JSON格式字符串
     * 例如: {"686x280": "https://xplable.oss-cn-hangzhou.aliyuncs.com/api/img/gam/20220708/ce7722c1-ee00-4d20-3f5a-9ad030095bf7.png"}
     */
    @TableField(value = "pics")
    private String pics;

    /**
     * 消息跳转值
     */
    @TableField("jump_val")
    private String jumpVal;

    /**
     * 排序
     */
    @TableField("sort")
    private int sort;

    /**
     * 跳转类型
     */
    @TableField("jump_type")
    private int jumpType;

    /**
     * 栏目（gam 社区，mall商城）
     */
    @TableField("column")
    private String column;

    /**
     * Banner有效开始时间
     */
    @TableField("start_time")
    private Date startTime;

    /**
     * Banner有效结束时间
     */
    @TableField("end_time")
    private Date endTime;

    /**
     * 分享时展示的标题
     */
    @TableField("share_title")
    private String shareTitle;

    /**
     * 分享时展示的内容
     */
    @TableField("share_content")
    private String shareContent;

    /**
     * 是否分享
     */
    @TableField("share_flag")
    private int shareFlag;

    /**
     * 显示最小版本版本号(为了能提前查看效果)
     */
    @TableField("version")
    private String version;

    /**
     * 备注信息,如果column是knowledge,则备注表达的是年级
     */
    @TableField("remark")
    private String remark;

    /**
     * 自定义组装的json格式，用于andriod前端调用
     */
    @TableField("param_android")
    private String paramAndroid;

    /**
     * 自定义组装的json格式，用于ios前端调用
     */
    @TableField("param_ios")
    private String paramIos;

    /**
     * 语言国际化
     */
    @TableField("locale_code")
    private String localeCode;

}
