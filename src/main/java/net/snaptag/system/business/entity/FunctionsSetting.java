package net.snaptag.system.business.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import net.snaptag.system.common.BaseEntity;

/**
 * <AUTHOR>
 * @date ：Created in 2024/1/11 10:34
 * @description：app中关于打印的功能展示设定
 * @modified By：
 * @version: $
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("v1_functions_setting")
public class FunctionsSetting extends BaseEntity {
    private static final long serialVersionUID = 1L;
    public static final String COLL = "v1_functions_setting";

    /**
     * 功能编码
     */
    @TableField("code")
    private String code;

    /**
     * 功能名称
     */
    @TableField("name")
    private String name;

    /**
     * 所属打印机
     */
    @TableField("printer_types")
    private String printerTypes;

    /**
     * 所属语种
     */
    @TableField("language_codes")
    private String languageCodes;

    /**
     * 参数
     */
    @TableField("params")
    private String params;

    /**
     * 是否展示新
     */
    @TableField("new_flag")
    private int newFlag;

    /**
     * 排序
     */
    @TableField("sort_num")
    private int sortNum;

    /**
     * 功能类型
     */
    @TableField("type")
    private String type;

}
