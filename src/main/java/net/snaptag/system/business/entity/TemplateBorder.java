package net.snaptag.system.business.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 模板边框实体类
 *
 * <AUTHOR>
 * @since 2025-08-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("v1_template_border")
public class TemplateBorder implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "borderId", type = IdType.ASSIGN_ID)
    private String borderId;

    @TableField("borderKindId")
    private String borderKindId;

    @TableField("headerImgUrl")
    private String headerImgUrl;

    @TableField("fillImgUrl")
    private String fillImgUrl;

    @TableField("footerImgUrl")
    private String footerImgUrl;

    @TableField("thumbUrl")
    private String thumbUrl;

    @TableField("sysUserId")
    private Integer sysUserId;

    @TableField(value = "createTime", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    // 非数据库字段 - 用于关联查询和业务逻辑
    @TableField(exist = false)
    private String groupName;

    @TableField(exist = false)
    private String groupDisplayName;

    @TableField(exist = false)
    private TemplateBorderKind borderKind;

    /**
     * 设置分类信息
     *
     * @param borderKind 边框分类对象
     * @param language 语言类型
     */
    public void setBorderKindInfo(TemplateBorderKind borderKind, Integer language) {
        this.borderKind = borderKind;
        if (borderKind != null) {
            this.groupName = borderKind.getBorderKindName();
            this.groupDisplayName = borderKind.getNameByLanguage(language);
        }
    }

    /**
     * 设置分类信息
     *
     * @param borderKind 边框分类对象
     * @param lang 语言标识
     */
    public void setBorderKindInfo(TemplateBorderKind borderKind, String lang) {
        this.borderKind = borderKind;
        if (borderKind != null) {
            this.groupName = borderKind.getBorderKindName();
            this.groupDisplayName = borderKind.getNameByLang(lang);
        }
    }

    /**
     * 移除敏感字段（用于API返回）
     */
    public void removeSensitiveFields() {
        this.sysUserId = null;
        this.createTime = null;
        this.borderKindId = null;
    }
}
