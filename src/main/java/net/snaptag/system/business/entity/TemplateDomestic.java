package net.snaptag.system.business.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import net.snaptag.system.common.BaseEntity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 家用行业模板
 */
@Data
@TableName("v1_template_domestic")
public class TemplateDomestic extends BaseEntity {
    private static final long serialVersionUID = 1L;
    public static final String COLL = "v1_template_domestic";

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 用户ID
     */
    @TableField("userId")
    private Integer userId;

    /**
     * 模板分组ID
     */
    @TableField("groupId")
    private String groupId;

    /**
     * 模板名称
     */
    @TableField("name")
    private String name;

    /**
     * 封面图片地址
     */
    @TableField("cover")
    private String cover;

    /**
     * 间隙
     */
    @TableField("gap")
    private BigDecimal gap;

    /**
     * 高度
     */
    @TableField("height")
    private BigDecimal height;

    /**
     * 宽度
     */
    @TableField("width")
    private BigDecimal width;

    /**
     * 纸张类型：1-间隙纸 2-连续纸 3-黑标纸 4-定孔纸
     */
    @TableField("paperType")
    private Integer paperType;

    /**
     * 打印方向
     */
    @TableField("printDirection")
    private Integer printDirection;

    /**
     * 标签内容数据
     */
    @TableField("data")
    private String data;

    /**
     * 黑标间隙
     */
    @TableField("blackLabelGap")
    private BigDecimal blackLabelGap;

    /**
     * 黑标偏移
     */
    @TableField("blackLabelOffset")
    private BigDecimal blackLabelOffset;

    /**
     * 类型：0-1.0老版本数据 1-行业模板 2-2.0新数据
     */
    @TableField("type")
    private Integer type;

    /**
     * 模板英语名称（行业模板）
     */
    @TableField("nameEn")
    private String nameEn;

    /**
     * 模板韩语名称（行业模板）
     */
    @TableField("nameKor")
    private String nameKor;


    /**
     * 机器类型：1-标签 2-票据 3-76针打
     */
    @TableField("machineType")
    private Integer machineType;

    /**
     * 打印后切纸：0-否 1-是
     */
    @TableField("cutAfterPrint")
    private Integer cutAfterPrint;

    /**
     * 标签数量，默认为1，大于1为多排标签
     */
    @TableField("labelNum")
    private Integer labelNum;

    /**
     * 多排标签间距
     */
    @TableField("labelGap")
    private BigDecimal labelGap;

    /**
     * 模板日语名称（行业模板）
     */
    @TableField("nameJP")
    private String nameJP;

    /**
     * 模板HK繁体名称（行业模板）
     */
    @TableField("nameHK")
    private String nameHK;

    /**
     * 多排类型：0-不复制 1-仅复制首排标签
     */
    @TableField("multiLabelType")
    private Integer multiLabelType;

    /**
     * 撕纸类型：1-撕离 2-剥离
     */
    @TableField("paperTearType")
    private Integer paperTearType;

    /**
     * 分享用户
     */
    @TableField("shareUser")
    private Integer shareUser;

    /**
     * 标签类型（标签形状）：1-矩形 2-圆角矩形 3-圆
     */
    @TableField("labelType")
    private Integer labelType;

    /**
     * 票据模板机器子类型：1-58票据 2-80票据 3-76针打
     */
    @TableField("ticketMachineType")
    private Integer ticketMachineType;

    /**
     * 打印后走纸长度
     */
    @TableField("paperFeedCount")
    private Integer paperFeedCount;

    /**
     * 是否开启镜像：0-否 1-是
     */
    @TableField("mirrorImage")
    private Boolean mirrorImage;

    /**
     * 模板俄语名称（行业模板）
     */
    @TableField("nameRU")
    private String nameRU;

    /**
     * 打印类型：0-标签打印 1-PDF打印 2-图片打印
     */
    @TableField("printType")
    private Integer printType;
}
