package net.snaptag.system.business.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import net.snaptag.system.common.BaseEntity;

/**
 * 我的资源
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("v1_resource_data")
public class ResourceData extends BaseEntity {
    private static final long serialVersionUID = 1L;
    public static final String COLL = "V1_ResourceData";
    @TableField("user_id")
    private String userId;                              // 用户ID

    /**
     * 资源地址 - JSON格式字符串
     * 例如: {"pic": "http://www.baidu.com", "size": 0, "width": 0, "height": 0}
     */
    @TableField(value = "res_url")
    private String resUrl;

    @TableField("res_type")
    private int resType;                             // 资源类型 0--编辑纸条数据 1--图片地址 2--语音 3--共享打印

    @TableField("flag")
    private String flag;                                // 资源标识--MD5

    @TableField("content")
    private String content;                             // 内容

    /**
     * 资源内容-新版本字段
     **/
    @TableField("data")
    private String data;

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getResUrl() {
        return resUrl;
    }

    public void setResUrl(String resUrl) {
        this.resUrl = resUrl;
    }

    public int getResType() {
        return resType;
    }

    public void setResType(int resType) {
        this.resType = resType;
    }

    public String getFlag() {
        return flag;
    }

    public void setFlag(String flag) {
        this.flag = flag;
    }

    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }
}
