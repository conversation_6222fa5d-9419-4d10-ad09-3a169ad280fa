package net.snaptag.system.business.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * Author：Chenjy
 * Date:2025/8/25
 * Description:
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("v1_machine_settings")
public class MachineSettings implements Serializable {

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    @TableField(value = "machineName")
    private String machineName;

    @TableField(value = "bluetoothName")
    private String bluetoothName;

    @TableField(value = "hardwareName")
    private String hardwareName;

    @TableField(value = "settings")
    private String settings;

    /**
     * 创建时间
     */
    @TableField("createTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;
}
