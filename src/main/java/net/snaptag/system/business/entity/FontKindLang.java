package net.snaptag.system.business.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 字体分类语言实体类
 *
 * <AUTHOR>
 * @since 2025-01-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("v1_font_kind_lang")
public class FontKindLang implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    @TableField("fontKind")
    private String fontKind;

    @TableField("fontLang")
    private String fontLang;
}
