package net.snaptag.system.business.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import net.snaptag.system.common.BaseEntity;


/**
 * <AUTHOR>
 * @date ：Created in 2023/5/17 9:16
 * @description：语种模板开启设置
 * @modified By：
 * @version: $
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("v1_template_drafts_language_config")
public class TemplateDraftsLanguageConfig extends BaseEntity {
    @TableField("language")
    private String language;

    @TableField("show_flag")
    private Boolean showFlag;

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

    public Boolean getShowFlag() {
        return showFlag;
    }

    public void setShowFlag(Boolean showFlag) {
        this.showFlag = showFlag;
    }
}
