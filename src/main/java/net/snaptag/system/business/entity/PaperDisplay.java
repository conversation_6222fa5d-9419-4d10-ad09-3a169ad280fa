package net.snaptag.system.business.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import net.snaptag.system.common.BaseEntity;

/**
 * <AUTHOR>
 * @date ：Created in 2024/3/28 16:56
 * @description：
 * @modified By：
 * @version: $
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("v1_paper_display")
public class PaperDisplay extends BaseEntity {
    public static final String SORTNUM_FIELD = "sortNum";
    private static final long      serialVersionUID = 1L;
    public static final String     COLL             = "V1_PaperDisplay";
    public static final String     NAME_FIELD     = "name";

    @TableField("name")
    private String name;

    @TableField("i18n_key")
    private String i18nKey;

    @TableField("types")
    private String types;

    @TableField("printer_types")
    private String printerTypes;

    @TableField("language_codes")
    private String languageCodes;

    @TableField("paper_ids")
    private String paperIds;

    @TableField("sort_num")
    private int sortNum;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getI18nKey() {
        return i18nKey;
    }

    public void setI18nKey(String i18nKey) {
        this.i18nKey = i18nKey;
    }

    public String getTypes() {
        return types;
    }

    public void setTypes(String types) {
        this.types = types;
    }

    public String getPrinterTypes() {
        return printerTypes;
    }

    public void setPrinterTypes(String printerTypes) {
        this.printerTypes = printerTypes;
    }

    public String getLanguageCodes() {
        return languageCodes;
    }

    public void setLanguageCodes(String languageCodes) {
        this.languageCodes = languageCodes;
    }

    public String getPaperIds() {
        return paperIds;
    }

    public int getSortNum() {
        return sortNum;
    }

    public void setSortNum(int sortNum) {
        this.sortNum = sortNum;
    }

    public void setPaperIds(String paperIds) {
        this.paperIds = paperIds;
    }
}
