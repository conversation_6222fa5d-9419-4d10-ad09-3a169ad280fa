package net.snaptag.system.business.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import net.snaptag.system.common.BaseEntity;

/**
 * 商品
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("v1_goods")
public class Goods extends BaseEntity {
    private static final long serialVersionUID = 1L;
    public static final String COLL = "v1_goods";
    public static final String SORT_FIELD = "sort_num";

    /**
     * 名称
     */
    @TableField("name")
    private String name;

    /**
     * 副标题
     */
    @TableField("sub_name")
    private String subName;

    /**
     * 图片地址
     */
    @TableField("pic_url")
    private String picUrl;

    /**
     * 消息跳转值
     */
    @TableField("jump_val")
    private String jumpVal;

    /**
     * 排序
     */
    @TableField("sort_num")
    private int sortNum;

    /**
     * 是否上架，0：未上架；1：上架
     */
    @TableField("sale_status")
    private int saleStatus;

    /**
     * 价格，包含单位
     */
    @TableField("price")
    private String price;

    /**
     * 备注信息
     */
    @TableField("remark")
    private String remark;

    /**
     * 自定义组装的json格式，用于android前端调用
     */
    @TableField("param_android")
    private String paramAndroid;

    /**
     * 自定义组装的json格式，用于ios前端调用
     */
    @TableField("param_ios")
    private String paramIos;

}
