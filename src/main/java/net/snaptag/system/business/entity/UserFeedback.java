package net.snaptag.system.business.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import net.snaptag.system.common.BaseEntity;

/**
 * 用户反馈信息类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("v1_user_feedback")
public class UserFeedback extends BaseEntity {

    private static final long serialVersionUID = 1L;
    public static final String COLL = "v1_user_feedback";
    public static final String USER_ID_FIELD = "user_id";

    /**
     * 用户ID
     */
    @TableField("user_id")
    private String userId;

    /**
     * 反馈类型
     */
    @TableField("type")
    private String type;
    /**
     * 问题类型
     */
    @TableField("qtype")
    private String qtype;

    /**
     * 反馈文本内容
     */
    @TableField("content")
    private String content;

    /**
     * 反馈图片地址列表 - JSON格式字符串
     * 例如: ["image1.jpg", "image2.jpg", "image3.jpg"]
     */
    @TableField(value = "images")
    private String images;

    /**
     * 系统响应结果信息
     */
    @TableField("result")
    private String result;

    /**
     * 错误信息
     */
    @TableField("errormsg")
    private String errormsg;

    /**
     * 手机号
     */
    @TableField("mobile")
    private String mobile;

    /**
     * 用户编号
     */
    @TableField("user_no")
    private Integer userNo;

    /**
     * 客户端信息
     */
    @TableField("client_info")
    private String clientInfo;

    @TableField("printer_type")
    private String printerType;

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getQtype() {
        return qtype;
    }

    public void setQtype(String qtype) {
        this.qtype = qtype;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getImages() {
        return images;
    }

    public void setImages(String images) {
        this.images = images;
    }

    public String getResult() {
        return result;
    }

    public void setResult(String result) {
        this.result = result;
    }

    public String getErrormsg() {
        return errormsg;
    }

    public void setErrormsg(String errormsg) {
        this.errormsg = errormsg;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public Integer getUserNo() {
        return userNo;
    }

    public void setUserNo(Integer userNo) {
        this.userNo = userNo;
    }

    public String getClientInfo() {
        return clientInfo;
    }

    public void setClientInfo(String clientInfo) {
        this.clientInfo = clientInfo;
    }

    public String getPrinterType() {
        return printerType;
    }

    public void setPrinterType(String printerType) {
        this.printerType = printerType;
    }
}
