package net.snaptag.system.business.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import net.snaptag.system.common.BaseEntity;

import java.util.Date;

/**
 * 设备连接日志
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("v1_device_connect_log")
public class DeviceConnectLog extends BaseEntity {
    private static final long  serialVersionUID = 1L;
    public static final String COLL             = "V1_DeviceConnectLog";
    public static final String USER_ID_FIELD    = "userId";
    public static final String MAC_ADDRESS_FIELD    = "macAddress";
    public static final String DEVICE_SN_FIELD  = "deviceSn";
    public static final String DEVICE_NAME_FIELD  = "deviceName";
    public static final String DEVICE_LASTEST_TIME = "lastestTime";
    @TableField("user_id")
    private String             userId;                           // 用户ID
    @TableField("mac_address")
    private String             macAddress;                      // 设备mac地址
    @TableField("device_sn")
    private String             deviceSn;                              // 设备SN

    @TableField("device_name")
    private String             deviceName;                            // 设备名称

    @TableField("device_version")
    private String             deviceVersion;                   // 设备固件版本号

    @TableField("app_version")
    private String             appVersion;                      // 应用版本

    @TableField("lastest_time")
    private Date               lastestTime;                     // 最后连接时间

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getMacAddress() {
        return macAddress;
    }

    public void setMacAddress(String macAddress) {
        this.macAddress = macAddress;
    }

    public String getDeviceSn() {
        return deviceSn;
    }

    public void setDeviceSn(String deviceSn) {
        this.deviceSn = deviceSn;
    }

    public String getDeviceName() {
        return deviceName;
    }

    public void setDeviceName(String deviceName) {
        this.deviceName = deviceName;
    }

    public String getDeviceVersion() {
        return deviceVersion;
    }

    public void setDeviceVersion(String deviceVersion) {
        this.deviceVersion = deviceVersion;
    }

    public String getAppVersion() {
        return appVersion;
    }

    public void setAppVersion(String appVersion) {
        this.appVersion = appVersion;
    }

    public Date getLastestTime() {
        return lastestTime;
    }

    public void setLastestTime(Date lastestTime) {
        this.lastestTime = lastestTime;
    }
}
