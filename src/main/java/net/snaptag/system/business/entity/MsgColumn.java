package net.snaptag.system.business.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import net.snaptag.system.common.BaseEntity;

/**
 * 消息栏目表
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("v1_msg_column")
public class MsgColumn extends BaseEntity {
    private static final long serialVersionUID = 1L;
    public static final String COLL = "v1_msg_column";
    public static final String USERID_FIELD = "userId";

    /**
     * 用户ID
     */
    @TableField("user_id")
    private String userId;

    /**
     * 栏目vo - JSON格式字符串
     * 例如: {"1": {"id": 1, "name": "栏目1"}, "2": {"id": 2, "name": "栏目2"}}
     */
    @TableField("column_map")
    private String columnMap;


}
