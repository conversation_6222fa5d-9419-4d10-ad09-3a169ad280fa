package net.snaptag.system.business.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import net.snaptag.system.common.BaseEntity;

/**
 * 素材库
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("v1_material")
public class Material extends BaseEntity {
    private static final long serialVersionUID = 1L;
    public static final String COLL = "v1_material";
    public static final String SORT_FIELD = "sort";

    /**
     * 父ID
     */
    @TableField("p_id")
    private String pId;

    /**
     * 名称
     */
    @TableField("name")
    private String name;

    /**
     * 排序
     */
    @TableField("sort")
    private int sort;

    /**
     * 类型
     */
    @TableField("type")
    private int type;

    /**
     * 副类型
     */
    @TableField("sub_type")
    private int subType;

    /**
     * 是否有内容
     */
    @TableField("has_content")
    private int hasContent;

    /**
     * 标签，用分号隔开
     */
    @TableField("label")
    private String label;

    /**
     * 图标地址 - JSON格式字符串
     * 例如: {"60x60": "/api/img/gam/20180827185191/print_ic_sticker.png"}
     */
    @TableField(value = "icon")
    private String icon;

    /**
     * 方位 - JSON格式字符串
     * 例如: ["top", "center", "bottom"]
     */
    @TableField(value = "position")
    private String position;

    /**
     * 模板组件名
     */
    @TableField("com")
    private String com;

    /**
     * 中文标题
     */
    @TableField("zh_title")
    private String zhTitle;

    /**
     * 英文标题
     */
    @TableField("en_title")
    private String enTitle;

}
