package net.snaptag.system.business.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import net.snaptag.system.common.BaseEntity;

/**
 * 网页打印
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("v1_web_print")
public class WebPrint extends BaseEntity {

    private static final long  serialVersionUID = 1L;
    public static final String COLL             = "V1_WebPrint";
    public static final String USER_ID_FIELD    = "userId";
    public static final String IS_DEFAULT_FIELD = "isDefault";

    /**
     * 用户ID
     */
    @TableField("user_id")
    private String userId;
    /**
     * 分组名称
     */
    @TableField("name")
    private String name;

    /**
     * 是否默认
     */
    @TableField("is_default")
    private int isDefault;

    /**
     * 来自默认
     */
    @TableField("from_default")
    private int fromDefault;

    /**
     * 我的网站集合 - JSON格式字符串
     * 例如: [{"url": "http://example.com", "title": "网站标题"}]
     */
    @TableField(value = "page_list")
    private String pageList;

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getIsDefault() {
        return isDefault;
    }

    public void setIsDefault(int isDefault) {
        this.isDefault = isDefault;
    }

    public int getFromDefault() {
        return fromDefault;
    }

    public void setFromDefault(int fromDefault) {
        this.fromDefault = fromDefault;
    }

    public String getPageList() {
        return pageList;
    }

    public void setPageList(String pageList) {
        this.pageList = pageList;
    }

}