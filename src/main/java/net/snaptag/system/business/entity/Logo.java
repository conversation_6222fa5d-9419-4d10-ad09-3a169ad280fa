package net.snaptag.system.business.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import lombok.EqualsAndHashCode;
import net.snaptag.system.common.BaseEntity;

import java.io.Serializable;
import java.util.Date;

/**
 * Author：Chenjy
 * Date:2025/8/21
 * Description:
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("v1_logo")
public class Logo implements Serializable {
    private static final long serialVersionUID = 1L;
    public static final String COLL = "v1_logo";
    public static final String SORT_FIELD = "sort_num";
    /**
     * logoId，主键，自增
     */
    @TableId(value = "logoId", type = IdType.ASSIGN_ID)
    private Integer logoId;

    /**
     * logo分类Id
     */
    @TableField("logoKindId")
    private Integer logoKindId;

    /**
     * logo子类id
     */
    @TableField("logoChildKindId")
    private Integer logoChildKindId;

    /**
     * logo图片路径
     */
    @TableField("logoImg")
    private String logoImg;

    /**
     * 操作人id
     */
    @TableField("sysUserId")
    private Integer sysUserId;

    /**
     * 创建时间
     */
    @TableField("createTime")
    private Date createTime;

    /**
     * 版本号区分数据-兼容老版本，默认值1
     */
    @TableField("version")
    private Integer version = 1;

    @TableField(exist = false)
    private String groupName;

}
