package net.snaptag.system.business.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import net.snaptag.system.common.BaseEntity;

/**
 * <AUTHOR>
 * @date ：Created in 2021/9/27 16:49
 * @description：用户手册的模板记录
 * @modified By：
 * @version: $
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("v1_user_template_drafts_collect")
public class UserTemplateDraftsCollect extends BaseEntity {

    public static final String USER_ID_COL = "userId";
    public static final String TEMPLATE_DRAFTS_ID_COL = "templateDraftsId";

    /**
     * 用户ID
     */
    @TableField("user_id")
    private String userId;

    /**
     * 模板草稿ID
     */
    @TableField("template_drafts_id")
    private String templateDraftsId;

    /**
     * 打印机类型
     */
    @TableField("printer_type")
    private String printerType;


}
