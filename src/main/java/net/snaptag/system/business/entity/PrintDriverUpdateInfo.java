package net.snaptag.system.business.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import net.snaptag.system.common.BaseEntity;

/**
 * <AUTHOR>
 * @date ：Created in 2023/4/6 8:51
 * @description： 打印机驱动更新
 * @modified By：
 * @version: $
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("v1_print_driver_update_info")
public class PrintDriverUpdateInfo extends BaseEntity {
    private static final long  serialVersionUID = 1L;
    public static final String COLL = "v1_print_driver_update_info";

    /**
     * 打印机型号
     */
    @TableField("printer_model")
    private String printerModel;

    /**
     * 固件名称
     */
    @TableField("version_name")
    private String             versionName;

    /**
     * 版本号编码
     */
    @TableField("version_code")
    private String             versionCode;

    /**
     * 下载地址
     */
    @TableField("url")
    private String             url;

    /**
     * 参数
     */
    @TableField("param")
    private String             param;

    /**
     * 版本描述
     */
    @TableField("remark")
    private String             remark;

    /**
     * 标题
     */
    @TableField("title")
    private String             title;

    /**
     * 是否强制升级
     */
    @TableField("need_force_update")
    private int                needForceUpdate;

    /**
     * 是否首页显示
     */
    @TableField("need_index_show")
    private int                needIndexShow;

    /**
     * 是否生效，不生效的不返回到前端
     */
    @TableField("used_flag")
    private int                usedFlag;

    /**
     * 前置升级的固件id
     */
    @TableField("pre_driver_id")
    private String             preDriverId;

    /**
     * 0: 不用，1：要
     */
    @TableField("need_check_power")
    private int                needCheckPower;

    /**
     * 符合升级的电量,0--100
     */
    @TableField("power_value")
    private int                powerValue;

    /**
     * md5码
     */
    @TableField("md5")
    private String             md5;

    /**
     * 是否重要基础版本  0:否， 1：是
     */
    @TableField("is_base_version")
    private int                isBaseVersion;

    /**
     * 增加流程状态
     */
    @TableField("follow_status")
    private int                followStatus;

    public String getVersionName() {
        return versionName;
    }

    public void setVersionName(String versionName) {
        this.versionName = versionName;
    }

    public String getVersionCode() {
        return versionCode;
    }

    public void setVersionCode(String versionCode) {
        this.versionCode = versionCode;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getParam() {
        return param;
    }

    public void setParam(String param) {
        this.param = param;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public int getNeedForceUpdate() {
        return needForceUpdate;
    }

    public void setNeedForceUpdate(int needForceUpdate) {
        this.needForceUpdate = needForceUpdate;
    }

    public int getNeedIndexShow() {
        return needIndexShow;
    }

    public void setNeedIndexShow(int needIndexShow) {
        this.needIndexShow = needIndexShow;
    }

    public String getPrinterModel() {
        return printerModel;
    }

    public void setPrinterModel(String printerModel) {
        this.printerModel = printerModel;
    }

    public int getUsedFlag() {
        return usedFlag;
    }

    public void setUsedFlag(int usedFlag) {
        this.usedFlag = usedFlag;
    }

    public String getPreDriverId() {
        return preDriverId;
    }

    public void setPreDriverId(String preDriverId) {
        this.preDriverId = preDriverId;
    }

    public int getNeedCheckPower() {
        return needCheckPower;
    }

    public void setNeedCheckPower(int needCheckPower) {
        this.needCheckPower = needCheckPower;
    }

    public int getPowerValue() {
        return powerValue;
    }

    public void setPowerValue(int powerValue) {
        this.powerValue = powerValue;
    }

    public String getMd5() {
        return md5;
    }

    public void setMd5(String md5) {
        this.md5 = md5;
    }

    public int getIsBaseVersion() {
        return isBaseVersion;
    }

    public void setIsBaseVersion(int isBaseVersion) {
        this.isBaseVersion = isBaseVersion;
    }

    public int getFollowStatus() {
        return followStatus;
    }

    public void setFollowStatus(int followStatus) {
        this.followStatus = followStatus;
    }
}
