package net.snaptag.system.business.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * Author：Chenjy
 * Date:2025/8/21
 * Description:
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("v1_logo_child_kind")
public class LogoChildKind implements Serializable {
    /**
     * logo子类Id，主键，自增
     */
    @TableId(value = "logoChildKindId", type = IdType.ASSIGN_ID)
    private Integer logoChildKindId;

    /**
     * logo子类名称
     */
    @TableField("logoChildKindName")
    private String logoChildKindName;

    /**
     * 英文名称
     */
    @TableField("englishName")
    private String englishName;

    /**
     * 繁体名称
     */
    @TableField("traditionalName")
    private String traditionalName;

    /**
     * 韩文名称
     */
    @TableField("koreanName")
    private String koreanName;

    /**
     * logo_kind id，关联到logo分类表
     */
    @TableField("logoKindId")
    private Integer logoKindId;

    /**
     * 操作人id
     */
    @TableField("sysUserId")
    private Integer sysUserId;

    /**
     * 创建日期
     */
    @TableField("createTime")
    private Date createTime;

    /**
     * 版本号区分数据-兼容老版本，默认值1
     */
    @TableField("version")
    private Integer version = 1;
}
