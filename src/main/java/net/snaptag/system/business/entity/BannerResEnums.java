package net.snaptag.system.business.entity;
import java.util.LinkedHashMap;

/**
 * banner资源key
 */
public enum BannerResEnums {
    P686x280("686x280", "社区顶部banner");
    private final String                               key;
    private final String                               desc;
    private static final LinkedHashMap<String, String> map;
    static {
        map = new LinkedHashMap<>();
        for (BannerResEnums bannerResEnums : BannerResEnums.values()) {
            map.put(bannerResEnums.getKey(), bannerResEnums.getDesc());
        } 
    }

    BannerResEnums(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public String getKey() {
        return key;
    }

    public String getDesc() {
        return desc;
    }

    public static LinkedHashMap<String, String> getMap() {
        return map;
    }
}
