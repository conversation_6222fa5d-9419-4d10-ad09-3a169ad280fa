package net.snaptag.system.business.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import net.snaptag.system.common.BaseEntity;

/**
 * 素材库资源
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("v1_material_resource")
public class MaterialResource extends BaseEntity {
    private static final long serialVersionUID = 1L;
    public static final String COLL = "v1_material_resource";
    public static final String M_ID_FIELD = "m_id";
    public static final String LOCALE_CODE_FIELD = "locale_code";

    /**
     * 素材库ID
     */
    @TableField("m_id")
    private String mId;

    /**
     * 纸张长度
     */
    @TableField("length")
    private int length;

    /**
     * 资源集合 - JSON格式字符串
     * 例如: {"resUrl": {"pic": "/api/img/gam/snapTag/2024/20241118/48dd57a5-3314-4b79-3953-62c534a8ea6e.png", "size": 0, "width": 674, "height": 707}}
     */
    @TableField(value = "res_map")
    private String resMap;

    /**
     * 主类型
     */
    @TableField("type")
    private int type;

    /**
     * 副类型
     */
    @TableField("sub_type")
    private int subType;

    /**
     * 0：竖向；1：横向
     */
    @TableField("place_type")
    private int placeType;

    /**
     * 是否标识new
     */
    @TableField("is_new")
    private int isNew;

    /**
     * 显示new的截止日期
     */
    @TableField("show_new_time_limit")
    private String showNewTimeLimit;

    /**
     * 标签
     */
    @TableField("label")
    private String label;

    /**
     * 排序字段
     */
    @TableField("sort_num")
    private int sortNum;

    /**
     * 语种
     */
    @TableField("locale_code")
    private String localeCode;


}
