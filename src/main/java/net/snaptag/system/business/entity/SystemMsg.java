package net.snaptag.system.business.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import net.snaptag.system.common.BaseEntity;

import java.util.Date;

/**
 * 系统消息表
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("v1_system_msg")
public class SystemMsg extends BaseEntity {
    private static final long serialVersionUID = 1L;
    public static final String COLL = "v1_system_msg";
    public static final String START_DATE_FIELD = "start_date";
    public static final String END_DATE_FIELD = "end_date";

    /**
     * 消息标题
     */
    @TableField("msg_title")
    private String msgTitle;

    /**
     * 消息内容
     */
    @TableField("msg_content")
    private String msgContent;

    /**
     * 消息主类型
     */
    @TableField("msg_type")
    private int msgType;

    /**
     * 消息图片 - JSON格式字符串
     * 例如: {"key1": "value1", "key2": "value2"}
     */
    @TableField(value = "pic_map")
    private String picMap;

    /**
     * 消息副类型 跳转类型 100表示无跳转 101表示h5页 102表示社区动态 103表示申请好友 104表示好友纸条 200官方消息
     */
    @TableField("msg_sub_type")
    private int msgSubType;

    /**
     * 消息跳转值
     */
    @TableField("jump_val")
    private String jumpVal;

    /**
     * 开始时间
     */
    @TableField("start_date")
    private Date startDate;

    /**
     * 结束时间
     */
    @TableField("end_date")
    private Date endDate;

    /**
     * 版本号后显示
     */
    @TableField("version")
    private String version;

    /**
     * 自定义组装的json格式，用于android前端调用
     */
    @TableField("param_android")
    private String paramAndroid;

    /**
     * 自定义组装的json格式，用于ios前端调用
     */
    @TableField("param_ios")
    private String paramIos;

}
