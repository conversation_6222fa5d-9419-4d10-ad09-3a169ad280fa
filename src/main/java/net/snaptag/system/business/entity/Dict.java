package net.snaptag.system.business.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import net.snaptag.system.common.BaseEntity;

/**
 * <AUTHOR>
 * @date ：Created in 2023/8/24 9:16
 * @description：字典项
 * @modified By：
 * @version: $
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("v1_dict")
public class Dict extends BaseEntity {

    /**
     * 名称
     */
    @TableField("name")
    private String name;

    /**
     * 类型
     */
    @TableField("type")
    private String type;

    /**
     * 标签
     */
    @TableField("label")
    private String label;

    /**
     * 值
     */
    @TableField("value")
    private String value;

    /**
     * i18n的code值
     */
    @TableField("locale_code")
    private String localeCode;

    /**
     * 图标地址
     */
    @TableField("icon")
    private String icon;

    /**
     * 排序
     */
    @TableField("sort_num")
    private int sortNum;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

}
