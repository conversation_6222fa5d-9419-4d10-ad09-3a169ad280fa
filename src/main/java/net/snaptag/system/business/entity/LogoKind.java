package net.snaptag.system.business.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * Author：Chenjy
 * Date:2025/8/21
 * Description:
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("v1_logo_kind")
public class LogoKind implements Serializable {
    /**
     * logo主分类Id，主键，自增
     */
    @TableId(value = "logoKindId", type = IdType.ASSIGN_ID)
    private Integer logoKindId;

    /**
     * logo分类名称
     */
    @TableField("logoKindName")
    private String logoKindName;

    /**
     * 英文名称
     */
    @TableField("englishName")
    private String englishName;

    /**
     * 繁体名称
     */
    @TableField("traditionalName")
    private String traditionalName;

    /**
     * 韩文名称
     */
    @TableField("koreanName")
    private String koreanName;

    /**
     * 操作人id
     */
    @TableField("sysUserId")
    private Integer sysUserId;

    /**
     * 创建日期
     */
    @TableField("createTime")
    private Date createTime;

    /**
     * 版本号区分数据-兼容老版本，默认值1
     */
    @TableField("version")
    private Integer version = 1;

    /**
     * 俄语名称
     */
    @TableField("russianName")
    private String russianName;

    /**
     * 德语名称
     */
    @TableField("germanyName")
    private String germanyName;

    /**
     * 法语名称
     */
    @TableField("frenchName")
    private String frenchName;

    /**
     * 西班牙语名称
     */
    @TableField("spainName")
    private String spainName;

    /**
     * 意大利语名称
     */
    @TableField("italyName")
    private String italyName;
}
