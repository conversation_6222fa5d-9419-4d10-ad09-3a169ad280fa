package net.snaptag.system.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import net.snaptag.system.business.entity.Banner;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * Banner Mapper接口
 */
@Mapper
public interface BannerMapper extends BaseMapper<Banner> {

    /**
     * 分页查询Banner列表
     */
    @Select("<script>" +
            "SELECT * FROM v1_banner WHERE status = #{status}" +
            "<if test='name != null and name != \"\"'> AND name LIKE CONCAT('%', #{name}, '%')</if>" +
            "<if test='column != null and column != \"\"'> AND `column` = #{column}</if>" +
            "<if test='localeCode != null and localeCode != \"\"'> AND locale_code LIKE CONCAT('%', #{localeCode}, '%')</if>" +
            " ORDER BY createtime DESC" +
            "</script>")
    IPage<Banner> findBannerPage(Page<Banner> page,
                                @Param("name") String name,
                                @Param("column") String column,
                                @Param("localeCode") String localeCode,
                                @Param("status") String status);

    /**
     * 查询所有Banner列表
     */
    @Select("SELECT * FROM v1_banner WHERE status = #{status} ORDER BY createtime DESC")
    List<Banner> findAllList(@Param("status") String status);

    /**
     * 按排序字段查询Banner列表
     */
    @Select("SELECT * FROM v1_banner WHERE status = #{status} ORDER BY sort ASC")
    List<Banner> findListSortBySort(@Param("status") String status);
}
