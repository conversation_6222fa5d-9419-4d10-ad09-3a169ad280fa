package net.snaptag.system.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import net.snaptag.system.business.entity.TemplateDrafts;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * TemplateDrafts Mapper接口
 */
@Mapper
public interface TemplateDraftsMapper extends BaseMapper<TemplateDrafts> {

    /**
     * 分页查询TemplateDrafts列表
     */
    @Select("SELECT * FROM v1_template_drafts WHERE status = #{status} ORDER BY createtime DESC")
    IPage<TemplateDrafts> findPage(Page<TemplateDrafts> page, @Param("status") String status);

    /**
     * 查询所有TemplateDrafts列表
     */
    @Select("SELECT * FROM v1_template_drafts WHERE status = #{status} ORDER BY createtime DESC")
    List<TemplateDrafts> findAllList(@Param("status") String status);

    /**
     * 根据用户ID查询模板草稿
     */
    @Select("SELECT * FROM v1_template_drafts WHERE user_id = #{userId} AND status = #{status} ORDER BY createtime DESC")
    List<TemplateDrafts> findByUserId(@Param("userId") String userId, @Param("status") String status);
}
