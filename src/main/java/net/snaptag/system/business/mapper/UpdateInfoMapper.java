package net.snaptag.system.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import net.snaptag.system.business.entity.UpdateInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * UpdateInfo Mapper接口
 */
@Mapper
public interface UpdateInfoMapper extends BaseMapper<UpdateInfo> {

    /**
     * 分页查询UpdateInfo列表
     */
    @Select("SELECT * FROM v1_update_info WHERE status = #{status} ORDER BY createtime DESC")
    IPage<UpdateInfo> findPage(Page<UpdateInfo> page, @Param("status") String status);

    /**
     * 查询所有UpdateInfo列表
     */
    @Select("SELECT * FROM v1_update_info WHERE status = #{status} ORDER BY createtime DESC")
    List<UpdateInfo> findAllList(@Param("status") String status);

    /**
     * 根据版本号查询更新信息
     */
    @Select("SELECT * FROM v1_update_info WHERE version = #{version} AND status = #{status}")
    UpdateInfo findByVersion(@Param("version") String version, @Param("status") String status);
}
