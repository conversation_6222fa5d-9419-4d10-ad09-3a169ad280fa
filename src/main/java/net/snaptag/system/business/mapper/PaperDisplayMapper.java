package net.snaptag.system.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import net.snaptag.system.business.entity.PaperDisplay;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * PaperDisplay Mapper接口
 */
@Mapper
public interface PaperDisplayMapper extends BaseMapper<PaperDisplay> {

    /**
     * 分页查询PaperDisplay列表
     */
    @Select("SELECT * FROM v1_paper_display WHERE status = #{status} ORDER BY sort_num ASC, createtime DESC")
    IPage<PaperDisplay> findPage(Page<PaperDisplay> page, @Param("status") String status);

    /**
     * 查询所有PaperDisplay列表
     */
    @Select("SELECT * FROM v1_paper_display WHERE status = #{status} ORDER BY sort_num ASC, createtime DESC")
    List<PaperDisplay> findAllList(@Param("status") String status);

    /**
     * 查询默认纸张
     */
    @Select("SELECT * FROM v1_paper_display WHERE status = #{status} AND is_default = 1 LIMIT 1")
    PaperDisplay findDefault(@Param("status") String status);
}
