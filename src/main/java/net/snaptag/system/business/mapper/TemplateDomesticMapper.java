package net.snaptag.system.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import net.snaptag.system.business.entity.TemplateDomestic;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * TemplateDomestic Mapper接口
 */
@Mapper
public interface TemplateDomesticMapper extends BaseMapper<TemplateDomestic> {

    /**
     * 分页查询家用行业模板列表
     */
    @Select("SELECT * FROM v1_template_domestic WHERE status = #{status} ORDER BY createtime DESC")
    IPage<TemplateDomestic> findPage(Page<TemplateDomestic> page, @Param("status") String status);

    /**
     * 查询所有家用行业模板列表
     */
    @Select("SELECT * FROM v1_template_domestic WHERE status = #{status} ORDER BY createtime DESC")
    List<TemplateDomestic> findAllList(@Param("status") String status);

    /**
     * 根据用户ID查询模板列表
     */
    @Select("SELECT * FROM v1_template_domestic WHERE status = #{status} AND userId = #{userId} ORDER BY createtime DESC")
    List<TemplateDomestic> findByUserId(@Param("status") String status, @Param("userId") Integer userId);

    /**
     * 根据模板类型查询列表
     */
    @Select("SELECT * FROM v1_template_domestic WHERE status = #{status} AND type = #{type} ORDER BY createtime DESC")
    List<TemplateDomestic> findByType(@Param("status") String status, @Param("type") Integer type);

    /**
     * 根据机器类型查询模板列表
     */
    @Select("SELECT * FROM v1_template_domestic WHERE status = #{status} AND machineType = #{machineType} ORDER BY createtime DESC")
    List<TemplateDomestic> findByMachineType(@Param("status") String status, @Param("machineType") Integer machineType);

    /**
     * 根据纸张类型查询模板列表
     */
    @Select("SELECT * FROM v1_template_domestic WHERE status = #{status} AND paperType = #{paperType} ORDER BY createtime DESC")
    List<TemplateDomestic> findByPaperType(@Param("status") String status, @Param("paperType") Integer paperType);

    /**
     * 根据分组ID查询模板列表
     */
    @Select("SELECT * FROM v1_template_domestic WHERE status = #{status} AND groupId = #{groupId} ORDER BY createtime DESC")
    List<TemplateDomestic> findByGroupId(@Param("status") String status, @Param("groupId") String groupId);

    /**
     * 根据模板名称模糊查询
     */
    @Select("SELECT * FROM v1_template_domestic WHERE status = #{status} AND name LIKE CONCAT('%', #{name}, '%') ORDER BY createtime DESC")
    List<TemplateDomestic> findByNameLike(@Param("status") String status, @Param("name") String name);

    /**
     * 统计用户模板数量
     */
    @Select("SELECT COUNT(*) FROM v1_template_domestic WHERE status = #{status} AND userId = #{userId}")
    Long countByUserId(@Param("status") String status, @Param("userId") Integer userId);

    /**
     * 统计指定类型模板数量
     */
    @Select("SELECT COUNT(*) FROM v1_template_domestic WHERE status = #{status} AND type = #{type}")
    Long countByType(@Param("status") String status, @Param("type") Integer type);
}
