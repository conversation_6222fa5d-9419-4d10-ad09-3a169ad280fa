package net.snaptag.system.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.snaptag.system.business.entity.FontKindLang;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 字体分类语言 Mapper 接口
 *
 * <AUTHOR>
 * @since 2025-01-16
 */
@Mapper
public interface FontKindLangMapper extends BaseMapper<FontKindLang> {

    /**
     * 根据语言查询字体分类
     *
     * @param fontLang 语言标识
     * @return 字体分类列表
     */
    @Select("SELECT DISTINCT fontKind FROM v1_font_kind_lang WHERE fontLang = #{fontLang}")
    List<String> selectFontKindsByLang(@Param("fontLang") String fontLang);

    /**
     * 根据分类查询支持的语言
     *
     * @param fontKind 字体分类
     * @return 语言列表
     */
    @Select("SELECT DISTINCT fontLang FROM v1_font_kind_lang WHERE fontKind = #{fontKind}")
    List<String> selectLangsByFontKind(@Param("fontKind") String fontKind);

    /**
     * 检查分类和语言组合是否存在
     *
     * @param fontKind 字体分类
     * @param fontLang 语言标识
     * @return 是否存在
     */
    @Select("SELECT COUNT(*) > 0 FROM v1_font_kind_lang WHERE fontKind = #{fontKind} AND fontLang = #{fontLang}")
    boolean existsKindLang(@Param("fontKind") String fontKind, @Param("fontLang") String fontLang);

    /**
     * 根据分类删除语言关系
     *
     * @param fontKind 字体分类
     * @return 删除数量
     */
    @Delete("DELETE FROM v1_font_kind_lang WHERE fontKind = #{fontKind}")
    int deleteByFontKind(@Param("fontKind") String fontKind);
}
