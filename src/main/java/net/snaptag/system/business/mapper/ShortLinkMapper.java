package net.snaptag.system.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import net.snaptag.system.business.entity.ShortLink;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * ShortLink Mapper接口
 */
@Mapper
public interface ShortLinkMapper extends BaseMapper<ShortLink> {

    /**
     * 分页查询ShortLink列表
     */
    @Select("SELECT * FROM v1_short_link WHERE status = #{status} ORDER BY createtime DESC")
    IPage<ShortLink> findPage(Page<ShortLink> page, @Param("status") String status);

    /**
     * 查询所有ShortLink列表
     */
    @Select("SELECT * FROM v1_short_link WHERE status = #{status} ORDER BY createtime DESC")
    List<ShortLink> findAllList(@Param("status") String status);

    /**
     * 根据短链接码查询
     */
    @Select("SELECT * FROM v1_short_link WHERE short_code = #{shortCode} AND status = #{status}")
    ShortLink findByShortCode(@Param("shortCode") String shortCode, @Param("status") String status);
}
