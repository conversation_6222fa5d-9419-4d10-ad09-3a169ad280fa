package net.snaptag.system.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import net.snaptag.system.business.entity.LogoChildKind;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * v1_logo_child_kind Mapper接口
 */
@Mapper
public interface LogoChildKindMapper extends BaseMapper<LogoChildKind> {

    /**
     * 分页查询v1_logo_child_kind列表
     */
    @Select("SELECT * FROM v1_logo_child_kind  ORDER BY createtime DESC")
    IPage<LogoChildKind> findPage(Page<LogoChildKind> page);

    /**
     * 查询所有v1_logo_child_kind列表
     */
    @Select("SELECT * FROM v1_logo_child_kind ORDER BY createtime DESC")
    List<LogoChildKind> findAllList();
}
