package net.snaptag.system.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import net.snaptag.system.business.entity.TemplateDraftsLanguageConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * TemplateDraftsLanguageConfig Mapper接口
 */
@Mapper
public interface TemplateDraftsLanguageConfigMapper extends BaseMapper<TemplateDraftsLanguageConfig> {

    /**
     * 分页查询TemplateDraftsLanguageConfig列表
     */
    @Select("SELECT * FROM v1_template_drafts_language_config WHERE status = #{status} ORDER BY createtime DESC")
    IPage<TemplateDraftsLanguageConfig> findPage(Page<TemplateDraftsLanguageConfig> page, @Param("status") String status);

    /**
     * 查询所有TemplateDraftsLanguageConfig列表
     */
    @Select("SELECT * FROM v1_template_drafts_language_config WHERE status = #{status} ORDER BY createtime DESC")
    List<TemplateDraftsLanguageConfig> findAllList(@Param("status") String status);
}
