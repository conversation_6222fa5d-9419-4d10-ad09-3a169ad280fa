package net.snaptag.system.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import net.snaptag.system.business.entity.PrintPaperInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * PrintPaperInfo Mapper接口
 */
@Mapper
public interface PrintPaperInfoMapper extends BaseMapper<PrintPaperInfo> {

    /**
     * 分页查询PrintPaperInfo列表
     */
    @Select("SELECT * FROM v1_print_paper_info WHERE status = #{status} ORDER BY createtime DESC")
    IPage<PrintPaperInfo> findPage(Page<PrintPaperInfo> page, @Param("status") String status);

    /**
     * 查询所有PrintPaperInfo列表
     */
    @Select("SELECT * FROM v1_print_paper_info WHERE status = #{status} ORDER BY createtime DESC")
    List<PrintPaperInfo> findAllList(@Param("status") String status);
}
