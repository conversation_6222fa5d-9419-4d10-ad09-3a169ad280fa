package net.snaptag.system.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import net.snaptag.system.business.entity.TemplateBorder;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 模板边框 Mapper 接口
 *
 * <AUTHOR>
 * @since 2025-08-20
 */
@Mapper
public interface TemplateBorderMapper extends BaseMapper<TemplateBorder> {

    /**
     * 查询所有边框列表（包含分类信息）
     *
     * @return 边框列表
     */
    @Select("SELECT b.borderId, b.borderKindId, b.headerImgUrl, b.fillImgUrl, " +
            "b.footerImgUrl, b.thumbUrl, b.createTime, " +
            "k.borderKindName as groupName " +
            "FROM v1_template_border b " +
            "LEFT JOIN v1_template_border_kind k ON b.borderKindId = k.borderKindId " +
            "ORDER BY b.createTime DESC")
    List<TemplateBorder> selectAllBorders();

    /**
     * 分页查询边框列表
     *
     * @param page 分页参数
     * @return 边框分页列表
     */
    @Select("SELECT b.borderId, b.borderKindId, b.headerImgUrl, b.fillImgUrl, " +
            "b.footerImgUrl, b.thumbUrl, b.createTime, " +
            "k.borderKindName as groupName " +
            "FROM v1_template_border b " +
            "LEFT JOIN v1_template_border_kind k ON b.borderKindId = k.borderKindId " +
            "ORDER BY b.createTime DESC")
    IPage<TemplateBorder> selectBorderPage(Page<TemplateBorder> page);

    /**
     * 根据分类ID分页查询边框列表
     *
     * @param page 分页参数
     * @param borderKindId 分类ID
     * @return 边框分页列表
     */
    @Select("SELECT b.borderId, b.borderKindId, b.headerImgUrl, b.fillImgUrl, " +
            "b.footerImgUrl, b.thumbUrl, b.createTime, " +
            "k.borderKindName as groupName " +
            "FROM v1_template_border b " +
            "LEFT JOIN v1_template_border_kind k ON b.borderKindId = k.borderKindId " +
            "WHERE b.borderKindId = #{borderKindId} " +
            "ORDER BY b.createTime DESC")
    IPage<TemplateBorder> selectBorderPageByKind(Page<TemplateBorder> page, @Param("borderKindId") String borderKindId);

    /**
     * 动态分页查询边框列表（支持可选的分类过滤）
     *
     * @param page 分页参数
     * @param borderKindId 分类ID（可选）
     * @return 边框分页列表
     */
    @Select("<script>" +
            "SELECT b.borderId, b.borderKindId, b.headerImgUrl, b.fillImgUrl, " +
            "b.footerImgUrl, b.thumbUrl, b.createTime, " +
            "k.borderKindName as groupName " +
            "FROM v1_template_border b " +
            "LEFT JOIN v1_template_border_kind k ON b.borderKindId = k.borderKindId " +
            "WHERE 1=1 " +
            "<if test='borderKindId != null and borderKindId != \"\"'>" +
            "  AND b.borderKindId = #{borderKindId}" +
            "</if>" +
            "ORDER BY b.createTime DESC" +
            "</script>")
    IPage<TemplateBorder> selectBorderPageDynamic(Page<TemplateBorder> page, @Param("borderKindId") String borderKindId);

    /**
     * 根据分类ID查询边框列表
     *
     * @param borderKindId 分类ID
     * @return 边框列表
     */
    @Select("SELECT b.borderId, b.borderKindId, b.headerImgUrl, b.fillImgUrl, " +
            "b.footerImgUrl, b.thumbUrl, b.createTime, " +
            "k.borderKindName as groupName " +
            "FROM v1_template_border b " +
            "LEFT JOIN v1_template_border_kind k ON b.borderKindId = k.borderKindId " +
            "WHERE b.borderKindId = #{borderKindId} " +
            "ORDER BY b.createTime DESC")
    List<TemplateBorder> selectBordersByKind(@Param("borderKindId") String borderKindId);

    /**
     * 根据ID查询边框详情（包含完整分类信息）
     *
     * @param borderId 边框ID
     * @return 边框详情
     */
    @Select("SELECT b.*, " +
            "k.borderKindName, k.englishName, k.traditionalName, k.koreanName, " +
            "k.russianName, k.frenchName, k.spanishName, k.germanyName, k.italyName " +
            "FROM v1_template_border b " +
            "LEFT JOIN v1_template_border_kind k ON b.borderKindId = k.borderKindId " +
            "WHERE b.borderId = #{borderId}")
    TemplateBorder selectBorderWithKind(@Param("borderId") String borderId);

    /**
     * 统计边框数量
     *
     * @return 边框数量
     */
    @Select("SELECT COUNT(*) FROM v1_template_border")
    Long countBorders();

    /**
     * 根据分类统计边框数量
     *
     * @param borderKindId 分类ID
     * @return 边框数量
     */
    @Select("SELECT COUNT(*) FROM v1_template_border WHERE borderKindId = #{borderKindId}")
    Long countBordersByKind(@Param("borderKindId") String borderKindId);

    /**
     * 检查边框ID是否存在
     *
     * @param borderId 边框ID
     * @return 是否存在
     */
    @Select("SELECT COUNT(*) > 0 FROM v1_template_border WHERE borderId = #{borderId}")
    boolean existsBorderId(@Param("borderId") String borderId);
}
