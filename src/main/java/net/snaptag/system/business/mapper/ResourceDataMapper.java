package net.snaptag.system.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import net.snaptag.system.business.entity.ResourceData;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * ResourceData Mapper接口
 */
@Mapper
public interface ResourceDataMapper extends BaseMapper<ResourceData> {

    /**
     * 分页查询ResourceData列表
     */
    @Select("SELECT * FROM v1_resource_data WHERE status = #{status} ORDER BY createtime DESC")
    IPage<ResourceData> findPage(Page<ResourceData> page, @Param("status") String status);

    /**
     * 查询所有ResourceData列表
     */
    @Select("SELECT * FROM v1_resource_data WHERE status = #{status} ORDER BY createtime DESC")
    List<ResourceData> findAllList(@Param("status") String status);
}
