package net.snaptag.system.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import net.snaptag.system.business.entity.Drafts;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * Drafts Mapper接口
 */
@Mapper
public interface DraftsMapper extends BaseMapper<Drafts> {

    /**
     * 分页查询Drafts列表
     */
    @Select("SELECT * FROM v1_drafts WHERE status = #{status} ORDER BY createtime DESC")
    IPage<Drafts> findPage(Page<Drafts> page, @Param("status") String status);

    /**
     * 查询所有Drafts列表
     */
    @Select("SELECT * FROM v1_drafts WHERE status = #{status} ORDER BY createtime DESC")
    List<Drafts> findAllList(@Param("status") String status);
}
