package net.snaptag.system.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import net.snaptag.system.business.entity.MachineSettings;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * v1_machine_settings Mapper接口
 */
@Mapper
public interface MachineSettingsMapper extends BaseMapper<MachineSettings> {

    /**
     * 分页查询v1_machine_settings列表
     */
    @Select("SELECT * FROM v1_v1_machine_settings WHERE status = #{status} ORDER BY createtime DESC")
    IPage<MachineSettings> findPage(Page<MachineSettings> page, @Param("status") String status);

    /**
     * 查询所有v1_machine_settings列表
     */
    @Select("SELECT * FROM v1_v1_machine_settings WHERE status = #{status} ORDER BY createtime DESC")
    List<MachineSettings> findAllList(@Param("status") String status);
}
