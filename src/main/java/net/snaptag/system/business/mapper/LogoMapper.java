package net.snaptag.system.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import net.snaptag.system.business.entity.Logo;
import net.snaptag.system.business.vo.LogoPageVO;
import net.snaptag.system.business.vo.LogoVO;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * v1_logo Mapper接口
 */
@Mapper
public interface LogoMapper extends BaseMapper<Logo> {

    /**
     * 分页查询v1_logo列表
     */
    @Select({
            "<script>",
            "SELECT g.logoId, g.logoImg,",
            "<choose>",
            "   <when test='lang == 1'>k.logoKindName as logoKindName</when>",
            "   <when test='lang == 2'>k.englishName as logoKindName</when>",
            "   <when test='lang == 3'>k.traditionalName as logoKindName</when>",
            "   <when test='lang == 4'>k.koreanName as logoKindName</when>",
            "   <when test='lang == 5'>k.russianName as logoKindName</when>",
            "   <otherwise>k.logoKindName as logoKindName</otherwise>",
            "</choose>",
            "FROM v1_logo g",
            "INNER JOIN v1_logo_kind k ON k.logoKindId = g.logoKindId",
            "WHERE g.version = 2",
            "<if test='kindId != null and kindId != \"\"'>",
            "   AND g.logoKindId = #{kindId}",
            "</if>",
            "</script>"
    })
    Page<Logo> findPage(Page<Logo> page, @Param("lang") Integer lang, @Param("kindId") String kindId);

    // 用注解编写多表查询SQL，并自定义字段映射
    @Select({
            "<script>",
            "SELECT g.logoId, g.logoImg, ${lang} as logoKindName ",
            "FROM v1_logo g",
            "INNER JOIN v1_logo_kind k ON k.logoKindId = g.logoKindId",
            "WHERE g.version = 2",
            "<if test='kindId != null and kindId != \"\"'>AND g.logoKindId = #{kindId}</if>",
            "</script>"
    })
    // 自定义字段映射（实体类属性 -> 数据库字段）
    @Results({
            @Result(column = "logoId", property = "logoId"),
            @Result(column = "logoImg", property = "logoImg"),
            @Result(column = "logoKindName", property = "logoKindName")
    })
    IPage<LogoPageVO> selectLogoVOPage(
            Page<LogoPageVO> page,  // 分页参数（必须放在第一个位置）
            @Param("lang") String lang,
            @Param("kindId") String kindId
    );

    /**
     * 查询所有v1_logo列表
     */
    @Select("SELECT * FROM v1_logo ORDER BY createtime DESC")
    List<Logo> findAllList();


}
