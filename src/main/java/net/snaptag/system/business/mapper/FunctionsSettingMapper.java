package net.snaptag.system.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import net.snaptag.system.business.entity.FunctionsSetting;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * FunctionsSetting Mapper接口
 */
@Mapper
public interface FunctionsSettingMapper extends BaseMapper<FunctionsSetting> {

    /**
     * 分页查询FunctionsSetting列表
     */
    @Select("SELECT * FROM v1_functions_setting WHERE status = #{status} ORDER BY createtime DESC")
    IPage<FunctionsSetting> findPage(Page<FunctionsSetting> page, @Param("status") String status);

    /**
     * 查询所有FunctionsSetting列表
     */
    @Select("SELECT * FROM v1_functions_setting WHERE status = #{status} ORDER BY createtime DESC")
    List<FunctionsSetting> findAllList(@Param("status") String status);
}
