package net.snaptag.system.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import net.snaptag.system.business.entity.MsgCenter;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * MsgCenter Mapper接口
 */
@Mapper
public interface MsgCenterMapper extends BaseMapper<MsgCenter> {

    /**
     * 分页查询MsgCenter列表
     */
    @Select("SELECT * FROM v1_msg_center WHERE status = #{status} ORDER BY createtime DESC")
    IPage<MsgCenter> findPage(Page<MsgCenter> page, @Param("status") String status);

    /**
     * 查询所有MsgCenter列表
     */
    @Select("SELECT * FROM v1_msg_center WHERE status = #{status} ORDER BY createtime DESC")
    List<MsgCenter> findAllList(@Param("status") String status);

    /**
     * 根据用户ID查询消息列表
     */
    @Select("SELECT * FROM v1_msg_center WHERE user_id = #{userId} AND status = #{status} ORDER BY createtime DESC")
    List<MsgCenter> findByUserId(@Param("userId") String userId, @Param("status") String status);

    /**
     * 根据用户ID和消息类型查询消息列表
     */
    @Select("SELECT * FROM v1_msg_center WHERE user_id = #{userId} AND msg_type = #{msgType} AND status = #{status} ORDER BY createtime DESC")
    List<MsgCenter> findByUserIdAndMsgType(@Param("userId") String userId, @Param("msgType") int msgType, @Param("status") String status);
}
