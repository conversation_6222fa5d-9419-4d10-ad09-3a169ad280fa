package net.snaptag.system.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.snaptag.system.business.entity.TemplateBorderKind;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 模板边框分类 Mapper 接口
 *
 * <AUTHOR>
 * @since 2025-08-20
 */
@Mapper
public interface TemplateBorderKindMapper extends BaseMapper<TemplateBorderKind> {

    /**
     * 查询所有边框分类（默认中文名称）
     *
     * @return 分类列表
     */
    @Select("SELECT borderKindId, borderKindName, borderKindName as displayName " +
            "FROM v1_template_border_kind " +
            "ORDER BY createTime ASC")
    List<TemplateBorderKind> selectAllKinds();

    /**
     * 根据语言类型查询边框分类
     *
     * @param language 语言类型 (1:中文, 2:英文, 3:繁体, 4:韩文, 5:俄语)
     * @return 分类列表
     */
    @Select("<script>" +
            "SELECT borderKindId, " +
            "CASE " +
            "  WHEN #{language} = 2 THEN IFNULL(NULLIF(englishName, ''), borderKindName) " +
            "  WHEN #{language} = 3 THEN IFNULL(NULLIF(traditionalName, ''), borderKindName) " +
            "  WHEN #{language} = 4 THEN IFNULL(NULLIF(koreanName, ''), borderKindName) " +
            "  WHEN #{language} = 5 THEN IFNULL(NULLIF(russianName, ''), borderKindName) " +
            "  ELSE borderKindName " +
            "END AS borderKindName, " +
            "CASE " +
            "  WHEN #{language} = 2 THEN IFNULL(NULLIF(englishName, ''), borderKindName) " +
            "  WHEN #{language} = 3 THEN IFNULL(NULLIF(traditionalName, ''), borderKindName) " +
            "  WHEN #{language} = 4 THEN IFNULL(NULLIF(koreanName, ''), borderKindName) " +
            "  WHEN #{language} = 5 THEN IFNULL(NULLIF(russianName, ''), borderKindName) " +
            "  ELSE borderKindName " +
            "END AS displayName " +
            "FROM v1_template_border_kind " +
            "ORDER BY createTime ASC" +
            "</script>")
    List<TemplateBorderKind> selectKindsByLanguage(@Param("language") Integer language);

    /**
     * 根据语言标识查询边框分类（返回完整信息）
     *
     * @param lang 语言标识 (english, traditional, korean, russian, french, spanish, germany, italy)
     * @return 分类列表
     */
    @Select("<script>" +
            "SELECT borderKindId, borderKindName, " +
            "englishName, traditionalName, koreanName, russianName, " +
            "frenchName, spanishName, germanyName, italyName, " +
            "CASE " +
            "  WHEN #{lang} = 'english' THEN IFNULL(NULLIF(englishName, ''), borderKindName) " +
            "  WHEN #{lang} = 'traditional' THEN IFNULL(NULLIF(traditionalName, ''), borderKindName) " +
            "  WHEN #{lang} = 'korean' THEN IFNULL(NULLIF(koreanName, ''), borderKindName) " +
            "  WHEN #{lang} = 'russian' THEN IFNULL(NULLIF(russianName, ''), borderKindName) " +
            "  WHEN #{lang} = 'french' THEN IFNULL(NULLIF(frenchName, ''), borderKindName) " +
            "  WHEN #{lang} = 'spanish' THEN IFNULL(NULLIF(spanishName, ''), borderKindName) " +
            "  WHEN #{lang} = 'germany' THEN IFNULL(NULLIF(germanyName, ''), borderKindName) " +
            "  WHEN #{lang} = 'italy' THEN IFNULL(NULLIF(italyName, ''), borderKindName) " +
            "  ELSE borderKindName " +
            "END AS displayName " +
            "FROM v1_template_border_kind " +
            "ORDER BY createTime ASC" +
            "</script>")
    List<TemplateBorderKind> selectKindsByLang(@Param("lang") String lang);

    /**
     * 检查语言字段是否存在
     *
     * @param columnName 字段名
     * @return 是否存在
     */
    @Select("SELECT COUNT(*) > 0 " +
            "FROM information_schema.columns " +
            "WHERE table_name = 'v1_template_border_kind' " +
            "AND column_name = #{columnName} " +
            "AND table_schema = DATABASE()")
    boolean isColumnExist(@Param("columnName") String columnName);

    /**
     * 根据ID查询完整的分类信息
     *
     * @param borderKindId 分类ID
     * @return 分类信息
     */
    @Select("SELECT * FROM v1_template_border_kind WHERE borderKindId = #{borderKindId}")
    TemplateBorderKind selectFullKindById(@Param("borderKindId") String borderKindId);

    /**
     * 统计分类数量
     *
     * @return 分类数量
     */
    @Select("SELECT COUNT(*) FROM v1_template_border_kind")
    Long countKinds();
}
