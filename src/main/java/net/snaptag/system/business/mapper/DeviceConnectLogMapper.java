package net.snaptag.system.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import net.snaptag.system.business.entity.DeviceConnectLog;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * DeviceConnectLog Mapper接口
 */
@Mapper
public interface DeviceConnectLogMapper extends BaseMapper<DeviceConnectLog> {

    /**
     * 分页查询DeviceConnectLog列表
     */
    @Select("SELECT * FROM v1_device_connect_log WHERE status = #{status} ORDER BY createtime DESC")
    IPage<DeviceConnectLog> findPage(Page<DeviceConnectLog> page, @Param("status") String status);

    /**
     * 查询所有DeviceConnectLog列表
     */
    @Select("SELECT * FROM v1_device_connect_log WHERE status = #{status} ORDER BY createtime DESC")
    List<DeviceConnectLog> findAllList(@Param("status") String status);
}
