package net.snaptag.system.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import net.snaptag.system.business.entity.PrintDriverUpdateInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * PrintDriverUpdateInfo Mapper接口
 */
@Mapper
public interface PrintDriverUpdateInfoMapper extends BaseMapper<PrintDriverUpdateInfo> {

    /**
     * 分页查询PrintDriverUpdateInfo列表
     */
    @Select("SELECT * FROM v1_print_driver_update_info WHERE status = #{status} ORDER BY createtime DESC")
    IPage<PrintDriverUpdateInfo> findPage(Page<PrintDriverUpdateInfo> page, @Param("status") String status);

    /**
     * 查询所有PrintDriverUpdateInfo列表
     */
    @Select("SELECT * FROM v1_print_driver_update_info WHERE status = #{status} ORDER BY createtime DESC")
    List<PrintDriverUpdateInfo> findAllList(@Param("status") String status);
}
