package net.snaptag.system.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import net.snaptag.system.business.entity.SystemHelpItem;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * SystemHelpItem Mapper接口
 */
@Mapper
public interface SystemHelpItemMapper extends BaseMapper<SystemHelpItem> {

    /**
     * 分页查询SystemHelpItem列表
     */
    @Select("SELECT * FROM v1_system_help_item WHERE status = #{status} ORDER BY sort_num ASC, createtime DESC")
    IPage<SystemHelpItem> findPage(Page<SystemHelpItem> page, @Param("status") String status);

    /**
     * 查询所有SystemHelpItem列表
     */
    @Select("SELECT * FROM v1_system_help_item WHERE status = #{status} ORDER BY sort_num ASC, createtime DESC")
    List<SystemHelpItem> findAllList(@Param("status") String status);
}
