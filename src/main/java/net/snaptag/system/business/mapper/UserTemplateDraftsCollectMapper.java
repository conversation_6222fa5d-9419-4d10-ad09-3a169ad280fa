package net.snaptag.system.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import net.snaptag.system.business.entity.UserTemplateDraftsCollect;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * UserTemplateDraftsCollect Mapper接口
 */
@Mapper
public interface UserTemplateDraftsCollectMapper extends BaseMapper<UserTemplateDraftsCollect> {

    /**
     * 分页查询UserTemplateDraftsCollect列表
     */
    @Select("SELECT * FROM v1_user_template_drafts_collect WHERE status = #{status} ORDER BY createtime DESC")
    IPage<UserTemplateDraftsCollect> findPage(Page<UserTemplateDraftsCollect> page, @Param("status") String status);

    /**
     * 查询所有UserTemplateDraftsCollect列表
     */
    @Select("SELECT * FROM v1_user_template_drafts_collect WHERE status = #{status} ORDER BY createtime DESC")
    List<UserTemplateDraftsCollect> findAllList(@Param("status") String status);

    /**
     * 根据用户ID查询收藏列表
     */
    @Select("SELECT * FROM v1_user_template_drafts_collect WHERE user_id = #{userId} AND status = #{status} ORDER BY createtime DESC")
    List<UserTemplateDraftsCollect> findByUserId(@Param("userId") String userId, @Param("status") String status);
}
