package net.snaptag.system.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import net.snaptag.system.business.entity.UserFeedback;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * UserFeedback Mapper接口
 */
@Mapper
public interface UserFeedbackMapper extends BaseMapper<UserFeedback> {

    /**
     * 分页查询UserFeedback列表
     */
    @Select("SELECT * FROM v1_user_feedback WHERE status = #{status} ORDER BY createtime DESC")
    IPage<UserFeedback> findPage(Page<UserFeedback> page, @Param("status") String status);

    /**
     * 查询所有UserFeedback列表
     */
    @Select("SELECT * FROM v1_user_feedback WHERE status = #{status} ORDER BY createtime DESC")
    List<UserFeedback> findAllList(@Param("status") String status);
}
