package net.snaptag.system.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import net.snaptag.system.business.entity.MsgColumn;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * MsgColumn Mapper接口
 */
@Mapper
public interface MsgColumnMapper extends BaseMapper<MsgColumn> {

    /**
     * 分页查询MsgColumn列表
     */
    @Select("SELECT * FROM v1_msg_column WHERE status = #{status} ORDER BY sort_num ASC, createtime DESC")
    IPage<MsgColumn> findPage(Page<MsgColumn> page, @Param("status") String status);

    /**
     * 查询所有MsgColumn列表
     */
    @Select("SELECT * FROM v1_msg_column WHERE status = #{status} ORDER BY sort_num ASC, createtime DESC")
    List<MsgColumn> findAllList(@Param("status") String status);

    /**
     * 查询启用的栏目列表
     */
    @Select("SELECT * FROM v1_msg_column WHERE status = #{status} AND is_enabled = 1 ORDER BY sort_num ASC")
    List<MsgColumn> findEnabledList(@Param("status") String status);
}
