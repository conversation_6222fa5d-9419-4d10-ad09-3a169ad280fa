package net.snaptag.system.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import net.snaptag.system.business.dto.FontQueryDTO;
import net.snaptag.system.business.entity.Font;
import net.snaptag.system.business.vo.FontListVO;
import net.snaptag.system.business.vo.FontVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 字体 Mapper 接口
 *
 * <AUTHOR>
 * @since 2025-01-16
 */
@Mapper
public interface FontMapper extends BaseMapper<Font> {

    // SQL片段常量 - 减少重复代码，使用IFNULL函数优化
    String LOCALIZED_FONT_NAME = "CASE " +
            "  WHEN #{locale} = 'en-US' THEN IFNULL(NULLIF(f.fontEnglishName, ''), f.fontName) " +
            "  WHEN #{locale} = 'zh-TW' THEN IFNULL(NULLIF(f.fontTraditionalName, ''), f.fontName) " +
            "  WHEN #{locale} = 'ko-KR' THEN IFNULL(NULLIF(f.fontKoreanName, ''), f.fontName) " +
            "  WHEN #{locale} = 'de-DE' THEN IFNULL(NULLIF(f.fontGermanyName, ''), f.fontName) " +
            "  WHEN #{locale} = 'it-IT' THEN IFNULL(NULLIF(f.fontItalyName, ''), f.fontName) " +
            "  WHEN #{locale} = 'es-ES' THEN IFNULL(NULLIF(f.fontSpainName, ''), f.fontName) " +
            "  WHEN #{locale} = 'fr-FR' THEN IFNULL(NULLIF(f.fontFranceName, ''), f.fontName) " +
            "  ELSE f.fontName " +
            "END AS fontName";

    String BASE_FONT_FIELDS = "f.fontId, " + LOCALIZED_FONT_NAME + ", f.fontKind, f.fontCover";

    String FROM_FONT_TABLE = "FROM v1_font f";

    String ACTIVE_FONT_CONDITION = "WHERE 1=1"; // v1_font表没有deleted字段

    /**
     * 分页查询字体列表（简化版本）
     *
     * @param page 分页参数
     * @param status 状态
     * @return 字体列表
     */
    @Select("SELECT fontId, fontName, fontKind, fontCover, createTime " +
            "FROM v1_font " +
            "ORDER BY createTime DESC")
    IPage<Font> selectFontPage(Page<Font> page, @Param("status") String status);

    /**
     * 根据ID查询字体详情
     *
     * @param fontId 字体ID
     * @param locale 语言标识
     * @return 字体详情
     */
    @Select("SELECT f.fontId, " +
            "CASE " +
            "  WHEN #{locale} = 'en-US' THEN IFNULL(NULLIF(f.fontEnglishName, ''), f.fontName) " +
            "  WHEN #{locale} = 'zh-TW' THEN IFNULL(NULLIF(f.fontTraditionalName, ''), f.fontName) " +
            "  WHEN #{locale} = 'ko-KR' THEN IFNULL(NULLIF(f.fontKoreanName, ''), f.fontName) " +
            "  WHEN #{locale} = 'de-DE' THEN IFNULL(NULLIF(f.fontGermanyName, ''), f.fontName) " +
            "  WHEN #{locale} = 'it-IT' THEN IFNULL(NULLIF(f.fontItalyName, ''), f.fontName) " +
            "  WHEN #{locale} = 'es-ES' THEN IFNULL(NULLIF(f.fontSpainName, ''), f.fontName) " +
            "  WHEN #{locale} = 'fr-FR' THEN IFNULL(NULLIF(f.fontFranceName, ''), f.fontName) " +
            "  ELSE f.fontName " +
            "END AS fontName, " +
            "f.fontEnglishName, f.fontTraditionalName, f.fontKoreanName, " +
            "f.fontGermanyName, f.fontItalyName, f.fontSpainName, f.fontFranceName, " +
            "f.fontKind, f.fontUrl, f.fontCover, f.fontValue, f.sysUserId, " +
            "f.createTime " +
            "FROM v1_font f " +
            "WHERE f.fontId = #{fontId}")
    FontVO selectFontDetail(@Param("fontId") String fontId, @Param("locale") String locale);

    /**
     * 根据语言查询字体列表
     *
     * @param locale 语言标识
     * @return 字体列表
     */
    @Select("SELECT f.fontId, " +
            "CASE " +
            "  WHEN #{locale} = 'en-US' THEN IFNULL(NULLIF(f.fontEnglishName, ''), f.fontName) " +
            "  WHEN #{locale} = 'zh-TW' THEN IFNULL(NULLIF(f.fontTraditionalName, ''), f.fontName) " +
            "  WHEN #{locale} = 'ko-KR' THEN IFNULL(NULLIF(f.fontKoreanName, ''), f.fontName) " +
            "  WHEN #{locale} = 'de-DE' THEN IFNULL(NULLIF(f.fontGermanyName, ''), f.fontName) " +
            "  WHEN #{locale} = 'it-IT' THEN IFNULL(NULLIF(f.fontItalyName, ''), f.fontName) " +
            "  WHEN #{locale} = 'es-ES' THEN IFNULL(NULLIF(f.fontSpainName, ''), f.fontName) " +
            "  WHEN #{locale} = 'fr-FR' THEN IFNULL(NULLIF(f.fontFranceName, ''), f.fontName) " +
            "  ELSE f.fontName " +
            "END AS fontName, " +
            "f.fontKind, f.fontCover, f.createTime " +
            "FROM v1_font f " +
            "LEFT JOIN v1_font_kind_lang fkl ON f.fontKind = fkl.fontKind " +
            "WHERE fkl.fontLang = #{locale} " +
            "ORDER BY f.createTime DESC")
    List<FontListVO> selectFontsByLocale(@Param("locale") String locale);

    /**
     * 根据分类查询字体列表
     *
     * @param fontKind 字体分类
     * @param locale 语言标识
     * @return 字体列表
     */
    @Select("SELECT f.fontId, " +
            "CASE " +
            "  WHEN #{locale} = 'en-US' THEN IFNULL(NULLIF(f.fontEnglishName, ''), f.fontName) " +
            "  WHEN #{locale} = 'zh-TW' THEN IFNULL(NULLIF(f.fontTraditionalName, ''), f.fontName) " +
            "  WHEN #{locale} = 'ko-KR' THEN IFNULL(NULLIF(f.fontKoreanName, ''), f.fontName) " +
            "  WHEN #{locale} = 'de-DE' THEN IFNULL(NULLIF(f.fontGermanyName, ''), f.fontName) " +
            "  WHEN #{locale} = 'it-IT' THEN IFNULL(NULLIF(f.fontItalyName, ''), f.fontName) " +
            "  WHEN #{locale} = 'es-ES' THEN IFNULL(NULLIF(f.fontSpainName, ''), f.fontName) " +
            "  WHEN #{locale} = 'fr-FR' THEN IFNULL(NULLIF(f.fontFranceName, ''), f.fontName) " +
            "  ELSE f.fontName " +
            "END AS fontName, " +
            "f.fontEnglishName, f.fontTraditionalName, f.fontKoreanName, " +
            "f.fontGermanyName, f.fontItalyName, f.fontSpainName, f.fontFranceName, " +
            "f.fontKind, f.fontUrl, f.fontCover, f.fontValue, f.sysUserId, f.createTime " +
            "FROM v1_font f " +
            "WHERE f.fontKind = #{fontKind} " +
            "ORDER BY f.createTime DESC")
    List<Font> selectFontsByKind(@Param("fontKind") String fontKind, @Param("locale") String locale);

    /**
     * 动态查询字体列表（支持可选的fontKind参数）
     * 关联font_kind_lang表获取locale信息
     *
     * @param fontKind 字体分类（可选）
     * @return 字体列表
     */
    @Select("<script>" +
            "SELECT f.*, fkl.fontLang as locale " +
            "FROM v1_font f " +
            "LEFT JOIN v1_font_kind_lang fkl ON fkl.fontKind = f.fontKind " +
            "WHERE 1 = 1 " +
            "<if test='fontKind != null and fontKind != \"\"'>" +
            "  AND f.fontKind = #{fontKind}" +
            "</if>" +
            "ORDER BY f.createTime DESC" +
            "</script>")
    List<Font> selectFontsDynamic(@Param("fontKind") String fontKind);

    /**
     * 查询字体分类列表
     *
     * @param locale 语言标识
     * @return 分类列表
     */
    @Select("<script>" +
            "SELECT DISTINCT f.fontKind " +
            "FROM v1_font f " +
            "<if test='locale != null and locale != \"\"'>" +
            "  LEFT JOIN v1_font_kind_lang fkl ON f.fontKind = fkl.fontKind " +
            "  WHERE fkl.fontLang = #{locale}" +
            "</if>" +
            "ORDER BY f.fontKind" +
            "</script>")
    List<String> selectFontKinds(@Param("locale") String locale);

    /**
     * 统计字体数量
     *
     * @return 字体数量
     */
    @Select("SELECT COUNT(*) FROM v1_font")
    Long countFonts();

    /**
     * 检查字体名称是否存在
     *
     * @param fontName 字体名称
     * @param fontId 排除的字体ID
     * @return 是否存在
     */
    @Select("<script>" +
            "SELECT COUNT(*) > 0 " +
            "FROM v1_font " +
            "WHERE fontName = #{fontName} " +
            "<if test='fontId != null'>" +
            "  AND fontId != #{fontId}" +
            "</if>" +
            "</script>")
    boolean existsFontName(@Param("fontName") String fontName, @Param("fontId") String fontId);
}
