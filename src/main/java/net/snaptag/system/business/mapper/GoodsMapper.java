package net.snaptag.system.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import net.snaptag.system.business.entity.Goods;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * Goods Mapper接口
 */
@Mapper
public interface GoodsMapper extends BaseMapper<Goods> {

    /**
     * 分页查询Goods列表
     */
    @Select("SELECT * FROM v1_goods WHERE status = #{status} ORDER BY sort_num ASC, createtime DESC")
    IPage<Goods> findPage(Page<Goods> page, @Param("status") String status);

    /**
     * 查询所有Goods列表
     */
    @Select("SELECT * FROM v1_goods WHERE status = #{status} ORDER BY sort_num ASC, createtime DESC")
    List<Goods> findAllList(@Param("status") String status);

    /**
     * 查询上架商品列表
     */
    @Select("SELECT * FROM v1_goods WHERE status = #{status} AND sale_status = 1 ORDER BY sort_num ASC")
    List<Goods> findOnSaleList(@Param("status") String status);
}
