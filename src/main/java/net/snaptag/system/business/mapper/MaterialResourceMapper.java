package net.snaptag.system.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import net.snaptag.system.business.entity.MaterialResource;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * MaterialResource Mapper接口
 */
@Mapper
public interface MaterialResourceMapper extends BaseMapper<MaterialResource> {

    /**
     * 分页查询MaterialResource列表
     */
    @Select("SELECT * FROM v1_material_resource WHERE status = #{status} ORDER BY createtime DESC")
    IPage<MaterialResource> findPage(Page<MaterialResource> page, @Param("status") String status);

    /**
     * 查询所有MaterialResource列表
     */
    @Select("SELECT * FROM v1_material_resource WHERE status = #{status} ORDER BY createtime DESC")
    List<MaterialResource> findAllList(@Param("status") String status);
}
