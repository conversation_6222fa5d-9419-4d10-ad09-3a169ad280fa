package net.snaptag.system.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import net.snaptag.system.business.entity.WebPrint;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * WebPrint Mapper接口
 */
@Mapper
public interface WebPrintMapper extends BaseMapper<WebPrint> {

    /**
     * 分页查询WebPrint列表
     */
    @Select("SELECT * FROM v1_web_print WHERE status = #{status} ORDER BY createtime DESC")
    IPage<WebPrint> findPage(Page<WebPrint> page, @Param("status") String status);

    /**
     * 查询所有WebPrint列表
     */
    @Select("SELECT * FROM v1_web_print WHERE status = #{status} ORDER BY createtime DESC")
    List<WebPrint> findAllList(@Param("status") String status);
}
