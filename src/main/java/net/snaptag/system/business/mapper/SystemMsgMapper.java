package net.snaptag.system.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import net.snaptag.system.business.entity.SystemMsg;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * SystemMsg Mapper接口
 */
@Mapper
public interface SystemMsgMapper extends BaseMapper<SystemMsg> {

    /**
     * 分页查询SystemMsg列表
     */
    @Select("SELECT * FROM v1_system_msg WHERE status = #{status} ORDER BY createtime DESC")
    IPage<SystemMsg> findPage(Page<SystemMsg> page, @Param("status") String status);

    /**
     * 查询所有SystemMsg列表
     */
    @Select("SELECT * FROM v1_system_msg WHERE status = #{status} ORDER BY createtime DESC")
    List<SystemMsg> findAllList(@Param("status") String status);
}
