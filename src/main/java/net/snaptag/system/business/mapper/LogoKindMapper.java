package net.snaptag.system.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import net.snaptag.system.business.entity.LogoKind;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * v1_logo_kind Mapper接口
 */
@Mapper
public interface LogoKindMapper extends BaseMapper<LogoKind> {

    /**
     * 分页查询v1_logo_kind列表
     */
    @Select("SELECT * FROM v1_logo_kind  ORDER BY createtime DESC")
    IPage<LogoKind> findPage(Page<LogoKind> page);

    /**
     * 查询所有v1_logo_kind列表
     */
    @Select("SELECT * FROM v1_logo_kind  ORDER BY createtime DESC")
    List<LogoKind> findAllList();

    @Select("SELECT logoKindId, ${KindName} as logoKindName from v1_logo_kind where version = 2 order by createTime asc")
    List<LogoKind> findMainLogoKind(@Param("KindName") String KindName);

    @Select("select * from information_schema.columns where table_name='v1_logo_kind' and column_name=#{ColumnName}")
    List<LogoKind> isColumnExistInLogoKind(@Param("ColumnName") String ColumnName);
}
