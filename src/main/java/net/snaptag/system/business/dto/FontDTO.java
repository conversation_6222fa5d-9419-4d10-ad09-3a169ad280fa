package net.snaptag.system.business.dto;

import lombok.Data;

import java.io.Serializable;


/**
 * 字体数据传输对象
 *
 * <AUTHOR>
 * @since 2025-01-16
 */
@Data
public class FontDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private String fontId;

    private String fontName;

    private String fontEnglishName;

    private String fontTraditionalName;

    private String fontKoreanName;

    private String fontKind;

    private String fontUrl;

    private String fontCover;

    private Integer sysUserId;
}
