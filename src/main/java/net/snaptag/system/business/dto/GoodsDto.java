package net.snaptag.system.business.dto;
import java.io.Serializable;

public class GoodsDto implements Serializable {
    /**
     * GoodsDto列表dto
     */
    private static final long serialVersionUID = 1L;
    private String              id;
    private String              name;                          // 名词
    private String              subName;                       // 副标题
    private String              picUrl;                        // 图片地址
    private String              jumpVal;                       // 消息跳转值
    private int                 sortNum;                       // 排序
    private int                 saleStatus;                    // 是否上架，0：未上架；1：上架
    private String              price;                         // 价格，包含单位，随便填
    private String              remark;                        // 备注信息,如果column是knowledge,则备注表达的是年级
    private String              paramAndroid;                  // 自定义组装的json格式，用于andriod前端调用
    private String              paramIos;                      // 自定义组装的json格式，用于ios前端调用

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getSubName() {
        return subName;
    }

    public void setSubName(String subName) {
        this.subName = subName;
    }

    public String getPicUrl() {
        return picUrl;
    }

    public void setPicUrl(String picUrl) {
        this.picUrl = picUrl;
    }

    public String getJumpVal() {
        return jumpVal;
    }

    public void setJumpVal(String jumpVal) {
        this.jumpVal = jumpVal;
    }

    public int getSortNum() {
        return sortNum;
    }

    public void setSortNum(int sortNum) {
        this.sortNum = sortNum;
    }

    public int getSaleStatus() {
        return saleStatus;
    }

    public void setSaleStatus(int saleStatus) {
        this.saleStatus = saleStatus;
    }

    public String getPrice() {
        return price;
    }

    public void setPrice(String price) {
        this.price = price;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getParamAndroid() {
        return paramAndroid;
    }

    public void setParamAndroid(String paramAndroid) {
        this.paramAndroid = paramAndroid;
    }

    public String getParamIos() {
        return paramIos;
    }

    public void setParamIos(String paramIos) {
        this.paramIos = paramIos;
    }
}
