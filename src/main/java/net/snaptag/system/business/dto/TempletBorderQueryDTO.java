package net.snaptag.system.business.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * 模板边框查询DTO
 *
 * <AUTHOR>
 * @since 2025-08-20
 */
@Data
public class TempletBorderQueryDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer pageNumber = 1;

    private Integer pageSize = 10;

    private String borderKindId;

    private Integer language = 1;

    private String lang;

    private String orderBy = "createTime";

    private String orderDirection = "DESC";

    private Boolean includeKindInfo = true;

    private Boolean includeMultiLang = false;

    /**
     * 获取标准化的页码
     */
    public Integer getPageNumber() {
        return pageNumber != null && pageNumber > 0 ? pageNumber : 1;
    }

    /**
     * 获取标准化的页大小
     */
    public Integer getPageSize() {
        if (pageSize == null || pageSize <= 0) {
            return 10;
        }
        // 限制最大页大小
        return Math.min(pageSize, 100);
    }

    /**
     * 获取标准化的语言类型
     */
    public Integer getLanguage() {
        return language != null ? language : 1;
    }

    /**
     * 获取标准化的排序字段
     */
    public String getOrderBy() {
        if (orderBy == null || orderBy.trim().isEmpty()) {
            return "createTime";
        }
        // 只允许特定字段排序，防止SQL注入
        String normalizedOrderBy = orderBy.trim().toLowerCase();
        switch (normalizedOrderBy) {
            case "createtime":
            case "create_time":
                return "createTime";
            case "borderid":
            case "border_id":
                return "borderId";
            case "borderkindid":
            case "border_kind_id":
                return "borderKindId";
            default:
                return "createTime";
        }
    }

    /**
     * 获取标准化的排序方向
     */
    public String getOrderDirection() {
        if (orderDirection == null || orderDirection.trim().isEmpty()) {
            return "DESC";
        }
        String normalized = orderDirection.trim().toUpperCase();
        return "ASC".equals(normalized) ? "ASC" : "DESC";
    }

    /**
     * 是否有分类过滤条件
     */
    public boolean hasKindFilter() {
        return borderKindId != null && !borderKindId.trim().isEmpty();
    }

    /**
     * 是否有语言参数
     */
    public boolean hasLangParam() {
        return lang != null && !lang.trim().isEmpty();
    }

    /**
     * 获取清理后的分类ID
     */
    public String getBorderKindId() {
        return borderKindId != null ? borderKindId.trim() : null;
    }

    /**
     * 获取清理后的语言标识
     */
    public String getLang() {
        return lang != null ? lang.trim().toLowerCase() : null;
    }
}
