package net.snaptag.system.business.dto;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 家用行业模板DTO
 */
public class TemplateDomesticDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private String id;

    /**
     * 用户ID
     */
    private Integer userId;

    /**
     * 模板分组ID
     */
    private String groupId;

    /**
     * 模板名称
     */
    private String name;

    /**
     * 封面图片地址
     */
    private String cover;

    /**
     * 间隙
     */
    private BigDecimal gap;

    /**
     * 高度
     */
    private BigDecimal height;

    /**
     * 宽度
     */
    private BigDecimal width;

    /**
     * 纸张类型：1-间隙纸 2-连续纸 3-黑标纸 4-定孔纸
     */
    private Integer paperType;

    /**
     * 打印方向
     */
    private Integer printDirection;

    /**
     * 标签内容数据
     */
    private String data;

    /**
     * 黑标间隙
     */
    private BigDecimal blackLabelGap;

    /**
     * 黑标偏移
     */
    private BigDecimal blackLabelOffset;

    /**
     * 类型：0-1.0老版本数据 1-行业模板 2-2.0新数据
     */
    private Integer type;

    /**
     * 模板英语名称
     */
    private String nameEn;

    /**
     * 模板韩语名称
     */
    private String nameKor;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 机器类型：1-标签 2-票据 3-76针打
     */
    private Integer machineType;

    /**
     * 打印后切纸：0-否 1-是
     */
    private Integer cutAfterPrint;

    /**
     * 标签数量，默认为1，大于1为多排标签
     */
    private Integer labelNum;

    /**
     * 多排标签间距
     */
    private BigDecimal labelGap;

    /**
     * 模板日语名称
     */
    private String nameJP;

    /**
     * 模板HK繁体名称
     */
    private String nameHK;

    /**
     * 多排类型：0-不复制 1-仅复制首排标签
     */
    private Integer multiLabelType;

    /**
     * 撕纸类型：1-撕离 2-剥离
     */
    private Integer paperTearType;

    /**
     * 分享用户
     */
    private Integer shareUser;

    /**
     * 标签类型（标签形状）：1-矩形 2-圆角矩形 3-圆
     */
    private Integer labelType;

    /**
     * 票据模板机器子类型：1-58票据 2-80票据 3-76针打
     */
    private Integer ticketMachineType;

    /**
     * 打印后走纸长度
     */
    private Integer paperFeedCount;

    /**
     * 是否开启镜像：false-否 true-是
     */
    private Boolean mirrorImage;

    /**
     * 模板俄语名称
     */
    private String nameRU;

    /**
     * 打印类型：0-标签打印 1-PDF打印 2-图片打印
     */
    private Integer printType;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 状态
     */
    private String status;

    // Getter and Setter methods

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    public String getGroupId() {
        return groupId;
    }

    public void setGroupId(String groupId) {
        this.groupId = groupId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCover() {
        return cover;
    }

    public void setCover(String cover) {
        this.cover = cover;
    }

    public BigDecimal getGap() {
        return gap;
    }

    public void setGap(BigDecimal gap) {
        this.gap = gap;
    }

    public BigDecimal getHeight() {
        return height;
    }

    public void setHeight(BigDecimal height) {
        this.height = height;
    }

    public BigDecimal getWidth() {
        return width;
    }

    public void setWidth(BigDecimal width) {
        this.width = width;
    }

    public Integer getPaperType() {
        return paperType;
    }

    public void setPaperType(Integer paperType) {
        this.paperType = paperType;
    }

    public Integer getPrintDirection() {
        return printDirection;
    }

    public void setPrintDirection(Integer printDirection) {
        this.printDirection = printDirection;
    }

    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }

    public BigDecimal getBlackLabelGap() {
        return blackLabelGap;
    }

    public void setBlackLabelGap(BigDecimal blackLabelGap) {
        this.blackLabelGap = blackLabelGap;
    }

    public BigDecimal getBlackLabelOffset() {
        return blackLabelOffset;
    }

    public void setBlackLabelOffset(BigDecimal blackLabelOffset) {
        this.blackLabelOffset = blackLabelOffset;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getNameEn() {
        return nameEn;
    }

    public void setNameEn(String nameEn) {
        this.nameEn = nameEn;
    }

    public String getNameKor() {
        return nameKor;
    }

    public void setNameKor(String nameKor) {
        this.nameKor = nameKor;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Integer getMachineType() {
        return machineType;
    }

    public void setMachineType(Integer machineType) {
        this.machineType = machineType;
    }

    public Integer getCutAfterPrint() {
        return cutAfterPrint;
    }

    public void setCutAfterPrint(Integer cutAfterPrint) {
        this.cutAfterPrint = cutAfterPrint;
    }

    public Integer getLabelNum() {
        return labelNum;
    }

    public void setLabelNum(Integer labelNum) {
        this.labelNum = labelNum;
    }

    public BigDecimal getLabelGap() {
        return labelGap;
    }

    public void setLabelGap(BigDecimal labelGap) {
        this.labelGap = labelGap;
    }

    public String getNameJP() {
        return nameJP;
    }

    public void setNameJP(String nameJP) {
        this.nameJP = nameJP;
    }

    public String getNameHK() {
        return nameHK;
    }

    public void setNameHK(String nameHK) {
        this.nameHK = nameHK;
    }

    public Integer getMultiLabelType() {
        return multiLabelType;
    }

    public void setMultiLabelType(Integer multiLabelType) {
        this.multiLabelType = multiLabelType;
    }

    public Integer getPaperTearType() {
        return paperTearType;
    }

    public void setPaperTearType(Integer paperTearType) {
        this.paperTearType = paperTearType;
    }

    public Integer getShareUser() {
        return shareUser;
    }

    public void setShareUser(Integer shareUser) {
        this.shareUser = shareUser;
    }

    public Integer getLabelType() {
        return labelType;
    }

    public void setLabelType(Integer labelType) {
        this.labelType = labelType;
    }

    public Integer getTicketMachineType() {
        return ticketMachineType;
    }

    public void setTicketMachineType(Integer ticketMachineType) {
        this.ticketMachineType = ticketMachineType;
    }

    public Integer getPaperFeedCount() {
        return paperFeedCount;
    }

    public void setPaperFeedCount(Integer paperFeedCount) {
        this.paperFeedCount = paperFeedCount;
    }

    public Boolean getMirrorImage() {
        return mirrorImage;
    }

    public void setMirrorImage(Boolean mirrorImage) {
        this.mirrorImage = mirrorImage;
    }

    public String getNameRU() {
        return nameRU;
    }

    public void setNameRU(String nameRU) {
        this.nameRU = nameRU;
    }

    public Integer getPrintType() {
        return printType;
    }

    public void setPrintType(Integer printType) {
        this.printType = printType;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
}
