package net.snaptag.system.business.dto;
import java.io.Serializable;

public class MaterialSelectDto implements Serializable {
    /**
     * 素材库Dto
     */
    private static final long serialVersionUID = 1L;
    private String            id;                   // 记录ID
    private String            name;                 // 名称
    private String title;
    private String com; // 模板组件名
    private String zhTitle;
    private String enTitle;
    private int type;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getCom() {
        return com;
    }

    public void setCom(String com) {
        this.com = com;
    }

    public String getZhTitle() {
        return zhTitle;
    }

    public void setZhTitle(String zhTitle) {
        this.zhTitle = zhTitle;
    }

    public String getEnTitle() {
        return enTitle;
    }

    public void setEnTitle(String enTitle) {
        this.enTitle = enTitle;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }
}
