package net.snaptag.system.business.dto;

import java.io.Serializable;
import java.util.List;

public class UserFeedbackItem implements Serializable {

    public UserFeedbackItem() {
        super();
    }

    public UserFeedbackItem(String userId, String type, String qtype, String content, List<String> images, String result) {
        super();
        this.userId = userId;
        this.type = type;
        this.qtype = qtype;
        this.content = content;
        this.images = images;
        this.result = result;
    }

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 反馈类型
     */
    private String type;
    /**
     * 问题类型
     */
    private String qtype;
    /***
     * 问题类型，返回列表时组装下中文名
     */
    private String qtypeName;

    /**
     * 反馈文本内容
     */
    private String content;
    /**
     * 反馈图片地址列表
     */
    private List<String> images;
    /**
     * 系统响应结果信息
     */
    private String result;
    /**
     * 创建时间
     */
    private String createTime;
    /**
     * 用户手机号
     */
    private String mobile;
    /**
     * 反馈时间
     */
    private String updateTime;
    /**
     * 用户编号
     */
    private Integer userNo;
    /**
     * 客户端信息
     */
    private String clientInfo;

    private String printerType;

    private int isRead;
    private String id;  //feedback的id
    private String mid; // 消息的id
    private String message; // 消息内容



    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getQtype() {
        return qtype;
    }

    public void setQtype(String qtype) {
        this.qtype = qtype;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public List<String> getImages() {
        return images;
    }

    public void setImages(List<String> images) {
        this.images = images;
    }

    public String getResult() {
        return result;
    }

    public void setResult(String result) {
        this.result = result;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }

    public Integer getUserNo() {
        return userNo;
    }

    public void setUserNo(Integer userNo) {
        this.userNo = userNo;
    }

    public String getClientInfo() {
        return clientInfo;
    }

    public void setClientInfo(String clientInfo) {
        this.clientInfo = clientInfo;
    }

    public String getPrinterType() {
        return printerType;
    }

    public void setPrinterType(String printerType) {
        this.printerType = printerType;
    }

    public String getQtypeName() {
        return qtypeName;
    }

    public void setQtypeName(String qtypeName) {
        this.qtypeName = qtypeName;
    }

    public int getIsRead() {
        return isRead;
    }

    public void setIsRead(int isRead) {
        this.isRead = isRead;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getMid() {
        return mid;
    }

    public void setMid(String mid) {
        this.mid = mid;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }
}
