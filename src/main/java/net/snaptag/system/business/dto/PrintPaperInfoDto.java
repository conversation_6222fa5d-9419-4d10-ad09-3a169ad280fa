package net.snaptag.system.business.dto;

import java.util.Date;

/**
 * 打印耗材信息
 * <AUTHOR>
 * @date ：Created in 2023/7/19 10:17
 * @description：
 * @modified By：
 * @version: $
 */
public class PrintPaperInfoDto {
    private String id;              // id
    private String paperId;         // 纸张id
    private int type;               // 1为连续纸，2为间隙纸
    private int material;           // 介质，1为合成纸面材；2未普通纸面材
    private float height;             // 纸张高度
    private float width;              // 纸张长度，连续纸统一：1
    private float gapWidth;           // 间隙长度
    private int rfidFlag;           // 0:不带rfid，1：带rfid

    private String name;            // 展示名称
    private String nameLeftTop;     // 左上角
    private String colorCode;       // 颜色代码
    private int colorNum;        // 颜色字节编号
    private String zhTitle;         // 名称
    private String enTitle;         // 名称-国际代码
    private String resUrl;          // 列表图
    private String imageUrl;        // 资源图
    private String printerType;     // 适合打印机，分号分隔

    private int isNew;;             // 是否展示，0表示不显示，1表示显示
    private Date showtimeEnd;       // 选择展示期限
    private String printColorCode;  // 打印色值

    private int direction;

    private int rotate;         // 纸张角度

    private float printX;       // 打印范围
    private float printY;       // 打印范围
    private float printH;       // 打印范围
    private float printW;       // 打印范围
    private String listName;        // 列表前缀名

    private float dieCutting=1.5f;   // 排废
    private int isBlackFlag =0;    // 是否黑标，0：否； 1：是

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getPaperId() {
        return paperId;
    }

    public void setPaperId(String paperId) {
        this.paperId = paperId;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public int getMaterial() {
        return material;
    }

    public void setMaterial(int material) {
        this.material = material;
    }

    public float getHeight() {
        return height;
    }

    public void setHeight(float height) {
        this.height = height;
    }

    public float getWidth() {
        return width;
    }

    public void setWidth(float width) {
        this.width = width;
    }

    public float getGapWidth() {
        return gapWidth;
    }

    public void setGapWidth(float gapWidth) {
        this.gapWidth = gapWidth;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getColorCode() {
        return colorCode;
    }

    public void setColorCode(String colorCode) {
        this.colorCode = colorCode;
    }

    public String getZhTitle() {
        return zhTitle;
    }

    public void setZhTitle(String zhTitle) {
        this.zhTitle = zhTitle;
    }

    public String getEnTitle() {
        return enTitle;
    }

    public void setEnTitle(String enTitle) {
        this.enTitle = enTitle;
    }

    public String getResUrl() {
        return resUrl;
    }

    public void setResUrl(String resUrl) {
        this.resUrl = resUrl;
    }

    public String getImageUrl() {
        return imageUrl;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    public String getPrinterType() {
        return printerType;
    }

    public void setPrinterType(String printerType) {
        this.printerType = printerType;
    }

    public int getIsNew() {
        return isNew;
    }

    public void setIsNew(int isNew) {
        this.isNew = isNew;
    }

    public Date getShowtimeEnd() {
        return showtimeEnd;
    }

    public void setShowtimeEnd(Date showtimeEnd) {
        this.showtimeEnd = showtimeEnd;
    }

    public int getRfidFlag() {
        return rfidFlag;
    }

    public void setRfidFlag(int rfidFlag) {
        this.rfidFlag = rfidFlag;
    }

    public String getPrintColorCode() {
        return printColorCode;
    }

    public void setPrintColorCode(String printColorCode) {
        this.printColorCode = printColorCode;
    }

    public int getColorNum() {
        return colorNum;
    }

    public void setColorNum(int colorNum) {
        this.colorNum = colorNum;
    }

    public int getDirection() {
        return direction;
    }

    public void setDirection(int direction) {
        this.direction = direction;
    }

    public String getNameLeftTop() {
        return nameLeftTop;
    }

    public void setNameLeftTop(String nameLeftTop) {
        this.nameLeftTop = nameLeftTop;
    }

    public float getPrintX() {
        return printX;
    }

    public void setPrintX(float printX) {
        this.printX = printX;
    }

    public float getPrintY() {
        return printY;
    }

    public void setPrintY(float printY) {
        this.printY = printY;
    }

    public float getPrintH() {
        return printH;
    }

    public void setPrintH(float printH) {
        this.printH = printH;
    }

    public float getPrintW() {
        return printW;
    }

    public void setPrintW(float printW) {
        this.printW = printW;
    }

    public int getRotate() {
        return rotate;
    }

    public void setRotate(int rotate) {
        this.rotate = rotate;
    }

    public String getListName() {
        return listName;
    }

    public void setListName(String listName) {
        this.listName = listName;
    }

    public float getDieCutting() {
        return dieCutting;
    }

    public void setDieCutting(float dieCutting) {
        this.dieCutting = dieCutting;
    }

    public int getIsBlackFlag() {
        return isBlackFlag;
    }

    public void setIsBlackFlag(int isBlackFlag) {
        this.isBlackFlag = isBlackFlag;
    }
}
