package net.snaptag.system.business.dto;

import java.io.Serializable;

public class ResourceDataDto implements Serializable {
    /**
     * 资源数据dto
     */
    private static final long serialVersionUID = 1L;
    private String            id;                   // 记录ID
    private String            userId;               // 用户ID
    private String            nickName;             // 昵称
    private String            userPic;              // 用户头像
    private PicDto            resUrl;               // 资源地址
    private int               resType;              // 资源类型 0--编辑纸条数据 1--图片地址
    private String            flag;                 // 资源标识--MD5
    private String            content;              // 内容
    private String            createDate;           // 创建时间

    public String getNickName() {
        return nickName;
    }

    public void setNickName(String nickName) {
        this.nickName = nickName;
    }

    public String getUserPic() {
        return userPic;
    }

    public void setUserPic(String userPic) {
        this.userPic = userPic;
    }

    public String getCreateDate() {
        return createDate;
    }

    public void setCreateDate(String createDate) {
        this.createDate = createDate;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public PicDto getResUrl() {
        return resUrl;
    }

    public void setResUrl(PicDto resUrl) {
        this.resUrl = resUrl;
    }

    public int getResType() {
        return resType;
    }

    public void setResType(int resType) {
        this.resType = resType;
    }

    public String getFlag() {
        return flag;
    }

    public void setFlag(String flag) {
        this.flag = flag;
    }
}
