package net.snaptag.system.business.dto;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 字体查询数据传输对象
 * 
 * <AUTHOR>
 * @since 2025-01-16
 */
@Data
public class FontQueryDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer current = 1;

    private Integer size = 10;

    private String fontName;

    private String fontKind;

    private Integer sysUserId;

    private String locale;

    private LocalDateTime startTime;

    private LocalDateTime endTime;

    private String orderBy = "create_time";

    private String orderDirection = "desc";
}
