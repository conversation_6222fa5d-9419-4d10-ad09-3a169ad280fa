package net.snaptag.system.business.dto;

import com.alibaba.fastjson.annotation.JSONField;

import java.io.Serializable;
import java.util.Date;

public class SystemMsgDto implements Serializable {
    /**
     * 消息中心dto--服务器返回
     */
    private static final long serialVersionUID = 1L;
    private String            id;                   // 记录ID
    private String            msgTitle;             // 消息标题
    private String            msgContent;           // 消息内容
    private int               msgType;              // 消息主类型
    private String            pic;                  // 消息图片
    private int               msgSubType;           // 消息副类型
    private String            jumpVal;              // 消息跳转值
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date              startDate;            // 开始时间
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date              endDate;              // 结束时间
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date              createtime;           // 创建时间
    private String            version;              // 最小版本显示记录
    private String            paramAndroid;                  // 自定义组装的json格式，用于andriod前端调用
    private String            paramIos;                      // 自定义组装的json格式，用于ios前端调用

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getMsgTitle() {
        return msgTitle;
    }

    public void setMsgTitle(String msgTitle) {
        this.msgTitle = msgTitle;
    }

    public String getMsgContent() {
        return msgContent;
    }

    public void setMsgContent(String msgContent) {
        this.msgContent = msgContent;
    }

    public int getMsgType() {
        return msgType;
    }

    public void setMsgType(int msgType) {
        this.msgType = msgType;
    }

    public String getPic() {
        return pic;
    }

    public void setPic(String pic) {
        this.pic = pic;
    }

    public int getMsgSubType() {
        return msgSubType;
    }

    public void setMsgSubType(int msgSubType) {
        this.msgSubType = msgSubType;
    }

    public String getJumpVal() {
        return jumpVal;
    }

    public void setJumpVal(String jumpVal) {
        this.jumpVal = jumpVal;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public Date getCreatetime() {
        return createtime;
    }

    public void setCreatetime(Date createtime) {
        this.createtime = createtime;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getParamAndroid() {
        return paramAndroid;
    }

    public void setParamAndroid(String paramAndroid) {
        this.paramAndroid = paramAndroid;
    }

    public String getParamIos() {
        return paramIos;
    }

    public void setParamIos(String paramIos) {
        this.paramIos = paramIos;
    }
}
