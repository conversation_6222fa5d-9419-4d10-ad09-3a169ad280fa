package net.snaptag.system.business.dto;

import java.io.Serializable;
import java.util.Map;

public class BannerDto implements Serializable {
    /**
     * banner列表dto
     */
    private static final long serialVersionUID = 1L;
    private String            name;                 // 名称
    private String            pics;                 // 图片
    private String            jumpVal;              // 消息跳转值
    private int               jumpType;             // 跳转类型
    private String              shareTitle;                    // 分享时展示的标题
    private String              shareContent;                  // 分享时展示的内容
    private int                 shareFlag;                     // 是否分享：0表示不分享，1表示分享
    private String            version;              // 大于等于该版本才显示
    private String            remark;               // 备注信息,如果column是knowledge,则备注表达的是年级
    private Map<String, Object> objInfo;            // 特殊处理，加工处理
    private String              paramAndroid;                  // 自定义组装的json格式，用于andriod前端调用
    private String              paramIos;                      // 自定义组装的json格式，用于ios前端调用
    private String localeCode;      // 语言国际
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPics() {
        return pics;
    }

    public void setPics(String pics) {
        this.pics = pics;
    }

    public String getJumpVal() {
        return jumpVal;
    }

    public void setJumpVal(String jumpVal) {
        this.jumpVal = jumpVal;
    }

    public int getJumpType() {
        return jumpType;
    }

    public void setJumpType(int jumpType) {
        this.jumpType = jumpType;
    }

    public String getShareTitle() {
        return shareTitle;
    }

    public void setShareTitle(String shareTitle) {
        this.shareTitle = shareTitle;
    }

    public String getShareContent() {
        return shareContent;
    }

    public void setShareContent(String shareContent) {
        this.shareContent = shareContent;
    }

    public int getShareFlag() {
        return shareFlag;
    }

    public void setShareFlag(int shareFlag) {
        this.shareFlag = shareFlag;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
    public Map<String, Object> getObjInfo() {
        return objInfo;
    }

    public void setObjInfo(Map<String, Object> objInfo) {
        this.objInfo = objInfo;
    }

    public String getParamIos() {
        return paramIos;
    }

    public void setParamIos(String paramIos) {
        this.paramIos = paramIos;
    }

    public String getParamAndroid() {
        return paramAndroid;
    }

    public void setParamAndroid(String paramAndroid) {
        this.paramAndroid = paramAndroid;
    }

    public String getLocaleCode() {
        return localeCode;
    }

    public void setLocaleCode(String localeCode) {
        this.localeCode = localeCode;
    }
}
