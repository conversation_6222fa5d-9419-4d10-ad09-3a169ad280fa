package net.snaptag.system.business.dto;

/**
 * <AUTHOR>
 * @date ：Created in 2023/4/6 8:57
 * @description：打印机驱动更新
 * @modified By：
 * @version: $
 */
public class PrintDriverUpdateInfoDto {
    private static final long  serialVersionUID = 1L;
    private String             printerModel;                      // 打印机型号
    private String             versionName;                       // 固件名称
    private String             versionCode;                       // 版本号编码
    private String             url;                               // 下载地址
    private String             param;                             // 参数
    private String             remark;                            // 版本描述
    private String             title;                             // 标题
    private int                needForceUpdate;                   // 是否强制升级
    private int                needIndexShow;                     // 是否首页显示
    private int                usedFlag;                          // 是否生效，不生效的不返回到前端
    private int                needCheckPower;                    // 0: 不用，1：要
    private int                powerValue;                        // 符合升级的电量,0--100
    private String             md5;                               // 实体文件的md5
    private String             describe;                          // 升级说明

    public String getPrinterModel() {
        return printerModel;
    }

    public void setPrinterModel(String printerModel) {
        this.printerModel = printerModel;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getParam() {
        return param;
    }

    public void setParam(String param) {
        this.param = param;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public int getNeedForceUpdate() {
        return needForceUpdate;
    }

    public void setNeedForceUpdate(int needForceUpdate) {
        this.needForceUpdate = needForceUpdate;
    }

    public int getNeedIndexShow() {
        return needIndexShow;
    }

    public void setNeedIndexShow(int needIndexShow) {
        this.needIndexShow = needIndexShow;
    }

    public String getVersionName() {
        return versionName;
    }

    public void setVersionName(String versionName) {
        this.versionName = versionName;
    }

    public String getVersionCode() {
        return versionCode;
    }

    public void setVersionCode(String versionCode) {
        this.versionCode = versionCode;
    }

    public int getUsedFlag() {
        return usedFlag;
    }

    public void setUsedFlag(int usedFlag) {
        this.usedFlag = usedFlag;
    }

    public int getNeedCheckPower() {
        return needCheckPower;
    }

    public void setNeedCheckPower(int needCheckPower) {
        this.needCheckPower = needCheckPower;
    }

    public int getPowerValue() {
        return powerValue;
    }

    public void setPowerValue(int powerValue) {
        this.powerValue = powerValue;
    }

    public String getMd5() {
        return md5;
    }

    public void setMd5(String md5) {
        this.md5 = md5;
    }

    public String getDescribe() {
        return describe;
    }

    public void setDescribe(String describe) {
        this.describe = describe;
    }
}
