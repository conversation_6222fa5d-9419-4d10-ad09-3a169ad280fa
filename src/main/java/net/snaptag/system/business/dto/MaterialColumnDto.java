package net.snaptag.system.business.dto;
import java.io.Serializable;
import java.util.Map;

public class MaterialColumnDto implements Serializable {
    /**
     * 素材库栏目Dto
     */
    private static final long serialVersionUID = 1L;
    private String            id;                   // 记录ID
    private String            name;                 // 名称
    private int               type;                 // 类型 1-编辑纸条 2-清单 3-便签
    private int               subType;              // 副类型 1-文字 2-图片 3-贴纸 4-涂鸦 5-二维码 6-纸条箱
    private String            label;                // 标签
    private int               hasContent;           // 是否有内容
    private String            icon;                 // 图标地址
    private Map<String, String> icons;

    public Map<String, String> getIcons() {
        return icons;
    }

    public void setIcons(Map<String, String> icons) {
        this.icons = icons;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public int getHasContent() {
        return hasContent;
    }

    public void setHasContent(int hasContent) {
        this.hasContent = hasContent;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public int getSubType() {
        return subType;
    }

    public void setSubType(int subType) {
        this.subType = subType;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }
}
