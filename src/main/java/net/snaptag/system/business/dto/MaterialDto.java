package net.snaptag.system.business.dto;
import java.io.Serializable;
import java.util.List;

public class MaterialDto implements Serializable {
    /**
     * 素材库Dto
     */
    private static final long serialVersionUID = 1L;
    private String            id;                   // 记录ID
    private String            name;                 // 名称
    private ResDto resDto;               // 资源dto
    private List<MaterialDto> materialDto;          // 素材dto
    private int               type;                 // 类型 1-编辑纸条 2-清单 3-便签
    private int               subType;              // 副类型 1-文字 2-图片 3-贴纸 4-涂鸦 5-二维码 6-纸条箱
    private int               isChoose;             // 是否选中
    private int               isNew;                // 是否显示new
    private String            label;                // 标签
    private String            materialColumnId;     // 栏目id
    private int               sortNum;

    private String            iconUrlEntity;         // 实体
    private String            iconUrlShadow;         // 阴影

    public int getIsChoose() {
        return isChoose;
    }

    public void setIsChoose(int isChoose) {
        this.isChoose = isChoose;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public int getSubType() {
        return subType;
    }

    public void setSubType(int subType) {
        this.subType = subType;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public ResDto getResDto() {
        return resDto;
    }

    public void setResDto(ResDto resDto) {
        this.resDto = resDto;
    }

    public List<MaterialDto> getMaterialDto() {
        return materialDto;
    }

    public void setMaterialDto(List<MaterialDto> materialDto) {
        this.materialDto = materialDto;
    }

    public int getIsNew() {
        return isNew;
    }

    public void setIsNew(int isNew) {
        this.isNew = isNew;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public String getMaterialColumnId() {
        return materialColumnId;
    }

    public void setMaterialColumnId(String materialColumnId) {
        this.materialColumnId = materialColumnId;
    }

    public int getSortNum() {
        return sortNum;
    }

    public void setSortNum(int sortNum) {
        this.sortNum = sortNum;
    }

    public String getIconUrlEntity() {
        return iconUrlEntity;
    }

    public void setIconUrlEntity(String iconUrlEntity) {
        this.iconUrlEntity = iconUrlEntity;
    }

    public String getIconUrlShadow() {
        return iconUrlShadow;
    }

    public void setIconUrlShadow(String iconUrlShadow) {
        this.iconUrlShadow = iconUrlShadow;
    }
}
