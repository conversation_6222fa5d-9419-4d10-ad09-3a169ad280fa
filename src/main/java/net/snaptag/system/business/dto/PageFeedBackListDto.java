package net.snaptag.system.business.dto;

import java.io.Serializable;
import java.util.List;

public class PageFeedBackListDto implements Serializable {

    /**
     * 用户反馈Dto
     */
    private static final long serialVersionUID = 1L;

    private String id;
    /**
     * 用户ID
     */
    private String userId;
    /**
     * 反馈类型
     */
    private String type;
    /**
     * 问题类型
     */
    private String qtype;
    /**
     * 反馈文本内容
     */
    private String content;
    /**
     * 反馈图片地址列表
     */
    private List<String> images;
    /**
     * 系统响应结果信息
     */
    private String result;
    /**
     * 错误信息
     */
    private String errormsg;
    /**
     * 手机号
     */
    private String mobile;
    /**
     * 反馈时间
     */
    private String updateTime;
    /**
     * 用户编号
     */
    private Integer userNo;
    /**
     * 客户端信息
     */
    private String clientInfo;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getQtype() {
        return qtype;
    }

    public void setQtype(String qtype) {
        this.qtype = qtype;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public List<String> getImages() {
        return images;
    }

    public void setImages(List<String> images) {
        this.images = images;
    }

    public String getResult() {
        return result;
    }

    public void setResult(String result) {
        this.result = result;
    }

    public String getErrormsg() {
        return errormsg;
    }

    public void setErrormsg(String errormsg) {
        this.errormsg = errormsg;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }

    public Integer getUserNo() {
        return userNo;
    }

    public void setUserNo(Integer userNo) {
        this.userNo = userNo;
    }

    public String getClientInfo() {
        return clientInfo;
    }

    public void setClientInfo(String clientInfo) {
        this.clientInfo = clientInfo;
    }

}
