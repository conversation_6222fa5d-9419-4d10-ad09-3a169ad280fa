package net.snaptag.system.business.dto;

import com.alibaba.fastjson.annotation.JSONField;

import java.io.Serializable;
import java.util.Date;

public class SaveMsgColumnDto implements Serializable {
    /**
     * 保存消息栏目dto
     */
    private static final long serialVersionUID = 1L;
    private String            userId;               // 用户ID
    private int               type;                 // 栏目类型
    private int               count;                // 数量
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date              lasdDate;             // 最后更新时间

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public int getCount() {
        return count;
    }

    public void setCount(int count) {
        this.count = count;
    }

    public Date getLasdDate() {
        return lasdDate;
    }

    public void setLasdDate(Date lasdDate) {
        this.lasdDate = lasdDate;
    }
}
