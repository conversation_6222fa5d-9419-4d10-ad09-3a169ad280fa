package net.snaptag.system.business.dto;
import java.io.Serializable;

public class SendMsgDto implements Serializable {
    /**
     * 发送消息dto--服务器返回
     */
    private static final long serialVersionUID = 1L;
    private String            msgTitle;             // 消息标题
    private String            msgContent;           // 消息内容
    private int               msgType;              // 消息主类型
    private int               msgSubType;           // 消息副类型
    private String            senderUserId;         // 消息发起者ID
    private String            receiverUserId;       // 接收人ID
    private String            pic;                  // 图片地址
    private MsgCenterParamDto param;                // 参数

    public String getMsgTitle() {
        return msgTitle;
    }

    public void setMsgTitle(String msgTitle) {
        this.msgTitle = msgTitle;
    }

    public String getMsgContent() {
        return msgContent;
    }

    public void setMsgContent(String msgContent) {
        this.msgContent = msgContent;
    }

    public int getMsgType() {
        return msgType;
    }

    public void setMsgType(int msgType) {
        this.msgType = msgType;
    }

    public int getMsgSubType() {
        return msgSubType;
    }

    public void setMsgSubType(int msgSubType) {
        this.msgSubType = msgSubType;
    }

    public String getSenderUserId() {
        return senderUserId;
    }

    public void setSenderUserId(String senderUserId) {
        this.senderUserId = senderUserId;
    }

    public String getReceiverUserId() {
        return receiverUserId;
    }

    public void setReceiverUserId(String receiverUserId) {
        this.receiverUserId = receiverUserId;
    }

    public String getPic() {
        return pic;
    }

    public void setPic(String pic) {
        this.pic = pic;
    }

    public MsgCenterParamDto getParam() {
        return param;
    }

    public void setParam(MsgCenterParamDto param) {
        this.param = param;
    }
}
