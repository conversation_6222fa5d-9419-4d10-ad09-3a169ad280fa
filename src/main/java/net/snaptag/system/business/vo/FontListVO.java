package net.snaptag.system.business.vo;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 字体列表视图对象
 *
 * <AUTHOR>
 * @since 2025-01-16
 */
@Data
public class FontListVO implements Serializable {

    private static final long serialVersionUID = 1L;

    private String fontId;

    private String fontName;

    private String fontKind;

    private String fontCover;

    private LocalDateTime createTime;

    private String downloadUrl;

    private String fileSizeFormatted;
}
