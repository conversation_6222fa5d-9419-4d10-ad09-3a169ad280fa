package net.snaptag.system.business.vo;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 字体视图对象
 *
 * <AUTHOR>
 * @since 2025-01-16
 */
@Data
public class FontVO implements Serializable {

    private static final long serialVersionUID = 1L;

    private String fontId;

    private String fontName;

    private String fontEnglishName;

    private String fontTraditionalName;

    private String fontKoreanName;

    private String fontGermanyName;

    private String fontItalyName;

    private String fontSpainName;

    private String fontFranceName;

    private String fontKind;

    private String fontUrl;

    private String fontCover;

    private String fontValue;

    private Integer sysUserId;

    private LocalDateTime createTime;

    private String downloadUrl;

    private Long fileSize;

    private String fileSizeFormatted;
}
