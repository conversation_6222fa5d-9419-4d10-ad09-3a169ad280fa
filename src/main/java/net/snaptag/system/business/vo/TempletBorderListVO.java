package net.snaptag.system.business.vo;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 模板边框列表VO
 *
 * <AUTHOR>
 * @since 2025-08-20
 */
@Data
public class TempletBorderListVO implements Serializable {

    private static final long serialVersionUID = 1L;

    private String borderId;

    private String headerImgUrl;

    private String fillImgUrl;

    private String footerImgUrl;

    private String thumbUrl;

    private String groupName;

    private String groupDisplayName;

    private LocalDateTime createTime;
}
