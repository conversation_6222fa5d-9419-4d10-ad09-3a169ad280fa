package net.snaptag.system.business.vo;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 模板边框详情VO
 *
 * <AUTHOR>
 * @since 2025-08-20
 */
@Data
public class TempletBorderVO implements Serializable {

    private static final long serialVersionUID = 1L;

    private String borderId;

    private String borderKindId;

    private String headerImgUrl;

    private String fillImgUrl;

    private String footerImgUrl;

    private String thumbUrl;

    private String groupName;

    private String groupDisplayName;

    private LocalDateTime createTime;

    // 分类的多语言信息
    private String englishName;

    private String traditionalName;

    private String koreanName;

    private String russianName;

    private String frenchName;

    private String spanishName;

    private String germanyName;

    private String italyName;
}
