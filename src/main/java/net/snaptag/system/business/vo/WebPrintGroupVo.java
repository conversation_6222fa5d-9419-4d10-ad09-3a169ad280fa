package net.snaptag.system.business.vo;

import java.io.Serializable;
import java.util.List;

/**
 * 网页打印分组视图
 */
public class WebPrintGroupVo implements Serializable {

    /**
     *
     */
    private static final long serialVersionUID = 1L;

    /**
     * 分组标识
     */
    private String id;
    /**
     * 分组名称
     */
    private String name;
    /**
     * 是否默认
     */
    private int isDefault;
    /**
     * 我的网站集合
     */
    private List<WebPagePrintVo> pageList;


    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getIsDefault() {
        return isDefault;
    }

    public void setIsDefault(int isDefault) {
        this.isDefault = isDefault;
    }

    public List<WebPagePrintVo> getPageList() {
        return pageList;
    }

    public void setPageList(List<WebPagePrintVo> pageList) {
        this.pageList = pageList;
    }

}
