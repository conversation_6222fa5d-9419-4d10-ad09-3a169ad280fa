package net.snaptag.system.business.vo;

import java.io.Serializable;

/**
 * 网站信息
 */
public class WebPagePrintVo implements Serializable {

    /**
     *
     */
    private static final long serialVersionUID = 1L;

    private int codeId;
    private String name;
    private String groupId;
    private String linkUrl;
    private String iconUrl;
    private int isDefault;

    public WebPagePrintVo() {
        super();
    }

    public WebPagePrintVo(int codeId) {
        super();
        this.codeId = codeId;
    }

    public int getCodeId() {
        return codeId;
    }

    public void setCodeId(int codeId) {
        this.codeId = codeId;
    }

    public String getGroupId() {
        return groupId;
    }

    public void setGroupId(String groupId) {
        this.groupId = groupId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getLinkUrl() {
        return linkUrl;
    }

    public void setLinkUrl(String linkUrl) {
        this.linkUrl = linkUrl;
    }

    public String getIconUrl() {
        return iconUrl;
    }

    public void setIconUrl(String iconUrl) {
        this.iconUrl = iconUrl;
    }

    public int getIsDefault() {
        return isDefault;
    }

    public void setIsDefault(int isDefault) {
        this.isDefault = isDefault;
    }

}