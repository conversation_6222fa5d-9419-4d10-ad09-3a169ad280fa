package net.snaptag.system.business.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * 模板边框分类VO
 *
 * <AUTHOR>
 * @since 2025-08-20
 */
@Data
public class TempletBorderKindVO implements Serializable {

    private static final long serialVersionUID = 1L;

    private String borderKindId;

    private String borderKindName;

    private String displayName;

    private String englishName;

    private String traditionalName;

    private String koreanName;

    private String russianName;

    private String frenchName;

    private String spanishName;

    private String germanyName;

    private String italyName;
}
