package net.snaptag.system.business.controller;

import lombok.extern.slf4j.Slf4j;
import net.snaptag.system.business.buservice.FontService;
import net.snaptag.system.business.entity.Font;
import net.snaptag.system.business.utils.Constant;
import net.snaptag.system.business.vo.FontListVO;
import net.snaptag.system.sadais.core.common.enums.ExceptionEnums;
import net.snaptag.system.sadais.web.core.BaseController;
import net.snaptag.system.sadais.web.dto.ReturnDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 字体客户端控制器
 *
 * <AUTHOR>
 * @since 2025-01-16
 */
@RestController
@RequestMapping(Constant.PRE_MAPPING_URL + "/domestic/font")
public class FontController extends BaseController {

    @Autowired
    private FontService fontService;

    @GetMapping
    public ReturnDto getFonts(
            @RequestParam(value = "locale", defaultValue = "zh-CN") String locale) {
        try {
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, fontService.getFontsByLocale(locale));
        } catch (Exception e) {
            return this.returnFailJson(e);
        }
    }

    @GetMapping("/kind/{fontKind}")
    public ReturnDto getFontsByKind(
            @PathVariable String fontKind,
            @RequestParam(value = "locale", defaultValue = "zh-CN") String locale) {
        try {
            return this.returnSuccessJson(fontService.getFontsByKind(fontKind, locale));
        } catch (Exception e) {
            return this.returnFailJson(e);
        }
    }

    @GetMapping("/kinds")
    public ReturnDto getFontKinds(
            @RequestParam(value = "locale") String locale) {
        try {
            return this.returnSuccessJson(fontService.getFontKinds(locale));
        } catch (Exception e) {
            return this.returnFailJson(e);
        }
    }

    @GetMapping("/{fontId}")
    public ReturnDto getFontDetail(
            @PathVariable String fontId,
            @RequestParam(value = "locale", defaultValue = "zh-CN") String locale) {
        try {
            return this.returnSuccessJson(fontService.getFontDetail(fontId, locale));
        } catch (Exception e) {
            return this.returnFailJson(e);
        }
    }

    @GetMapping("/{fontId}/download")
    public void downloadFont(
            @PathVariable String fontId,
            HttpServletResponse response) {
        try {
            fontService.downloadFont(fontId, response);
        } catch (Exception e) {
            try {
                response.sendError(HttpServletResponse.SC_INTERNAL_SERVER_ERROR, "下载失败");
            } catch (Exception ex) {
                System.err.format("发送错误响应失败：%s%n", ex);
            }
        }
    }

    /**
     * 根据字体分类获取字体列表 (兼容旧API)
     * 支持动态查询：fontKind参数可选，不传则查询所有字体
     * locale通过关联font_kind_lang表获取
     *
     * @return 字体列表
     */
    @RequestMapping(value = "/getFont")
    public ReturnDto getFont() {
        try {
            // 获取字体分类参数（可选）
            String fontKind = this.getValue("fontKind");

            // 使用动态查询，locale通过SQL关联获取
            List<Font> fonts = fontService.getFontsDynamic(fontKind);

            if (fonts == null || fonts.isEmpty()) {
                throw new RuntimeException("字体不存在");
            }

            return this.returnSuccessJson(ExceptionEnums.SUCCESS, fonts);
        } catch (Exception e) {
            return this.returnFailJson(e);
        }
    }
}
