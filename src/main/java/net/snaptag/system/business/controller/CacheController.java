package net.snaptag.system.business.controller;

import com.vida.sls.annotation.SLSLog;
import com.vida.sls.enums.OperationType;
import net.snaptag.system.business.utils.Constant;
import net.snaptag.system.sadais.cache.kit.CacheKit;
import net.snaptag.system.sadais.core.common.enums.ExceptionEnums;
import net.snaptag.system.sadais.core.common.exception.ServiceException;
import net.snaptag.system.sadais.web.core.BaseController;
import net.snaptag.system.sadais.web.dto.ReturnDto;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(Constant.PRE_MAPPING_URL + "/cache")
public class CacheController extends BaseController {
    /**
     * 获取banner信息
     *
     * @return
     */
    @RequestMapping(value = "/deleteCache")
    @SLSLog(value = "删除缓存", configKey = "business", businessType = "缓存模块", operation = OperationType.DELETE)
    public ReturnDto deleteCache() {
        try {
            String key = this.getValue("key");
            CacheKit.cache().del(key);
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, "");
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    @RequestMapping(value = "/queryStringCache")
    @SLSLog(value = "查询缓存", configKey = "business", businessType = "缓存模块", operation = OperationType.SELECT)
    public ReturnDto queryStringCache() {
        try {
            String key = this.getValue("key");
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, CacheKit.cache().get(key, String.class));
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }
}
