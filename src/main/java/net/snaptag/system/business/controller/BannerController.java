package net.snaptag.system.business.controller;

import com.vida.sls.annotation.SLSLog;
import com.vida.sls.enums.OperationType;
import net.snaptag.system.business.buservice.BannerBuService;
import net.snaptag.system.business.dto.BannerInfoDto;
import net.snaptag.system.business.utils.Constant;
import net.snaptag.system.sadais.core.common.core.ToolsConst;
import net.snaptag.system.sadais.core.common.enums.ExceptionEnums;
import net.snaptag.system.sadais.core.common.exception.ServiceException;
import net.snaptag.system.sadais.util.core.ToolsKit;
import net.snaptag.system.sadais.web.core.BaseController;
import net.snaptag.system.sadais.web.dto.HeadInfoDto;
import net.snaptag.system.sadais.web.dto.ReturnDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(Constant.PRE_MAPPING_URL + "/banner")
public class BannerController extends BaseController {
    @Autowired
    private BannerBuService bannerBuService;

    /**
     * 获取banner列表
     *
     * @return
     */
    @RequestMapping(value = "/getbannerlist")
    @SLSLog(value = "获取banner列表", configKey = "business", businessType = "Banner模块", operation = OperationType.SELECT)
    public ReturnDto getBannerList() {
        try {
            String type = this.getValue("type");
            HeadInfoDto headInfoDto = this.getHeadInfoDto();
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, bannerBuService.getBannerList(type, headInfoDto));
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    /***
     * 获取所有banner
     * @return
     */
    @RequestMapping(value = "/findAllList")
    @SLSLog(value = "获取所有banner", configKey = "business", businessType = "Banner模块", operation = OperationType.SELECT)
    public ReturnDto getBannerListAll() {
        try {
            String type = this.getValue("type");
            HeadInfoDto headInfoDto = this.getHeadInfoDto();
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, bannerBuService.getBannerListAll());
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    /**
     * 新增或更新banner信息
     *
     * @return
     */
    @RequestMapping(value = "/saveorupdate")
    @SLSLog(value = "新增或更新banner信息", configKey = "business", businessType = "Banner模块", operation = OperationType.UPDATE)
    public ReturnDto saveOrUpdate() {
        try {
            BannerInfoDto bannerInfoDto = this.getBean(BannerInfoDto.class);
            bannerBuService.saveOrUpdate(bannerInfoDto);
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, null);
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    /**
     * 删除banner信息
     *
     * @return
     */
    @RequestMapping(value = "/del")
    @SLSLog(value = "删除banner信息", configKey = "business", businessType = "Banner模块", operation = OperationType.DELETE)
    public ReturnDto del() {
        try {
            String id = this.getValue("id");
            bannerBuService.del(id);
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, null);
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    /**
     * 获取banner列表
     *
     * @return
     */
    @RequestMapping(value = "/findbannerpage")
    @SLSLog(value = "获取banner列表（分页）", configKey = "business", businessType = "Banner模块", operation = OperationType.SELECT)
    public ReturnDto findBannerPage() {
        try {
            String pageNo = this.getValue("pageno");
            String pageSize = this.getValue("pagesize");
            String column = this.getValue("column");
            String localeCode = this.getValue("localeCode");
            String name = this.getValue("name");
            if (ToolsKit.isEmpty(pageNo)) {
                pageNo = "1";
            }
            if (ToolsKit.isEmpty(pageSize)) {
                pageSize = String.valueOf(ToolsConst.PHONEPAGESIZE);
            }
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, bannerBuService.findBannerPage(name, column, localeCode, Integer.parseInt(pageNo), Integer.parseInt(pageSize)));
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    /**
     * 获取banner信息
     *
     * @return
     */
    @RequestMapping(value = "/getbannerinfo")
    @SLSLog(value = "获取banner信息", configKey = "business", businessType = "Banner模块", operation = OperationType.SELECT)
    public ReturnDto getBannerInfo() {
        try {
            String id = this.getValue("id");
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, bannerBuService.getBannerInfo(id));
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }
}
