package net.snaptag.system.business.controller;

import net.snaptag.system.business.buservice.MachineService;
import net.snaptag.system.business.enums.MachineSettingsSearchType;
import net.snaptag.system.business.utils.Constant;
import net.snaptag.system.sadais.core.common.enums.ExceptionEnums;
import net.snaptag.system.sadais.util.core.ToolsKit;
import net.snaptag.system.sadais.web.core.BaseController;
import net.snaptag.system.sadais.web.dto.ReturnDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Author：Chenjy
 * Date:2025/8/25
 * Description:
 */
@RestController
@RequestMapping(Constant.PRE_MAPPING_URL + "/domestic/device")
public class MachineController extends BaseController {
    @Autowired
    private MachineService machineService;

    @GetMapping("/getSettings")
    public ReturnDto getSettings() {
        try {
            Integer type = this.getIntValue("type") == -1 ? 1 : this.getIntValue("type");
            MachineSettingsSearchType searchType = MachineSettingsSearchType.get(type);
            if (searchType == MachineSettingsSearchType.NULL) {
                throw new RuntimeException("请求参数type不正确！");
            }
            String search = this.getValue("search");
            if (ToolsKit.isEmpty(search)) {
                throw new RuntimeException("请求参数search是必需的！");
            }

            return returnSuccessJson(ExceptionEnums.SUCCESS, machineService.getSettings(searchType, search));
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
