package net.snaptag.system.business.controller;

import com.vida.sls.annotation.SLSLog;
import com.vida.sls.enums.OperationType;
import net.snaptag.system.business.buservice.PaperDisplayBuService;
import net.snaptag.system.business.entity.PaperDisplay;
import net.snaptag.system.business.utils.Constant;
import net.snaptag.system.sadais.core.common.core.ToolsConst;
import net.snaptag.system.sadais.core.common.enums.ExceptionEnums;
import net.snaptag.system.sadais.core.common.exception.ServiceException;
import net.snaptag.system.sadais.util.core.ToolsKit;
import net.snaptag.system.sadais.web.core.BaseController;
import net.snaptag.system.sadais.web.dto.ReturnDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date ：Created in 2024/3/29 10:30
 * @description：
 * @modified By：
 * @version: $
 */
@RestController
@RequestMapping(Constant.PRE_MAPPING_URL + "/paperdisplay")
public class PaperDisplayController extends BaseController {
    @Autowired
    private PaperDisplayBuService paperDisplayBuService;

    @RequestMapping(value = "/getPage")
    @SLSLog(value = "获取纸张", configKey = "business", businessType = "纸张模块", operation = OperationType.SELECT)
    public ReturnDto findPage() {
        try {
            String pageNo = this.getValue("pageno");
            String pageSize = this.getValue("pagesize");
            String name = this.getValue("name");

            if (ToolsKit.isEmpty(pageNo)) {
                pageNo = "0";
            }
            if (ToolsKit.isEmpty(pageSize)) {
                pageSize = ToolsConst.PHONEPAGESIZE + "";
            }

            return this.returnSuccessJson(ExceptionEnums.SUCCESS, paperDisplayBuService.findPage(Integer.parseInt(pageNo), Integer.parseInt(pageSize), name));
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    @RequestMapping(value = "/saveorupdate")
    @SLSLog(value = "新增或更新纸张", configKey = "business", businessType = "纸张模块", operation = OperationType.UPDATE)
    public ReturnDto saveOrUpdate() {
        try {
            PaperDisplay paperDisplay = this.getBean(PaperDisplay.class);
            String userid = this.getValue("userid");
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, paperDisplayBuService.saveOrUpdate(paperDisplay, userid));
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    @RequestMapping(value = "/delete")
    @SLSLog(value = "删除纸张", configKey = "business", businessType = "纸张模块", operation = OperationType.DELETE)
    public ReturnDto delete() {
        try {
            String id = this.getValue("id");
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, paperDisplayBuService.delById(id));
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }
}
