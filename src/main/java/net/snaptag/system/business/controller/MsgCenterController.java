package net.snaptag.system.business.controller;

import com.vida.sls.annotation.SLSLog;
import com.vida.sls.enums.OperationType;
import net.snaptag.system.business.buservice.MsgCenterBuService;
import net.snaptag.system.business.buservice.MsgColumnBuService;
import net.snaptag.system.business.dto.GetMsgColumnDto;
import net.snaptag.system.business.dto.SaveMsgColumnDto;
import net.snaptag.system.business.dto.SendMsgDto;
import net.snaptag.system.business.utils.Constant;
import net.snaptag.system.sadais.core.common.core.ToolsConst;
import net.snaptag.system.sadais.core.common.enums.ExceptionEnums;
import net.snaptag.system.sadais.core.common.exception.ServiceException;
import net.snaptag.system.sadais.util.core.ToolsKit;
import net.snaptag.system.sadais.web.core.BaseController;
import net.snaptag.system.sadais.web.dto.HeadInfoDto;
import net.snaptag.system.sadais.web.dto.ReturnDto;
import net.snaptag.system.sadais.web.utils.WebTools;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Locale;

/**
 * 消息模块
 */
@RestController
@RequestMapping(Constant.PRE_MAPPING_URL + "/msgcenter")
public class MsgCenterController extends BaseController {
    @Autowired
    private MsgCenterBuService msgCenterBuService;
    @Autowired
    private MsgColumnBuService msgColumnBuService;

    /**
     * 获取消息列表
     *
     * @return
     */
    @RequestMapping(value = "/getmsgcenterlist")
    @SLSLog(value = "获取消息列表", configKey = "business", businessType = "消息模块", operation = OperationType.SELECT)
    public ReturnDto getMsgCenterDtoList() {
        try {
            String userId = this.getValue("userid");
            String msgType = this.getValue("msgtype");
            String pageNo = this.getValue("pageno");
            String pageSize = this.getValue("pagesize");
            String lastId = this.getValue("lastid");
            if (ToolsKit.isEmpty(pageNo)) {
                pageNo = "1";
            }
            if (ToolsKit.isEmpty(pageSize)) {
                pageSize = String.valueOf(ToolsConst.PHONEPAGESIZE);
            }
            HeadInfoDto headInfoDto = this.getHeadInfoDto();
            Locale locale = WebTools.getLocaleByLanguage(headInfoDto.getLanguage());
            return this.returnSuccessJson(ExceptionEnums.SUCCESS,
                    msgCenterBuService.getMsgCenterDtoList(userId, msgType, Integer.parseInt(pageNo), Integer.parseInt(pageSize), lastId, locale, headInfoDto.getVersion()));
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    /**
     * 获取消息栏目信息
     *
     * @return
     */
    @RequestMapping(value = "/getmsgcolumn")
    @SLSLog(value = "获取消息栏目信息", configKey = "business", businessType = "消息模块", operation = OperationType.SELECT)
    public ReturnDto getMsgColumnInfo() {
        try {
            String userId = this.getValue("userid");
            HeadInfoDto headInfoDto = this.getHeadInfoDto();
            Locale locale = WebTools.getLocaleByLanguage(headInfoDto.getLanguage());
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, msgColumnBuService.getMsgColumnInfo(userId, true, locale, headInfoDto.getVersion()));
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    /**
     * 获取消息栏目信息(只是获取feedback类型)
     *
     * @return
     */
    @RequestMapping(value = "/getmsgcolumnfeedback")
    @SLSLog(value = "获取消息栏目信息(只是获取feedback类型)", configKey = "business", businessType = "消息模块", operation = OperationType.SELECT)
    public ReturnDto getMsgColumnInfoFeedback() {
        try {
            String userId = this.getValue("userid");
            HeadInfoDto headInfoDto = this.getHeadInfoDto();
            Locale locale = WebTools.getLocaleByLanguage(headInfoDto.getLanguage());
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, msgColumnBuService.getMsgColumnInfoFeedback(userId, locale));
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    /**
     * 获取消息栏目信息(只是获取offical类型)
     *
     * @return
     */
    @RequestMapping(value = "/getmsgcolumnoffical")
    @SLSLog(value = "获取消息栏目信息(只是获取offical类型)", configKey = "business", businessType = "消息模块", operation = OperationType.SELECT)
    public ReturnDto getMsgColumnInfoOffical() {
        try {
            String userId = this.getValue("userid");
            HeadInfoDto headInfoDto = this.getHeadInfoDto();
            Locale locale = WebTools.getLocaleByLanguage(headInfoDto.getLanguage());
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, msgColumnBuService.getMsgColumnInfoOffical(userId, locale));
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }


    /**
     * 根据类型获取消息栏目信息
     *
     * @return
     */
    @RequestMapping(value = "/getmsgcolumnbytype")
    @SLSLog(value = "根据类型获取消息栏目信息", configKey = "business", businessType = "消息模块", operation = OperationType.SELECT)
    public ReturnDto getMsgColumnByType() {
        try {
            GetMsgColumnDto getMsgColumnDto = this.getBean(GetMsgColumnDto.class);
            HeadInfoDto headInfoDto = this.getHeadInfoDto();
            Locale locale = WebTools.getLocaleByLanguage(headInfoDto.getLanguage());
            return this.returnSuccessJson(ExceptionEnums.SUCCESS,
                    msgColumnBuService.getMsgColumnByType(getMsgColumnDto.getUserId(), getMsgColumnDto.getType(), false, locale));
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    /**
     * 保存消息栏目信息
     *
     * @return
     */
    @RequestMapping(value = "/savemsgcolumnbytype")
    @SLSLog(value = "保存消息栏目信息", configKey = "business", businessType = "消息模块", operation = OperationType.INSERT)
    public ReturnDto saveMsgColumnByType() {
        try {
            SaveMsgColumnDto saveMsgColumnDto = this.getBean(SaveMsgColumnDto.class);
            msgColumnBuService.saveMsgColumnByType(saveMsgColumnDto);
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, null);
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    /**
     * 发送消息
     *
     * @return
     */
    @RequestMapping(value = "/sendmsg")
    @SLSLog(value = "发送消息", configKey = "business", businessType = "消息模块", operation = OperationType.SUBMIT)
    public ReturnDto sendMsg() {
        try {
            SendMsgDto sendMsgDto = this.getBean(SendMsgDto.class);
            msgCenterBuService.sendMsg(sendMsgDto);
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, null);
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    /**
     * 获取小铃铛消息
     *
     * @return
     */
    @RequestMapping(value = "/getmsgcount")
    @SLSLog(value = "获取小铃铛消息", configKey = "business", businessType = "消息模块", operation = OperationType.SELECT)
    public ReturnDto getMsgCount() {
        try {
            String userId = this.getValue("userid");
            HeadInfoDto headInfoDto = this.getHeadInfoDto();
            Locale locale = WebTools.getLocaleByLanguage(headInfoDto.getLanguage());
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, msgColumnBuService.getMsgCount(userId, locale, headInfoDto.getVersion()));
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    /**
     * 获取消息统计：
     * 1.个人主页：粉丝消息FOLLOWS
     * 2.消息中心：社区动态COMMUNITY, 系统公告SYSTEM, 官方消息FEEDBACK，互动打印SHARE
     *
     * @return
     */
    @RequestMapping(value = "/getmsgcountstat")
    @SLSLog(value = "获取消息统计", configKey = "business", businessType = "消息模块", operation = OperationType.SELECT)
    public ReturnDto getMsgCountStat() {
        try {
            String userId = this.getValue("userid");
            HeadInfoDto headInfoDto = this.getHeadInfoDto();
            Locale locale = WebTools.getLocaleByLanguage(headInfoDto.getLanguage());
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, msgColumnBuService.getMsgCountStat(userId, locale, headInfoDto.getVersion()));
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    /**
     * 删除消息
     *
     * @return
     */
    @RequestMapping(value = "/del")
    @SLSLog(value = "删除消息", configKey = "business", businessType = "消息模块", operation = OperationType.DELETE)
    public ReturnDto del() {
        try {
            String id = this.getValue("id");
            msgCenterBuService.delMsg(id);
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, null);
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    /**
     * 删除消息
     *
     * @return
     */
    @RequestMapping(value = "/delbytype")
    @SLSLog(value = "删除消息", configKey = "business", businessType = "消息模块", operation = OperationType.DELETE)
    public ReturnDto delByType() {
        try {
            String id = this.getValue("id");
            String userId = this.getValue("userid");
            String type = this.getValue("type");
            msgCenterBuService.delMsgByType(userId, Integer.parseInt(type));
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, null);
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    /**
     * 标识消息为已读状态
     *
     * @return
     */
    @RequestMapping(value = "/read")
    @SLSLog(value = "标识消息为已读状态", configKey = "business", businessType = "消息模块", operation = OperationType.UPDATE)
    public ReturnDto read() {
        try {
            String id = this.getValue("id");
            msgCenterBuService.read(id);
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, null);
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

}
