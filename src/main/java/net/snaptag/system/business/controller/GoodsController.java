package net.snaptag.system.business.controller;

import com.vida.sls.annotation.SLSLog;
import com.vida.sls.enums.OperationType;
import net.snaptag.system.business.buservice.GoodsBuService;
import net.snaptag.system.business.entity.Goods;
import net.snaptag.system.business.utils.Constant;
import net.snaptag.system.sadais.core.common.core.ToolsConst;
import net.snaptag.system.sadais.core.common.enums.ExceptionEnums;
import net.snaptag.system.sadais.core.common.exception.ServiceException;
import net.snaptag.system.sadais.util.core.ToolsKit;
import net.snaptag.system.sadais.web.core.BaseController;
import net.snaptag.system.sadais.web.dto.ReturnDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date ：Created in 2021/10/22 10:37
 * @description：
 * @modified By：
 * @version: $
 */
@RestController
@RequestMapping(Constant.PRE_MAPPING_URL + "/goods")
public class GoodsController extends BaseController {
    @Autowired
    private GoodsBuService goodsBuService;

    /**
     * 获取商品列表
     *
     * @return
     */
    @RequestMapping(value = "/getlist")
    @SLSLog(value = "获取商品列表", configKey = "business", businessType = "商品模块", operation = OperationType.SELECT)
    public ReturnDto getGoodsList() {
        try {
            String pageNo = this.getValue("pageno");
            String pageSize = this.getValue("pagesize");
            if (ToolsKit.isEmpty(pageNo)) {
                pageNo = "1";
            }
            if (ToolsKit.isEmpty(pageSize)) {
                pageSize = String.valueOf(ToolsConst.PHONEPAGESIZE);
            }
            return this.returnSuccessJson(ExceptionEnums.SUCCESS,
                    goodsBuService.getPageList(Integer.parseInt(pageNo), Integer.parseInt(pageSize)));
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    // 支撑平台 ----------------------------------------
    @RequestMapping(value = "/getPage")
    @SLSLog(value = "获取商品列表", configKey = "business", businessType = "商品模块", operation = OperationType.SELECT)
    public ReturnDto getGoodsPage() {
        try {
            String pageNo = this.getValue("pageno");
            String pageSize = this.getValue("pagesize");
            String saleStatus = this.getValue("saleStatus");
            Integer saleStatusValue = null;
            if (ToolsKit.isNotEmpty(saleStatus)) {
                saleStatusValue = Integer.parseInt(saleStatus);
            }
            if (ToolsKit.isEmpty(pageNo)) {
                pageNo = "1";
            }
            if (ToolsKit.isEmpty(pageSize)) {
                pageSize = String.valueOf(ToolsConst.PHONEPAGESIZE);
            }
            return this.returnSuccessJson(ExceptionEnums.SUCCESS,
                    goodsBuService.getPageList(saleStatusValue, Integer.parseInt(pageNo), Integer.parseInt(pageSize)));
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    @RequestMapping(value = "/addorupdate")
    @SLSLog(value = "新增或更新商品", configKey = "business", businessType = "商品模块", operation = OperationType.UPDATE)
    public ReturnDto addOrUpdate() {
        try {
            String userId = this.getValue("userid");
            Goods goods = this.getBean(Goods.class);
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, goodsBuService.addOrUpdate(goods, userId));
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    @RequestMapping(value = "/deletebyid")
    @SLSLog(value = "删除商品", configKey = "business", businessType = "商品模块", operation = OperationType.DELETE)
    public ReturnDto deleteById() {
        try {
            String id = this.getValue("id");
            goodsBuService.deleteById(id);
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, null);
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }
}
