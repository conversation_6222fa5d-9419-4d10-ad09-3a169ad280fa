package net.snaptag.system.business.controller;

import com.vida.sls.annotation.SLSLog;
import com.vida.sls.enums.OperationType;
import net.snaptag.system.business.buservice.SystemMsgBuService;
import net.snaptag.system.business.dto.SystemMsgDto;
import net.snaptag.system.business.utils.Constant;
import net.snaptag.system.sadais.core.common.core.ToolsConst;
import net.snaptag.system.sadais.core.common.enums.ExceptionEnums;
import net.snaptag.system.sadais.core.common.exception.ServiceException;
import net.snaptag.system.sadais.util.core.ToolsKit;
import net.snaptag.system.sadais.web.core.BaseController;
import net.snaptag.system.sadais.web.dto.ReturnDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 系统消息模块
 */
@RestController
@RequestMapping(Constant.PRE_MAPPING_URL + "/sysmsg")
public class SystemMsgController extends BaseController {
    @Autowired
    private SystemMsgBuService systemMsgBuService;

    /**
     * 保存或更新系统消息
     *
     * @return
     */
    @RequestMapping(value = "/saveorupdate")
    @SLSLog(value = "保存或更新系统消息", configKey = "business", businessType = "系统消息模块", operation = OperationType.UPDATE)
    public ReturnDto saveOrUpdate() {
        try {
            SystemMsgDto systemMsgDto = this.getBean(SystemMsgDto.class);
            systemMsgBuService.saveOrUpdate(systemMsgDto);
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, null);
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    /**
     * 删除系统消息
     *
     * @return
     */
    @RequestMapping(value = "/del")
    @SLSLog(value = "删除系统消息", configKey = "business", businessType = "系统消息模块", operation = OperationType.DELETE)
    public ReturnDto del() {
        try {
            String id = this.getValue("id");
            systemMsgBuService.del(id);
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, null);
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    /**
     * 获取系统消息列表
     *
     * @return
     */
    @RequestMapping(value = "/findsystemmsgpage")
    @SLSLog(value = "获取系统消息列表", configKey = "business", businessType = "系统消息模块", operation = OperationType.SELECT)
    public ReturnDto findSystemMsgPage() {
        try {
            String startDate = this.getValue("startdate");
            String endDate = this.getValue("enddate");
            String pageNo = this.getValue("pageno");
            String pageSize = this.getValue("pagesize");
            if (ToolsKit.isEmpty(pageNo)) {
                pageNo = "1";
            }
            if (ToolsKit.isEmpty(pageSize)) {
                pageSize = String.valueOf(ToolsConst.PHONEPAGESIZE);
            }
            return this.returnSuccessJson(ExceptionEnums.SUCCESS,
                    systemMsgBuService.findSystemMsgPage(startDate, endDate, Integer.parseInt(pageNo), Integer.parseInt(pageSize)));
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }
}
