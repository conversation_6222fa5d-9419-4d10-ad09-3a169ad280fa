package net.snaptag.system.business.controller;

import com.vida.sls.annotation.SLSLog;
import com.vida.sls.enums.OperationType;
import net.snaptag.system.business.buservice.DictBuService;
import net.snaptag.system.business.entity.Dict;
import net.snaptag.system.business.utils.Constant;
import net.snaptag.system.sadais.core.common.core.ToolsConst;
import net.snaptag.system.sadais.core.common.enums.ExceptionEnums;
import net.snaptag.system.sadais.core.common.exception.ServiceException;
import net.snaptag.system.sadais.util.core.ToolsKit;
import net.snaptag.system.sadais.web.core.BaseController;
import net.snaptag.system.sadais.web.dto.ReturnDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date ：Created in 2023/8/24 14:15
 * @description：字典项
 * @modified By：
 * @version: $
 */
@RestController
@RequestMapping(Constant.PRE_MAPPING_URL + "/dict")
public class DictController extends BaseController {
    @Autowired
    private DictBuService dictBuService;

    @RequestMapping(value = "/findPage")
    @SLSLog(value = "获取字典项列表", configKey = "business", businessType = "字典模块", operation = OperationType.SELECT)
    public ReturnDto getDictPage() {
        try {
            String type = this.getValue("type");
            String pageNo = this.getValue("pageNo");
            String pageSize = this.getValue("pageSize");

            if (ToolsKit.isEmpty(pageNo)) {
                pageNo = "1";
            }
            if (ToolsKit.isEmpty(pageSize)) {
                pageSize = String.valueOf(ToolsConst.PHONEPAGESIZE);
            }
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, dictBuService.findPageByType(Integer.parseInt(pageNo), Integer.parseInt(pageSize), type));
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    @RequestMapping(value = "/findListByType")
    @SLSLog(value = "获取字典项列表（类型）", configKey = "business", businessType = "字典模块", operation = OperationType.SELECT)
    public ReturnDto findListByType() {
        try {
            String type = this.getValue("type");
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, dictBuService.findListByType(type));
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    @RequestMapping(value = "/findDistinctTypeList")
    @SLSLog(value = "获取类型列表", configKey = "business", businessType = "字典模块", operation = OperationType.SELECT)
    public ReturnDto findDistinctTypeList() {
        try {
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, dictBuService.getDistinctType());
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    @RequestMapping(value = "/addOrUpdate")
    @SLSLog(value = "新增或更新字典", configKey = "business", businessType = "字典模块", operation = OperationType.UPDATE)
    public ReturnDto addOrUpdate() {
        try {
            Dict dict = this.getBean(Dict.class);
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, dictBuService.addOrUpdate(dict));
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    @RequestMapping(value = "/del")
    @SLSLog(value = "删除字典", configKey = "business", businessType = "字典模块", operation = OperationType.DELETE)
    public ReturnDto del() {
        try {
            String id = this.getValue("id");
            dictBuService.delete(id);
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, null);
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    /***
     * 获取打印机wifi名称的信息
     * @return
     */
    @RequestMapping(value = "/getprinterinfo")
    @SLSLog(value = "获取打印机wifi名称的信息", configKey = "business", businessType = "字典模块", operation = OperationType.SELECT)
    public ReturnDto getWifiInfo() {
        try {
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, dictBuService.getWifiInfo());
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    /***
     * 获取打印机wifi名称的信息
     * @return
     */
    @RequestMapping(value = "/refreshWifiMd5")
    @SLSLog(value = "刷新打印机WiFiMD5", configKey = "business", businessType = "字典模块", operation = OperationType.UPDATE)
    public ReturnDto refreshWifiMd5() {
        try {
            String id = this.getValue("id");
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, dictBuService.refreshWifiMd5(id));
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

}
