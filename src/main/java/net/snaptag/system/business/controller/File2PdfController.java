package net.snaptag.system.business.controller;

import com.vida.sls.annotation.SLSLog;
import com.vida.sls.enums.OperationType;
import net.snaptag.system.business.utils.Constant;
import net.snaptag.system.sadais.web.core.BaseController;
import net.snaptag.system.sadais.web.dto.ReturnDto;
import net.snaptag.system.sadais.core.common.enums.ExceptionEnums;
import net.snaptag.system.sadais.core.common.exception.ServiceException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import net.snaptag.system.business.buservice.File2PdfBuService;

/**
 * <AUTHOR>
 * @date ：Created in 2024/12/10 09:18
 * @description：
 * @modified By：
 * @version: $
 */
@RestController
@RequestMapping(Constant.PRE_MAPPING_URL + "/file2pdf")
public class File2PdfController extends BaseController {
    @Autowired
    private File2PdfBuService file2PdfBuService;

    @RequestMapping(value = "/convert")
    @SLSLog(value = "文档转PDF", configKey = "business", businessType = "PDF模块", operation = OperationType.SUBMIT)
    public ReturnDto file2Pdf() {
        try {
            String fileUrl = this.getValue("fileUrl");
            return this.returnSuccessJson(ExceptionEnums.SUCCESS,
                    file2PdfBuService.word2pdf(fileUrl));
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    @RequestMapping(value = "/result")
    @SLSLog(value = "获取结果", configKey = "business", businessType = "PDF模块", operation = OperationType.SELECT)
    public ReturnDto queryResult() {
        try {
            String convertTaskId = this.getValue("convertTaskId");
            return this.returnSuccessJson(ExceptionEnums.SUCCESS,
                    file2PdfBuService.result(convertTaskId));
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }
}
