package net.snaptag.system.business.controller;

import com.alibaba.nacos.api.utils.StringUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.vida.sls.annotation.SLSLog;
import com.vida.sls.enums.OperationType;
import net.snaptag.system.business.buservice.UserFeedbackBuService;
import net.snaptag.system.business.entity.UserFeedback;
import net.snaptag.system.business.utils.Constant;
import net.snaptag.system.sadais.core.common.core.ToolsConst;
import net.snaptag.system.sadais.core.common.enums.ExceptionEnums;
import net.snaptag.system.sadais.core.common.exception.ServiceException;
import net.snaptag.system.sadais.util.core.ToolsKit;
import net.snaptag.system.sadais.web.core.BaseController;
import net.snaptag.system.sadais.web.dto.HeadInfoDto;
import net.snaptag.system.sadais.web.dto.ReturnDto;
import net.snaptag.system.sadais.web.utils.WebTools;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.Locale;
import java.util.Locale;

/**
 * 反馈控制器
 * 已修正JSON字段映射问题
 */
@RestController
@RequestMapping(Constant.PRE_MAPPING_URL + "/feedback")
public class FeedBackController extends BaseController {
    
    @Autowired
    private UserFeedbackBuService userFeedbackBuService;

    /**
     * 获取用户的反馈列表
     * @return
     */
    @RequestMapping(value = "/getpagelist")
    public ReturnDto getPageList() {
        try {
            String startDate = this.getValue("startDate");
            String endDate = this.getValue("endDate");
            String pageNo = this.getValue("pageNo");
            String pageSize = this.getValue("pageSize");
            if (ToolsKit.isEmpty(pageNo)) {
                pageNo = "1";
            }
            if (ToolsKit.isEmpty(pageSize)) {
                pageSize = String.valueOf(ToolsConst.PHONEPAGESIZE);
            }
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, userFeedbackBuService.findUserFeedbackPage(startDate, endDate, Integer.parseInt(pageNo)-1, Integer.parseInt(pageSize)));
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    /**
     * 评论用户的反馈
     *
     * @return
     */
    @RequestMapping(value = "/update")
    public ReturnDto update() {
        try {
            String id = this.getValue("id");
            String remark = this.getValue("remark");
            userFeedbackBuService.update(id, remark);
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, null);
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    /**
     * 删除用户的反馈
     *
     * @return
     */
    @RequestMapping(value = "/delete")
    public ReturnDto delete() {
        try {
            String id = this.getValue("id");
            userFeedbackBuService.deleteById(id);
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, null);
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    /**
     * 获取用户历史反馈
     * @return
     */
    @RequestMapping(value = "/gethistories")
    public ReturnDto getHistories() {
        try {
            String userId = this.getValue("userid");
            String pageNo = this.getValue("pageno");
            String pageSize = this.getValue("pagesize");

            if (ToolsKit.isEmpty(pageNo)) {
                pageNo = "1";
            }
            if (ToolsKit.isEmpty(pageSize)) {
                pageSize = String.valueOf(ToolsConst.PHONEPAGESIZE);
            }
            if (StringUtils.isBlank(userId)) {
                return this.returnSuccessJson(ExceptionEnums.PARAM_ERROR, new ArrayList<>());
            }

            return this.returnSuccessJson(ExceptionEnums.SUCCESS,
                    userFeedbackBuService.getUserFeedbackList(userId, Integer.parseInt(pageNo), Integer.parseInt(pageSize)));
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    /**
     * 获取用户历史反馈
     * @return
     */
    @RequestMapping(value = "/gethistories2")
    public ReturnDto getHistories2() {
        try {
            String userId = this.getValue("userid");
            String pageNo = this.getValue("pageno");
            String pageSize = this.getValue("pagesize");

            if (ToolsKit.isEmpty(pageNo)) {
                pageNo = "1";
            }
            if (ToolsKit.isEmpty(pageSize)) {
                pageSize = String.valueOf(ToolsConst.PHONEPAGESIZE);
            }
            if (StringUtils.isBlank(userId)) {
                return this.returnSuccessJson(ExceptionEnums.PARAM_ERROR, new ArrayList<>());
            }
            HeadInfoDto headInfoDto = getHeadInfoDto();
            Locale locale = WebTools.getLocaleByLanguage(headInfoDto.getLanguage());
            return this.returnSuccessJson(ExceptionEnums.SUCCESS,
                    userFeedbackBuService.getUserFeedbackList2(userId, Integer.parseInt(pageNo), Integer.parseInt(pageSize), locale));
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    /**
     * 添加用户反馈
     * @return
     */
    @RequestMapping(value = "/saveuserfeedback")
    @SLSLog(value = "保存用户反馈", configKey = "business", businessType = "用户反馈模块", operation = OperationType.UPDATE)
    public ReturnDto saveUserFeedback() {
        try {
            String userId = this.getValue("userid");
            String clientInfo = this.getValue("sadais-agent");
            UserFeedback addUserFeedbackDto = this.getBean(UserFeedback.class);
            HeadInfoDto headInfoDto = getHeadInfoDto();
            Locale locale = WebTools.getLocaleByLanguage(headInfoDto.getLanguage());

            if (StringUtils.isBlank(userId) || addUserFeedbackDto == null) {
                return this.returnSuccessJson(ExceptionEnums.PARAM_ERROR, "");
            }

            // 调用兼容的addUserFeedback方法
            userFeedbackBuService.addUserFeedback(
                    userId,
                    addUserFeedbackDto.getType(),
                    addUserFeedbackDto.getQtype(),
                    addUserFeedbackDto.getContent(),
                    parseImagesFromDto(addUserFeedbackDto), // 处理图片列表
                    addUserFeedbackDto.getMobile(),
                    clientInfo,
                    addUserFeedbackDto.getPrinterType(),
                    locale
            );

            return this.returnSuccessJson(ExceptionEnums.SUCCESS, "反馈提交成功");
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    /**
     * 从DTO中解析图片列表
     */
    private List<String> parseImagesFromDto(UserFeedback dto) {
        List<String> imageList = new ArrayList<>();
        if (dto != null && ToolsKit.isNotEmpty(dto.getImages())) {
            // 如果images是JSON字符串，解析它
            try {
                ObjectMapper mapper = new ObjectMapper();
                imageList = mapper.readValue(dto.getImages(), new TypeReference<List<String>>() {});
            } catch (Exception e) {
                // 如果解析失败，尝试按逗号分割
                String[] images = dto.getImages().split(",");
                for (String image : images) {
                    if (ToolsKit.isNotEmpty(image.trim())) {
                        imageList.add(image.trim());
                    }
                }
            }
        }
        return imageList;
    }

    /**
     * 获取用户反馈详情
     * @return
     */
    @RequestMapping(value = "/getDetail")
    public ReturnDto getDetail() {
        try {
            String id = this.getValue("id");
            String mid = this.getValue("mid");
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, userFeedbackBuService.getById(id, mid));
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    /**
     * 获取需要反馈的机器类型
     * @return
     */
    @RequestMapping(value = "/getprintertype")
    public ReturnDto getPrinterType(){
        HeadInfoDto headInfoDto = getHeadInfoDto();
        Locale locale = WebTools.getLocaleByLanguage(headInfoDto.getLanguage());
        return this.returnSuccessJson(ExceptionEnums.SUCCESS, userFeedbackBuService.getPrinterType(locale));
    }

    /**
     * 获取需要反馈的问题类型
     * @return
     */
    @RequestMapping(value = "/getproblemtype")
    public ReturnDto getProblemType(){
        HeadInfoDto headInfoDto = getHeadInfoDto();
        Locale locale = WebTools.getLocaleByLanguage(headInfoDto.getLanguage());
        return this.returnSuccessJson(ExceptionEnums.SUCCESS, userFeedbackBuService.getProblemType(locale));
    }


}
