package net.snaptag.system.business.controller;


import com.vida.sls.annotation.SLSLog;
import com.vida.sls.enums.OperationType;
import net.snaptag.system.business.buservice.DeviceConnectLogBuService;
import net.snaptag.system.business.dto.ConnectLogDto;
import net.snaptag.system.business.utils.Constant;
import net.snaptag.system.sadais.core.common.core.ToolsConst;
import net.snaptag.system.sadais.core.common.enums.ExceptionEnums;
import net.snaptag.system.sadais.core.common.exception.ServiceException;
import net.snaptag.system.sadais.util.core.ToolsKit;
import net.snaptag.system.sadais.web.core.BaseController;
import net.snaptag.system.sadais.web.dto.HeadInfoDto;
import net.snaptag.system.sadais.web.dto.ReturnDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(Constant.PRE_MAPPING_URL + "/device")
public class DeviceController extends BaseController {
    @Autowired
    private DeviceConnectLogBuService deviceConnectLogBuService;

    /**
     * 保存设备连接日志
     *
     * @return
     */
    @RequestMapping(value = "/saveconnlog")
    @SLSLog(value = "保存设备连接日志", configKey = "business", businessType = "设备模块", operation = OperationType.SUBMIT)
    public ReturnDto saveConnLog() {
        try {
            HeadInfoDto headInfoDto = this.getHeadInfoDto();
            ConnectLogDto connectLogDto = this.getBean(ConnectLogDto.class);
            connectLogDto.setAppVersion(headInfoDto.getVersion());
            if (ToolsKit.isEmpty(connectLogDto.getUserId())) {
                connectLogDto.setUserId(this.getValue("userid"));
            }
            ToolsKit.Thread.execute(new Runnable() {
                @Override
                public void run() {
                    deviceConnectLogBuService.addOrUpdateEntity(connectLogDto);
                }
            });
//            QueueKit.queue().addToQueue(new QueueMessage(connectLogDto, "DeviceConnectLogBuService"));
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, null);
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    /****支撑平台***/

    /**
     * 获取page
     *
     * @return
     */
    @RequestMapping(value = "/getPage")
    @SLSLog(value = "获取page", configKey = "business", businessType = "设备模块", operation = OperationType.SELECT)
    public ReturnDto findPage() {
        try {
            String otherUserId = this.getValue("otherUserId");
            String deviceSn = this.getValue("deviceSn");
            String deviceName = this.getValue("deviceName");
            String lastTimeBegin = this.getValue("lastTimeBegin");
            String lastTimeEnd = this.getValue("lastTimeEnd");
            String macAddress = this.getValue("macAddress");

            String pageNo = this.getValue("pageno");
            String pageSize = this.getValue("pagesize");

            if (ToolsKit.isEmpty(pageNo)) {
                pageNo = "0";
            }
            if (ToolsKit.isEmpty(pageSize)) {
                pageSize = ToolsConst.PHONEPAGESIZE + "";
            }

            return this.returnSuccessJson(ExceptionEnums.SUCCESS,
                    deviceConnectLogBuService.findPage(Integer.parseInt(pageNo),
                            Integer.parseInt(pageSize),
                            otherUserId,
                            deviceSn,
                            deviceName,
                            macAddress,
                            lastTimeBegin,
                            lastTimeEnd
                    ));
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }
}
