package net.snaptag.system.business.controller;

import com.vida.sls.annotation.SLSLog;
import com.vida.sls.enums.OperationType;
import net.snaptag.system.business.buservice.PrintPaperInfoBuService;
import net.snaptag.system.business.entity.PrintPaperInfo;
import net.snaptag.system.business.utils.Constant;
import net.snaptag.system.business.utils.ToolUtils;
import net.snaptag.system.sadais.core.common.enums.ExceptionEnums;
import net.snaptag.system.sadais.core.common.exception.ServiceException;
import net.snaptag.system.sadais.web.core.BaseController;
import net.snaptag.system.sadais.web.dto.HeadInfoDto;
import net.snaptag.system.sadais.web.dto.ReturnDto;
import net.snaptag.system.sadais.web.utils.WebTools;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Locale;

/**
 * <AUTHOR>
 * @date ：Created in 2023/7/20 11:36
 * @description：
 * @modified By：
 * @version: $
 */
@RestController
@RequestMapping(Constant.PRE_MAPPING_URL + "/printpaper")
public class PrintPaperInfoController extends BaseController {
    @Autowired
    private PrintPaperInfoBuService printPaperInfoBuService;

    @RequestMapping(value = "/findList")
    @SLSLog(value = "获取列表", configKey = "business", businessType = "打印纸张信息模块", operation = OperationType.SELECT)
    public ReturnDto findList() {
        try {
            HeadInfoDto headInfoDto = getHeadInfoDto();
            Locale locale = WebTools.getLocaleByLanguage(headInfoDto.getLanguage());
            String printerType = this.getValue("printerType");
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, printPaperInfoBuService.getList(printerType, null, locale));
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    @RequestMapping(value = "/findSizesList")
    @SLSLog(value = "获取尺寸列表", configKey = "business", businessType = "打印纸张信息模块", operation = OperationType.SELECT)
    public ReturnDto findSize() {
        try {
            HeadInfoDto headInfoDto = getHeadInfoDto();
            Locale locale = WebTools.getLocaleByLanguage(headInfoDto.getLanguage());
            String printerType = this.getValue("printerType");
            String type = this.getValue("type");
            String height = this.getValue("height");
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, printPaperInfoBuService.getSizeList2(printerType, type, height, locale, headInfoDto.getVersion()));
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    /**
     * ---------------支撑平台----------------
     **/
    @RequestMapping(value = "/findListAll")
    @SLSLog(value = "获取列表", configKey = "business", businessType = "打印纸张信息模块", operation = OperationType.SELECT)
    public ReturnDto findList_pl() {
        try {
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, printPaperInfoBuService.getList());
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    @RequestMapping(value = "/saveOrUpdate")
    @SLSLog(value = "新增或更新纸张信息", configKey = "business", businessType = "打印纸张信息模块", operation = OperationType.UPDATE)
    public ReturnDto saveOrUpdate() {
        try {
            PrintPaperInfo printPaperInfo = getBean(PrintPaperInfo.class);
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, printPaperInfoBuService.saveOrUpdate(printPaperInfo));
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    @RequestMapping(value = "/delete")
    @SLSLog(value = "删除纸张信息", configKey = "business", businessType = "打印纸张信息模块", operation = OperationType.DELETE)
    public ReturnDto delete() {
        try {
            String id = this.getValue("id");
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, printPaperInfoBuService.delete(id));
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }
}
