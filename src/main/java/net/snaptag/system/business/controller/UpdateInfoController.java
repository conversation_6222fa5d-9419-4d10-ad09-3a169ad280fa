package net.snaptag.system.business.controller;

import com.vida.sls.annotation.SLSLog;
import com.vida.sls.enums.OperationType;
import net.snaptag.system.business.buservice.UpdateInfoBuService;
import net.snaptag.system.business.utils.Constant;
import net.snaptag.system.sadais.core.common.core.ToolsConst;
import net.snaptag.system.sadais.core.common.enums.ExceptionEnums;
import net.snaptag.system.sadais.core.common.exception.ServiceException;
import net.snaptag.system.sadais.util.core.ToolsKit;
import net.snaptag.system.sadais.web.core.BaseController;
import net.snaptag.system.sadais.web.dto.ReturnDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(Constant.PRE_MAPPING_URL + "/upinfo")
public class UpdateInfoController extends BaseController {
    @Autowired
    private UpdateInfoBuService updateInfoBuService;
    // ------------------------支撑平台-----------------------------------

    /**
     * 获取版本更新信息
     *
     * @return
     */
    @RequestMapping(value = "/getupinfo")
    @SLSLog(value = "获取版本更新信息", configKey = "business", businessType = "系统版本模块", operation = OperationType.SELECT)
    public ReturnDto getUpInfo() {
        try {
            String id = this.getValue("id");
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, updateInfoBuService.getUpInfo(id));
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    /**
     * 新增或更新版本更新信息
     *
     * @return
     */
    @RequestMapping(value = "/saveorupdate")
    @SLSLog(value = "新增或更新版本更新信息", configKey = "business", businessType = "系统版本模块", operation = OperationType.UPDATE)
    public ReturnDto saveOrUpdate() {
        try {
            String id = this.getValue("id");
            String channel = this.getValue("channel");
            String version = this.getValue("version");
            String url = this.getValue("url");
            String param = this.getValue("param");
            String remark = this.getValue("remark");
            String needForceUpdate = this.getValue("needForceUpdate");

            String title = this.getValue("title");
            String needIndexShow = this.getValue("needIndexShow");

            updateInfoBuService.saveOrUpdate(id, channel, version, url, param, remark, needForceUpdate, title, needIndexShow);
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, null);
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    /**
     * 删除版本更新信息
     *
     * @return
     */
    @RequestMapping(value = "/del")
    @SLSLog(value = "删除版本更新信息", configKey = "business", businessType = "系统版本模块", operation = OperationType.DELETE)
    public ReturnDto del() {
        try {
            String id = this.getValue("id");
            updateInfoBuService.del(id);
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, null);
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    /**
     * 获取版本更新信息列表
     *
     * @param request
     * @return
     */
    @RequestMapping(value = "/findupdateinfopage")
    @SLSLog(value = "获取版本更新信息列表", configKey = "business", businessType = "系统版本模块", operation = OperationType.SELECT)
    public ReturnDto findUpdateInfoPage() {
        try {
            String pageNo = this.getValue("pageno");
            String pageSize = this.getValue("pagesize");
            if (ToolsKit.isEmpty(pageNo)) {
                pageNo = "1";
            }
            if (ToolsKit.isEmpty(pageSize)) {
                pageSize = String.valueOf(ToolsConst.PHONEPAGESIZE);
            }
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, updateInfoBuService.findUpdateInfoPage(Integer.parseInt(pageNo), Integer.parseInt(pageSize)));
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }
}
