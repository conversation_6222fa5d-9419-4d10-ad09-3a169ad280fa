package net.snaptag.system.business.controller;

import com.vida.sls.annotation.SLSLog;
import com.vida.sls.enums.OperationType;
import net.snaptag.system.business.utils.Constant;
import net.snaptag.system.sadais.core.common.enums.ExceptionEnums;
import net.snaptag.system.sadais.core.common.exception.ServiceException;
import net.snaptag.system.sadais.util.core.ToolsKit;
import net.snaptag.system.sadais.web.config.CommonProperties;
import net.snaptag.system.sadais.web.core.BaseController;
import net.snaptag.system.sadais.web.dto.ReturnDto;
import net.snaptag.system.storage.aliyun.core.STSMessage;
import net.snaptag.system.storage.aliyun.kit.STSKit;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * 云存储模块
 */
@RestController
@RequestMapping(Constant.PRE_MAPPING_URL + "/storage")
public class StorageController extends BaseController {
    @Autowired
    private CommonProperties commonProperties;

    /**
     * 获取osstoken
     * 
     * @return
     */
    @RequestMapping(value = "/getosstoken")
    @SLSLog(value = "获取osstoken", configKey = "business", businessType = "云存储模块", operation = OperationType.SUBMIT)
    public ReturnDto getOssToken() {
        try {
            STSMessage STSMessage = STSKit.getInstance().getSTSResponse();
            Map<String, Map<String, String>> map = STSMessage.getParamMap();
            for (Map.Entry<String, Map<String, String>> entry : map.entrySet()) {
                Map<String, String> value = entry.getValue();
                value.put("domain", commonProperties.getFileDomain());
                value.put("endPoint", commonProperties.getFileDomain());
                value.put("objectName", value.get("objectName") + ToolsKit.Date.thisYear() + "/");
                map.put(entry.getKey(), value);
            }

            return this.returnSuccessJson(ExceptionEnums.SUCCESS, STSMessage);
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        } catch (Exception e) {
            return this.returnFailJson(e);
        }
    }
}
