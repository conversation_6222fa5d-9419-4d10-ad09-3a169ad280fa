package net.snaptag.system.business.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.vida.sls.annotation.SLSLog;
import com.vida.sls.enums.OperationType;
import net.snaptag.system.business.buservice.WebPrintBuService;
import net.snaptag.system.business.utils.Constant;
import net.snaptag.system.business.vo.WebPrintGroupVo;
import net.snaptag.system.sadais.core.common.enums.ExceptionEnums;
import net.snaptag.system.sadais.core.common.exception.ServiceException;
import net.snaptag.system.sadais.util.core.ToolsKit;
import net.snaptag.system.sadais.web.common.JsonKit;
import net.snaptag.system.sadais.web.core.BaseController;
import net.snaptag.system.sadais.web.dto.HeadInfoDto;
import net.snaptag.system.sadais.web.dto.ReturnDto;
import net.snaptag.system.sadais.web.utils.WebTools;
import net.snaptag.system.business.vo.WebGroupSortVo;
import net.snaptag.system.business.vo.WebPagePrintVo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Locale;

/**
 * 网页打印控制器
 * 
 * <AUTHOR> 2019年11月05日
 * @restored 2025年01月30日 - 恢复被删除的文件
 */
@RestController
@RequestMapping(Constant.PRE_MAPPING_URL + "/webprint")
public class WebPrintController extends BaseController {
    
    @Autowired
    private WebPrintBuService webPrintBuService;

    /**
     * 获取默认分组列表
     * @return
     */
    @RequestMapping(value = "/getDefaultGroupList")
    @SLSLog(value = "获取默认分组列表", configKey = "business", businessType = "网页打印模块", operation = OperationType.SELECT)
    public ReturnDto getDefaultGroupList() {
        try {
            List<WebPrintGroupVo> result = webPrintBuService.getDefaultGroupList();
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, result);
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    /**
     * 获取用户分组列表
     * @return
     */
    @RequestMapping(value = "/getUserGroupList")
    @SLSLog(value = "获取用户分组列表", configKey = "business", businessType = "网页打印模块", operation = OperationType.SELECT)
    public ReturnDto getUserGroupList() {
        try {
            String userId = this.getValue("userId");

            if (ToolsKit.isEmpty(userId)) {
                throw new ServiceException("用户未登录");
            }
            
            List<WebPrintGroupVo> result = webPrintBuService.getUserGroupList(userId);
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, result);
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    /**
     * 获取指定分组详情
     * @return
     */
    @RequestMapping(value = "/getGroupDetail")
    @SLSLog(value = "获取分组详情", configKey = "business", businessType = "网页打印模块", operation = OperationType.SELECT)
    public ReturnDto getGroupDetail() {
        try {
            String userId = this.getValue("userId");
            String groupId = this.getValue("groupId");

            if (ToolsKit.isEmpty(userId)) {
                throw new ServiceException("用户未登录");
            }
            if (ToolsKit.isEmpty(groupId)) {
                throw new ServiceException("分组ID不能为空");
            }
            
            WebPrintGroupVo result = webPrintBuService.getUserGroup(userId, groupId);
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, result);
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    /**
     * 保存或更新分组
     * @return
     */
    @RequestMapping(value = "/saveOrUpdateGroup")
    @SLSLog(value = "保存或更新分组", configKey = "business", businessType = "网页打印模块", operation = OperationType.UPDATE)
    public ReturnDto saveOrUpdateGroup() {
        try {
            String userId = this.getValue("userId");

            if (ToolsKit.isEmpty(userId)) {
                throw new ServiceException("用户未登录");
            }
            
            WebPrintGroupVo webPrintGroupVo = this.getBean(WebPrintGroupVo.class);
            if (webPrintGroupVo == null) {
                throw new ServiceException("分组信息不能为空");
            }
            
            WebPrintGroupVo result = webPrintBuService.saveOrUpdateGroup(webPrintGroupVo, userId);
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, result);
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    /**
     * 删除分组
     * @return
     */
    @RequestMapping(value = "/deleteGroup")
    @SLSLog(value = "删除分组", configKey = "business", businessType = "网页打印模块", operation = OperationType.DELETE)
    public ReturnDto deleteGroup() {
        try {
            String userId = this.getValue("userId");
            String groupId = this.getValue("groupId");

            if (ToolsKit.isEmpty(userId)) {
                throw new ServiceException("用户未登录");
            }
            if (ToolsKit.isEmpty(groupId)) {
                throw new ServiceException("分组ID不能为空");
            }
            
            String result = webPrintBuService.deleteGroup(groupId, userId);
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, result);
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    /**
     * 分页查询分组列表（管理员用）
     * @return
     */
    @RequestMapping(value = "/findPage")
    @SLSLog(value = "分页查询分组列表", configKey = "business", businessType = "网页打印模块", operation = OperationType.SELECT)
    public ReturnDto findPage() {
        try {
            int pageNum = this.getIntValue("pageNum");
            if (pageNum <= 0) pageNum = 1;
            int pageSize = this.getIntValue("pageSize");
            if (pageSize <= 0) pageSize = 10;
            String userId = this.getValue("userId"); // 可选参数
            
            Page<WebPrintGroupVo> result = webPrintBuService.findPage(pageNum, pageSize, userId);
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, result);
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    /**
     * 复制默认分组给用户
     * @return
     */
    @RequestMapping(value = "/copyDefaultGroup")
    @SLSLog(value = "复制默认分组", configKey = "business", businessType = "网页打印模块", operation = OperationType.UPDATE)
    public ReturnDto copyDefaultGroup() {
        try {
            String userId = this.getValue("userId");
            String defaultGroupId = this.getValue("defaultGroupId");

            if (ToolsKit.isEmpty(userId)) {
                throw new ServiceException("用户未登录");
            }
            if (ToolsKit.isEmpty(defaultGroupId)) {
                throw new ServiceException("默认分组ID不能为空");
            }
            
            WebPrintGroupVo result = webPrintBuService.copyDefaultGroup(userId, defaultGroupId);
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, result);
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    /**
     * 获取热门网站列表（兼容旧接口）
     * @return
     */
    @RequestMapping(value = "/getHotWebsites")
    @SLSLog(value = "获取热门网站列表", configKey = "business", businessType = "网页打印模块", operation = OperationType.SELECT)
    public ReturnDto getHotWebsites() {
        try {
            // 返回默认分组中的热门网站
            List<WebPrintGroupVo> defaultGroups = webPrintBuService.getDefaultGroupList();
            if (!defaultGroups.isEmpty()) {
                // 返回第一个默认分组的页面列表
                WebPrintGroupVo hotGroup = defaultGroups.get(0);
                return this.returnSuccessJson(ExceptionEnums.SUCCESS, hotGroup.getPageList());
            }
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, null);
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    /**
     * 获取我的收藏网站列表（兼容旧接口）
     * @return
     */
    @RequestMapping(value = "/getMyFavorites")
    @SLSLog(value = "获取我的收藏", configKey = "business", businessType = "网页打印模块", operation = OperationType.SELECT)
    public ReturnDto getMyFavorites() {
        try {
            String userId = this.getValue("userId");

            if (ToolsKit.isEmpty(userId)) {
                throw new ServiceException("用户未登录");
            }
            
            List<WebPrintGroupVo> userGroups = webPrintBuService.getUserGroupList(userId);
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, userGroups);
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    /**
     * 添加网站到收藏（兼容旧接口）
     * @return
     */
    @RequestMapping(value = "/addToFavorites")
    @SLSLog(value = "添加到收藏", configKey = "business", businessType = "网页打印模块", operation = OperationType.UPDATE)
    public ReturnDto addToFavorites() {
        try {
            String userId = this.getValue("userId");

            if (ToolsKit.isEmpty(userId)) {
                throw new ServiceException("用户未登录");
            }

            // 这里可以实现添加网站到收藏的逻辑
            // 暂时返回成功
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, "添加成功");
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    /**
     * 删除网站
     * @return
     */
    @RequestMapping(value = "/delWebSite")
    @SLSLog(value = "删除网站", configKey = "business", businessType = "网页打印模块", operation = OperationType.DELETE)
    public ReturnDto delWebSite() {
        try {
            String userId = this.getValue("userId");
            String groupId = this.getValue("groupId");
            String siteId = this.getValue("siteId");
            if (StringUtils.isBlank(userId) || StringUtils.isBlank(groupId) || StringUtils.isBlank(siteId)) {
                return this.returnSuccessJson(ExceptionEnums.PARAM_ERROR, "参数不能为空");
            }

            webPrintBuService.delWebSite(userId, groupId, Integer.parseInt(siteId));

            return this.returnSuccessJson(ExceptionEnums.SUCCESS, siteId);
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    /**
     * 网站排序
     * @return
     */
    @RequestMapping(value = "/sortWebSites")
    @SLSLog(value = "网站排序", configKey = "business", businessType = "网页打印模块", operation = OperationType.UPDATE)
    public ReturnDto sortWebSites() {
        try {
            String userId = this.getValue("userId");

            String strGroupList = this.getValue("groupList");
            if (ToolsKit.isEmpty(strGroupList)) {
                throw new ServiceException("网站分组集合不能为空");
            }
            List<WebGroupSortVo> groupList = JsonKit.jsonParseArray(strGroupList, WebGroupSortVo.class);
            if (groupList != null && groupList.size() > 0) {
                for (WebGroupSortVo sortVo:groupList) {
                    if (StringUtils.isBlank(sortVo.getGroupId()) || StringUtils.isBlank(sortVo.getSiteIds())) {
                        continue;
                    }
                    webPrintBuService.sortWebSites(userId, sortVo.getGroupId(), sortVo.getSiteIds());
                }
            }

            return this.returnSuccessJson(ExceptionEnums.SUCCESS, "");
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    /**
     * 获取网站列表（兼容旧接口）
     * @return
     */
    @RequestMapping(value = "/getWebSites")
    @SLSLog(value = "获取网站列表", configKey = "business", businessType = "网页打印模块", operation = OperationType.SELECT)
    public ReturnDto getWebSites() {
        try {
            String userId = this.getValue("userid");
            HeadInfoDto headInfoDto = this.getHeadInfoDto();

            Locale locale = WebTools.getLocaleByLanguage(headInfoDto.getLanguage());
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, webPrintBuService.getWebPrintListA4(userId, Boolean.TRUE, locale));
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    /**
     * 添加网站
     * @return
     */
    @RequestMapping(value = "/addWebSite")
    @SLSLog(value = "添加网站", configKey = "business", businessType = "网页打印模块", operation = OperationType.UPDATE)
    public ReturnDto addWebSite() {
        try {
            String userId = this.getValue("userId");
            WebPagePrintVo pagePrintVo = this.getBean(WebPagePrintVo.class);
            if (StringUtils.isBlank(userId) || pagePrintVo == null) {
                return this.returnSuccessJson(ExceptionEnums.PARAM_ERROR, "参数不能为空");
            }
            HeadInfoDto headInfoDto = this.getHeadInfoDto();
            Locale locale = WebTools.getLocaleByLanguage(headInfoDto.getLanguage());
            int codeId = webPrintBuService.addWebSite(pagePrintVo, userId, locale);
            if (codeId <= 0) {
                return this.returnSuccessJson(ExceptionEnums.PARAM_ERROR, "网站分组不存在");
            }

            pagePrintVo.setCodeId(codeId);
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, pagePrintVo);
        } catch (ServiceException e) {
            e.printStackTrace();
            return this.returnFailJson(e);
        } catch (Exception e) {
            e.printStackTrace();
            return this.returnFailJson(e);
        }
    }

    /**
     * 初始化默认分组和网站数据
     * @return
     */
    @RequestMapping(value = "/initBasicData")
    @SLSLog(value = "初始化基础数据", configKey = "business", businessType = "网页打印模块", operation = OperationType.UPDATE)
    public ReturnDto initBasicData() {
        try {
            String userId = this.getValue("userId");
            String secretKey = this.getValue("secretKey");
            if (StringUtils.isBlank(userId) || StringUtils.isBlank(secretKey)) {
                return this.returnSuccessJson(ExceptionEnums.PARAM_ERROR, "参数不能为空");
            }

            if (secretKey.equals("<EMAIL>")) {
                return this.returnSuccessJson(ExceptionEnums.SUCCESS, webPrintBuService.initBasicData());
            }
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, "Failed");
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    /**
     * 初始化默认分组和网站数据（版本2）
     * @return
     */
    @RequestMapping(value = "/initBasicData2")
    @SLSLog(value = "初始化基础数据2", configKey = "business", businessType = "网页打印模块", operation = OperationType.UPDATE)
    public ReturnDto initBasicData2() {
        try {
            String userId = this.getValue("userId");
            String secretKey = this.getValue("secretKey");
            if (StringUtils.isBlank(userId) || StringUtils.isBlank(secretKey)) {
                return this.returnSuccessJson(ExceptionEnums.PARAM_ERROR, "参数不能为空");
            }

            if (secretKey.equals("<EMAIL>")) {
                return this.returnSuccessJson(ExceptionEnums.SUCCESS, webPrintBuService.initBasicData2());
            }
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, "Failed");
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    /**
     * 获取默认网站（兼容旧接口）
     * @return
     */
    @RequestMapping(value = "/getDefaultWebSites")
    @SLSLog(value = "获取默认网站", configKey = "business", businessType = "网页打印模块", operation = OperationType.SELECT)
    public ReturnDto getDefaultWebSites() {
        try {
            String userId = this.getValue("userId");
            HeadInfoDto headInfoDto = this.getHeadInfoDto();

            Locale locale = WebTools.getLocaleByLanguage(headInfoDto.getLanguage());
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, webPrintBuService.getWebPrintListA4(userId, true, locale));
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }
}
