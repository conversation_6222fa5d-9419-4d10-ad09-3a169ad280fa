package net.snaptag.system.business.controller;

import com.vida.sls.annotation.SLSLog;
import com.vida.sls.enums.OperationType;
import net.snaptag.system.business.buservice.TemplateDraftsService;
import net.snaptag.system.business.entity.TemplateDrafts;
import net.snaptag.system.business.utils.Constant;
import net.snaptag.system.sadais.core.common.core.ToolsConst;
import net.snaptag.system.sadais.core.common.enums.ExceptionEnums;
import net.snaptag.system.sadais.core.common.exception.ServiceException;
import net.snaptag.system.sadais.util.core.ToolsKit;
import net.snaptag.system.sadais.web.core.BaseController;
import net.snaptag.system.sadais.web.dto.HeadInfoDto;
import net.snaptag.system.sadais.web.dto.ReturnDto;
import net.snaptag.system.sadais.web.enums.LanguageEnums;
import net.snaptag.system.sadais.web.utils.WebTools;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date ：Created in 2021/9/27 16:53
 * @description：
 * @modified By：
 * @version: $
 */
@RestController
@RequestMapping(Constant.PRE_MAPPING_URL + "/templatedrafts")
public class TemplateDraftsController extends BaseController {
    @Autowired
    private TemplateDraftsService templateDraftsService;

    /**
     * 获取模板列表
     *
     * @return
     */
    @RequestMapping(value = "/getlist")
    @SLSLog(value = "获取模板列表", configKey = "business", businessType = "模板模块", operation = OperationType.SELECT)
    public ReturnDto getTemplateDraftsList() {
        try {
            String userId = this.getValue("userid");
            String type = this.getValue("type");
            String printerType = this.getValue("printerType");
            String paperType = this.getValue("paperType");
            String length = this.getValue("length");// 具体的纸张类型，有它，printerType的条件自动否决
            int isHot = this.getIntValue("ishot");

            if (isHot != 1) {
                isHot = 0;
            }

            String pageNo = this.getValue("pageno");
            String pageSize = this.getValue("pagesize");
            String keyword = this.getValue("keyword");
            if (ToolsKit.isEmpty(pageNo)) {
                pageNo = "1";
            }
            if (ToolsKit.isEmpty(pageSize)) {
                pageSize = String.valueOf(ToolsConst.PHONEPAGESIZE);
            }
            HeadInfoDto headInfoDto = this.getHeadInfoDto();
            String localeCode = initLocaleCodeByLanguage(headInfoDto.getLanguage());
            return this.returnSuccessJson(ExceptionEnums.SUCCESS,
                    templateDraftsService.getTemplateDraftsList(userId, type, isHot, Integer.parseInt(pageNo), Integer.parseInt(pageSize), localeCode, printerType, paperType, length, headInfoDto.getVersion(), keyword));
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }


    /**
     * 获取用户模板的类型
     *
     * @return
     */
    @RequestMapping(value = "/getTemplateDraftsType")
    @SLSLog(value = "获取用户模板的类型", configKey = "business", businessType = "模板模块", operation = OperationType.SELECT)
    public ReturnDto getTemplateDraftsType() {
        try {
            String printerType = this.getValue("printerType");
            HeadInfoDto headInfoDto = this.getHeadInfoDto();
            return this.returnSuccessJson(ExceptionEnums.SUCCESS,
                    templateDraftsService.getTemplateDraftsType(printerType, WebTools.getLocaleByLanguage(headInfoDto.getLanguage())));
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }


    // 支撑平台使用的接口

    /**
     * 获取模板列表
     *
     * @return
     */
    @RequestMapping(value = "/findPage")
    @SLSLog(value = "获取模板列表（分页）", configKey = "business", businessType = "模板模块", operation = OperationType.SELECT)
    public ReturnDto findPage() {
        try {
            String userId = this.getValue("userid");
            String type = this.getValue("type");
            String pageNo = this.getValue("pageno");
            String pageSize = this.getValue("pagesize");
            String keyword = this.getValue("keyword");
            if (ToolsKit.isEmpty(pageNo)) {
                pageNo = "1";
            }
            if (ToolsKit.isEmpty(pageSize)) {
                pageSize = String.valueOf(ToolsConst.PHONEPAGESIZE);
            }
            int isHot = this.getIntValue("ishot");

            if (isHot != 1) {
                isHot = 0;
            }

            if (ToolsKit.isNotEmpty(keyword)) {
                keyword = keyword.toLowerCase();
            }

            String printerType = this.getValue("printerType");
            String paperType = this.getValue("paperType");

            HeadInfoDto headInfoDto = this.getHeadInfoDto();
            String localeCode = initLocaleCodeByLanguage(headInfoDto.getLanguage());
            return this.returnSuccessJson(ExceptionEnums.SUCCESS,
                    templateDraftsService.findPage(userId, keyword, Integer.parseInt(pageNo), Integer.parseInt(pageSize), localeCode, printerType, paperType));
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    @RequestMapping(value = "/getmycollectlist")
    @SLSLog(value = "获取我的收藏", configKey = "business", businessType = "模板模块", operation = OperationType.SELECT)
    public ReturnDto getMyCollectList() {
        try {
            String userId = this.getValue("userid");
            String pageNo = this.getValue("pageno");
            String pageSize = this.getValue("pagesize");
            String printerType = this.getValue("printerType");
            if (ToolsKit.isEmpty(pageNo)) {
                pageNo = "1";
            }
            if (ToolsKit.isEmpty(pageSize)) {
                pageSize = String.valueOf(ToolsConst.PHONEPAGESIZE);
            }
            return this.returnSuccessJson(ExceptionEnums.SUCCESS,
                    templateDraftsService.getMyCollectTemplateDraftsList(userId, Integer.parseInt(pageNo), Integer.parseInt(pageSize), printerType));
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    /***
     * 根据id获取对应的模板数据
     * @return
     */
    @RequestMapping(value = "/getbyid")
    @SLSLog(value = "根据id获取对应的模板数据", configKey = "business", businessType = "模板模块", operation = OperationType.SELECT)
    public ReturnDto getById() {
        try {
            String id = this.getValue("id");
            String userId = this.getValue("userid");
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, templateDraftsService.getById(id, userId));
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    // 获取当前语言是否开启（根据header头去判断，默认开启）
    @RequestMapping(value = "/checkusertemplateshow")
    @SLSLog(value = "获取当前语言是否开启", configKey = "business", businessType = "模板模块", operation = OperationType.SELECT)
    public ReturnDto checkUserTemplateShow() {
        try {
            HeadInfoDto headInfoDto = this.getHeadInfoDto();
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, templateDraftsService.checkUserTemplateShow(headInfoDto.getLanguage()));
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    //----------------关于用户模板的收藏--------------

    /**
     * 收藏模板
     *
     * @return
     */
    @RequestMapping(value = "/addcollect")
    @SLSLog(value = "收藏模板", configKey = "business", businessType = "模板模块", operation = OperationType.INSERT)
    public ReturnDto addCollect() {
        try {
            String templateid = this.getValue("templateid");
            String userId = this.getValue("userid");
            templateDraftsService.addCollect(userId, templateid);
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, "");
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    /***
     * 取消收藏模板
     * @return
     */
    @RequestMapping(value = "/cancelcollect")
    @SLSLog(value = "取消收藏模板", configKey = "business", businessType = "模板模块", operation = OperationType.CANCEL)
    public ReturnDto cancelCollect() {
        try {
            String templateid = this.getValue("templateid");
            String userId = this.getValue("userid");
            templateDraftsService.cancelCollect(userId, templateid);
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, "");
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    /***
     * 添加或取消收藏模板
     * 有收藏的取消收藏，没有收藏的新增收藏
     * @return
     */
    @RequestMapping(value = "/addorcancelcollect")
    @SLSLog(value = "添加或取消收藏模板", configKey = "business", businessType = "模板模块", operation = OperationType.UPDATE)
    public ReturnDto addOrCancelCollect() {
        try {
            String templateid = this.getValue("templateid");
            String userId = this.getValue("userid");
            templateDraftsService.addOrCancelCollect(userId, templateid);
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, "");
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    // 支撑平台使用的接口

    /**
     * 获取模板列表
     *
     * @return
     */
    @RequestMapping(value = "/getPage")
    @SLSLog(value = "获取模板列表", configKey = "business", businessType = "模板模块", operation = OperationType.SELECT)
    public ReturnDto getTemplateDraftsPage() {
        try {
            String type = this.getValue("type");
            String pageNo = this.getValue("pageno");
            String pageSize = this.getValue("pagesize");
            String localeCode = this.getValue("localecode");
            if (ToolsKit.isEmpty(pageNo)) {
                pageNo = "1";
            }
            if (ToolsKit.isEmpty(pageSize)) {
                pageSize = String.valueOf(ToolsConst.PHONEPAGESIZE);
            }
            int isHot = this.getIntValue("ishot");

            if (isHot != 1) {
                isHot = 0;
            }
            String printerType = this.getValue("printerType");
            String paperType = this.getValue("paperType");
            return this.returnSuccessJson(ExceptionEnums.SUCCESS,
                    templateDraftsService.getTemplateDraftsPage(type, isHot, Integer.parseInt(pageNo), Integer.parseInt(pageSize), localeCode, printerType, paperType));
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    @RequestMapping(value = "/addOrUpdate")
    @SLSLog(value = "新增或更新模板", configKey = "business", businessType = "模板模块", operation = OperationType.UPDATE)
    public ReturnDto addOrUpdateTemplateDrafts() {
        try {
            TemplateDrafts templateDrafts = this.getBean(TemplateDrafts.class);
            String userId = this.getValue("userid");
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, templateDraftsService.saveOrUpdate(templateDrafts, userId));
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    @RequestMapping(value = "/deleteById")
    @SLSLog(value = "删除模板", configKey = "business", businessType = "模板模块", operation = OperationType.DELETE)
    public ReturnDto deleteTemplateDrafts() {
        try {
            String id = this.getValue("id");
            String userId = this.getValue("userid");
            templateDraftsService.deleteById(id, userId);
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, "");
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    @SLSLog(value = "初始化语言", configKey = "business", businessType = "模板模块", operation = OperationType.SELECT)
    private String initLocaleCodeByLanguage(String language) {
        // 不为空，且不是简体、繁体、日语、韩语的，全部归类到英语
        if (ToolsKit.isNotEmpty(language) && LanguageEnums.getMap().get(language) == null) {
            return LanguageEnums.EN_US.getKey();
        } else {
            return language;
        }
    }

    @RequestMapping(value = "/turnusertemplateshow")
    @SLSLog(value = "更新用户模板语言", configKey = "business", businessType = "模板模块", operation = OperationType.UPDATE)
    public ReturnDto turnUserTemplateShow() {
        try {
            String language = this.getValue("language");
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, templateDraftsService.turnUserTemplateShow(language));
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    // 获取所有语言的开启状态（支撑平台使用）
    @RequestMapping(value = "/getUserTemplateShowList")
    @SLSLog(value = "获取所有语言的开启状态", configKey = "business", businessType = "模板模块", operation = OperationType.SELECT)
    public ReturnDto getUserTemplateShowList() {
        try {
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, templateDraftsService.getUserTemplateShowList());
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }


}
