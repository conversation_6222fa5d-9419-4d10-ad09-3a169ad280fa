package net.snaptag.system.business.controller;

import com.vida.sls.annotation.SLSLog;
import com.vida.sls.enums.OperationType;
import net.snaptag.system.business.buservice.BaiduOcrApiBuService;
import net.snaptag.system.business.utils.Constant;
import net.snaptag.system.sadais.web.core.BaseController;
import net.snaptag.system.sadais.web.dto.ReturnDto;
import net.snaptag.system.sadais.core.common.enums.ExceptionEnums;
import net.snaptag.system.sadais.core.common.exception.ServiceException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Locale;

/**
 * <AUTHOR>
 * @date ：Created in 2022/8/11 10:32
 * @description：
 * @modified By：
 * @version: $
 */
@RestController
@RequestMapping(Constant.PRE_MAPPING_URL + "/ocr")
public class OcrApiController extends BaseController {

    private static final String baiduOrc = "baiduOrc";

    @Autowired
    private BaiduOcrApiBuService baiduOcrApiBuService;

    // 图文识别
    @RequestMapping(value = "/ocrbydata")
    @SLSLog(value = "图文识别（图片）", configKey = "business", businessType = "OCR模块", operation = OperationType.SELECT)
    public ReturnDto getOrcByImageData() {
        try {
            String imageData = this.getValue("imageData");
//            HeadInfoDto headInfoDto = this.getHeadInfoDto();
//            String version = headInfoDto.getVersion();
//            Locale locale = WebTools.getLocaleByLanguage(headInfoDto.getLanguage());
//            String userId = this.getValue("userid");
//            this.countApi(userId);
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, baiduOcrApiBuService.doRequestData(imageData));
//            return this.returnSuccessJson(ExceptionEnums.SUCCESS, xfyunApiBuService.doRequest(imageData));
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        } catch (Exception e) {
            return this.returnFailJson(e);
        }
    }

    // 图文识别
    @RequestMapping(value = "/ocrbyurl")
    @SLSLog(value = "图文识别（url）", configKey = "business", businessType = "OCR模块", operation = OperationType.SELECT)
    public ReturnDto getIndexFunction() {
        try {
            String url = this.getValue("url");
//            HeadInfoDto headInfoDto = this.getHeadInfoDto();
//            String userId = this.getValue("userid");
//            this.countApi(userId);
//            return this.returnSuccessJson(ExceptionEnums.SUCCESS, xfyunApiBuService.doRequestByUrl(url));
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, baiduOcrApiBuService.doRequest(url));
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        } catch (Exception e) {
            return this.returnFailJson(e);
        }
    }

//    private void countApi(String userId) {
//        ToolsKit.Thread.execute(new Runnable() {
//            @Override
//            public void run() {
//                apiCallAnalysisService.operateNum(userId, baiduOrc);
//            }
//        });
//    }
}
