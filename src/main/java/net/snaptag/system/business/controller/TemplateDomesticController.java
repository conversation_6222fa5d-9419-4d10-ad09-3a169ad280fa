package net.snaptag.system.business.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.vida.sls.annotation.SLSLog;
import com.vida.sls.enums.OperationType;
import net.snaptag.system.business.buservice.TemplateDraftsService;
import net.snaptag.system.business.utils.Constant;
import net.snaptag.system.business.buservice.TemplateDomesticService;
import net.snaptag.system.business.dto.TemplateDomesticDto;
import net.snaptag.system.sadais.core.common.enums.ExceptionEnums;
import net.snaptag.system.sadais.core.common.exception.ServiceException;
import net.snaptag.system.sadais.util.core.ToolsKit;
import net.snaptag.system.sadais.web.core.BaseController;
import net.snaptag.system.sadais.web.dto.HeadInfoDto;
import net.snaptag.system.sadais.web.dto.ReturnDto;
import net.snaptag.system.sadais.web.utils.WebTools;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 家用行业模板控制器
 */
@RestController
@RequestMapping(Constant.PRE_MAPPING_URL + "/domestic/template")
public class TemplateDomesticController extends BaseController {

    @Autowired
    private TemplateDomesticService templateDomesticService;

    @Autowired
    private TemplateDraftsService templateDraftsService;

    /**
     * 获取家用行业模板列表
     * 参考JFinal实现思路：
     * - 支持name模糊查询
     * - 支持groupId分组查询
     * - 支持indexRange尺寸范围查询
     *
     * @return 模板列表
     */
    @RequestMapping(value = "/getlist")
    @SLSLog(value = "获取家用行业模板列表", configKey = "business", businessType = "家用行业模板", operation = OperationType.SELECT)
    public ReturnDto getDomesticTemplateList() {
        try {
            // 获取分页参数
            Integer pageNumber = this.getIntValue("pageNumber") == -1 ? 1 : this.getIntValue("pageNumber");
            Integer pageSize = this.getIntValue("pageSize") == -1 ? 20 : this.getIntValue("pageSize");

            // 获取查询参数
            String name = this.getValue("name");
            String groupId = this.getValue("groupId");
            String indexRange = this.getValue("width");
            Integer paperType = this.getIntValue("paperType") == -1 ? 1 : this.getIntValue("paperType");

            return this.returnSuccessJson(ExceptionEnums.SUCCESS, templateDomesticService.getDomesticTemplateList(
                    pageNumber, pageSize, name, groupId, indexRange, paperType));
            // // 构造返回结果（参考JFinal的RetKit.ok格式）
            // Map<String, Object> result = new HashMap<>();
            // result.put("list", list);
            // result.put("pageNumber", pageNumber);
            // result.put("pageSize", pageSize);
            // result.put("total", list.size());
            //
            // return this.returnSuccessJson(ExceptionEnums.SUCCESS, result);
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        } catch (Exception e) {
            return this.returnFailJson(new ServiceException("获取模板列表失败: " + e.getMessage()));
        }
    }

    /**
     * 获取家用模板的类型
     *
     * @return
     */
    @RequestMapping(value = "/getType")
    @SLSLog(value = "获取家用模板的类型", configKey = "business", businessType = "模板模块", operation = OperationType.SELECT)
    public ReturnDto getTemplateDomesticType() {
        try {
            String printerType = this.getValue("printerType");
            HeadInfoDto headInfoDto = this.getHeadInfoDto();
            return this.returnSuccessJson(ExceptionEnums.SUCCESS,
                    templateDraftsService.getTemplateDraftsType(printerType,
                            WebTools.getLocaleByLanguage(headInfoDto.getLanguage()), "template_type_home"));
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    /**
     * 获取家用行业模板分页列表
     *
     * @return 分页数据
     */
    @RequestMapping(value = "/getpagelist")
    @SLSLog(value = "获取家用行业模板分页列表", configKey = "business", businessType = "家用行业模板", operation = OperationType.SELECT)
    public ReturnDto getDomesticTemplatePageList() {
        try {
            // 获取分页参数
            String pageNumberStr = this.getValue("pageNumber");
            String pageSizeStr = this.getValue("pageSize");
            int pageNumber = ToolsKit.isEmpty(pageNumberStr) ? 1 : Integer.parseInt(pageNumberStr);
            int pageSize = ToolsKit.isEmpty(pageSizeStr) ? 20 : Integer.parseInt(pageSizeStr);

            // 获取查询参数
            String name = this.getValue("name");
            String groupId = this.getValue("groupId");
            String indexRange = this.getValue("indexRange");

            // 调用服务方法
            IPage<TemplateDomesticDto> page = templateDomesticService.getDomesticTemplatePageList(
                    pageNumber, pageSize, name, groupId, indexRange);

            // 构造返回结果（同时保留新旧字段，确保前后端兼容）
            Map<String, Object> result = new HashMap<>();
            result.put("result", page.getRecords());
            result.put("records", page.getRecords());
            result.put("pageNo", page.getCurrent());
            result.put("pageSize", page.getSize());
            result.put("totalCount", page.getTotal());
            result.put("current", page.getCurrent());
            result.put("size", page.getSize());
            result.put("total", page.getTotal());
            result.put("pages", page.getPages());

            return this.returnSuccessJson(ExceptionEnums.SUCCESS, result);
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        } catch (Exception e) {
            return this.returnFailJson(new ServiceException("获取模板分页列表失败: " + e.getMessage()));
        }
    }

    /**
     * 根据ID获取模板详情
     *
     * @return 模板详情
     */
    @RequestMapping(value = "/getdetail")
    @SLSLog(value = "获取家用行业模板详情", configKey = "business", businessType = "家用行业模板", operation = OperationType.SELECT)
    public ReturnDto getTemplateDomesticDetail() {
        try {
            String id = this.getValue("id");
            if (ToolsKit.isEmpty(id)) {
                throw new ServiceException("模板ID不能为空");
            }

            TemplateDomesticDto dto = templateDomesticService.getTemplateDomesticById(id);
            if (dto == null) {
                throw new ServiceException("模板不存在");
            }

            return this.returnSuccessJson(ExceptionEnums.SUCCESS, dto);
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    /**
     * 根据条件查询模板列表
     *
     * @return 模板列表
     */
    @RequestMapping(value = "/findlist")
    @SLSLog(value = "条件查询家用行业模板", configKey = "business", businessType = "家用行业模板", operation = OperationType.SELECT)
    public ReturnDto findTemplateDomesticList() {
        try {
            // 获取查询参数
            String userIdStr = this.getValue("userId");
            String typeStr = this.getValue("type");
            String machineTypeStr = this.getValue("machineType");
            String paperTypeStr = this.getValue("paperType");
            String name = this.getValue("name");

            Integer userId = ToolsKit.isEmpty(userIdStr) ? null : Integer.parseInt(userIdStr);
            Integer type = ToolsKit.isEmpty(typeStr) ? null : Integer.parseInt(typeStr);
            Integer machineType = ToolsKit.isEmpty(machineTypeStr) ? null : Integer.parseInt(machineTypeStr);
            Integer paperType = ToolsKit.isEmpty(paperTypeStr) ? null : Integer.parseInt(paperTypeStr);

            List<TemplateDomesticDto> list = templateDomesticService.findTemplateDomesticList(
                    userId, type, machineType, paperType, name);

            return this.returnSuccessJson(ExceptionEnums.SUCCESS, list);
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        } catch (Exception e) {
            return this.returnFailJson(new ServiceException("查询模板列表失败: " + e.getMessage()));
        }
    }

    /**
     * 统计模板数量
     *
     * @return 统计结果
     */
    @RequestMapping(value = "/count")
    @SLSLog(value = "统计家用行业模板数量", configKey = "business", businessType = "家用行业模板", operation = OperationType.SELECT)
    public ReturnDto countTemplateDomestic() {
        try {
            String userIdStr = this.getValue("userId");
            String typeStr = this.getValue("type");

            Integer userId = ToolsKit.isEmpty(userIdStr) ? null : Integer.parseInt(userIdStr);
            Integer type = ToolsKit.isEmpty(typeStr) ? null : Integer.parseInt(typeStr);

            long count = templateDomesticService.countTemplateDomestic(userId, type);

            Map<String, Object> result = new HashMap<>();
            result.put("count", count);

            return this.returnSuccessJson(ExceptionEnums.SUCCESS, result);
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        } catch (Exception e) {
            return this.returnFailJson(new ServiceException("统计模板数量失败: " + e.getMessage()));
        }
    }

    /**
     * 调试接口 - 查看查询参数处理和数据库状态
     *
     * @return 调试信息
     */
    @RequestMapping(value = "/debug")
    @SLSLog(value = "调试家用行业模板查询", configKey = "business", businessType = "家用行业模板", operation = OperationType.SELECT)
    public ReturnDto debugTemplateDomestic() {
        try {
            // 获取所有参数
            String pageNumberStr = this.getValue("pageNumber");
            String pageSizeStr = this.getValue("pageSize");
            String name = this.getValue("name");
            String groupId = this.getValue("groupId");
            String indexRange = this.getValue("indexRange");

            // 构造调试信息
            Map<String, Object> debugInfo = new HashMap<>();
            debugInfo.put("pageNumber", pageNumberStr);
            debugInfo.put("pageSize", pageSizeStr);
            debugInfo.put("name", name);
            debugInfo.put("groupId", groupId);
            debugInfo.put("indexRange", indexRange);

            // 解析indexRange
            if (ToolsKit.String.isNotBlank(indexRange)) {
                String[] indexRangeArr = indexRange.split(",");
                Map<String, String> rangeInfo = new HashMap<>();
                if (indexRangeArr.length > 0) {
                    rangeInfo.put("widthBegin", indexRangeArr[0]);
                }
                if (indexRangeArr.length > 1) {
                    rangeInfo.put("widthEnd", indexRangeArr[1]);
                }
                debugInfo.put("parsedIndexRange", rangeInfo);
            }

            // 调用Service的调试方法
            Map<String, Object> serviceDebugInfo = templateDomesticService.debugQuery();
            debugInfo.put("serviceDebugInfo", serviceDebugInfo);

            return this.returnSuccessJson(ExceptionEnums.SUCCESS, debugInfo);
        } catch (Exception e) {
            return this.returnFailJson(new ServiceException("调试失败: " + e.getMessage()));
        }
    }
}
