package net.snaptag.system.business.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.extern.slf4j.Slf4j;
import net.snaptag.system.business.buservice.TemplateBorderService;
import net.snaptag.system.business.dto.TempletBorderQueryDTO;
import net.snaptag.system.business.entity.TemplateBorder;
import net.snaptag.system.business.entity.TemplateBorderKind;
import net.snaptag.system.business.utils.Constant;
import net.snaptag.system.sadais.core.common.enums.ExceptionEnums;
import net.snaptag.system.sadais.web.core.BaseController;
import net.snaptag.system.sadais.web.dto.ReturnDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 模板边框控制器
 *
 * <AUTHOR>
 * @since 2025-08-20
 */
@Slf4j
@RestController
@RequestMapping(Constant.PRE_MAPPING_URL + "/domestic/templateBorder")
public class TemplateBorderController extends BaseController {

    @Autowired
    private TemplateBorderService templateBorderService;

    /**
     * 获取所有边框列表
     * 对应原接口：getBorderList
     *
     * @return 边框列表
     */
    @RequestMapping(value = "/getBorderList")
    public ReturnDto getBorderList() {
        try {
            List<TemplateBorder> borders = templateBorderService.getBorderList();
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, borders);
        } catch (Exception e) {
            log.error("获取边框列表失败", e);
            return this.returnFailJson(e);
        }
    }

    /**
     * 获取边框分类列表（支持多语言）
     * 对应原接口：getBorderKindList
     *
     * @return 分类列表
     */
    @RequestMapping(value = "/getBorderKindList")
    public ReturnDto getBorderKindList() {
        try {
            // 获取参数
            Integer language = this.getIntValue("language");
            String lang = this.getValue("lang");

            List<TemplateBorderKind> kinds = templateBorderService.getBorderKindList(language, lang);

            // 转换为期望的格式
            List<java.util.Map<String, Object>> items = new java.util.ArrayList<>();
            for (TemplateBorderKind kind : kinds) {
                java.util.Map<String, Object> item = new java.util.HashMap<>();
                item.put("borderKindId", kind.getBorderKindId());
                item.put("borderKindName", kind.getDisplayName() != null ? kind.getDisplayName() : kind.getBorderKindName());

                // 根据语言参数添加对应的语言字段
                if ("korean".equals(lang)) {
                    item.put("koreanName", kind.getKoreanName() != null ? kind.getKoreanName() : kind.getBorderKindName());
                } else if ("english".equals(lang)) {
                    item.put("englishName", kind.getEnglishName() != null ? kind.getEnglishName() : kind.getBorderKindName());
                } else if ("traditional".equals(lang)) {
                    item.put("traditionalName", kind.getTraditionalName() != null ? kind.getTraditionalName() : kind.getBorderKindName());
                } else if ("russian".equals(lang)) {
                    item.put("russianName", kind.getRussianName() != null ? kind.getRussianName() : kind.getBorderKindName());
                } else if ("french".equals(lang)) {
                    item.put("frenchName", kind.getFrenchName() != null ? kind.getFrenchName() : kind.getBorderKindName());
                } else if ("spanish".equals(lang)) {
                    item.put("spanishName", kind.getSpanishName() != null ? kind.getSpanishName() : kind.getBorderKindName());
                } else if ("germany".equals(lang)) {
                    item.put("germanyName", kind.getGermanyName() != null ? kind.getGermanyName() : kind.getBorderKindName());
                } else if ("italy".equals(lang)) {
                    item.put("italyName", kind.getItalyName() != null ? kind.getItalyName() : kind.getBorderKindName());
                }

                items.add(item);
            }

            return this.returnSuccessJson(ExceptionEnums.SUCCESS, items);
        } catch (Exception e) {
            log.error("获取边框分类列表失败", e);
            return this.returnFailJson(e);
        }
    }

    /**
     * 分页获取边框列表
     * 对应原接口：getBorderPage
     *
     * @return 分页边框列表
     */
    @RequestMapping(value = "/getBorderPage")
    public ReturnDto getBorderPage() {
        try {
            // 获取分页参数
            int pageNumber = this.getIntValue("pageNumber") == -1 ? 1 : this.getIntValue("pageNumber");
            int pageSize = this.getIntValue("pageSize") == -1 ? 10 : this.getIntValue("pageSize");
            int language = this.getIntValue("lang") == -1 ? 1 : this.getIntValue("lang");
            String borderKindId = this.getValue("borderKindId");

            IPage<TemplateBorder> page = templateBorderService.getBorderPage(pageNumber, pageSize, borderKindId, language);

            // 转换为期望的格式
            List<java.util.Map<String, Object>> list = new java.util.ArrayList<>();
            for (TemplateBorder border : page.getRecords()) {
                java.util.Map<String, Object> item = new java.util.HashMap<>();
                item.put("borderId", border.getBorderId());
                item.put("borderKindName", border.getGroupName() != null ? border.getGroupName() : "");
                item.put("headerImgUrl", border.getHeaderImgUrl());
                item.put("fillImgUrl", border.getFillImgUrl());
                item.put("footerImgUrl", border.getFooterImgUrl());
                item.put("thumbUrl", border.getThumbUrl());
                list.add(item);
            }

            // 构建分页信息
            java.util.Map<String, Object> pageInfo = new java.util.HashMap<>();
            pageInfo.put("totalRow", page.getTotal());
            pageInfo.put("pageNumber", page.getCurrent());
            pageInfo.put("pageSize", page.getSize());
            pageInfo.put("totalPage", page.getPages());
            pageInfo.put("firstPage", page.getCurrent() == 1);
            pageInfo.put("lastPage", page.getCurrent() >= page.getPages());
            pageInfo.put("list", list);

            return this.returnSuccessJson(ExceptionEnums.SUCCESS, pageInfo);
        } catch (Exception e) {
            log.error("分页获取边框列表失败", e);
            return this.returnFailJson(e);
        }
    }

    /**
     * 动态分页获取边框列表（推荐使用）
     *
     * @return 分页边框列表
     */
    @RequestMapping(value = "/getBorderPageDynamic")
    public ReturnDto getBorderPageDynamic() {
        try {
            // 获取分页参数
            int pageNumber = this.getIntValue("pageNumber") == -1 ? 1 : this.getIntValue("pageNumber");
            int pageSize = this.getIntValue("pageSize") == -1 ? 10 : this.getIntValue("pageSize");
            String borderKindId = this.getValue("borderKindId");

            IPage<TemplateBorder> page = templateBorderService.getBorderPageDynamic(pageNumber, pageSize, borderKindId);
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, page);
        } catch (Exception e) {
            log.error("动态分页获取边框列表失败", e);
            return this.returnFailJson(e);
        }
    }

    /**
     * 根据分类获取边框列表
     *
     * @return 边框列表
     */
    @RequestMapping(value = "/getBordersByKind")
    public ReturnDto getBordersByKind() {
        try {
            String borderKindId = this.getValue("borderKindId");
            if (borderKindId == null || borderKindId.trim().isEmpty()) {
                throw new RuntimeException("边框分类ID不能为空");
            }

            List<TemplateBorder> borders = templateBorderService.getBordersByKind(borderKindId);
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, borders);
        } catch (Exception e) {
            log.error("根据分类获取边框列表失败", e);
            return this.returnFailJson(e);
        }
    }

    /**
     * 获取边框详情
     *
     * @return 边框详情
     */
    @RequestMapping(value = "/getBorderDetail")
    public ReturnDto getBorderDetail() {
        try {
            String borderId = this.getValue("borderId");
            if (borderId == null || borderId.trim().isEmpty()) {
                throw new RuntimeException("边框ID不能为空");
            }

            TemplateBorder border = templateBorderService.getBorderDetail(borderId);
            if (border == null) {
                throw new RuntimeException("边框不存在");
            }

            return this.returnSuccessJson(ExceptionEnums.SUCCESS, border);
        } catch (Exception e) {
            log.error("获取边框详情失败", e);
            return this.returnFailJson(e);
        }
    }

    /**
     * 获取边框详情（包含多语言分类信息）
     *
     * @return 边框详情
     */
    @RequestMapping(value = "/getBorderDetailWithLanguage")
    public ReturnDto getBorderDetailWithLanguage() {
        try {
            String borderId = this.getValue("borderId");
            if (borderId == null || borderId.trim().isEmpty()) {
                throw new RuntimeException("边框ID不能为空");
            }

            // 获取语言参数
            Integer language = this.getIntValue("language") == -1 ? 1 : this.getIntValue("language");
            String lang = this.getValue("lang");

            TemplateBorder border;
            if (lang != null && !lang.trim().isEmpty()) {
                border = templateBorderService.getBorderDetailWithLang(borderId, lang);
            } else {
                border = templateBorderService.getBorderDetailWithLanguage(borderId, language);
            }

            if (border == null) {
                throw new RuntimeException("边框不存在");
            }

            return this.returnSuccessJson(ExceptionEnums.SUCCESS, border);
        } catch (Exception e) {
            log.error("获取边框详情失败", e);
            return this.returnFailJson(e);
        }
    }

    /**
     * 获取统计信息
     *
     * @return 统计信息
     */
    @RequestMapping(value = "/getStatistics")
    public ReturnDto getStatistics() {
        try {
            Long borderCount = templateBorderService.getBorderCount();
            Long kindCount = templateBorderService.getKindCount();

            java.util.Map<String, Object> statistics = new java.util.HashMap<>();
            statistics.put("borderCount", borderCount);
            statistics.put("kindCount", kindCount);

            return this.returnSuccessJson(ExceptionEnums.SUCCESS, statistics);
        } catch (Exception e) {
            log.error("获取统计信息失败", e);
            return this.returnFailJson(e);
        }
    }

    /**
     * 检查边框是否存在
     *
     * @return 检查结果
     */
    @RequestMapping(value = "/checkBorderExists")
    public ReturnDto checkBorderExists() {
        try {
            String borderId = this.getValue("borderId");
            if (borderId == null || borderId.trim().isEmpty()) {
                throw new RuntimeException("边框ID不能为空");
            }

            boolean exists = templateBorderService.existsBorderId(borderId);
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, exists);
        } catch (Exception e) {
            log.error("检查边框是否存在失败", e);
            return this.returnFailJson(e);
        }
    }

    /**
     * 验证语言参数
     *
     * @return 验证结果
     */
    @RequestMapping(value = "/validateLanguage")
    public ReturnDto validateLanguage() {
        try {
            String lang = this.getValue("lang");
            if (lang == null || lang.trim().isEmpty()) {
                throw new RuntimeException("语言标识不能为空");
            }

            boolean valid = templateBorderService.isValidLanguage(lang);
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, valid);
        } catch (Exception e) {
            log.error("验证语言参数失败", e);
            return this.returnFailJson(e);
        }
    }

    /**
     * 增强的分页查询接口
     *
     * @return 分页边框列表
     */
    @RequestMapping(value = "/getBorderPageEnhanced")
    public ReturnDto getBorderPageEnhanced() {
        try {
            // 构建查询DTO
            TempletBorderQueryDTO queryDTO = new TempletBorderQueryDTO();
            queryDTO.setPageNumber(this.getIntValue("pageNumber") == -1 ? 1 : this.getIntValue("pageNumber"));
            queryDTO.setPageSize(this.getIntValue("pageSize") == -1 ? 10 : this.getIntValue("pageSize"));
            queryDTO.setBorderKindId(this.getValue("borderKindId"));
            queryDTO.setLanguage(this.getIntValue("language") == -1 ? 1 : this.getIntValue("language"));
            queryDTO.setLang(this.getValue("lang"));
            queryDTO.setIncludeMultiLang(this.getBooleanValue("includeMultiLang"));
            queryDTO.setIncludeKindInfo(this.getBooleanValue("includeKindInfo"));

            IPage<TemplateBorder> page = templateBorderService.getBorderPageEnhanced(queryDTO);
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, page);
        } catch (Exception e) {
            log.error("增强分页查询失败", e);
            return this.returnFailJson(e);
        }
    }

    /**
     * 高级分页查询接口
     *
     * @return 分页边框列表
     */
    @RequestMapping(value = "/getBorderPageAdvanced")
    public ReturnDto getBorderPageAdvanced() {
        try {
            // 构建查询DTO
            TempletBorderQueryDTO queryDTO = new TempletBorderQueryDTO();
            queryDTO.setPageNumber(this.getIntValue("pageNumber") == -1 ? 1 : this.getIntValue("pageNumber"));
            queryDTO.setPageSize(this.getIntValue("pageSize") == -1 ? 10 : this.getIntValue("pageSize"));
            queryDTO.setBorderKindId(this.getValue("borderKindId"));
            queryDTO.setLanguage(this.getIntValue("language") == -1 ? 1 : this.getIntValue("language"));
            queryDTO.setLang(this.getValue("lang"));
            queryDTO.setOrderBy(this.getValue("orderBy").isEmpty() ? "createTime" : this.getValue("orderBy"));
            queryDTO.setOrderDirection(this.getValue("orderDirection").isEmpty() ? "DESC" : this.getValue("orderDirection"));
            queryDTO.setIncludeMultiLang(this.getBooleanValue("includeMultiLang"));
            queryDTO.setIncludeKindInfo(this.getBooleanValue("includeKindInfo"));

            IPage<TemplateBorder> page = templateBorderService.getBorderPageAdvanced(queryDTO);
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, page);
        } catch (Exception e) {
            log.error("高级分页查询失败", e);
            return this.returnFailJson(e);
        }
    }

    /**
     * 获取分页查询统计信息
     *
     * @return 统计信息
     */
    @RequestMapping(value = "/getBorderPageStatistics")
    public ReturnDto getBorderPageStatistics() {
        try {
            // 构建查询DTO
            TempletBorderQueryDTO queryDTO = new TempletBorderQueryDTO();
            queryDTO.setBorderKindId(this.getValue("borderKindId"));

            java.util.Map<String, Object> statistics = templateBorderService.getBorderPageStatistics(queryDTO);
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, statistics);
        } catch (Exception e) {
            log.error("获取分页统计信息失败", e);
            return this.returnFailJson(e);
        }
    }
}
