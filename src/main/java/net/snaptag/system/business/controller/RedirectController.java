package net.snaptag.system.business.controller;

import com.vida.sls.annotation.SLSLog;
import com.vida.sls.enums.OperationType;
import net.snaptag.system.business.buservice.ShortLinkBuService;
import net.snaptag.system.sadais.util.core.ToolsKit;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * <AUTHOR>
 * @date ：Created in 2022/3/24 8:56
 * @description： 短链接重定向
 * @modified By：
 * @version: $
 */
@Controller
public class RedirectController {
    @Autowired
    private ShortLinkBuService shortLinkBuService;

    @RequestMapping("/api/redir")
    @SLSLog(value = "短链接重定向", configKey = "business", businessType = "短链接模块", operation = OperationType.OTHER)
    public void test1(HttpServletRequest req, HttpServletResponse rsp) throws IOException {
        String id = req.getParameter("id");
        String url = shortLinkBuService.getUrlById(id);
        if (ToolsKit.isEmpty(url)) {
            rsp.getWriter().println("资源已过期");
        } else {
            rsp.sendRedirect(url);
        }

    }
}
