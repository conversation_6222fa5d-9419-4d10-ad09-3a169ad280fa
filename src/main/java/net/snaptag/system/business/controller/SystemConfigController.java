package net.snaptag.system.business.controller;

import com.vida.sls.annotation.SLSLog;
import com.vida.sls.enums.OperationType;
import net.snaptag.system.business.buservice.SystemConfigBuService;
import net.snaptag.system.business.buservice.UpdateInfoBuService;
import net.snaptag.system.business.utils.Constant;
import net.snaptag.system.sadais.core.common.enums.ExceptionEnums;
import net.snaptag.system.sadais.core.common.exception.ServiceException;
import net.snaptag.system.sadais.web.core.BaseController;
import net.snaptag.system.sadais.web.dto.HeadInfoDto;
import net.snaptag.system.sadais.web.dto.ReturnDto;
import net.snaptag.system.sadais.web.utils.WebTools;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Locale;

@RestController
@RequestMapping(Constant.PRE_MAPPING_URL + "/system")
public class SystemConfigController extends BaseController {
    @Autowired
    private SystemConfigBuService systemConfigBuService;
    @Autowired
    private UpdateInfoBuService updateInfoBuService;
//
//    /**
//     * 获取系统启动信息
//     *
//     * @param request
//     * @return
//     */
//    @RequestMapping(value = "/getindex")
//    public ReturnDto getIndex() {
//        try {
//            HeadInfoDto headInfoDto = this.getHeadInfoDto();
//            Locale locale = WebTools.getLocaleByLanguage(headInfoDto.getLanguage());
//            return this.returnSuccessJson(ExceptionEnums.SUCCESS, systemConfigBuService.getIndex(locale, headInfoDto.getOverseas(), headInfoDto.getVersion()));
//        } catch (ServiceException e) {
//            return this.returnFailJson(e);
//        }
//    }

    @RequestMapping(value = "/getPaperInfoList")
    @SLSLog(value = "获取打印纸张信息", configKey = "business", businessType = "系统配置模块", operation = OperationType.SELECT)
    public ReturnDto getPaperInfoList() {
        try {
            HeadInfoDto headInfoDto = this.getHeadInfoDto();
            Locale locale = WebTools.getLocaleByLanguage(headInfoDto.getLanguage());

            String printerType = this.getValue("printerType");


            return this.returnSuccessJson(ExceptionEnums.SUCCESS, systemConfigBuService.getPaperInfoList(locale, printerType));
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    @RequestMapping(value = "/getPrinterTypeList")
    @SLSLog(value = "获取打印机类型列表", configKey = "business", businessType = "系统配置模块", operation = OperationType.SELECT)
    public ReturnDto getPrinterTypeList() {
        try {
            HeadInfoDto headInfoDto = this.getHeadInfoDto();
            Locale locale = WebTools.getLocaleByLanguage(headInfoDto.getLanguage());
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, systemConfigBuService.getPrinterTypeList(locale));
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

//    /**
//     * 获取网页打印信息
//     *
//     * @param request
//     * @return
//     */
//    @RequestMapping(value = "/getwebprintindex")
//    public ReturnDto getWebPrintIndex() {
//        try {
//            HeadInfoDto headInfoDto = this.getHeadInfoDto();
//            Locale locale = WebTools.getLocaleByLanguage(headInfoDto.getLanguage());
//            return this.returnSuccessJson(ExceptionEnums.SUCCESS, systemConfigBuService.getWebPrintIndex(locale));
//        } catch (ServiceException e) {
//            return this.returnFailJson(e);
//        }
//    }

    /**
     * 获取版本更新信息
     *
     * @return
     */
    @RequestMapping(value = "/getupdateinfo")
    @SLSLog(value = "获取版本更新信息", configKey = "business", businessType = "系统配置模块", operation = OperationType.SELECT)
    public ReturnDto getUpdateInfo() {
        try {
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, updateInfoBuService.getUpdateInfoList());
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    /***
     * 苹果的一些过审问题，是否开启屏蔽
     * @return
     */
    @RequestMapping(value = "/passcheckapple")
    @SLSLog(value = "苹果的一些过审问题，是否开启屏蔽", configKey = "business", businessType = "系统配置模块", operation = OperationType.APPROVE)
    public ReturnDto passCheckApple() {
        return this.returnSuccessJson(ExceptionEnums.SUCCESS, Boolean.FALSE);
    }
}
