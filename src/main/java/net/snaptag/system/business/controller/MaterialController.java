package net.snaptag.system.business.controller;

import com.vida.sls.annotation.SLSLog;
import com.vida.sls.enums.OperationType;
import net.snaptag.system.business.buservice.MaterialBuService;
import net.snaptag.system.business.entity.MaterialResource;
import net.snaptag.system.business.utils.Constant;
import net.snaptag.system.sadais.core.common.core.ToolsConst;
import net.snaptag.system.sadais.core.common.enums.ExceptionEnums;
import net.snaptag.system.sadais.core.common.exception.ServiceException;
import net.snaptag.system.sadais.util.core.ToolsKit;
import net.snaptag.system.sadais.web.core.BaseController;
import net.snaptag.system.sadais.web.dto.HeadInfoDto;
import net.snaptag.system.sadais.web.dto.ReturnDto;
import net.snaptag.system.sadais.web.utils.WebTools;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.util.Locale;

@RestController
@RequestMapping(Constant.PRE_MAPPING_URL + "/material")
public class MaterialController extends BaseController {
    @Autowired
    private MaterialBuService materialBuService;

    /**
     * 获取素材库栏目
     *
     * @return
     */
    @RequestMapping(value = "/getmaterialcolumn")
    @SLSLog(value = "获取素材库栏目", configKey = "business", businessType = "素材库模块", operation = OperationType.SELECT)
    public ReturnDto getMaterialColumn() {
        try {
            String type = this.getValue("type");
            String subtype = this.getValue("subType");
            HeadInfoDto headInfoDto = this.getHeadInfoDto();
            Locale locale = WebTools.getLocaleByLanguage(headInfoDto.getLanguage());
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, materialBuService.getMaterialColumn(type, subtype, headInfoDto.getVersion(), locale, headInfoDto.getOverseas()));
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    @RequestMapping(value = "/getmaterialcolumnandlist")
    @SLSLog(value = "获取素材栏目和资源", configKey = "business", businessType = "素材库模块", operation = OperationType.SELECT)
    public ReturnDto getMaterialColumnAndList() {
        try {
            String type = this.getValue("type");
            String subtype = this.getValue("subType");
            String length = this.getValue("length");
            String placeType = this.getValue("placeType");

            HeadInfoDto headInfoDto = this.getHeadInfoDto();
            Locale locale = WebTools.getLocaleByLanguage(headInfoDto.getLanguage());
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, materialBuService.getMaterialColumnAndList(type, subtype, headInfoDto.getVersion(), locale, headInfoDto.getOverseas(), length, placeType));
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    /**
     * 获取素材库资源
     *
     * @return
     */
    @RequestMapping(value = "/getmateriallist")
    @SLSLog(value = "获取素材资源", configKey = "business", businessType = "素材库模块", operation = OperationType.SELECT)
    public ReturnDto getMaterialList() {
        try {
            String userId = this.getValue("userid");
            String id = this.getValue("id");
            String length = this.getValue("length");
            String placeType = this.getValue("placeType");
            String label = this.getValue("label");
            HeadInfoDto headInfoDto = this.getHeadInfoDto();
            Locale locale = WebTools.getLocaleByLanguage(headInfoDto.getLanguage());
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, materialBuService.getMaterialList(id, userId, length, placeType, locale, headInfoDto.getOverseas(), headInfoDto.getVersion(), label));
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    /**
     * 获取素材库资源
     *
     * @return
     */
    @RequestMapping(value = "/getfontmateriallist")
    @SLSLog(value = "获取字体素材资源", configKey = "business", businessType = "素材库模块", operation = OperationType.SELECT)
    public ReturnDto getAllFontMaterialList() {
        try {

            //            String userId = this.getValue("userid");
//            String id = this.getValue("id");
//            String length = this.getValue("length");
//            String placeType = this.getValue("placeType");
//            String label = this.getValue("label");
            HeadInfoDto headInfoDto = this.getHeadInfoDto();
            Locale locale = WebTools.getLocaleByLanguage(headInfoDto.getLanguage());
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, materialBuService.getFontMaterialList(locale));
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    @RequestMapping(value = "/getfontmateriallistwithlanguage")
    @SLSLog(value = "根据语言获取字体素材资源", configKey = "business", businessType = "素材库模块", operation = OperationType.SELECT)
    public ReturnDto getAllFontMaterialListWithLanguage() {
        try {
            String targetLanguage = this.getValue("fontKind");
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, materialBuService.getFontMaterialListWithLanguage(targetLanguage));
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    @RequestMapping(value = "/getfontmateriallanguage")
    @SLSLog(value = "获取字体语言栏目", configKey = "business", businessType = "素材库模块", operation = OperationType.SELECT)
    public ReturnDto getSupportedLanguages() {
        try {
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, materialBuService.getSupportedLanguages());
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    @RequestMapping(value = "/getmateriallistwithbiankuang")
    @SLSLog(value = "根据边框获取字体素材资源", configKey = "business", businessType = "素材库模块", operation = OperationType.SELECT)
    public ReturnDto getAllFontMaterialListWithBianKuang() {
        try {
            HeadInfoDto headInfoDto = this.getHeadInfoDto();
            Locale locale = WebTools.getLocaleByLanguage(headInfoDto.getLanguage());
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, materialBuService.getMaterialListWithBiankuang(locale));
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    /**
     * 获取素材选择列表（平台）
     *
     * @return
     */
    @RequestMapping(value = "/getmateriallist_pl")
    @SLSLog(value = "获取素材选择列表（平台）", configKey = "business", businessType = "素材库模块", operation = OperationType.SELECT)
    public ReturnDto getMaterialListPl() {
        try {
            HeadInfoDto headInfoDto = this.getHeadInfoDto();
            Locale locale = WebTools.getLocaleByLanguage(headInfoDto.getLanguage());
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, materialBuService.getMaterialList(locale));
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    /**
     * 获取素材列表（平台）
     *
     * @return
     */
    @RequestMapping(value = "/getpagelist_pl")
    @SLSLog(value = "获取素材列表（平台）", configKey = "business", businessType = "素材库模块", operation = OperationType.SELECT)
    public ReturnDto getPageListPl() {
        try {
            String id = this.getValue("id");
            String pageNo = this.getValue("pageno");
            String pageSize = this.getValue("pagesize");
            String label = this.getValue("label");
            String localeCode = this.getValue("localeCode");
            if (ToolsKit.isEmpty(pageNo)) {
                pageNo = "1";
            }
            if (ToolsKit.isEmpty(pageSize)) {
                pageSize = String.valueOf(ToolsConst.PHONEPAGESIZE);
            }
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, materialBuService.getPageList(id, label, Integer.parseInt(pageNo), Integer.parseInt(pageSize), localeCode));
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    /**
     * 新增或更新素材
     *
     * @return
     */
    @RequestMapping(value = "/saveorupdate_pl")
    @SLSLog(value = "新增或更新素材", configKey = "business", businessType = "素材库模块", operation = OperationType.UPDATE)
    public ReturnDto saveOrUpdatePl() {
        try {
            String id = this.getValue("id");
            String mId = this.getValue("mId");
            String length = this.getValue("length");
            String picDto = this.getValue("picDto");
            String placeType = this.getValue("placeType");
            String isNew = this.getValue("isNew");
            String label = this.getValue("label");
            String localeCode = this.getValue("localeCode");
            String sortNum = this.getValue("sortNum");
            String showNewTimeLimit = this.getValue("showNewTimeLimit");
            materialBuService.saveOrUpdate(id, mId, length, placeType, picDto, isNew, label, sortNum, localeCode, showNewTimeLimit);
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, null);
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    @RequestMapping(value = "/saveorupdatebatch_pl")
    @SLSLog(value = "批量新增或更新素材", configKey = "business", businessType = "素材库模块", operation = OperationType.UPDATE)
    public ReturnDto saveOrUpdateBatchPl() {
        try {
            String id = this.getValue("id");
            String mId = this.getValue("mId");
            String length = this.getValue("length");
            String pics = this.getValue("pics");
            String placeType = this.getValue("placeType");
            String isNew = this.getValue("isNew");
            String label = this.getValue("label");
            String localeCode = this.getValue("localeCode");
            String showNewTimeLimit = this.getValue("showNewTimeLimit");
            materialBuService.saveOrUpdateBatch(id, mId, length, placeType, pics, isNew, label, localeCode, showNewTimeLimit);
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, null);
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }


    @RequestMapping(value = "/export_pl")
    @SLSLog(value = "导出素材", configKey = "business", businessType = "素材库模块", operation = OperationType.EXPORT)
    public ReturnDto exportPl() {
        try {
            String id = this.getValue("id");
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, materialBuService.getMaterialResourceById(id));
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    @RequestMapping(value = "/exportxxxx")
    public ReturnDto exportPl111() {
        try {
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, materialBuService.getAllMaterialResource());
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    @RequestMapping(value = "/import_pl")
    @SLSLog(value = "导入素材", configKey = "business", businessType = "素材库模块", operation = OperationType.IMPORT)
    public ReturnDto importPl() {
        try {
            MaterialResource materialResource = this.getBean(MaterialResource.class);
            materialBuService.importMaterialResource(materialResource);
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, null);
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    /**
     * 删除素材
     *
     * @return
     */
    @RequestMapping(value = "/del_pl")
    @SLSLog(value = "删除素材", configKey = "business", businessType = "素材库模块", operation = OperationType.DELETE)
    public ReturnDto delPl() {
        try {
            String id = this.getValue("id");
            materialBuService.del(id);
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, null);
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    /**
     * 获取素材信息
     *
     * @return
     */
    @RequestMapping(value = "/getmaterial_pl")
    @SLSLog(value = "获取素材信息", configKey = "business", businessType = "素材库模块", operation = OperationType.SELECT)
    public ReturnDto getMaterialPl() {
        try {
            String id = this.getValue("id");
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, materialBuService.getMaterial(id));
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    /**
     * 添加素材库
     *
     * @return
     */
    @RequestMapping(value = "/addMaterial_pl")
    @SLSLog(value = "添加素材库", configKey = "business", businessType = "素材库模块", operation = OperationType.INSERT)
    public ReturnDto addMaterialPl() {
        try {
            String name = this.getValue("name");
            String zhTitle = this.getValue("zhTitle");
            String enTitle = this.getValue("enTitle");
            String com = this.getValue("com");
            String type = this.getValue("type");
            String subType = this.getValue("subType");
            materialBuService.saveMaterial(name, com, zhTitle, enTitle, type, subType);
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, null);
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    /**
     * 修改素材库
     *
     * @return
     */
    @RequestMapping(value = "/updateMaterial_pl")
    @SLSLog(value = "修改素材库", configKey = "business", businessType = "素材库模块", operation = OperationType.UPDATE)
    public ReturnDto updateMaterialPl() {
        try {
            String id = this.getValue("id");
            String zhTitle = this.getValue("zhTitle");
            String enTitle = this.getValue("enTitle");

            materialBuService.updateMaterial(id, zhTitle, enTitle);
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, null);
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    /**
     * 删除素材库
     *
     * @return
     */
    @RequestMapping(value = "/deleteMaterial_pl")
    @SLSLog(value = "删除素材库", configKey = "business", businessType = "素材库模块", operation = OperationType.DELETE)
    public ReturnDto deleteMaterial() {
        try {
            String id = this.getValue("id");
            materialBuService.deleteMaterial(id);
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, null);
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    @RequestMapping(value = "/export")
    @SLSLog(value = "导出素材", configKey = "business", businessType = "素材库模块", operation = OperationType.EXPORT)
    public ReturnDto exportUserInfoList() {
        try {
            String mid = this.getValue("mid");
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, materialBuService.export(mid));
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }
}
