package net.snaptag.system.business.controller;

import com.vida.sls.annotation.SLSLog;
import com.vida.sls.enums.OperationType;
import net.snaptag.system.business.buservice.FunctionsSettingBuService;
import net.snaptag.system.business.entity.FunctionsSetting;
import net.snaptag.system.business.utils.Constant;
import net.snaptag.system.sadais.core.common.enums.ExceptionEnums;
import net.snaptag.system.sadais.core.common.exception.ServiceException;
import net.snaptag.system.sadais.web.core.BaseController;
import net.snaptag.system.sadais.web.dto.HeadInfoDto;
import net.snaptag.system.sadais.web.dto.ReturnDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date ：Created in 2024/1/11 10:44
 * @description：
 * @modified By：
 * @version: $
 */
@RestController
@RequestMapping(Constant.PRE_MAPPING_URL + "/functionsetting")
public class FunctionsSettingController extends BaseController {
    @Autowired
    private FunctionsSettingBuService functionsSettingService;

    // list
    @RequestMapping(value = "/findList")
    @SLSLog(value = "获取列表", configKey = "business", businessType = "功能设置模块", operation = OperationType.SELECT)
    public ReturnDto findList() {
        try {
            String printerType = this.getValue("printerType");
            String type = this.getValue("type");
            HeadInfoDto headInfoDto = this.getHeadInfoDto();
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, functionsSettingService.findListByType(printerType, type, headInfoDto.getLanguage()));
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    // list_pl
    @RequestMapping(value = "/findlistpl")
    @SLSLog(value = "获取列表", configKey = "business", businessType = "功能设置模块", operation = OperationType.SELECT)
    public ReturnDto findList_pl() {
        try {
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, functionsSettingService.findList());
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    // addOrUpdate
    @RequestMapping(value = "/saveorupdate")
    @SLSLog(value = "新增或更新设置", configKey = "business", businessType = "功能设置模块", operation = OperationType.UPDATE)
    public ReturnDto saveOrUpdate() {
        try {
            FunctionsSetting functionsSetting = this.getBean(FunctionsSetting.class);
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, functionsSettingService.saveOrUpdate(functionsSetting));
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    // delete
    @RequestMapping(value = "/deletebyid")
    @SLSLog(value = "删除设置", configKey = "business", businessType = "功能设置模块", operation = OperationType.DELETE)
    public ReturnDto deleteById() {
        try {
            String id = this.getValue("id");
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, functionsSettingService.deleteById(id));
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }
}
