package net.snaptag.system.business.controller;

import com.vida.sls.annotation.SLSLog;
import com.vida.sls.enums.OperationType;
import net.snaptag.system.business.buservice.SystemHelpBuService;
import net.snaptag.system.business.entity.SystemHelpItem;
import net.snaptag.system.business.utils.Constant;
import net.snaptag.system.sadais.core.common.core.ToolsConst;
import net.snaptag.system.sadais.core.common.enums.ExceptionEnums;
import net.snaptag.system.sadais.core.common.exception.ServiceException;
import net.snaptag.system.sadais.util.core.ToolsKit;
import net.snaptag.system.sadais.web.core.BaseController;
import net.snaptag.system.sadais.web.dto.HeadInfoDto;
import net.snaptag.system.sadais.web.dto.ReturnDto;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 系统帮助控制器类
 */
@RestController
@RequestMapping(Constant.PRE_MAPPING_URL + "/help")
public class SystemHelpController extends BaseController {

    @Autowired
    private SystemHelpBuService systemHelpBuService;

    /**
     * 获取帮助列表
     *
     * @return
     */
    @RequestMapping(value = "/getlists")
    @SLSLog(value = "获取帮助列表", configKey = "business", businessType = "系统帮助模块", operation = OperationType.SELECT)
    public ReturnDto getLists() {
        try {
            String pageNo = this.getValue("pageno");
            String pageSize = this.getValue("pagesize");
            if (ToolsKit.isEmpty(pageNo)) {
                pageNo = "1";
            }
            String type = this.getValue("type");
            String printerType = this.getValue("printerType");
            if (ToolsKit.isEmpty(pageSize)) {
                pageSize = String.valueOf(ToolsConst.PHONEPAGESIZE);
            }
            HeadInfoDto headInfoDto = this.getHeadInfoDto();
            return this.returnSuccessJson(ExceptionEnums.SUCCESS,
                    systemHelpBuService.getHelpItemList(Integer.parseInt(pageNo), Integer.parseInt(pageSize), type, printerType, headInfoDto.getLanguage()));
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    /**
     * 获取帮助列表
     *
     * @return
     */
    @RequestMapping(value = "/getPage")
    @SLSLog(value = "获取帮助列表（分页）", configKey = "business", businessType = "系统帮助模块", operation = OperationType.SELECT)
    public ReturnDto getPage() {
        try {
            String pageNo = this.getValue("pageno");
            String pageSize = this.getValue("pagesize");
            String type = this.getValue("type");
            if (ToolsKit.isEmpty(pageNo)) {
                pageNo = "1";
            }
            if (ToolsKit.isEmpty(pageSize)) {
                pageSize = "10";
            }
            String language = this.getValue("localLanguageCode");
            String printerType = this.getValue("printerType");
            return this.returnSuccessJson(ExceptionEnums.SUCCESS,
                    systemHelpBuService.getHelpItemPage(Integer.parseInt(pageNo), Integer.parseInt(pageSize), type, printerType, language));
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    @RequestMapping(value = "/additem")
    @SLSLog(value = "新增帮助项", configKey = "business", businessType = "系统帮助模块", operation = OperationType.INSERT)
    public ReturnDto addHelpItem() {
        try {
            String title = this.getValue("title");
            String url = this.getValue("url");

            String sortNum = this.getValue("sortNum");
            String type = this.getValue("type");
            if (StringUtils.isBlank(title) || StringUtils.isBlank(url)) {
                return this.returnSuccessJson(ExceptionEnums.PARAM_ERROR, "Null");
            }
            String coverUrl = this.getValue("coverUrl");

            String printerType = this.getValue("printerType");
            String localLanguageCode = this.getValue("localLanguageCode");

            systemHelpBuService.addSystemHelpItem(title, url, coverUrl, sortNum, type, printerType, localLanguageCode);
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, "Ok");
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    @RequestMapping(value = "/updateitem")
    @SLSLog(value = "更新帮助项", configKey = "business", businessType = "系统帮助模块", operation = OperationType.UPDATE)
    public ReturnDto updateHelpItem() {
        try {
            SystemHelpItem updateItem = this.getBean(SystemHelpItem.class);
            systemHelpBuService.update(updateItem);
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, "Ok");
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    @RequestMapping(value = "/deleteitem")
    @SLSLog(value = "删除帮助项", configKey = "business", businessType = "系统帮助模块", operation = OperationType.DELETE)
    public ReturnDto deleteHelpItem() {
        try {
            String id = this.getValue("id");
            systemHelpBuService.deleteSystemHelp(id);
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, "Ok");
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

}
