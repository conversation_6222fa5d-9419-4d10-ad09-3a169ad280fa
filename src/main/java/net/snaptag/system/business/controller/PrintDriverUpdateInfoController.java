package net.snaptag.system.business.controller;

import com.vida.sls.annotation.SLSLog;
import com.vida.sls.enums.OperationType;
import net.snaptag.system.business.buservice.PrintDriverUpdateInfoBuService;
import net.snaptag.system.business.entity.PrintDriverUpdateInfo;
import net.snaptag.system.business.utils.Constant;
import net.snaptag.system.sadais.core.common.core.ToolsConst;
import net.snaptag.system.sadais.core.common.enums.ExceptionEnums;
import net.snaptag.system.sadais.core.common.exception.ServiceException;
import net.snaptag.system.sadais.util.core.ToolsKit;
import net.snaptag.system.sadais.web.core.BaseController;
import net.snaptag.system.sadais.web.dto.HeadInfoDto;
import net.snaptag.system.sadais.web.dto.ReturnDto;
import net.snaptag.system.sadais.web.utils.WebTools;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Locale;

/**
 * <AUTHOR>
 * @date ：Created in 2023/4/6 10:44
 * @description：打印机驱动更新
 * @modified By：
 * @version: $
 */
@RestController
@RequestMapping(Constant.PRE_MAPPING_URL + "/printdriver")
public class PrintDriverUpdateInfoController extends BaseController {

    @Autowired
    private PrintDriverUpdateInfoBuService printDriverUpdateInfoBuService;

    @RequestMapping(value = "/findList")
    @SLSLog(value = "获取驱动列表", configKey = "business", businessType = "打印驱动更新信息模块", operation = OperationType.SELECT)
    public ReturnDto findList() {
        try {
            HeadInfoDto headInfoDto = getHeadInfoDto();
            Locale locale = WebTools.getLocaleByLanguage(headInfoDto.getLanguage());
//            return this.returnSuccessJson(ExceptionEnums.SUCCESS, printDriverUpdateInfoBuService.findListMap(locale));
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, new ArrayList());
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    @RequestMapping(value = "/findUpgradeDriverInfo")
    @SLSLog(value = "获取驱动信息", configKey = "business", businessType = "打印驱动更新信息模块", operation = OperationType.SELECT)
    public ReturnDto findUpgradeDriverInfo() {
        try {
            String userId = this.getValue("userid");
            String printerType = getValue("printerType");
            String version = getValue("versionCode");
            HeadInfoDto headInfoDto = getHeadInfoDto();
            Locale locale = WebTools.getLocaleByLanguage(headInfoDto.getLanguage());
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, printDriverUpdateInfoBuService.findByUpgradeInfoByTypeAndVersion(userId, printerType, version, locale));
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }


    // 支撑平台，增删改查

    /**
     * 获取page
     *
     * @return
     */
    @RequestMapping(value = "/getPage")
    @SLSLog(value = "获取列表", configKey = "business", businessType = "打印驱动更新信息模块", operation = OperationType.SELECT)
    public ReturnDto findPage() {
        try {
            String printerModel = this.getValue("printerModel");
            String pageNo = this.getValue("pageno");
            String pageSize = this.getValue("pagesize");

            if (ToolsKit.isEmpty(pageNo)) {
                pageNo = "1";
            }
            if (ToolsKit.isEmpty(pageSize)) {
                pageSize = ToolsConst.PHONEPAGESIZE + "";
            }

            return this.returnSuccessJson(ExceptionEnums.SUCCESS, printDriverUpdateInfoBuService.findPageByCond(printerModel, Integer.parseInt(pageNo), Integer.parseInt(pageSize)));
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    /***
     * 根据id删除
     * @return
     */
    @RequestMapping(value = "/delete")
    @SLSLog(value = "根据id删除", configKey = "business", businessType = "打印驱动更新信息模块", operation = OperationType.DELETE)
    public ReturnDto delete() {
        try {
            String id = this.getValue("id");
            printDriverUpdateInfoBuService.del(id);
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, null);
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    /***
     * 新增或修改
     * @return
     */
    @RequestMapping(value = "/saveorupdate")
    @SLSLog(value = "新增或修改", configKey = "business", businessType = "打印驱动更新信息模块", operation = OperationType.UPDATE)
    public ReturnDto saveOrUpdate() {
        try {
            PrintDriverUpdateInfo printDriverUpdateInfo = this.getBean(PrintDriverUpdateInfo.class);
            String autoCale = this.getValue("autoCale");
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, printDriverUpdateInfoBuService.addOrUpdate(printDriverUpdateInfo, autoCale));
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    @RequestMapping(value = "/findById")
    @SLSLog(value = "根据ID获取", configKey = "business", businessType = "打印驱动更新信息模块", operation = OperationType.SELECT)
    public ReturnDto findById() {
        try {
            String id = this.getValue("id");
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, printDriverUpdateInfoBuService.findByid(id));
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    @RequestMapping(value = "/findtypelist")
    @SLSLog(value = "获取类型列表", configKey = "business", businessType = "打印驱动更新信息模块", operation = OperationType.SELECT)
    public ReturnDto findTypeList() {
        try {
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, printDriverUpdateInfoBuService.findTypeList());
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    @RequestMapping(value = "/findAll")
    @SLSLog(value = "获取所有数据", configKey = "business", businessType = "打印驱动更新信息模块", operation = OperationType.SELECT)
    public ReturnDto findAll() {
        try {
            String printerType = this.getValue("printerType");
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, printDriverUpdateInfoBuService.findAll(printerType));
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    @RequestMapping(value = "/findWhileList")
    @SLSLog(value = "获取白名单", configKey = "business", businessType = "打印驱动更新信息模块", operation = OperationType.SELECT)
    public ReturnDto findWhileList() {
        try {
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, printDriverUpdateInfoBuService.findWhileList());
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    @RequestMapping(value = "/addWhileList")
    @SLSLog(value = "新增白名单", configKey = "business", businessType = "打印驱动更新信息模块", operation = OperationType.INSERT)
    public ReturnDto addWhileList() {
        try {
            String otherid = this.getValue("otherid");
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, printDriverUpdateInfoBuService.addWhileUser(otherid));
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    @RequestMapping(value = "/removeWhileList")
    @SLSLog(value = "删除白名单", configKey = "business", businessType = "打印驱动更新信息模块", operation = OperationType.DELETE)
    public ReturnDto removeWhileList() {
        try {
            String otherid = this.getValue("otherid");
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, printDriverUpdateInfoBuService.removeWhileUser(otherid));
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    @RequestMapping(value = "/update-submit")
    @SLSLog(value = "提交审核", configKey = "business", businessType = "打印驱动更新信息模块", operation = OperationType.SUBMIT)
    public ReturnDto updateSubmit() {
        try {
            String id = this.getValue("id");
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, printDriverUpdateInfoBuService.updateSubmit(id));
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    @RequestMapping(value = "/update-reject")
    @SLSLog(value = "打回，使记录变成预发布状态", configKey = "business", businessType = "打印驱动更新信息模块", operation = OperationType.REJECT)
    public ReturnDto updateReject() {
        try {
            String id = this.getValue("id");
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, printDriverUpdateInfoBuService.updateReject(id));
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    @RequestMapping(value = "/update-pass")
    @SLSLog(value = "通过审核", configKey = "business", businessType = "打印驱动更新信息模块", operation = OperationType.APPROVE)
    public ReturnDto updatePass() {
        try {
            String id = this.getValue("id");
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, printDriverUpdateInfoBuService.updatePass(id));
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }
}