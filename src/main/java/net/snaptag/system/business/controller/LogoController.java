package net.snaptag.system.business.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import net.snaptag.system.business.buservice.LogoService;
import net.snaptag.system.business.entity.Logo;
import net.snaptag.system.business.utils.Constant;
import net.snaptag.system.sadais.core.common.enums.ExceptionEnums;
import net.snaptag.system.sadais.web.core.BaseController;
import net.snaptag.system.sadais.web.dto.HeadInfoDto;
import net.snaptag.system.sadais.web.dto.ReturnDto;
import net.snaptag.system.sadais.web.utils.WebTools;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Locale;

/**
 * Author：Chenjy
 * Date:2025/8/21
 * Description:
 */
@RestController
@RequestMapping(Constant.PRE_MAPPING_URL + "/domestic/logo")
public class LogoController extends BaseController {
    @Autowired
    private LogoService logoService;

    @RequestMapping(value = "/getLogoList")
    public ReturnDto getLogoList() {
        try {
//            Integer language = this.getIntValue("langType");
            HeadInfoDto headInfoDto = this.getHeadInfoDto();
            Locale locale = WebTools.getLocaleByLanguage(headInfoDto.getLanguage());
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, logoService.getLogoList(locale));
        } catch (Exception e) {
            return this.returnFailJson(e);
        }
    }


    /**
     * @description 获取logo分组
     * <AUTHOR> @date
     */
    @RequestMapping(value = "/getLogoKindList")
    public ReturnDto getLogoKindList() {
        try {
//            Integer language = this.getIntValue("langType") == -1 ? 1 : this.getIntValue("langType");
//            String lang = this.getValue("lang");
            HeadInfoDto headInfoDto = this.getHeadInfoDto();
            Locale locale = WebTools.getLocaleByLanguage(headInfoDto.getLanguage());
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, logoService.getLogoKindList(locale));
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }


    /**
     * @description 获取logo列表分页
     * <AUTHOR> @date
     */
    @RequestMapping(value = "/getLogoPage")
    public ReturnDto getLogoPage() {
        try {
            int pageNumber = this.getIntValue("pageNumber") == -1 ? 1 : this.getIntValue("pageNumber");
            int pageSize = this.getIntValue("pageSize") == -1 ? 10 : this.getIntValue("pageSize");
//            int language = this.getIntValue("langType") == -1 ? 1 : this.getIntValue("langType");
            HeadInfoDto headInfoDto = this.getHeadInfoDto();
            Locale locale = WebTools.getLocaleByLanguage(headInfoDto.getLanguage());
            String logoKindId = this.getValue("logoKindId");
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, logoService.getLogoPage(pageNumber, pageSize, logoKindId, locale));
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

    }
}
