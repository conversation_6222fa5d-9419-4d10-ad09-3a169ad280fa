package net.snaptag.system.business.enums;

import java.util.LinkedHashMap;

/**
 * 素材类型
 */
public enum MaterialTypeEnums {
    EDIT(0, "编辑纸条"),
    TIEZHI(1, "贴纸"),
    PRINT(2, "打印模板"),
    FRONT(3, "字体"),
    BIAN_KUANG(4, "边框"),
    LOGO(5, "装饰"),
    ;
    private final int value;
    private final String desc;
    private static final LinkedHashMap<Integer, String> map;

    static {
        map = new LinkedHashMap<>();
        for (MaterialTypeEnums materialTypeEnums : MaterialTypeEnums.values()) {
            map.put(materialTypeEnums.getValue(), materialTypeEnums.getDesc());
        }
    }

    MaterialTypeEnums(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }


    public int getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public static LinkedHashMap<Integer, String> getMap() {
        return map;
    }

    public static MaterialTypeEnums getByValue(int value) {
        for (MaterialTypeEnums enumItem : values()) {
            if (enumItem.value == value) {
                return enumItem;
            }
        }
        return null; // 或抛出异常
    }
}
