package net.snaptag.system.business.enums;

import java.util.LinkedHashMap;

public enum PrinterTypeEnums {

    HP1("HP1", "hp1", "https://share.xplable.com/api/img/gam/common/hp1.png"),
    HP2("HP2", "hp2", "https://share.xplable.com/api/img/gam/common/hp2.png"),
    TP6("TP6/TP6-S", "tp6", "https://share.xplable.com/api/img/gam/common/tp6.png");

    private final String name;
    private final String code;
    private final String icon;

    PrinterTypeEnums(String name, String code, String icon) {
        this.code = code;
        this.icon = icon;
        this.name = name;
    }

    private static final LinkedHashMap<String, PrinterTypeEnums> map;
    static {
        map = new LinkedHashMap<>();
        for (PrinterTypeEnums objEnums : PrinterTypeEnums.values()) {
            map.put(objEnums.getCode(), objEnums);
        }
    }

    public String getName() {
        return this.name;
    }

    public String getCode() {
        return code;
    }

    public String getIcon() {
        return icon;
    }

    public static LinkedHashMap<String, PrinterTypeEnums> getMap() {
        return map;
    }

}
