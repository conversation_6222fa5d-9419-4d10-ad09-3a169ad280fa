package net.snaptag.system.business.enums;

import net.snaptag.system.business.vo.WebPagePrintVo;
import net.snaptag.system.business.vo.WebPrintGroupVo;

import java.util.ArrayList;

public enum WebPrintDefaultEnums {
    GOOGLE("Google", "https://www.google.com", "https://m.snaptag.top/api/img/gam/snapTag/common/webprint/web_ic_google.png"),
    FACEBOOK("Facebook", "https://www.facebook.com", "https://m.snaptag.top/api/img/gam/snapTag/common/webprint/web_ic_facebook.png"),
    X("X", "https://www.x.com", "https://m.snaptag.top/api/img/gam/snapTag/common/webprint/web_ic_x.png"),
    WIKI("Wiki", "https://www.wiki.com", "https://m.snaptag.top/api/img/gam/snapTag/common/webprint/web_ic_wiki.png"),
    <PERSON><PERSON><PERSON>("Quora", "https://www.quora.com", "https://m.snaptag.top/api/img/gam/snapTag/common/webprint/web_ic_quora.png"),;
    private final String                               name;
    private final String                               url;
    private final String                               icon;
    private static final WebPrintGroupVo defaultWebPrintGroup;

    static {
        defaultWebPrintGroup = new WebPrintGroupVo();
        defaultWebPrintGroup.setId("");
        defaultWebPrintGroup.setIsDefault(1);
        defaultWebPrintGroup.setName("hot");
        defaultWebPrintGroup.setPageList(new ArrayList<>());

        for (WebPrintDefaultEnums enumsObj: WebPrintDefaultEnums.values()) {
            WebPagePrintVo wpp = new WebPagePrintVo();
            wpp.setCodeId(1);
            wpp.setGroupId("");
            wpp.setIconUrl(enumsObj.getIcon());
            wpp.setName(enumsObj.getName());
            wpp.setLinkUrl(enumsObj.getUrl());
            wpp.setIsDefault(1);
            defaultWebPrintGroup.getPageList().add(wpp);
        }
    }

    WebPrintDefaultEnums(String name, String url, String icon) {
        this.icon = icon;
        this.name = name;
        this.url = url;
    }

    public String getName() {
        return name;
    }

    public String getUrl() {
        return url;
    }

    public String getIcon() {
        return icon;
    }

    public static WebPrintGroupVo getDefaultWebPrintGroup() {
        return defaultWebPrintGroup;
    }
}
