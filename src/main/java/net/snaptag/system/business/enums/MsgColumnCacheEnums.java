package net.snaptag.system.business.enums;

import net.snaptag.system.sadais.core.common.enums.ICacheEnums;

import java.util.LinkedHashMap;

public enum MsgColumnCacheEnums implements ICacheEnums {
    MSG_COLUMN_BY_USER_ID("sysmsg:msg:col:by:uid:", "消息栏目记录");
    private final String                               key;
    private final String                               desc;
    private static final LinkedHashMap<String, String> map;
    static {
        map = new LinkedHashMap<String, String>();
        for (MsgColumnCacheEnums msgColumnCacheEnums : MsgColumnCacheEnums.values()) {
            map.put(msgColumnCacheEnums.getKey(), msgColumnCacheEnums.getDesc());
        }
    }

    private MsgColumnCacheEnums(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public String getKey() {
        return key;
    }

    public String getDesc() {
        return desc;
    }

    public static LinkedHashMap<String, String> getMap() {
        return map;
    }
}
