package net.snaptag.system.business.enums;

/**
 * 问题反馈时需要反馈的打印机类型
 */
public enum FeedBackPrinterTypeEnums {
    HP1("YOYINPRINTER", "HP1","https://share.xplable.com/api/img/gam/common/feedback/tp2.png"),
    LP2S("YOYINPRINTER", "HP2","https://share.xplable.com/api/img/gam/common/feedback/lp2-s.png"),
    TP6("YOYINPRINTER", "TP6/TP6-S", "https://share.xplable.com/api/img/gam/common/tp6.png"),
    OTHER("YOYINPRINTER", "其他","https://share.xplable.com/api/img/gam/common/feedback/other.png");
    private final String                                appName;
    private final String                                key;
    private final String                                picUrl;


    FeedBackPrinterTypeEnums(String appName, String key, String picUrl) {
        this.appName = appName;
        this.key = key;
        this.picUrl = picUrl;
    }

    public String getAppName() {
        return appName;
    }

    public String getKey() {
        return key;
    }

    public String getPicUrl() {
        return picUrl;
    }

}
