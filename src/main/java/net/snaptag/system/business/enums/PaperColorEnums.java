package net.snaptag.system.business.enums;

import java.util.LinkedHashMap;

public enum PaperColorEnums {
    llk(1, "间隙纸", "123"),
    LX(2, "连续纸", "ffff");
    private final int                                key;
    private final String                             name;
    private final String                             code;

    PaperColorEnums(int key, String name, String code) {
        this.key = key;
        this.name = name;
        this.code = code;
    }

    private static final LinkedHashMap<Integer, PaperColorEnums> map;
    static {
        map = new LinkedHashMap<>();
        for (PaperColorEnums objEnums : PaperColorEnums.values()) {
            map.put(objEnums.key, objEnums);
        }
    }
    public static final LinkedHashMap<Integer, PaperColorEnums> getMap(){
        return map;
    }
}
