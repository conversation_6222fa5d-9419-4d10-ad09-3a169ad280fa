package net.snaptag.system.business.enums;
import java.util.LinkedHashMap;

/**
 * 草稿箱类型
 */
public enum DraftsTypeEnums {
    DRAFTS(0, "草稿箱"), 
    HISTORY(1, "我的历史");
    private final int                                   value;
    private final String                                desc;
    private static final LinkedHashMap<Integer, String> map;
    static {
        map = new LinkedHashMap<>();
        for (DraftsTypeEnums draftsTypeEnums : DraftsTypeEnums.values()) {
            map.put(draftsTypeEnums.getValue(), draftsTypeEnums.getDesc());
        }
    }

    DraftsTypeEnums(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public int getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public static LinkedHashMap<Integer, String> getMap() {
        return map;
    }
}
