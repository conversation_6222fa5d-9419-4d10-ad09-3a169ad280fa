package net.snaptag.system.business.enums;

import java.util.LinkedHashMap;

public enum PaperMaterialTypeEnums {
    HC(1, "合成纸面材"),
    PT(2, "普通纸面材");
    private final int                                key;
    private final String                             value;

    PaperMaterialTypeEnums(int key, String value) {
        this.key = key;
        this.value = value;
    }

    private static final LinkedHashMap<Integer, PaperMaterialTypeEnums> map;
    static {
        map = new LinkedHashMap<>();
        for (PaperMaterialTypeEnums objEnums : PaperMaterialTypeEnums.values()) {
            map.put(objEnums.key, objEnums);
        }
    }
    public static final LinkedHashMap<Integer, PaperMaterialTypeEnums> getMap(){
        return map;
    }
}
