package net.snaptag.system.business.enums;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;

public enum PaperInfoEnums {

//    P1(15,22,"22×15mm", "330"),
//    P2(15,30,"30×15mm", "450"),
//    P3(15,40,"40×15mm", "600"),
//    P4(15,50,"50×15mm", "750"),
//    P1(15,30,"30×15mm", "450"),
//    P2(15,50,"50×15mm", "750"),
//    P3(12,30,"30×12mm", "360"),
//    P4(12,40,"40×12mm", "480"),
//    P5(12,50,"50×12mm", "600"),

    P5(14,22,"22×14mm", "308", "hp1"),
    P6(14,30,"30×14mm", "420", "hp1"),
    P7(14,40,"40×14mm", "560", "hp1"),
    P8(14,50,"50×14mm", "700", "hp1"),
    P51(15,22,"22×15mm", "308", "hp1"),
    P61(15,30,"30×15mm", "420", "hp1"),
    P71(15,40,"40×15mm", "560", "hp1"),
    P81(15,50,"50×15mm", "700", "hp1"),
    LXZ1(0,0,"标签连续纸", "-1", "hp1"),

    LP5(14,22,"22×14mm", "308", "hp2"),
    LP6(14,30,"30×14mm", "420", "hp2"),
    LP7(14,40,"40×14mm", "560", "hp2"),
    LP8(14,50,"50×14mm", "700", "hp2"),
    LP51(15,22,"22×15mm", "308", "hp2"),
    LP61(15,30,"30×15mm", "420", "hp2"),
    LP71(15,40,"40×15mm", "560", "hp2"),
    LP81(15,50,"50×15mm", "700", "hp2"),
    LLXZ1(0,0,"标签连续纸", "-1", "hp2"),

    SLP5(14,22,"22×14mm", "308", "hp2s"),
    SLP6(14,30,"30×14mm", "420", "hp2s"),
    SLP7(14,40,"40×14mm", "560", "hp2s"),
    SLP8(14,50,"50×14mm", "700", "hp2s"),
    SLP51(15,22,"22×15mm", "308", "hp2s"),
    SLP61(15,30,"30×15mm", "420", "hp2s"),
    SLP71(15,40,"40×15mm", "560", "hp2s"),
    SLP81(15,50,"50×15mm", "700", "hp2s"),
    SLLXZ1(0,0,"标签连续纸", "-1", "hp2s"),

    P53(14,22,"22×14mm", "308", "hp3"),
    P63(14,30,"30×14mm", "420", "hp3"),
    P73(14,40,"40×14mm", "560", "hp3"),
    P83(14,50,"50×14mm", "700", "hp3"),
    P513(15,22,"22×15mm", "308", "hp3"),
    P613(15,30,"30×15mm", "420", "hp3"),
    P713(15,40,"40×15mm", "560", "hp3"),
    P813(15,50,"50×15mm", "700", "hp3"),
    LXZ13(0,0,"标签连续纸", "-1", "hp3"),

    P54(14,22,"22×14mm", "308", "hp4"),
    P64(14,30,"30×14mm", "420", "hp4"),
    P74(14,40,"40×14mm", "560", "hp4"),
    P84(14,50,"50×14mm", "700", "hp4"),
    P514(15,22,"22×15mm", "308", "hp4"),
    P614(15,30,"30×15mm", "420", "hp4"),
    P714(15,40,"40×15mm", "560", "hp4"),
    P814(15,50,"50×15mm", "700", "hp4"),
    LXZ14(0,0,"标签连续纸", "-1", "hp4"),

    LXZhp7(0,0,"标签连续纸", "12", "hp7"),

    tp61(30,50,"50×30mm", "1500", "tp6"),
    tp62(40,50,"50×40mm", "2000", "tp6"),
    tp63(75,50,"50×75mm", "3750", "tp6"),

    bp11(30,56,"56×30mm", "1680", "bq1"),
    bp12(30,50,"50×30mm", "1500 ", "bq1"),
    bp13(30,40,"40×30mm", "1200", "bq1"),
    bp16(30,20,"20×30mm", "600", "bq1"),
    bp17(60,40,"60×40mm", "2400", "bq1"),
    bp14(15,25,"25×15mm", "375", "bq1"),
    bp15(1,56,"56mm连续纸", "56", "bq1");

    private final int height;
    private final int width;
    private final String name;
    private final String length;
    private final String printerType;

    PaperInfoEnums(int height, int width, String name, String length, String printerType) {
        this.width = width;
        this.height = height;
        this.name = name;
        this.length = length;
        this.printerType = printerType;
    }

    private static final LinkedHashMap<String, PaperInfoEnums> map;
    static {
        map = new LinkedHashMap<>();
        for (PaperInfoEnums objEnums : PaperInfoEnums.values()) {
            map.put(objEnums.getLength(), objEnums);
        }
    }

    public String getName() {
        return this.name;
    }

    public int getHeight() {
        return height;
    }

    public int getWidth() {
        return width;
    }

    public String getLength() {
        return length;
    }

    public String getPrinterType() {
        return printerType;
    }

    public static LinkedHashMap<String, PaperInfoEnums> getMap() {
        return map;
    }

    public static List<PaperInfoEnums> getEnumsByPrinterTYpe(String printerType){
        List<PaperInfoEnums> result = new ArrayList<>();
        for (PaperInfoEnums objEnums : PaperInfoEnums.values()) {
            if (objEnums.getPrinterType().equals(printerType)){
                result.add(objEnums);
            }
        }
        return result;
    }
}
