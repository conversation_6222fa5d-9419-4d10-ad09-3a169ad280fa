package net.snaptag.system.business.enums;

import net.snaptag.system.sadais.core.common.enums.ICacheEnums;

import java.util.LinkedHashMap;

/**
 * <AUTHOR>
 * @date ：Created in 2024/6/27 9:45
 * @description：
 * @modified By：
 * @version: $
 */
public enum CacheRefreshRemarkEnums implements ICacheEnums {
    BANNER("cache:refresh:time:by:banner", "banner更新时间"),
    DICT("cache:refresh:time:by:dict", "dict更新时间"),
    MATERIAL("cache:refresh:time:by:material", "素材箱更新时间"),
    ;
    private final String                               key;
    private final String                               desc;
    private static final LinkedHashMap<String, String> map;
    static {
        map = new LinkedHashMap<String, String>();
        for (CacheRefreshRemarkEnums enums : CacheRefreshRemarkEnums.values()) {
            map.put(enums.getKey(), enums.getDesc());
        }
    }

    private CacheRefreshRemarkEnums(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public String getKey() {
        return key;
    }

    public String getDesc() {
        return desc;
    }

    public static LinkedHashMap<String, String> getMap() {
        return map;
    }
}
