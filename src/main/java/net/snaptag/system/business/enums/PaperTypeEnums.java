package net.snaptag.system.business.enums;

import java.util.LinkedHashMap;

public enum PaperTypeEnums {
    JX(1, "间隙纸"),
    LX(2, "连续纸");
    private final int                                key;
    private final String                             value;

    PaperTypeEnums(int key, String value) {
        this.key = key;
        this.value = value;
    }

    private static final LinkedHashMap<Integer, PaperTypeEnums> map;
    static {
        map = new LinkedHashMap<>();
        for (PaperTypeEnums objEnums : PaperTypeEnums.values()) {
            map.put(objEnums.key, objEnums);
        }
    }
    public static final LinkedHashMap<Integer, PaperTypeEnums> getMap(){
        return map;
    }
}
