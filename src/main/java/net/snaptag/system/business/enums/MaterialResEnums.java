package net.snaptag.system.business.enums;
import java.util.LinkedHashMap;

/**
 * 素材资源key
 */
public enum MaterialResEnums {
    LIST("listUrl", "列表资源地址"), 
    RES("resUrl", "资源地址"), 
    TOP("topUrl", "上图地址"), 
    DOWN("downUrl", "下图地址"), 
    ADD_LEFT("addLeftUrl", "添加左图地址"), 
    LEFT("leftUrl", "左图地址"), 
    RIGHT("rightUrl", "右图地址");
    private final String                               key;
    private final String                               desc;
    private static final LinkedHashMap<String, String> map;
    static {
        map = new LinkedHashMap<>();
        for (MaterialResEnums materialTypeEnums : MaterialResEnums.values()) {
            map.put(materialTypeEnums.getKey(), materialTypeEnums.getDesc());
        }
    }

    MaterialResEnums(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public String getKey() {
        return key;
    }

    public String getDesc() {
        return desc;
    }

    public static LinkedHashMap<String, String> getMap() {
        return map;
    }
}
