package net.snaptag.system.business.enums;
import java.util.LinkedHashMap;

/**
 * 资源类型
 */
public enum ResourceTypeEnums {
    DATA(0, "编辑纸条数据"), 
    PIC(1, "图片"), 
    VOICE(2, "语音"), 
    SHARE(3, "共享打印");
    private final int                                   value;
    private final String                                desc;
    private static final LinkedHashMap<Integer, String> map;
    static {
        map = new LinkedHashMap<>();
        for (ResourceTypeEnums resourceTypeEnums : ResourceTypeEnums.values()) {
            map.put(resourceTypeEnums.getValue(), resourceTypeEnums.getDesc());
        }
    }

    ResourceTypeEnums(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public int getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public static LinkedHashMap<Integer, String> getMap() {
        return map;
    }
}
