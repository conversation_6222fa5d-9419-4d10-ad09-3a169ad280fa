package net.snaptag.system.business.enums;

import java.util.LinkedHashMap;

/**
 * 问题反馈时需要反馈的打印机类型
 */
public enum FeedBackProblemTypeEnums {
    /**
     软件问题有（闪退 crash 下载 download 其他 other 不能调整文字大小 notresizetext 页面美化 pagebeautifly 新功能优化 featureoptimization），
     硬件问题有（打印卡纸 printjam 无法取纸 cannottake 打印不清晰 notclear 无法连接 cannotconnect 其他 other）
     * */
//    SOFT_CRASH("crash", "闪退","software"),
//    SOFT_DOWNLOAD("download", "下载","software"),
////    SOFT_NOTRESIZETEXT("notresizetext", "不能调整文字大小","software"),
//    SOFT_PAGEBEAUTIFLY("pagebeautifly", "页面美化","software"),
//    SOFT_FEATURE_OPTIMIZATION("featureoptimization", "新功能优化","software"),
//    HARDWARE_PRINTJAM("printjam", "打印卡纸","hardware"),
////    HARDWARE_CANNOTTAKE("cannottake", "无法取纸","hardware"),
//    HARDWARE_NOTCLEAR("notclear", "打印不清晰","hardware"),
//    HARDWARE_CANNOT_CONNECT("cannotconnect", "无法连接","hardware"),
//    SOFT_OTHER("other", "其他","software"),
    SOFT_CRASH("crash", "闪退","software", "feedback_problem_crash"),
    SOFT_DOWNLOAD("download", "下载","software", "feedback_problem_download"),
//    SOFT_NOTRESIZETEXT("notresizetext", "不能调整文字大小","software", "feedback_problem_notresizetext"),
    SOFT_PAGEBEAUTIFLY("pagebeautifly", "页面美化","software", "feedback_problem_pagebeautifly"),
    SOFT_FEATURE_OPTIMIZATION("featureoptimization", "新功能优化","software", "feedback_problem_featureoptimization"),
    HARDWARE_PRINTJAM("printjam", "打印卡纸","hardware", "feedback_problem_printjam"),
    //    HARDWARE_CANNOTTAKE("cannottake", "无法取纸","hardware", "feedback_problem_cannottake"),
    HARDWARE_NOTCLEAR("notclear", "打印不清晰","hardware", "feedback_problem_notclear"),
    HARDWARE_CANNOT_CONNECT("cannotconnect", "无法连接","hardware", "feedback_problem_cannotconnect"),
    SOFT_OTHER("other", "其他","software", "feedback_problem_other"),;
    private final String                                code;
    private final String                                desc;
    private final String                                type;
    private final String                                languageCode;

    FeedBackProblemTypeEnums(String code, String desc, String type, String languageCode) {
        this.code = code;
        this.type = type;
        this.desc = desc;
        this.languageCode = languageCode;
    }

    public String getType() {
        return type;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public String getLanguageCode() {
        return languageCode;
    }

    private static final LinkedHashMap<String, FeedBackProblemTypeEnums> map;
    static {
        map = new LinkedHashMap<String, FeedBackProblemTypeEnums>();
        for (FeedBackProblemTypeEnums cacheEnums : FeedBackProblemTypeEnums.values()) {
            map.put(cacheEnums.getCode(), cacheEnums);
        }
    }
    public static LinkedHashMap<String, FeedBackProblemTypeEnums> getMap() {
        return map;
    }
}
