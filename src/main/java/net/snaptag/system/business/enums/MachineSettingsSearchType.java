package net.snaptag.system.business.enums;

/**
 * Author：Chenjy
 * Date:2025/8/25
 * Description:
 */
public enum MachineSettingsSearchType {
    // 蓝牙
    BLUETOOTH(0),
    // 机器产品名称-对外销售
    MACHINE(1),
    // 机器指令返回-硬件
    HARDWARE(2),
    // 未知
    NULL(-1);

    private final Integer type;

    MachineSettingsSearchType(Integer type) {
        this.type = type;
    }

    /**
     * 通过type获取枚举类型
     *
     * @param type 枚举type
     * @return 枚举对象
     */
    public static MachineSettingsSearchType get(Integer type) {
        for (MachineSettingsSearchType enumObj : MachineSettingsSearchType.values()) {
            if (enumObj.type.equals(type)) {
                return enumObj;
            }
        }
        return NULL;
    }
}
