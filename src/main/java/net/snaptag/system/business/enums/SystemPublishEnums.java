package net.snaptag.system.business.enums;

import net.snaptag.system.sadais.core.common.enums.ICacheEnums;

import java.util.LinkedHashMap;

public enum SystemPublishEnums implements ICacheEnums {
	PAPER_INFO_UPDATE_PUB_SUB("xeasylabel:paperinfo:update:pub:sub", "纸张更新广播"),
    PAPER_DISPLAY_UPDATE_PUB_SUB("xeasylabel:paperdisplay:update:pub:sub", "纸张显示更新广播"),
	MATERIAL_UPDATE_PUB_SUB("xeasylabel:material:update:pub:sub", "素材库更新广播"),
	MATERIAL_RES_UPDATE_PUB_SUB("xeasylabel:material:res:update:pub:sub", "素材库资源更新广播"),
	BANNER_UPDATE_PUB_SUB("xeasylabel:banner:info:update:pub:sub", "banner更新广播"),
    DICT_UPDATE_PUB_SUB("xeasylabel:dict:update:pub:sub", "字典项更新"),
    FUNCTION_SETTING_UPDATE_PUB_SUB("xeasylabel:functionsetting:update:pub:sub", "功能展示更新"),
    PRINTER_DRIVER_UPDATE_PUB_SUB("xeasylabel:printerdriver:update:pub:sub", "固件升级更新"),;
    private final String key;
    private final String desc;
    private static final LinkedHashMap<String, String> map;

    private SystemPublishEnums(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }
    
    

    static {
        map = new LinkedHashMap<>();  
        for (SystemPublishEnums systemCacheEnums : SystemPublishEnums.values()) {
            map.put(systemCacheEnums.getKey(), systemCacheEnums.getDesc());
        }
    }

    public String getKey() {
        return key;
    }

    public String getDesc() {
        return desc;
    }
    
    public static LinkedHashMap<String, String> getMap() {
        return map;
    }
}
