package net.snaptag.system.business.enums;

import net.snaptag.system.sadais.core.common.enums.ICacheEnums;

import java.util.LinkedHashMap;

public enum MsgCenterCacheEnums implements ICacheEnums {
    MSG_CENTER_BY_ID("sysmsg:msgc:by:id:", "消息数据记录"), 
    MSG_CENTER_ID_LIST("sysmsg:msg:by:list:", "消息数据ID集合");
    private final String                               key;
    private final String                               desc;
    private static final LinkedHashMap<String, String> map;
    static {
        map = new LinkedHashMap<String, String>();
        for (MsgCenterCacheEnums msgCenterCacheEnums : MsgCenterCacheEnums.values()) {
            map.put(msgCenterCacheEnums.getKey(), msgCenterCacheEnums.getDesc());
        }
    }

    private MsgCenterCacheEnums(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public String getKey() {
        return key;
    }

    public String getDesc() {
        return desc;
    }

    public static LinkedHashMap<String, String> getMap() {
        return map;
    }
}
