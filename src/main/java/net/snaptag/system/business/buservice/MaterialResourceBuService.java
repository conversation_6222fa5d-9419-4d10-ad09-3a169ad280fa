package net.snaptag.system.business.buservice;

import com.baomidou.mybatisplus.core.metadata.IPage;
import net.snaptag.system.business.dao.MaterialResourceDao;
import net.snaptag.system.business.entity.MaterialResource;
import net.snaptag.system.common.DataConst;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class MaterialResourceBuService {
    @Autowired
    private MaterialResourceDao materialResourceDao;

    /**
     * 保存
     * 
     * @param materialResource
     */
    public void save(MaterialResource materialResource) {
        materialResourceDao.saveOrUpdate(materialResource);
    }

    /**
     * 获取素材库资源列表
     * 
     * @return 素材库资源列表
     * @throws Exception
     */
    public List<MaterialResource> findMaterialResourceList() {
        return materialResourceDao.findMaterialResourceList();
    }

    /**
     * 获取素材列表
     * 
     * @param id
     *            素材ID
     * @param pageNo
     *            当前页码
     * @param pageSize
     *            每页大小
     * @return
     */
    public IPage<MaterialResource> findResourcePage(String id, String label, int pageNo, int pageSize, String localeCode) {
        return materialResourceDao.findResourcePage(id, label, pageNo, pageSize, localeCode);
    }

    /**
     * 根据记录ID删除素材
     * 
     * @param id
     */
    public void delById(String id) {
        materialResourceDao.removeById(id);
    }

    /**
     * 根据记录ID获取素材
     * 
     * @param id
     */
    public MaterialResource getById(String id) {
        return materialResourceDao.getById(id);
    }
}
