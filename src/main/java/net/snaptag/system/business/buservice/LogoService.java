package net.snaptag.system.business.buservice;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import net.snaptag.system.business.entity.Logo;
import net.snaptag.system.business.entity.LogoChildKind;
import net.snaptag.system.business.entity.LogoKind;
import net.snaptag.system.business.mapper.LogoChildKindMapper;
import net.snaptag.system.business.mapper.LogoKindMapper;
import net.snaptag.system.business.mapper.LogoMapper;
import net.snaptag.system.business.utils.LanguageUtils;
import net.snaptag.system.business.vo.LogoKindVO;
import net.snaptag.system.business.vo.LogoPageVO;
import net.snaptag.system.business.vo.LogoVO;
import net.snaptag.system.sadais.core.common.exception.ServiceException;
import net.snaptag.system.sadais.util.core.ToolsKit;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Locale;

/**
 * Author：Chenjy
 * Date:2025/8/21
 * Description:
 */
@Service
public class LogoService {
    public static final LogoService me = new LogoService();

    @Autowired
    private LogoMapper logoMapper;
    @Autowired
    private LogoKindMapper logoKindMapper;
    @Autowired
    private LogoChildKindMapper logoChildKindMapper;

    /*
     * 语言类型
     */
    private static final int LANGUAGE_SYSTEM = 0;
    private static final int LANGUAGE_CHINESE = 1;
    private static final int LANGUAGE_ENGLISH = 2;
    private static final int LANGUAGE_TRADITION = 3;
    private static final int LANGUAGE_KOREAN = 4;

    public List<LogoVO> getLogoList(Locale language) {
//        if (language != LANGUAGE_CHINESE && language != LANGUAGE_ENGLISH && language != LANGUAGE_TRADITION && language != LANGUAGE_KOREAN) {
//            throw new ServiceException("语言类型传入有误！");
//        }
        QueryWrapper<Logo> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("version", 1);
        List<Logo> list = logoMapper.selectList(queryWrapper);
        List<LogoVO> logoVOList = new ArrayList<>();
        for (Logo logo : list) {
            LogoKind kind = logoKindMapper.selectById(logo.getLogoKindId());
            if (kind == null) {
                continue;
            }
            LogoChildKind childKind = logoChildKindMapper.selectById(logo.getLogoChildKindId());
            if (childKind == null) {
                continue;
            }

            String groupName = "";
            if (LanguageUtils.LOCALE_ZH_TW.equalsIgnoreCase(language.toString())) {
                groupName = kind.getTraditionalName() + "/" + childKind.getTraditionalName();
            } else if (LanguageUtils.LOCALE_EN.equalsIgnoreCase(language.toString())) {
                groupName = kind.getEnglishName() + "/" + childKind.getEnglishName();
            } else if (LanguageUtils.LOCALE_KOREA.equalsIgnoreCase(language.toString())) {
                groupName = kind.getKoreanName() + "/" + childKind.getKoreanName();
            } else {
                groupName = kind.getLogoKindName() + "/" + childKind.getLogoChildKindName();
            }

            LogoVO logoVO = new LogoVO();
            BeanUtils.copyProperties(logo, logoVO);
            logoVO.setGroupName(groupName);
            logoVOList.add(logoVO);
        }
        return logoVOList;
    }


    /**
     * 获取logo主类别列表
     *
     * @param language 显示的语言类型 depressed
     * @return
     */
    public List<LogoKindVO> getLogoKindList(Locale language) {
        String queryKindName = LanguageUtils.getColumnByLocale(language, "englishName");

        List<LogoKind> list = logoKindMapper.findMainLogoKind(queryKindName);
        return ToolsKit.copyList(list, LogoKindVO.class);
    }

    private Boolean isColumnExistInLogoKind(String columnName) {
        List<LogoKind> list = logoKindMapper.isColumnExistInLogoKind(columnName);
        return !list.isEmpty();
    }

    /**
     * @param pageNumber 分页
     * @param pageSize   分页
     * @param kindId     主类别
     * @param language
     * @return
     */
    public IPage<LogoPageVO> getLogoPage(int pageNumber, int pageSize, String kindId, Locale language) {
        try {
            return logoMapper.selectLogoVOPage(new Page<>(pageNumber, pageSize), getKindNameFieldByLang(language), kindId);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 根据语言类型获取对应的字段名
     */
    private String getKindNameFieldByLang(Locale locale) {
        if (locale == null) {
            return "k.logoKindName";
        }
        switch (locale.toString()) {
            case LanguageUtils.LOCALE_EN:
                return "k.englishName";
            case LanguageUtils.LOCALE_ZH_TW:
                return "k.traditionalName";
            case LanguageUtils.LOCALE_KOREA:
                return "k.koreanName";
            case LanguageUtils.LOCALE_RUSSIAN:
                return "k.russianName";
            default:
                return "k.logoKindName";
        }
    }
}
