package net.snaptag.system.business.buservice;

import com.alibaba.fastjson.JSONObject;
import net.snaptag.system.sadais.core.common.exception.ServiceException;
import net.snaptag.system.sadais.http.core.response.HttpResponse;
import net.snaptag.system.sadais.http.kit.HttpKit;
import org.springframework.stereotype.Service;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;

/**
 * <AUTHOR>
 * @date ：Created in 2024/12/10 09:19
 * @description：
 * @modified By：
 * @version: $
 */
@Service
public class File2PdfBuService {
    private static String jumeizhishu_url_word2pdf = "https://jmwjzhwjzh.market.alicloudapi.com/file-convert/word2pdf";
    private static String jumeizhishu_url_result = "https://jmwjzhwjzh.market.alicloudapi.com/file-convert/result";
    private static String jumeizhishu_appcode = "dbe6bf5fd2bc4607b5ba75649a27a53c";
    public Object word2pdf(String fileUrl) {

        try {
            fileUrl = URLDecoder.decode(fileUrl.replace("m.snaptag.top", "xplable.oss-cn-hangzhou.aliyuncs.com"), "UTF-8");
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        System.out.println("请求文档地址：" + fileUrl);
        HttpResponse httpResponse = HttpKit.http().header("Authorization", "APPCODE " + jumeizhishu_appcode).url(jumeizhishu_url_word2pdf).param("fileUrl", fileUrl).post();
        String result = httpResponse.asString();
        System.out.println("请求转换返回结果：" + result);
        JSONObject jsonObject = JSONObject.parseObject(result);
        if (jsonObject.getBoolean("success")) {
            return jsonObject.get("data");
        } else {
            throw new ServiceException(jsonObject.getString("msg"));
        }
    }

    public Object result(String convertTaskId) {
        HttpResponse httpResponse = HttpKit.http().header("Authorization", "APPCODE " + jumeizhishu_appcode).url(jumeizhishu_url_result).param("convertTaskId", convertTaskId).post();
        String result = httpResponse.asString();
        JSONObject jsonObject = JSONObject.parseObject(result);
        if (jsonObject.getBoolean("success")) {
            return jsonObject.get("data");
        } else {
            System.out.println(result);
            throw new ServiceException(jsonObject.getString("msg"));
        }
    }

    public static void main(String[] args) {
        File2PdfBuService file2PdfBuService = new File2PdfBuService();
        JSONObject xx= (JSONObject)file2PdfBuService.word2pdf("https://start-oss.oss-cn-hangzhou.aliyuncs.com/tmp/wordsList.xls");
        System.out.println(xx);
        String convertTaskId = xx.getString("convertTaskId");
        System.out.println(convertTaskId);
        System.out.println(file2PdfBuService.result(convertTaskId));
    }
}
