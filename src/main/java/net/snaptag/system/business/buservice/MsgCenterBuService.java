package net.snaptag.system.business.buservice;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import net.snaptag.system.account.buservice.UserAccountAndInfoBuService;
import net.snaptag.system.account.buservice.UserAccountBuService;
import net.snaptag.system.account.buservice.UserInfoBuService;
import net.snaptag.system.account.dto.UserLoginDto;
import net.snaptag.system.business.cache.MsgCenterCacheService;
import net.snaptag.system.business.dao.MsgCenterDao;
import net.snaptag.system.business.dto.MsgCenterDto;
import net.snaptag.system.business.dto.MsgCenterParamDto;
import net.snaptag.system.business.dto.SendMsgDto;
import net.snaptag.system.business.entity.MsgCenter;
import net.snaptag.system.business.entity.MsgColumn;
import net.snaptag.system.business.enums.MsgColumnTypeEnums;
import net.snaptag.system.business.vo.ColumnVo;
import net.snaptag.system.sadais.core.common.core.ToolsConst;
import net.snaptag.system.sadais.core.common.enums.ExceptionEnums;
import net.snaptag.system.sadais.core.common.exception.ServiceException;
import net.snaptag.system.sadais.util.core.ToolsKit;
import net.snaptag.system.sadais.web.config.CommonProperties;
import net.snaptag.system.sadais.web.core.I18nUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 消息中心服务类
 * 
 * <AUTHOR> 2018年7月10日
 */
@Service
public class MsgCenterBuService {
    @Autowired
    private MsgCenterDao msgCenterDao;
    @Autowired
    private MsgCenterCacheService msgCenterCacheService;
    @Autowired
    private UserAccountBuService userAccountService;
    @Autowired
    private UserInfoBuService userInfoBuService;
    @Autowired
    private CommonProperties commonProperties;
    @Autowired
    private MsgColumnBuService    msgColumnBuService;
    @Autowired
    private SystemMsgBuService    systemMsgBuService;
    @Autowired
    private UserAccountAndInfoBuService userAccountAndInfoBuServiced;
    @Autowired
    private I18nUtils i18nUtils;

    private static SimpleDateFormat sdfTime = new SimpleDateFormat("HH:mm");
    private static SimpleDateFormat sdfMonthDay = new SimpleDateFormat("MM-dd HH:mm");
    private static SimpleDateFormat sdfDate = new SimpleDateFormat("yyyy-MM-dd HH:mm");
    private static final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 将JSON字符串转换为Map<Integer, ColumnVo>
     */
    private Map<Integer, ColumnVo> parseColumnMapFromJson(String columnMapJson) {
        if (columnMapJson == null || columnMapJson.trim().isEmpty()) {
            return new HashMap<>();
        }
        try {
            return objectMapper.readValue(columnMapJson, new TypeReference<Map<Integer, ColumnVo>>() {});
        } catch (Exception e) {
            // 如果解析失败，返回空Map
            return new HashMap<>();
        }
    }

    /**
     * 将Map<Integer, ColumnVo>转换为JSON字符串
     */
    private String columnMapToJson(Map<Integer, ColumnVo> columnMap) {
        if (columnMap == null || columnMap.isEmpty()) {
            return "{}";
        }
        try {
            return objectMapper.writeValueAsString(columnMap);
        } catch (Exception e) {
            return "{}";
        }
    }

    public void save(MsgCenter msgCenter) {
        msgCenterDao.saveOrUpdate(msgCenter);
        msgCenterCacheService.saveMsgCenter(msgCenter);
    }

    /**
     * 获取消息ID列表
     * 
     * @param userId
     *            用户ID
     * @param msgType
     *            消息类型
     * @param page
     *            当前页
     * @param pageSize
     *            每页大小
     * @param lastId
     *            最后一条数据ID
     * @return 消息ID列表
     */
    public List<String> getMsgCenterIdsList(String userId, int msgType, int page, int pageSize, String lastId) {
        List<String> idsList = null;
        List<MsgCenter> msgCenterList = msgCenterDao.findMsgCenterList(userId, msgType, page, pageSize, lastId);
        if (ToolsKit.isNotEmpty(msgCenterList)) {
            idsList = new ArrayList<String>();
            for (MsgCenter msgCenter : msgCenterList) {
                msgCenterCacheService.addMsgCenterIdToList(userId, msgType, msgCenter.getId(), msgCenter.getCreatetime().getTime());
                idsList.add(msgCenter.getId());
            }
        }
//        if (!msgCenterCacheService.existsList(userId, msgType)) {
//            List<MsgCenter> msgCenterList = msgCenterDao.findMsgCenterList(userId, msgType, page, pageSize, lastId);
//            if (ToolsKit.isNotEmpty(msgCenterList)) {
//                idsList = new ArrayList<String>();
//                for (MsgCenter msgCenter : msgCenterList) {
//                    msgCenterCacheService.addMsgCenterIdToList(userId, msgType, msgCenter.getId(), msgCenter.getCreatetime().getTime());
//                    idsList.add(msgCenter.getId());
//                }
//            }
//        } else {
//            Long rank = null;
//            if (ToolsKit.isNotEmpty(lastId)) {
//                rank = msgCenterCacheService.zrevrank(userId, msgType, lastId);
//            }
//            int start = 0;
//            int end = 0;
//            if (ToolsKit.isNotEmpty(rank)) {
//                start = Integer.parseInt(String.valueOf(rank)) + 1;
//                end = start + pageSize;
//            } else {
//                start = page * pageSize;
//                end = page * pageSize + pageSize;
//            }
//            idsList = msgCenterCacheService.getMsgCenterIdsList(userId, msgType, start, end);
//            if (ToolsKit.isEmpty(idsList)) {
//                List<MsgCenter> msgCenterList = msgCenterDao.findMsgCenterList(userId, msgType, page, pageSize, lastId);
//                if (ToolsKit.isNotEmpty(msgCenterList)) {
//                    idsList = new ArrayList<String>();
//                    for (MsgCenter msgCenter : msgCenterList) {
//                        msgCenterCacheService.addMsgCenterIdToList(userId, msgType, msgCenter.getId(), msgCenter.getCreatetime().getTime());
//                        idsList.add(msgCenter.getId());
//                    }
//                }
//            }
//        }
        return idsList;
    }

    /**
     * 获取消息列表
     *
     * @param pageNo
     *            当前页
     * @param pageSize
     *            每页大小
     * @return 推荐动态列表
     */
    public List<MsgCenterDto> getMsgCenterDtoList(String userId, String msgType, int pageNo, int pageSize, String lastId, Locale locale, String version)
            throws ServiceException {
        if (ToolsKit.isEmpty(userId)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("用户ID不能为空");
        }
        if (ToolsKit.isEmpty(msgType)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("消息类型不能为空");
        }
        List<MsgCenterDto> dtoList = new ArrayList<MsgCenterDto>();
        try {
            if (pageNo > 0) {
                pageNo--;
            }
            MsgColumn msgColumn = msgColumnBuService.getMsgColumn(userId);
            Map<Integer, ColumnVo> columnMap = parseColumnMapFromJson(msgColumn.getColumnMap());
            msgColumnBuService.resetCount(userId, Integer.parseInt(msgType));// 消除栏目红点
            boolean isUpdate = false;
            if (Integer.parseInt(msgType) == MsgColumnTypeEnums.SYSTEM.getType()) {
                isUpdate = systemMsgBuService.getSystemMsgList(userId, pageNo, pageSize, dtoList, columnMap, isUpdate, version);
            } else if (Integer.parseInt(msgType) == MsgColumnTypeEnums.FRIENDS.getType()) {
                isUpdate = fillFriendsApplyList(userId, msgType, pageNo, pageSize, lastId, dtoList, columnMap, isUpdate);
            } else {
                isUpdate = fillMsgCenterDtoList(userId, msgType, pageNo, pageSize, lastId, dtoList, columnMap, isUpdate, locale);
            }
            if (isUpdate) {// 记录最后一次阅读时间
                ColumnVo vo = columnMap.get(Integer.parseInt(msgType));
                vo.setLastDate(new Date());
                vo.setCount(0);
                columnMap.put(Integer.parseInt(msgType), vo);
                msgColumn.setColumnMap(columnMapToJson(columnMap));
                msgColumnBuService.save(msgColumn);
            }
            if (Integer.parseInt(msgType) == MsgColumnTypeEnums.COMMENT.getType()
                    || Integer.parseInt(msgType) == MsgColumnTypeEnums.GIVELIKE.getType()){
                ColumnVo temp3 = columnMap.get(MsgColumnTypeEnums.COMMUNITY.getType());
                if (temp3.getCount()>0){
                    temp3.setLastDate(new Date());
                    temp3.setCount(0);
                    columnMap.put(MsgColumnTypeEnums.COMMUNITY.getType(), temp3);
                    msgColumn.setColumnMap(columnMapToJson(columnMap));
                    msgColumnBuService.save(msgColumn);
                }
            }

            // 根据类型，标志所有消息为已读
            setReadByMsgType(userId, msgType);

        } catch (Exception e) {
            e.printStackTrace();
        }
        return dtoList;
    }

    private void setReadByMsgType(String userId, String msgType) {
        // 获取列表的同时，直接赋值已读
        ToolsKit.Thread.execute(new Runnable() {
            List<MsgCenter> list = msgCenterDao.findUnReadMsgCenterList(userId, Integer.parseInt(msgType));
            @Override
            public void run() {
                if (ToolsKit.isNotEmpty(list)) {
                    for (MsgCenter msgCenter: list) {
                        MsgCenter tempMsgCenter = getMsgCenterById(msgCenter.getId());
                        tempMsgCenter.setRead(ToolsConst.STATUS_1);
                        save(tempMsgCenter);
                    }
                }
            }
        });
    }

    /**
     * 填充消息中心dto-好友
     * 
     * @param userId
     * @param msgType
     * @param pageNo
     * @param pageSize
     * @param lastId
     * @param dtoList
     * @param columnMap
     * @param isUpdate
     * @return
     */
    private boolean fillFriendsApplyList(String userId, String msgType, int pageNo, int pageSize, String lastId, List<MsgCenterDto> dtoList,
            Map<Integer, ColumnVo> columnMap, boolean isUpdate) {
//        List<FriendsApplyDto> applyList = friendsApplyService.getFriendsApplyList(userId, pageNo, pageSize, lastId);
//        if (ToolsKit.isNotEmpty(applyList)) {
//            for (FriendsApplyDto applyDto : applyList) {
//                MsgCenterDto dto = new MsgCenterDto();
//                dto.setId(applyDto.getId());
//                dto.setMsgType(MsgColumnTypeEnums.FRIENDS.getType());
//                dto.setMsgSubType(MsgTypeEnums.NO_JUMP.getType());
//                dto.setMsgTime(applyDto.getApplyDate());
//                dto.setMsgContent(applyDto.getContent());
//                dto.setSenderUserId(applyDto.getUserId());
//                dto.setSenderNickName(applyDto.getNickName());
//                dto.setSenderSex(applyDto.getSex());
//                dto.setSenderPic(applyDto.getPic());
//                if (applyDto.getApplyDate().getTime() > columnMap.get(Integer.parseInt(msgType)).getLastDate().getTime()) {// 消息时间大于最后阅读时间，说明未看过
//                    dto.setIsRead(ToolsConst.STATUS_1);
//                    isUpdate = true;
//                }
//
//                UserLoginDto userLoginDto = userAccountService.getUserInfo(applyDto.getUserId());
//                if (ToolsKit.isNotEmpty(userLoginDto) && ToolsKit.isNotEmpty(userLoginDto.getUserInfoDto())){
//                    dto.setIsOfficial(userLoginDto.getUserInfoDto().getIsOfficial());
//                    dto.setUserTitleObj(userLoginDto.getUserInfoDto().getUserTitleObj());
//                }
//
//                MsgCenterParamDto param = new MsgCenterParamDto();
//                param.setStatus(applyDto.getApplyStatus());
//                dto.setParam(param);
//                dtoList.add(dto);
//            }
//        }
        return isUpdate;
    }

    /**
     * 填充消息中心dto-社区
     * 
     * @param userId
     * @param msgType
     * @param page
     * @param pageSize
     * @param lastId
     * @param dtoList
     * @param columnMap
     * @param isUpdate
     * @return
     */
    private boolean fillMsgCenterDtoList(String userId, String msgType, int page, int pageSize, String lastId, List<MsgCenterDto> dtoList,
            Map<Integer, ColumnVo> columnMap, boolean isUpdate, Locale locale) {
       List<String> idsList = this.getMsgCenterIdsList(userId, Integer.parseInt(msgType), page, pageSize, lastId);
        if (ToolsKit.isNotEmpty(idsList)) {
            String today = ToolsKit.Date.formatDate(new Date());
            Calendar ca = Calendar.getInstance();
            int thisYear = ca.get(Calendar.YEAR);

            for (String id : idsList) {
                MsgCenter msgCenter = this.getMsgCenterById(id);

                if (ToolsKit.isNotEmpty(msgCenter)) {
                    MsgCenterDto dto = new MsgCenterDto();
                    ToolsKit.Bean.copyProperties(msgCenter, dto);
//                    if (("喜欢了你素材").equals(dto.getMsgContent())) {
//                        dto.setMsgContent(i18nUtils.getKey(Constant.MSG_LIKE_KEY, locale));
//                    }
//                    if (("点赞了你动态").equals(dto.getMsgContent())){
//                        dto.setMsgContent(i18nUtils.getKey("msg_givelikemessage2", locale));
//                    }
                    dto.setId(msgCenter.getId());

                    if (msgCenter.getPic() != null) {
                        dto.setPic(ToolsKit.URL.getUrlByServer(commonProperties.getFileDomain(), msgCenter.getPic()));
                    }
                    UserLoginDto userLoginDto = userAccountAndInfoBuServiced.getLoginInfo(msgCenter.getSenderUserId());
                    if (ToolsKit.isNotEmpty(userLoginDto) && ToolsKit.isNotEmpty(userLoginDto.getUserInfoDto())) {
                        dto.setSenderUserId(userLoginDto.getUserInfoDto().getUserId());
                        dto.setSenderNickName(userLoginDto.getUserInfoDto().getNickName());
                        dto.setSenderSex(userLoginDto.getUserInfoDto().getSex());
                        dto.setSenderPic(ToolsKit.URL.getUrlByServer(commonProperties.getFileDomain(), userLoginDto.getUserInfoDto().getUserPic()));
                        dto.setIsOfficial(userLoginDto.getUserInfoDto().getIsOfficial());
                        dto.setUserTitleObj(userLoginDto.getUserInfoDto().getUserTitleObj());
                    }
                    MsgCenterParamDto msgCenterParamDto = dto.getParam();
                    if (ToolsKit.isNotEmpty(msgCenterParamDto)) {
                        msgCenterParamDto.setPic(ToolsKit.URL.getUrlByServer(commonProperties.getFileDomain(), msgCenterParamDto.getPic()));
                        dto.setParam(msgCenterParamDto);
                    }
                    if (columnMap.containsKey(Integer.parseInt(msgType)) && (msgCenter.getMsgTime().getTime() > columnMap.get(Integer.parseInt(msgType)).getLastDate().getTime())) {// 消息时间大于最后阅读时间，说明未看过
                        dto.setIsRead(ToolsConst.STATUS_1);
                        isUpdate = true;
                    }

                    int year = ca.get(Calendar.YEAR);

                    String currDate = ToolsKit.Date.formatDate(msgCenter.getMsgTime());
                    if (currDate.equals(today)) {
                        // 当天 HH:mm
                        dto.setMsgFmtTime(sdfTime.format(msgCenter.getMsgTime()));
                    } else if (year == thisYear) {
                        // 同一年非当前 MM-dd
                        dto.setMsgFmtTime(sdfMonthDay.format(msgCenter.getMsgTime()));
                    } else {
                        // 非同年 yyyy-MM-dd
                        dto.setMsgFmtTime(currDate);
                    }

                    // 反馈回复消息
                    ca.setTime(msgCenter.getMsgTime());

                    // 标识已读状态
                    dto.setIsRead(ToolsConst.STATUS_0);
                    if (msgCenter.getRead() == ToolsConst.STATUS_1) {
                        dto.setIsRead(ToolsConst.STATUS_1);
                    }

                    if (msgCenter.getMsgType() == MsgColumnTypeEnums.FEEDBACK.getType()) {
                        if (msgCenter.getMsgContent().contains("\n\n")){
                            int begin = msgCenter.getMsgContent().lastIndexOf("\n\n")+2;
                            int end = msgCenter.getMsgContent().length();
                            dto.setMsgContent(msgCenter.getMsgContent().substring(begin, end));
                        } else if (msgCenter.getMsgContent().contains("\n")){
                            dto.setMsgContent(msgCenter.getMsgContent().substring(0, msgCenter.getMsgContent().indexOf("\n")));
                        }
                    }

                    // 获取列表的同时，直接赋值已读
                    ToolsKit.Thread.execute(new Runnable() {
                        @Override
                        public void run() {
                            msgCenter.setRead(ToolsConst.STATUS_1);
                            save(msgCenter);
                        }
                    });

                    dtoList.add(dto);
                }
            }
        }
        return isUpdate;
    }

    /**
     * 获取消息信息
     * 
     * @param id
     *            ID
     * @return 消息信息
     * @throws ServiceException
     */
    public MsgCenter getMsgCenterById(String id) throws ServiceException {
        if (ToolsKit.isEmpty(id)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("记录信息ID不能为空");
        }
        MsgCenter msgCenter = msgCenterCacheService.getMsgCenter(id);
        if (ToolsKit.isEmpty(msgCenter)) {
            try {
                msgCenter = msgCenterDao.getMsgCenterById(id);
            } catch (Exception e) {
                e.printStackTrace();
            }
            if (ToolsKit.isNotEmpty(msgCenter)) {
                msgCenterCacheService.saveMsgCenter(msgCenter);
            }
        }
        return msgCenter;
    }

    /**
     * 发送消息
     * 
     * @param sendMsgDto
     */
    public void sendMsg(SendMsgDto sendMsgDto) throws ServiceException {
        if (ToolsKit.isEmpty(sendMsgDto)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("消息信息不能为空");
        }
        if (ToolsKit.isEmpty(sendMsgDto.getSenderUserId())) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("发送人ID不能为空");
        }
        if (ToolsKit.isEmpty(sendMsgDto.getMsgContent())) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("消息内容不能为空");
        }
        MsgCenter msgCenter = new MsgCenter();
        ToolsKit.setIdEntityData(msgCenter, sendMsgDto.getSenderUserId());
        ToolsKit.Bean.copyProperties(sendMsgDto, msgCenter);
        msgCenter.setUserId(sendMsgDto.getReceiverUserId());
        msgCenter.setMsgTime(new Date());
        msgCenter.setRead(ToolsConst.STATUS_0);
        if (sendMsgDto.getPic() != null) {
            msgCenter.setPic(ToolsKit.URL.replaceUrl(commonProperties.getFileDomain(), sendMsgDto.getPic()));
        }
        this.save(msgCenter);
        msgCenterCacheService.addMsgCenterIdToList(sendMsgDto.getReceiverUserId(), sendMsgDto.getMsgType(), msgCenter.getId(),
                msgCenter.getCreatetime().getTime());
        msgColumnBuService.addCount(sendMsgDto.getReceiverUserId(), sendMsgDto.getMsgType(), 1);
    }

    public void delMsg(String id) {
        MsgCenter msgCenter = getMsgCenterById(id);
        if (msgCenter != null) {
            msgCenterCacheService.removeMsgCenterIdToList(msgCenter.getUserId(), msgCenter.getMsgType(), msgCenter.getId());
            msgCenterCacheService.removeMsgCenter(id);
            msgCenterDao.removeById(id);
        }
    }

    /**
     * 标识消息为已读状态
     * @param id
     */
    public void read(String id) {
        MsgCenter msgCenter = getMsgCenterById(id);
        if (msgCenter != null) {
            msgCenter.setRead(ToolsConst.STATUS_1);
            msgCenterDao.saveOrUpdate(msgCenter);
            msgCenterCacheService.saveMsgCenter(msgCenter);
        }
    }

    public int hasUnreadMsgCount(String userId) {
        List<MsgCenter> msgCenterList = msgCenterDao.findMsgCenterList(userId, MsgColumnTypeEnums.FEEDBACK.getType(), 0, 10000, null);
        if (ToolsKit.isNotEmpty(msgCenterList)) {
            for (MsgCenter msgCenter : msgCenterList) {
                MsgCenter msgCenterCache = this.getMsgCenterById(msgCenter.getId());
                if (msgCenterCache != null && msgCenterCache.getRead() == ToolsConst.STATUS_0) {
                    return ToolsConst.STATUS_1;
                }
            }
        }
        return ToolsConst.STATUS_0;
    }

    public int hasUnreadMsgCount(String userId, int type) {
        Long count = msgCenterDao.countMsgCenter(userId, type, ToolsConst.STATUS_0);
        return count.intValue();
    }

    public List<MsgCenter> getFeedbackMsgList(String userId){
        return msgCenterDao.findMsgCenterList(userId, MsgColumnTypeEnums.FEEDBACK.getType(), 0, 10000, null);
    }

    public List<MsgCenter> getMsgListByUserIdAndType(String userId, int type, int pageNo, int pageSize){
        return msgCenterDao.findMsgCenterList(userId, type, pageNo, pageSize, null);
    }

    public void delMsgByType(String userId, int type) {
        List<MsgCenter> list = getMsgListByUserIdAndType(userId, type, 0, 9999);
        if (ToolsKit.isNotEmpty(list)){
            for (MsgCenter msg: list) {
                delMsg(msg.getId());
            }
        }
    }
}
