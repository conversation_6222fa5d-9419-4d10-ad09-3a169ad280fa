package net.snaptag.system.business.buservice;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import net.snaptag.system.business.dao.WebPrintDao;
import net.snaptag.system.business.entity.WebPrint;
import net.snaptag.system.business.enums.WebPrintDefaultEnums;
import net.snaptag.system.business.vo.WebPagePrintVo;
import net.snaptag.system.business.vo.WebPrintGroupVo;
import net.snaptag.system.common.DataConst;
import net.snaptag.system.sadais.core.common.core.ToolsConst;
import net.snaptag.system.sadais.core.common.enums.ExceptionEnums;
import net.snaptag.system.sadais.core.common.exception.ServiceException;
import net.snaptag.system.sadais.util.core.ToolsKit;
import net.snaptag.system.sadais.web.core.I18nUtils;
import net.snaptag.system.sadais.web.utils.LocalTools;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.Locale;

/**
 * 网页打印业务服务类
 * 
 * <AUTHOR> 2019年11月05日
 * @restored 2025年01月30日 - 恢复被删除的文件
 */
@Service
public class WebPrintBuService {

    @Autowired
    private WebPrintDao webPrintDao;

    @Autowired
    private I18nUtils i18nUtils;

    private static final ObjectMapper objectMapper = new ObjectMapper();

    public static final int TRUE = 1;
    public static final int FALSE = 0;

    /**
     * 将JSON字符串转换为List<WebPagePrintVo>
     */
    private List<WebPagePrintVo> parsePageListJson(String pageListJson) {
        if (pageListJson == null || pageListJson.trim().isEmpty()) {
            return new ArrayList<>();
        }
        try {
            return objectMapper.readValue(pageListJson, new TypeReference<List<WebPagePrintVo>>() {});
        } catch (Exception e) {
            // 如果解析失败，返回空List
            return new ArrayList<>();
        }
    }

    /**
     * 将List<WebPagePrintVo>转换为JSON字符串
     */
    private String pageListToJson(List<WebPagePrintVo> pageList) {
        if (pageList == null || pageList.isEmpty()) {
            return null;
        }
        try {
            return objectMapper.writeValueAsString(pageList);
        } catch (Exception e) {
            // 如果转换失败，返回null
            return null;
        }
    }

    /**
     * 获取默认分组列表
     * @return
     */
    public List<WebPrintGroupVo> getDefaultGroupList() {
        List<WebPrint> webPrintList = webPrintDao.findDefaultGroupList();
        List<WebPrintGroupVo> result = new ArrayList<>();
        
        // 添加系统默认分组
        result.add(WebPrintDefaultEnums.getDefaultWebPrintGroup());
        
        // 转换数据库中的默认分组
        for (WebPrint webPrint : webPrintList) {
            WebPrintGroupVo groupVo = convertToGroupVo(webPrint);
            result.add(groupVo);
        }
        
        return result;
    }

    /**
     * 获取指定用户的分组列表
     * @param userId - 用户标识
     * @return
     */
    public List<WebPrintGroupVo> getUserGroupList(String userId) {
        if (ToolsKit.isEmpty(userId)) {
            throw new ServiceException("用户ID不能为空");
        }
        
        List<WebPrint> webPrintList = webPrintDao.findUserGroupList(userId);
        List<WebPrintGroupVo> result = new ArrayList<>();
        
        // 转换用户分组
        for (WebPrint webPrint : webPrintList) {
            WebPrintGroupVo groupVo = convertToGroupVo(webPrint);
            result.add(groupVo);
        }
        
        return result;
    }

    /**
     * 获取指定用户的分组
     * @param userId - 用户标识
     * @param groupId - 分组标识
     * @return
     */
    public WebPrintGroupVo getUserGroup(String userId, String groupId) {
        if (ToolsKit.isEmpty(userId)) {
            throw new ServiceException("用户ID不能为空");
        }
        if (ToolsKit.isEmpty(groupId)) {
            throw new ServiceException("分组ID不能为空");
        }
        
        WebPrint webPrint = webPrintDao.findUserGroup(userId, groupId);
        if (webPrint == null) {
            return null;
        }
        
        return convertToGroupVo(webPrint);
    }

    /**
     * 保存或更新网页打印分组
     * @param webPrintGroupVo - 分组信息
     * @param userId - 用户ID
     * @return
     */
    public WebPrintGroupVo saveOrUpdateGroup(WebPrintGroupVo webPrintGroupVo, String userId) {
        if (webPrintGroupVo == null) {
            throw new ServiceException("分组信息不能为空");
        }
        if (ToolsKit.isEmpty(userId)) {
            throw new ServiceException("用户ID不能为空");
        }
        
        WebPrint webPrint = new WebPrint();
        
        // 如果有ID，则为更新操作
        if (ToolsKit.isNotEmpty(webPrintGroupVo.getId())) {
            webPrint = webPrintDao.getById(webPrintGroupVo.getId());
            if (webPrint == null) {
                throw new ServiceException("分组不存在");
            }
            ToolsKit.setIdEntityData(webPrint, userId);
        } else {
            // 新增操作
            ToolsKit.setIdEntityData(webPrint, userId);
            webPrint.setUserId(userId);
        }
        
        // 设置基本信息
        webPrint.setName(webPrintGroupVo.getName());
        webPrint.setIsDefault(webPrintGroupVo.getIsDefault());
        webPrint.setFromDefault(0); // 用户自定义分组
        
        // 转换页面列表为JSON
        String pageListJson = pageListToJson(webPrintGroupVo.getPageList());
        webPrint.setPageList(pageListJson);
        
        // 保存到数据库
        webPrintDao.saveOrUpdate(webPrint);
        
        // 返回转换后的VO
        return convertToGroupVo(webPrint);
    }

    /**
     * 删除分组
     * @param groupId - 分组ID
     * @param userId - 用户ID
     * @return
     */
    public String deleteGroup(String groupId, String userId) {
        if (ToolsKit.isEmpty(groupId)) {
            throw new ServiceException("分组ID不能为空");
        }
        if (ToolsKit.isEmpty(userId)) {
            throw new ServiceException("用户ID不能为空");
        }
        
        WebPrint webPrint = webPrintDao.getById(groupId);
        if (webPrint == null) {
            throw new ServiceException("分组不存在");
        }
        
        // 检查权限：只能删除自己的分组
        if (!userId.equals(webPrint.getUserId())) {
            throw new ServiceException("无权限删除此分组");
        }
        
        webPrintDao.removeById(groupId);
        return "删除成功";
    }

    /**
     * 分页查询分组列表
     * @param pageNum - 页码
     * @param pageSize - 页大小
     * @param userId - 用户ID（可选）
     * @return
     */
    public Page<WebPrintGroupVo> findPage(int pageNum, int pageSize, String userId) {
        Page<WebPrint> page = new Page<>(pageNum, pageSize);
        Page<WebPrint> resultPage = webPrintDao.page(page);
        
        // 转换为VO
        Page<WebPrintGroupVo> voPage = new Page<>(pageNum, pageSize);
        voPage.setTotal(resultPage.getTotal());
        voPage.setPages(resultPage.getPages());
        
        List<WebPrintGroupVo> voList = new ArrayList<>();
        for (WebPrint webPrint : resultPage.getRecords()) {
            voList.add(convertToGroupVo(webPrint));
        }
        voPage.setRecords(voList);
        
        return voPage;
    }

    /**
     * 将WebPrint实体转换为WebPrintGroupVo
     */
    private WebPrintGroupVo convertToGroupVo(WebPrint webPrint) {
        WebPrintGroupVo groupVo = new WebPrintGroupVo();
        groupVo.setId(webPrint.getId());
        groupVo.setName(webPrint.getName());
        groupVo.setIsDefault(webPrint.getIsDefault());
        
        // 解析页面列表JSON
        List<WebPagePrintVo> pageList = parsePageListJson(webPrint.getPageList());
        groupVo.setPageList(pageList);
        
        return groupVo;
    }

    /**
     * 复制默认分组给用户
     * @param userId - 用户ID
     * @param defaultGroupId - 默认分组ID
     * @return
     */
    public WebPrintGroupVo copyDefaultGroup(String userId, String defaultGroupId) {
        if (ToolsKit.isEmpty(userId)) {
            throw new ServiceException("用户ID不能为空");
        }
        if (ToolsKit.isEmpty(defaultGroupId)) {
            throw new ServiceException("默认分组ID不能为空");
        }
        
        WebPrint defaultGroup = webPrintDao.getById(defaultGroupId);
        if (defaultGroup == null || defaultGroup.getIsDefault() != 1) {
            throw new ServiceException("默认分组不存在");
        }
        
        // 创建新的用户分组
        WebPrint userGroup = new WebPrint();
        ToolsKit.setIdEntityData(userGroup, userId);
        userGroup.setUserId(userId);
        userGroup.setName(defaultGroup.getName());
        userGroup.setIsDefault(0); // 用户分组不是默认分组
        userGroup.setFromDefault(1); // 来自默认分组
        userGroup.setPageList(defaultGroup.getPageList());
        
        webPrintDao.save(userGroup);
        
        return convertToGroupVo(userGroup);
    }

    /**
     * 添加网站
     * @param pagePrintVo
     * @param userId
     * @param locale
     * @return
     */
    public int addWebSite(WebPagePrintVo pagePrintVo, String userId, Locale locale) {
        // 5位随机数
        int codeId = (int)((Math.random() * 9 + 1) * 10000);
        String groupId = pagePrintVo.getGroupId();
        WebPrint printGroup = webPrintDao.findUserGroup(userId, groupId);
        if (printGroup != null) {

            if ("我的收藏".equals(printGroup.getName())){
                if (checkIsExist(userId, pagePrintVo.getLinkUrl(), locale)){
                    String msg = i18nUtils.getKey(LocalTools.app_websites_tip, locale);
                    if (ToolsKit.isEmpty(msg)){
                        msg = "the current website has been collected, please do not repeat the collection";
                    }
                    throw new ServiceException(msg);
                }
            }

            WebPagePrintVo printVo = new WebPagePrintVo(codeId);
            printVo.setGroupId(groupId);
            printVo.setName(pagePrintVo.getName());
            printVo.setLinkUrl(pagePrintVo.getLinkUrl());
            printVo.setIconUrl(pagePrintVo.getIconUrl());
            printVo.setIsDefault(FALSE);

            // 解析现有页面列表
            List<WebPagePrintVo> pageList = parsePageListJson(printGroup.getPageList());
            pageList.add(printVo);

            // 转换回JSON并保存
            printGroup.setPageList(pageListToJson(pageList));
            webPrintDao.saveOrUpdate(printGroup);
        }
        return codeId;
    }

    /**
     * 删除网站
     * @param userId
     * @param groupId
     * @param siteId
     */
    public void delWebSite(String userId, String groupId, Integer siteId) {
        WebPrint printGroup = webPrintDao.findUserGroup(userId, groupId);
        if (printGroup != null) {
            List<WebPagePrintVo> pageList = parsePageListJson(printGroup.getPageList());
            int deleteIdx = -1;
            int idx = 0;
            for (WebPagePrintVo printVo : pageList) {
                if (printVo.getCodeId() == siteId) {
                    deleteIdx = idx;
                    break;
                }
                idx++;
            }
            if (deleteIdx >= 0) {
                pageList.remove(deleteIdx);
                printGroup.setPageList(pageListToJson(pageList));
                webPrintDao.saveOrUpdate(printGroup);
            }
        }
    }

    /**
     * 网站排序
     * @param userId
     * @param groupId
     * @param siteIdList
     */
    public void sortWebSites(String userId, String groupId, String siteIdList) {
        WebPrint printGroup = webPrintDao.findUserGroup(userId, groupId);
        if (printGroup != null && StringUtils.isNotBlank(siteIdList)) {

            List<WebPagePrintVo> printVoList = new ArrayList<>();
            String[] siteIds = siteIdList.trim().split(",");
            if (siteIds != null && siteIds.length > 0) {

                List<WebPagePrintVo> pageList = parsePageListJson(printGroup.getPageList());
                Map<String, WebPagePrintVo> webPageMap = new HashMap<>();
                for (WebPagePrintVo printVo : pageList) {
                    webPageMap.put(printVo.getCodeId() + "", printVo);
                }

                // 按排序删除取出
                for (String siteId : siteIds) {
                    if (StringUtils.isBlank(siteId)) {
                        continue;
                    }

                    WebPagePrintVo pagePrintVo = webPageMap.remove(siteId);
                    if (pagePrintVo != null) {
                        printVoList.add(pagePrintVo);
                    }
                }

                // 不指定排序的排在最后
                for (WebPagePrintVo pagePrintVo : webPageMap.values()) {
                    printVoList.add(pagePrintVo);
                }

                // 保存
                printGroup.setPageList(pageListToJson(printVoList));
                webPrintDao.saveOrUpdate(printGroup);
            }
        }
    }

    /**
     * 检查网站是否已存在
     */
    private boolean checkIsExist(String userId, String link, Locale locale){
        if (ToolsKit.isEmpty(userId) || ToolsKit.isEmpty(link) ){
            throw new ServiceException("连接地址为空");
        }

        List<WebPrintGroupVo> groupVoList = getWebPrintListA4(userId, false, locale);
        for (WebPrintGroupVo groupVo : groupVoList) {
            if (ToolsKit.isNotEmpty(groupVo.getPageList())){
                for (WebPagePrintVo vo: groupVo.getPageList()) {
                    if (link.equals(vo.getLinkUrl())){
                        return true;
                    }
                }
            }
        }

        return false;
    }

    /**
     * 获取网页打印列表（A4版本）
     */
    public List<WebPrintGroupVo> getWebPrintListA4(String userId, Boolean overseas, Locale locale) {
        String nameHots = i18nUtils.getKey(LocalTools.app_websites_hots, locale);
        String nameFav = i18nUtils.getKey(LocalTools.app_websites_favorite, locale);
        if (ToolsKit.isEmpty(nameHots)){
            nameHots = "hot";
        }
        if (ToolsKit.isEmpty(nameFav)){
            nameFav = "favorite";
        }
        List<WebPrintGroupVo> printGroupVos = new ArrayList<>();

        // 添加默认的热门网站分组
        WebPrintGroupVo defaultGroup = WebPrintDefaultEnums.getDefaultWebPrintGroup();
        defaultGroup.setName(nameHots);
        printGroupVos.add(defaultGroup);

        // 如果用户已登录，添加用户的收藏分组
        if (ToolsKit.isNotEmpty(userId)){
            List<WebPrint> printGroups = webPrintDao.findUserGroupList(userId);
            if (ToolsKit.isNotEmpty(printGroups)){
                for (WebPrint printGroup:printGroups) {
                    if ("我的收藏".equals(printGroup.getName())){
                        WebPrintGroupVo printGroupVo = new WebPrintGroupVo();
                        printGroupVo.setId(printGroup.getId());
                        printGroupVo.setName(nameFav);
                        printGroupVo.setIsDefault(FALSE);
                        printGroupVo.setPageList(parsePageListJson(printGroup.getPageList()));
                        printGroupVos.add(printGroupVo);
                        break;
                    }
                }

                // 如果没有找到"我的收藏"分组，创建一个
                if (printGroupVos.size() < 2){
                    Map<String, String> newGroup = addGroup("我的收藏", userId);
                    WebPrintGroupVo userWebPrintVo = new WebPrintGroupVo();
                    userWebPrintVo.setId(newGroup.get("id"));
                    userWebPrintVo.setName(nameFav);
                    userWebPrintVo.setIsDefault(FALSE);
                    userWebPrintVo.setPageList(new ArrayList<>());
                    printGroupVos.add(userWebPrintVo);
                }
            } else {
                // 创建默认的"我的收藏"分组
                Map<String, String> newGroup = addGroup("我的收藏", userId);
                WebPrintGroupVo userWebPrintVo = new WebPrintGroupVo();
                userWebPrintVo.setId(newGroup.get("id"));
                userWebPrintVo.setName(nameFav);
                userWebPrintVo.setIsDefault(FALSE);
                userWebPrintVo.setPageList(new ArrayList<>());
                printGroupVos.add(userWebPrintVo);
            }
        } else {
            // 用户未登录时，添加空的收藏分组
            WebPrintGroupVo printGroupVo = new WebPrintGroupVo();
            printGroupVo.setId("");
            printGroupVo.setName(nameFav);
            printGroupVo.setIsDefault(FALSE);
            printGroupVo.setPageList(new ArrayList<>());
            printGroupVos.add(printGroupVo);
        }

        return printGroupVos;
    }

    /**
     * 获取指定用户所有分组和网站（完整版本）
     * @param userId
     * @param overseas
     * @param locale
     * @return
     */
    public List<WebPrintGroupVo> getWebPrintList(String userId, Boolean overseas, Locale locale) {
        List<WebPrintGroupVo> printGroupVos = new ArrayList<>();
        if (StringUtils.isNotBlank(userId)) {
            // 用户已登录
            Set<String> defGroupNames = new HashSet<>();
            List<WebPrint> defPrintGroups = webPrintDao.findDefaultGroupList();
            if (defPrintGroups != null) {
                for (WebPrint defGroup : defPrintGroups) {
                    defGroupNames.add(defGroup.getName());
                }
            }

            // --------- 处理海外版本，只保留"推荐网站"的 google ----------
            if (overseas) {
                defGroupNames.clear();
                defGroupNames.add("推荐网站");
                defPrintGroups = new ArrayList<>();
                WebPrint defGroup = new WebPrint();
                defGroup.setName("推荐网站");
                defGroup.setPageList("[]");
                defPrintGroups.add(defGroup);
                // 添加 google 网站
                int codeId = (int)((Math.random() * 9 + 1) * 10000);
                WebPagePrintVo printVo = new WebPagePrintVo(codeId);
                printVo.setGroupId(defGroup.getId());
                printVo.setName("google");
                printVo.setLinkUrl("https://www.google.com");
                printVo.setIconUrl("https://m.snaptag.top/api/img/gam/snapTag/common/webprint/web_ic_google.png");
                printVo.setIsDefault(TRUE);
                List<WebPagePrintVo> pageList = new ArrayList<>();
                pageList.add(printVo);
                defGroup.setPageList(pageListToJson(pageList));
            }
            // -------------------------------------------------------------

            List<WebPrint> printGroups = webPrintDao.findUserGroupList(userId);
            if (printGroups != null && printGroups.size() > 0) {
                for (WebPrint printGroup : printGroups) {
                    WebPrintGroupVo printGroupVo = new WebPrintGroupVo();
                    printGroupVo.setId(printGroup.getId());
                    printGroupVo.setName(printGroup.getName());

                    if (defGroupNames.contains(printGroup.getName()) && printGroup.getFromDefault() == TRUE) {
                        if (ToolsKit.isNotEmpty(printGroup.getName()) && "阅读".equals(printGroup.getName())){
                            printGroupVo.setIsDefault(FALSE);
                        }
                        printGroupVo.setIsDefault(TRUE);
                    } else {
                        printGroupVo.setIsDefault(FALSE);
                    }
                    printGroupVo.setPageList(parsePageListJson(printGroup.getPageList()));
                    printGroupVos.add(printGroupVo);
                }
                // 添加没有的默认项（系统自带的栏目不允许删除,以前没有的或需求新增的，添加上）
                for (WebPrint defPrintGroup: defPrintGroups) {
                    boolean needAdd = true;
                    for (WebPrint printGroup:printGroups) {
                        if (printGroup.getName().equals(defPrintGroup.getName())){
                            needAdd = false;
                        }
                    }

                    if (needAdd){
                        WebPrint printGroupAdd = new WebPrint();
                        ToolsKit.setIdEntityData(printGroupAdd, userId);
                        printGroupAdd.setUserId(userId);
                        printGroupAdd.setName(defPrintGroup.getName());
                        printGroupAdd.setIsDefault(FALSE);
                        printGroupAdd.setFromDefault(TRUE);
                        printGroupAdd.setPageList(defPrintGroup.getPageList());
                        webPrintDao.saveOrUpdate(printGroupAdd);

                        WebPrintGroupVo printGroupVo = new WebPrintGroupVo();
                        printGroupVo.setId(printGroupAdd.getId());
                        printGroupVo.setName(printGroupAdd.getName());
                        printGroupVo.setIsDefault(TRUE);
                        printGroupVo.setPageList(parsePageListJson(printGroupAdd.getPageList()));
                        printGroupVos.add(printGroupVo);
                    }
                }
            } else {
                // 第一次访问，给用户添加默认分组和网站
                if (defPrintGroups != null) {
                    printGroupVos.clear();
                    for (WebPrint defGroup:defPrintGroups) {
                        WebPrint printGroupAdd = new WebPrint();
                        ToolsKit.setIdEntityData(printGroupAdd, userId);
                        printGroupAdd.setUserId(userId);
                        printGroupAdd.setName(defGroup.getName());
                        printGroupAdd.setIsDefault(FALSE);
                        printGroupAdd.setFromDefault(TRUE);
                        printGroupAdd.setPageList(defGroup.getPageList());
                        webPrintDao.saveOrUpdate(printGroupAdd);

                        WebPrintGroupVo printGroupVo = new WebPrintGroupVo();
                        printGroupVo.setId(printGroupAdd.getId());
                        printGroupVo.setName(printGroupAdd.getName());
                        printGroupVo.setIsDefault(TRUE);
                        if (ToolsKit.isNotEmpty(printGroupAdd.getName()) && "阅读".equals(printGroupAdd.getName())){
                            printGroupVo.setIsDefault(FALSE);
                        }
                        printGroupVo.setPageList(parsePageListJson(printGroupAdd.getPageList()));
                        printGroupVos.add(printGroupVo);
                    }
                }
            }
        } else {
            // 用户未登录(只有海外版本)
            WebPrintGroupVo groupVo = new WebPrintGroupVo();
            groupVo.setName("推荐网站");
            groupVo.setId("0");
            groupVo.setIsDefault(TRUE);
            groupVo.setPageList(new ArrayList<>());
            printGroupVos.add(groupVo);

            // 添加 google 网站
            int codeId = (int)((Math.random() * 9 + 1) * 10000);
            WebPagePrintVo printVo = new WebPagePrintVo(codeId);
            printVo.setGroupId(groupVo.getId());
            printVo.setName("google");
            printVo.setLinkUrl("https://www.google.com");
            printVo.setIconUrl("https://m.snaptag.top/app/webprint/icons/print_ic_google.png");
            printVo.setIsDefault(TRUE);
            groupVo.getPageList().add(printVo);
        }

        // 处理海外版本，只保留"推荐网站"的 google
        if (overseas) {
            for (WebPrintGroupVo groupVo:printGroupVos) {
                if (groupVo.getName().equals("推荐网站")) {
                    groupVo.setName(i18nUtils.getKey(LocalTools.app_default_websites, locale));
                }
            }
        }

        return printGroupVos;
    }

    /**
     * 添加分组
     * @param groupName
     * @param userId
     * @return
     */
    public Map<String, String> addGroup(String groupName, String userId) {
        WebPrint printGroup = new WebPrint();
        ToolsKit.setIdEntityData(printGroup, userId);
        printGroup.setUserId(userId);
        printGroup.setName(groupName);
        printGroup.setIsDefault(FALSE);
        printGroup.setFromDefault(FALSE);
        printGroup.setPageList("[]");

        webPrintDao.saveOrUpdate(printGroup);
        Map<String, String> map = new HashMap<String, String>();
        map.put("id", printGroup.getId());
        return map;
    }

    /**
     * 删除分组（简单版本）
     * @param groupId
     */
    public void delGroup(String groupId) {
        webPrintDao.removeById(groupId);
    }

    /**
     * 初始化默认分组和网站
     * @return
     */
    public String initBasicData() {
        addGroupSite("推荐网站", "百度@https://www.baidu.com@https://share.yoyin.net/app/webprint/icons/print_ic_webbaidu.png,花瓣@https://huaban.com@https://share.yoyin.net/app/webprint/icons/print_ic_webhuaban.png,知乎@https://www.zhihu.com@https://share.yoyin.net/app/webprint/icons/print_ic_zhihu.png,网易新闻@https://news.163.com@https://share.yoyin.net/app/webprint/icons/print_ic_wangyi.png,搜狐新闻@http://news.sohu.com@https://share.yoyin.net/app/webprint/icons/print_ic_shouhu.png,今日头条@https://www.toutiao.com@https://share.yoyin.net/app/webprint/icons/print_ic_toutiao.png");
        addGroupSite("素材", "花瓣@https://huaban.com@https://share.yoyin.net/app/webprint/icons/print_ic_webhuaban.png,推糖@https://www.duitang.com@https://share.yoyin.net/app/webprint/icons/print_ic_webtangdui.png,快看漫画@https://www.kuaikanmanhua.com@https://share.yoyin.net/app/webprint/icons/print_ic_kuaikan.png,斗图@https://www.52doutu.cn@https://share.yoyin.net/app/webprint/icons/print_ic_doutu.png");
        addGroupSite("学习", "百度汉语@https://hanyu.baidu.com@https://share.yoyin.net/app/webprint/icons/print_ic_baiduhanyu.png,有道词典@http://www.youdao.com@https://share.yoyin.net/app/webprint/icons/print_ic_youdao.png,故事365@https://www.gushi365.com@https://share.yoyin.net/app/webprint/icons/print_ic_gushi.png,美食天下@https://home.meishichina.com@https://share.yoyin.net/app/webprint/icons/print_ic_meishi.png");
        addGroupSite("公众号", "微信公众号@https://share.yoyin.net/app/webprint/icons/print_ic_webwechat.png");
        addGroupSite("阅读", "纵横小说@https://share.yoyin.net/app/webprint/icons/print_ic_zongheng.png,17K@https://share.yoyin.net/app/webprint/icons/print_ic_17K.png,百度小说@https://share.yoyin.net/app/webprint/icons/print_ic_baidu.png");
        return "Ok";
    }

    /**
     * 初始化默认分组和网站（版本2）
     * @return
     */
    public String initBasicData2() {
        addGroupSite("热门网站", "百度@https://www.baidu.com@https://share.yoyin.net/app/webprint/icons/print_ic_webbaidu.png,花瓣@https://huaban.com@https://share.yoyin.net/app/webprint/icons/print_ic_webhuaban.png,知乎@https://www.zhihu.com@https://share.yoyin.net/app/webprint/icons/print_ic_zhihu.png,网易新闻@https://news.163.com@https://share.yoyin.net/app/webprint/icons/print_ic_wangyi.png,搜狐新闻@http://news.sohu.com@https://share.yoyin.net/app/webprint/icons/print_ic_shouhu.png,今日头条@https://www.toutiao.com@https://share.yoyin.net/app/webprint/icons/print_ic_toutiao.png");
        return "Ok";
    }

    /**
     * 添加分组和网站的私有方法
     * @param groupName 分组名称
     * @param strSites 网站字符串，格式：名称@URL@图标URL,名称@URL@图标URL
     */
    private void addGroupSite(String groupName, String strSites) {
        WebPrint printGroup = new WebPrint();
        ToolsKit.setIdEntityData(printGroup, "");
        printGroup.setUserId("");
        printGroup.setName(groupName);
        printGroup.setIsDefault(TRUE);
        printGroup.setFromDefault(FALSE);

        List<WebPagePrintVo> pageList = new ArrayList<>();

        String[] siteArr = strSites.split(",");
        int codeId = 0;
        for (String site : siteArr) {
            if (StringUtils.isBlank(site)) {
                continue;
            }

            codeId++;
            String[] siteItems = site.split("@");

            WebPagePrintVo printVo = new WebPagePrintVo(codeId);
            printVo.setGroupId(printGroup.getId());
            printVo.setName(siteItems[0]);
            if (siteItems.length > 1) {
                printVo.setLinkUrl(siteItems[1]);
            } else {
                printVo.setLinkUrl("");
            }
            if (siteItems.length > 2) {
                printVo.setIconUrl(siteItems[2]);
            } else {
                printVo.setIconUrl("");
            }
            printVo.setIsDefault(TRUE);
            pageList.add(printVo);
        }

        printGroup.setPageList(pageListToJson(pageList));
        webPrintDao.saveOrUpdate(printGroup);
    }

    /**
     * 获取默认网站（兼容方法）
     * @return
     */
    public Object getDefaultWebSites() {
        return null;
    }
}
