package net.snaptag.system.business.buservice;

import net.snaptag.system.business.dao.TemplateDomesticDao;
import net.snaptag.system.business.dao.TemplateDraftsDao;
import net.snaptag.system.business.dao.TemplateDraftsLanguageConfigDao;
import net.snaptag.system.business.dao.UserTemplateDraftsCollectDao;
import net.snaptag.system.business.dto.*;
import net.snaptag.system.business.entity.*;
import net.snaptag.system.business.enums.PaperInfoEnums;
import net.snaptag.system.business.utils.Constant;
import net.snaptag.system.business.utils.ToolUtils;
import net.snaptag.system.business.vo.PicVo;
import net.snaptag.system.common.DataConst;
import net.snaptag.system.sadais.core.common.exception.ServiceException;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import net.snaptag.system.sadais.util.core.ToolsKit;
import net.snaptag.system.sadais.web.common.JsonKit;
import net.snaptag.system.sadais.web.core.I18nUtils;
import net.snaptag.system.sadais.web.enums.LanguageEnums;
import org.checkerframework.checker.units.qual.Length;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;

/**
 * <AUTHOR>
 * @date ：Created in 2021/9/28 11:14
 * @description：
 * @modified By：
 * @version: $
 */
@Service
public class TemplateDraftsService {
    private static final String DICT_KEY_TEMPLATE_TYPE = "template_type";
    private static final String DICT_KEY_TEMPLATE_TYPE_LABEL = "template_type_label";
    @Autowired
    private I18nUtils i18nUtils;
    @Autowired
    private TemplateDraftsDao templateDraftsDao;
    @Autowired
    private TemplateDomesticDao templateDomesticDao;

    @Autowired
    private DictBuService dictBuService;

    @Autowired
    private UserTemplateDraftsCollectDao userTemplateDraftsCollectDao;

    @Autowired
    private TemplateDraftsLanguageConfigDao templateDraftsLanguageConfigDao;

    private Map<String, TemplateDraftsLanguageConfig> templateDraftsLanguageConfigMap = new HashMap();

    @Autowired
    private PrintPaperInfoBuService printPaperInfoBuService;

    /***
     * 获取模板列表
     * @param userId
     * @param type
     * @param isHot
     * @param pageno
     * @param pagesize
     * @return
     */
    public List<TemplateDraftsDto> getTemplateDraftsList(String userId, String type, int isHot, int pageno, int pagesize, String locale, String printerType, String paperType, String length, String version, String keyword) {
        List<TemplateDraftsDto> result = new ArrayList<>();

        List<TemplateDrafts> list;
        // 版本差异处
//        if (ToolUtils.compareVersion(Constant.VERSION380, version)){
//            list = templateDraftsDao.findList(pageno, pagesize, type, isHot, locale, getLensByPrinterType(printerType, paperType), paperType, length);
//        } else {
//            list = templateDraftsDao.findList(pageno, pagesize, type, isHot, locale, printerType, paperType, length);
//        }
        // 处理尺寸范围查询（参考JFinal的indexRange处理）
        Integer widthBegin = null;
        Integer widthEnd = null;
        if (ToolsKit.String.isNotBlank(length)) {
            String[] indexRangeArr = length.split(",");
            if (indexRangeArr.length > 0 && ToolsKit.String.isNotBlank(indexRangeArr[0])) {
                try {
                    widthBegin = Integer.valueOf(indexRangeArr[0]);
                } catch (NumberFormatException e) {
                    // 忽略无效的宽度开始值
                }
            }
            if (indexRangeArr.length > 1 && ToolsKit.String.isNotBlank(indexRangeArr[1])) {
                try {
                    widthEnd = Integer.valueOf(indexRangeArr[1]);
                } catch (NumberFormatException e) {
                    // 忽略无效的宽度结束值
                }
            }
        }
        list = templateDraftsDao.findList(pageno, pagesize, type, isHot, locale, getLensByPrinterType(printerType, paperType), paperType, widthBegin, widthEnd, keyword);

        Map<String, String> mycollection = getMyCollectionMap(userId);

        if (ToolsKit.isNotEmpty(list)) {
            for (TemplateDrafts templateDrafts : list) {
                TemplateDraftsDto dto = new TemplateDraftsDto();

                // 手动复制属性，避免类型转换问题
                dto.setId(templateDrafts.getId());
                dto.setName(templateDrafts.getName());
                dto.setContent(templateDrafts.getContent());
                dto.setPic(templateDrafts.getPic());
                dto.setType(templateDrafts.getType());
                dto.setIsHot(templateDrafts.getIsHot());
                dto.setSortNum(templateDrafts.getSortNum());
                dto.setLocaleCode(templateDrafts.getLocaleCode());
                dto.setPrinterType(templateDrafts.getPrinterType());
                dto.setPaperType(templateDrafts.getPaperType());
                dto.setPaperSize(templateDrafts.getPaperSize());

                // 处理JSON字符串转换为对象
                if (ToolsKit.isNotEmpty(templateDrafts.getDraftsDto())) {
                    try {
                        DraftsDto draftsDto = JsonKit.jsonParseObject(templateDrafts.getDraftsDto(), DraftsDto.class);
                        dto.setDraftsDto(draftsDto);
                    } catch (Exception e) {
                        System.err.println("解析draftsDto JSON失败: " + e.getMessage());
                        dto.setDraftsDto(null);
                    }
                }

                // 是否已收藏
                if (mycollection.get(templateDrafts.getId()) != null) {
                    dto.setHadCollected(1);
                } else {
                    dto.setHadCollected(0);
                }

                dto.setPicVo(dto.getDraftsDto() != null ? dto.getDraftsDto().getPicVo() : new PicVo());
                if (dto.getDraftsDto() != null && dto.getDraftsDto().getPaperObj() != null) {
                    setPaperObjInfo(dto.getDraftsDto().getPaperObj());
                }
                result.add(dto);
            }
        }
        return result;
    }


    private List<String> getLensByPrinterType(String printerType, String paperType) {
        List<PrintPaperInfoDto> paperInfoDtoList = printPaperInfoBuService.getList(printerType, paperType, Locale.CHINESE, null);
        if (ToolsKit.isEmpty(paperInfoDtoList)) {
            return null;
        }
        List<String> result = new ArrayList<>();
        Map<String, String> existMap = new HashMap<>();
        paperInfoDtoList.forEach(item -> {
            Float heightTemp = item.getHeight();
            float kuandu = (heightTemp.intValue() == 1 || heightTemp.intValue() == 0) ? item.getWidth() : item.getHeight();
            String labelName = item.getDirection() == 1 ? deleteZero(item.getHeight()) + "*" + deleteZero(item.getWidth()) : deleteZero(item.getWidth()) + "*" + deleteZero(item.getHeight());
            String key = item.getType() == 1 ? deleteZero(kuandu) : labelName;
            if (existMap.get(key) == null) {
                existMap.put(key, key);
                result.add(key);
            }
        });
        return result;
    }

    private String deleteZero(float f) {
        return new BigDecimal(Float.toString(f)).stripTrailingZeros().toPlainString();
    }

    public Page<TemplateDraftsDto> findPage(String userId, String keyword, int pageNo, int pageSize, String locale, String printerType, String paperType) {
        Map<String, String> mycollection = getMyCollectionMap(userId);
        Page<TemplateDraftsDto> result = new Page<>(pageNo + 1, pageSize);
        result.setRecords(null);
        result.setTotal(0);
        result.setCurrent(pageNo + 1);
        result.setSize(pageSize);
        if (ToolsKit.isNotEmpty(keyword)) {
            IPage<TemplateDrafts> page = templateDraftsDao.findPage(pageNo, pageSize, keyword, locale, printerType, paperType);
            if (ToolsKit.isNotEmpty(page.getRecords())) {
                List<TemplateDraftsDto> dtoList = new ArrayList<>();
                page.getRecords().forEach(item -> {
                    TemplateDraftsDto temp = new TemplateDraftsDto();
                    ToolsKit.Bean.copyProperties(item, temp);
                    temp.setPicVo(temp.getDraftsDto() != null ? temp.getDraftsDto().getPicVo() : new PicVo());
                    // 是否已收藏
                    if (mycollection.get(item.getId()) != null) {
                        temp.setHadCollected(1);
                    } else {
                        temp.setHadCollected(0);
                    }

                    if (temp.getDraftsDto() != null && temp.getDraftsDto().getPaperObj() != null) {
                        setPaperObjInfo(temp.getDraftsDto().getPaperObj());
                    }

                    dtoList.add(temp);
                });
                result.setRecords(dtoList);
                result.setTotal(page.getTotal());
                result.setCurrent(page.getCurrent());
                result.setSize(page.getSize());
            } else {
                result.setRecords(new ArrayList<>());
                result.setTotal(page.getTotal());
                result.setCurrent(page.getCurrent());
                result.setSize(page.getSize());
            }
        }
        return result;
    }

    public List<TemplateDraftsDto> getMyCollectTemplateDraftsList(String userId, int pageno, int pagesize, String printerType) {
        if (ToolsKit.isEmpty(userId)) {
            throw new ServiceException("当前用户不能为空");
        }
        List<UserTemplateDraftsCollect> list = userTemplateDraftsCollectDao.findByUserIdForPage(userId, pageno, pagesize);
        List<TemplateDraftsDto> result = new ArrayList<>();
        if (ToolsKit.isNotEmpty(list)) {
            List<String> paperInfoDtoList = new ArrayList<>();
            if (ToolsKit.isNotEmpty(printerType)) {
                paperInfoDtoList = this.getLensByPrinterType(printerType, null);
            }
            System.out.println("==================查询我的收藏=====================");
            for (UserTemplateDraftsCollect coll : list) {
//                System.out.println("coll=" + JSONObject.toJSONString(coll));
                TemplateDrafts drafts = templateDraftsDao.getById(coll.getTemplateDraftsId());

                if (drafts == null) {
                    userTemplateDraftsCollectDao.removeById(coll.getId());
                    continue;
                }

                boolean isExist = false;
                for (String item : paperInfoDtoList) {
//                    System.out.println("------paperInfo=" + JSONObject.toJSONString(item));
                    if (ToolsKit.isNotEmpty(drafts.getPaperSize())
                            && (drafts.getPaperSize().endsWith(item) || drafts.getPaperSize().contains(item + ";"))) {
                        isExist = true;
                    }
                }

                if (!isExist) {
                    continue;
                }

                TemplateDraftsDto temp = new TemplateDraftsDto();
                ToolsKit.Bean.copyProperties(drafts, temp);
                temp.setHadCollected(1);// 设置已收藏
                result.add(temp);
            }
        }
        return result;
    }

    public TemplateDraftsDto getById(String templateDraftsId, String userId) {
        if (ToolsKit.isEmpty(templateDraftsId)) {
            throw new ServiceException("模板id不能为空");
        }
        TemplateDrafts drafts = templateDraftsDao.getById(templateDraftsId);

        if (drafts == null) {
            throw new ServiceException("当前模板已被删除");
        }

        TemplateDraftsDto dto = new TemplateDraftsDto();
        ToolsKit.Bean.copyProperties(drafts, dto);
        Map<String, String> mycollection = getMyCollectionMap(userId);

        return dto;
    }

    /***
     * 新增收藏
     * @param userId
     * @param templateDraftsId
     */
    public void addCollect(String userId, String templateDraftsId) {
        if (ToolsKit.isEmpty(userId)) {
            throw new ServiceException("您需要登录后再进行操作");
        }
        if (ToolsKit.isEmpty(templateDraftsId)) {
            throw new ServiceException("收藏的模板id不能为空");
        }
        Map<String, String> mycollection = getMyCollectionMap(userId);
        if (mycollection.get(templateDraftsId) != null) {
            throw new ServiceException("不能重复收藏");
        }
        UserTemplateDraftsCollect userTemplateDraftsCollect = new UserTemplateDraftsCollect();
        userTemplateDraftsCollect.setUserId(userId);

        TemplateDraftsDto dto = getById(templateDraftsId, userId);
        if (dto != null) {
            userTemplateDraftsCollect.setPrinterType(dto.getPrinterType());
        }

        userTemplateDraftsCollect.setTemplateDraftsId(templateDraftsId);
        ToolsKit.setIdEntityData(userTemplateDraftsCollect, userId);
        userTemplateDraftsCollectDao.saveOrUpdate(userTemplateDraftsCollect);
    }

    /***
     * 取消收藏
     * @param userId
     * @param templateDraftsId
     */
    public void cancelCollect(String userId, String templateDraftsId) {
        if (ToolsKit.isEmpty(userId)) {
            throw new ServiceException("您需要登录后再进行操作");
        }
        if (ToolsKit.isEmpty(templateDraftsId)) {
            throw new ServiceException("取消收藏的模板id不能为空");
        }
        Map<String, String> mycollection = getMyCollectionMap(userId);
        String[] ids = templateDraftsId.split(",");
        System.out.println(ids.length);
        for (int i = 0; i < ids.length; i++) {
            System.out.println(ids[i]);
            if (mycollection.get(ids[i]) != null) {
                userTemplateDraftsCollectDao.removeById(mycollection.get(ids[i]));
            }
        }
    }

    public void addOrCancelCollect(String userId, String templateDraftsId) {
        if (ToolsKit.isEmpty(userId)) {
            throw new ServiceException("您需要登录后再进行操作");
        }
        if (ToolsKit.isEmpty(templateDraftsId)) {
            throw new ServiceException("模板id不能为空");
        }
        Map<String, String> mycollection = getMyCollectionMap(userId);
        if (mycollection.get(templateDraftsId) != null) {
            userTemplateDraftsCollectDao.removeById(mycollection.get(templateDraftsId));
        } else {
            UserTemplateDraftsCollect userTemplateDraftsCollect = new UserTemplateDraftsCollect();
            userTemplateDraftsCollect.setUserId(userId);
            userTemplateDraftsCollect.setTemplateDraftsId(templateDraftsId);

            TemplateDraftsDto dto = getById(templateDraftsId, userId);
            if (dto != null) {
                userTemplateDraftsCollect.setPrinterType(dto.getPrinterType());
            }

            ToolsKit.setIdEntityData(userTemplateDraftsCollect, userId);
            userTemplateDraftsCollectDao.saveOrUpdate(userTemplateDraftsCollect);
        }
    }

    /***
     * <模板id， 收藏记录的id></模板id，>
     * @param userId
     * @return
     */
    private Map<String, String> getMyCollectionMap(String userId) {
        if (ToolsKit.isEmpty(userId)) {
            return new HashMap<>();
        }
        List<UserTemplateDraftsCollect> list = userTemplateDraftsCollectDao.findByUserIdAndTemplateId(userId, null);
        if (ToolsKit.isEmpty(list)) {
            return new HashMap<>();
        }
        Map<String, String> result = new HashMap<>();
        for (UserTemplateDraftsCollect ut : list) {
            result.put(ut.getTemplateDraftsId(), ut.getId());
        }
        return result;
    }

    // ------------------------------支撑平台
    public IPage<TemplateDrafts> getTemplateDraftsPage(String type, int isHot, int pageNo, int pageSize, String localeCode, String printerType, String paperType) {
        return templateDraftsDao.findPage(pageNo, pageSize, type, isHot, localeCode, null, paperType, null);
    }

    /***
     * 新增或保存模板
     * @param templateDrafts
     * @param userId
     * @return
     */
    public TemplateDrafts saveOrUpdate(TemplateDrafts templateDrafts, String userId) {
        if (ToolsKit.isEmpty(templateDrafts.getId())) {
            ToolsKit.setIdEntityData(templateDrafts, userId);
            templateDrafts.setId(null);
        }
        templateDraftsDao.saveOrUpdate(templateDrafts);
        return templateDrafts;
    }

    /***
     * 删除模板
     * @param id
     * @param userId
     */
    public void deleteById(String id, String userId) {
        templateDraftsDao.removeById(id);
    }

    private void setPaperObjInfo(PaperInfoDto paperInfoDto) {
        if (PaperInfoEnums.getMap().get(paperInfoDto.getLengthType()) != null) {
            paperInfoDto.setName(PaperInfoEnums.getMap().get(paperInfoDto.getLengthType()).getName());
        } else {
            paperInfoDto.setName(PaperInfoEnums.LXZ1.getName());
        }
    }

    public Object turnUserTemplateShow(String language) {
        TemplateDraftsLanguageConfig config = getConfigByLanguage(language);
        config.setShowFlag(!config.getShowFlag());
        templateDraftsLanguageConfigDao.saveOrUpdate(config);
        this.templateDraftsLanguageConfigMap.put(language, config);
        return true;
    }

    public Boolean checkUserTemplateShow(String language) {
        if (ToolsKit.isEmpty(language)) {
            return false;
        }
        return getConfigByLanguage(language).getShowFlag();
    }

    synchronized private TemplateDraftsLanguageConfig getConfigByLanguage(String language) {
        if (ToolsKit.isEmpty(language)) {
            throw new ServiceException("缺省参数");
        }

        // 从缓存找
        if (this.templateDraftsLanguageConfigMap.get(language) != null) {
            return this.templateDraftsLanguageConfigMap.get(language);
        }

        // 从数据库找
        TemplateDraftsLanguageConfig config = templateDraftsLanguageConfigDao.getConfigByLanguage(language);
        if (config != null) {
            this.templateDraftsLanguageConfigMap.put(language, config);
            return config;
        }

        // 没有记录，重新创建
        config = new TemplateDraftsLanguageConfig();
        ToolsKit.setIdEntityData(config, Constant.DEFAULT_USER_COURSE_ID);
        config.setLanguage(language);
        config.setShowFlag(Boolean.FALSE);
        templateDraftsLanguageConfigDao.saveOrUpdate(config);
        this.templateDraftsLanguageConfigMap.put(language, config);
        return config;
    }

    public List<Map> getUserTemplateShowList() {
        List<Map> array = new ArrayList<>();
        for (LanguageEnums languageEnums : LanguageEnums.values()) {
            array.add(getConfigMapByLanguage(languageEnums));
        }
        return array;
    }

    private Map<String, Object> getConfigMapByLanguage(LanguageEnums enums) {
        Map<String, Object> map = new HashMap<>();
        map.put("id", enums.getKey());
        map.put("name", enums.getDesc());
        map.put("flag", getConfigByLanguage(enums.getKey()).getShowFlag());
        return map;
    }

    //获取商用行业模板类型
    public Map<String, Object> getTemplateDraftsType(String printerType, Locale local) {
        return getTemplateDraftsType(printerType, local, DICT_KEY_TEMPLATE_TYPE);
    }

    public Map<String, Object> getTemplateDraftsType(String printerType, Locale local, String dictType) {
        Map<String, Object> resMap = new HashMap<>();

        List<Map<String, String>> resultLxz = new ArrayList();
        List<Map<String, String>> resultJxz = new ArrayList();
        List<Dict> dictList = dictBuService.findListByType(dictType);

        if (ToolsKit.isNotEmpty(printerType)) {
            printerType = printerType.toLowerCase();
        }

        if (ToolsKit.isNotEmpty(dictList)) {
            for (Dict dict : dictList) {
                Map<String, String> tmp = new HashMap<>();
                tmp.put("groupId", dict.getId());
                tmp.put("id", dict.getValue());
                String name = i18nUtils.getKey(dict.getLocaleCode(), local);
                tmp.put("name", ToolsKit.isEmpty(name) ? dict.getLabel() : name);

                // 如果传的printerType为空，则返回所有，兼顾以前的数据
                if (ToolsKit.isEmpty(printerType)) {
                    resultLxz.add(tmp);
                    resultJxz.add(tmp);
                    continue;
                }

                if (ToolsKit.isNotEmpty(dict.getRemark())) {
                    DictTemplateDraftsRemarkDto remarkDto = JsonKit.jsonParseObject(dict.getRemark(), DictTemplateDraftsRemarkDto.class);
                    if (ToolsKit.isNotEmpty(remarkDto.getLanguage()) && !remarkDto.getLanguage().contains(local.toString())) {
                        continue;
                    }

//                    if (ToolsKit.isNotEmpty(remarkDto.getLianxu()) && remarkDto.getLianxu().contains(printerType.toLowerCase()+";")) {
                    if (ToolsKit.isNotEmpty(remarkDto.getLianxu()) && ToolUtils.hasPrinterType(remarkDto.getLianxu(), printerType)) {
                        resultLxz.add(tmp);
                    } else if (ToolsKit.isEmpty(remarkDto.getLianxu())) {
                        resultLxz.add(tmp);
                    }
//                    if (ToolsKit.isNotEmpty(remarkDto.getJianxi()) && remarkDto.getJianxi().contains(printerType.toLowerCase()+";")) {
                    if (ToolsKit.isNotEmpty(remarkDto.getJianxi()) && ToolUtils.hasPrinterType(remarkDto.getJianxi(), printerType)) {
                        resultJxz.add(tmp);
                    } else if (ToolsKit.isEmpty(remarkDto.getJianxi())) {
                        resultJxz.add(tmp);
                    }
                } else {
                    resultLxz.add(tmp);
                    resultJxz.add(tmp);
                }
            }
        }
        resMap.put("lxz", resultLxz);
        resMap.put("jxz", resultJxz);
        return resMap;
    }


}
