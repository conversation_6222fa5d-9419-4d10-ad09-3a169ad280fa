package net.snaptag.system.business.buservice;

import net.snaptag.system.business.cache.PubSubCacheService;
import net.snaptag.system.business.dao.DictDao;
import net.snaptag.system.business.entity.Dict;
import net.snaptag.system.business.utils.MD5Utils;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import net.snaptag.system.common.DataConst;
import net.snaptag.system.sadais.util.core.ToolsKit;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * <AUTHOR>
 * @date ：Created in 2023/8/24 9:56
 * @description：字典项服务类p
 * @modified By：
 * @version: $
 */
@Service
public class DictBuService {
    @Autowired
    private PubSubCacheService pubSubCacheService;

    private LinkedHashMap<String, String> distinctTypeMap = new LinkedHashMap<>();
    private HashMap<String, List<Dict>> dictsMap = new HashMap<>();

    @Autowired
    private DictDao dictDao;

    /***
     * 返回前端字典项的所有类型
     * @return
     */
    public List<Map<String, String>> getDistinctType(){
        if (ToolsKit.isEmpty(this.distinctTypeMap)){
            this.distinctTypeMap = dictDao.findDistinctType();
        }
        List<Map<String, String>> result = new ArrayList<>();
        this.distinctTypeMap.keySet().forEach(key -> {
            Map<String, String> temp = new HashMap<>();
            temp.put("name", this.distinctTypeMap.get(key));
            temp.put("value", key);
            result.add(temp);
        });
        return result;
    }

    public IPage<Dict> findPageByType(int pageNo, int pageSize, String type){
        if (pageNo>0) pageNo--;
        return dictDao.findPageByType(pageNo, pageSize, type);
    }

    public List<Dict> findListByType(String type){
        if (ToolsKit.isEmpty(type)){
            return new ArrayList<>();
        }
        if (ToolsKit.isEmpty(this.dictsMap) || this.dictsMap.get(type)==null){
            this.dictsMap.put(type, dictDao.findListByType(type));
        }
        return this.dictsMap.get(type);
    }

    public String addOrUpdate(Dict dict){
        if (dict==null){
            return "fail";
        }
        if (ToolsKit.isEmpty(dict.getId())){
            dict.setId(null);
            dict.setStatus(DataConst.DATA_SUCCESS_STATUS);
            dict.setCreatetime(new java.util.Date());
            dict.setCreateuserid("SYSTEM_USER_ID");
        }

        if ("app_driver_wifi".equals(dict.getType()) && dict.getValue()!=null) {
            dict.setRemark(MD5Utils.getMD5(dict.getValue()));
        }
        dictDao.saveOrUpdate(dict);
        // 保存完后，刷新下缓存
//        if (this.distinctTypeMap.get(dict.getType())==null){
//            this.distinctTypeMap.put(dict.getType(), dict.getName());
//        }
        pubSubCacheService.updateDictCacheData();
        return "success";
    }

    public void delete(String id){
        if (ToolsKit.isEmpty(id)){
            return;
        }
        dictDao.removeById(id);
        pubSubCacheService.updateDictCacheData();
    }

    public void init() {
        this.distinctTypeMap = new LinkedHashMap<>();
        this.dictsMap = new HashMap<>();
    }

    public Map<String, String> getWifiInfo() {
        List<Dict> list = findListByType("app_driver_wifi");
        Map<String, String> result = new HashMap<>();
        if (ToolsKit.isNotEmpty(list)){
           Dict dist = list.get(0);
           result.put("url", dist.getValue());
           result.put("md5", dist.getRemark());
        }
        return result;
    }

    public Dict refreshWifiMd5(String id){
        Dict dict = dictDao.getById(id);
        if (dict!=null && "app_driver_wifi".equals(dict.getType()) && dict.getValue()!=null){
            dict.setRemark(MD5Utils.getMD5(dict.getValue()));
            this.dictDao.saveOrUpdate(dict);
            pubSubCacheService.updateDictCacheData();
            return dict;
        }
        return null;
    }
}
