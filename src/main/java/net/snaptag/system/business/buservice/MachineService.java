package net.snaptag.system.business.buservice;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import net.snaptag.system.business.entity.MachineSettings;
import net.snaptag.system.business.enums.MachineSettingsSearchType;
import net.snaptag.system.business.mapper.MachineSettingsMapper;
import net.snaptag.system.business.utils.LanguageUtils;
import net.snaptag.system.sadais.util.core.ToolsKit;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;


/**
 * Author：Chenjy
 * Date:2025/8/25
 * Description:
 */
@Service
public class MachineService {

    @Autowired
    private MachineSettingsMapper machineSettingsMapper;
    /**
     * @param name
     * @return
     * @Title: getDeviveList
     * @Description:
     * <AUTHOR>
     */
//    public List<MachineSettings> getDeviveList(String name, String locale) {
//        if (ToolsKit.isNotEmpty(name)) {
//            name = "%" + name + "%";
//        }
//
//        if (ToolsKit.isEmpty(locale) || locale.equals(LanguageUtils.LOCALE_ZH_CN)) {
//            SqlPara sqlPara = Db.getSqlPara("app.machine.list", Kv.by("name", name));
//            List<MachineSettings> list = MachineSettings.dao.find(sqlPara);
//            return RetKit.ok("list", list);
//        }
//
//        String sql = String.format("select m.machineId, " +
//                " ifnull(i18n.machineName, m.machineName) as machineName, " +
//                " m.createTime " +
//                " from machine m" +
//                " left join machine_i18n i18n on i18n.machineId = m.machineId and i18n.locale = '%s' " +
//                " where m.machineName like '%s' " +
//                " order by m.createTime asc", locale, ToolsKit.isEmpty(name) ? '%' : name);
//        LogKit.error(sql);
//        List<Help> list = Help.dao.find(sql);
//        return list;
//    }

    /**
     * @param
     * @return
     * @Title: hiddenType
     * @Description: APP搜索需要过滤隐藏的设备类型
     * <AUTHOR>
     */
//    public RetKit hiddenTypeList() {
//        // 过滤设备的字典KEY
//        String hiddenKey = "bluetooth-device-hidden";
//
//        String sql = String.format("select * from app_dict where dictKey = '%s' ", hiddenKey);
//        List<AppDict> list = AppDict.dao.find(sql);
//        if (CollectionUtil.isNotEmpty(list)) {
//            List<String> hiddenTypeList = list.stream().map(appDict -> appDict.getDictValue()).collect(Collectors.toList());
//            return RetKit.ok("list", hiddenTypeList);
//        }
//        return RetKit.ok("list", new ArrayList<>());
//    }

    /**
     * @param searchType 搜索类型  0 - bluetoothName   1 - machineName  2 - hardwareName  default 0
     * @return
     * @Title: list
     * @Description: 获取机器对应的默认设置
     * <AUTHOR>
     */
    public String getSettings(MachineSettingsSearchType searchType, String searchValue) {
        String search_colum_name = "bluetoothName";
        switch (searchType) {
            case MACHINE:
                search_colum_name = "machineName";
                break;
            case HARDWARE:
                search_colum_name = "hardwareName";
                break;
            default:
                break;
        }

        // 构建条件表达式，search_colum_name是字段名，searchValue是要匹配的值
        String condition = String.format("instr('%s', %s) = 1", searchValue, search_colum_name);
        QueryWrapper<MachineSettings> queryWrapper = new QueryWrapper<>();
        queryWrapper.apply(condition)
                .select("settings")
                .orderByDesc("createTime");

        MachineSettings machineSettings = machineSettingsMapper.selectOne(queryWrapper);
        if (ObjectUtil.isNull(machineSettings)) {
            return null;
        }
        return machineSettings.getSettings();
    }
}
