package net.snaptag.system.business.buservice;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fasterxml.jackson.databind.ObjectMapper;
import net.snaptag.system.account.buservice.UserAccountAndInfoBuService;
import net.snaptag.system.account.dto.UserLoginDto;
import net.snaptag.system.business.cache.DraftsCacheService;
import net.snaptag.system.business.dao.DraftsDao;
import net.snaptag.system.business.dto.*;
import net.snaptag.system.business.entity.Drafts;
import net.snaptag.system.business.entity.PrintPaperInfo;
import net.snaptag.system.business.entity.ResourceData;
import net.snaptag.system.business.enums.DraftsSubTypeEnums;
import net.snaptag.system.business.enums.PaperInfoEnums;
import net.snaptag.system.business.enums.ResourceTypeEnums;
import net.snaptag.system.business.utils.Constant;
import net.snaptag.system.business.utils.ToolUtils;
import net.snaptag.system.business.vo.DraftsParamVo;
import net.snaptag.system.business.vo.PicVo;
import net.snaptag.system.common.DataConst;
import net.snaptag.system.sadais.cache.kit.CacheKit;
import net.snaptag.system.sadais.core.common.core.ToolsConst;
import net.snaptag.system.sadais.core.common.enums.ExceptionEnums;
import net.snaptag.system.sadais.core.common.exception.ServiceException;
import net.snaptag.system.sadais.util.core.ToolsKit;
import net.snaptag.system.sadais.web.config.CommonProperties;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.Locale;

/**
 * 草稿箱业务服务类
 * 已修正JSON字段映射问题
 */
@Service
public class DraftsBuService {

    @Autowired
    private DraftsDao draftsDao;
    @Autowired
    private DraftsCacheService draftsCacheService;
    @Autowired
    private CommonProperties commonProperties;
    @Autowired
    private ResourceDataBuService resourceDataBuService;
    @Autowired
    private PrintPaperInfoBuService printPaperInfoBuService;
    @Autowired
    private UserAccountAndInfoBuService userAccountAndInfoBuServiced;

    private static final ObjectMapper objectMapper = new ObjectMapper();
    private static final String CACHE_KEY_USERID_DRAFF_RECENT_PAPERINFO_LIST = "comm:drafts:recent:parperinfo:userid:";

    /**
     * 将JSON字符串转换为DraftsParamVo
     */
    private DraftsParamVo parseDraftsParamJson(String draftsParamJson) {
        if (draftsParamJson == null || draftsParamJson.trim().isEmpty()) {
            return new DraftsParamVo();
        }
        try {
            return objectMapper.readValue(draftsParamJson, DraftsParamVo.class);
        } catch (Exception e) {
            // 如果解析失败，返回空DraftsParamVo
            return new DraftsParamVo();
        }
    }

    /**
     * 将JSON字符串转换为PicVo
     */
    private PicVo parsePicVoFromJson(String picVoJson) {
        if (picVoJson == null || picVoJson.trim().isEmpty()) {
            return new PicVo();
        }
        try {
            return objectMapper.readValue(picVoJson, PicVo.class);
        } catch (Exception e) {
            // 如果解析失败，返回空PicVo
            return new PicVo();
        }
    }

    /**
     * 将DraftsParamVo转换为JSON字符串
     */
    private String draftsParamToJson(DraftsParamVo draftsParamVo) {
        if (draftsParamVo == null) {
            return null;
        }
        try {
            return objectMapper.writeValueAsString(draftsParamVo);
        } catch (Exception e) {
            // 如果转换失败，返回null
            return null;
        }
    }

    /**
     * 根据ID获取草稿箱对象
     *
     * @param id ID
     * @return 草稿箱对象
     * @throws ServiceException
     */
    public Drafts getDraftsById(String id) throws ServiceException {
        if (ToolsKit.isEmpty(id)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("记录信息ID不能为空");
        }
        Drafts drafts = draftsCacheService.getDraftsById(id);
        if (ToolsKit.isEmpty(drafts)) {
            try {
                drafts = draftsDao.getById(id);
            } catch (Exception e) {
                e.printStackTrace();
            }
            if (ToolsKit.isNotEmpty(drafts)) {
                draftsCacheService.saveDrafts(drafts);
            }
        }
        return drafts;
    }

    /**
     * 保存草稿
     */
    public void save(Drafts drafts) {
        if (drafts != null) {
            draftsDao.saveOrUpdate(drafts);
            draftsCacheService.saveDrafts(drafts);
        }
    }

    /**
     * 根据ID获取草稿
     */
    public Drafts getById(String id) {
        if (ToolsKit.isEmpty(id)) {
            return null;
        }
        return draftsDao.getById(id);
    }

    /**
     * 删除草稿
     */
    public void deleteById(String id) {
        if (ToolsKit.isNotEmpty(id)) {
            draftsDao.removeById(id);
        }
    }

    /**
     * 根据用户ID获取草稿列表
     */
    public List<Drafts> getDraftsByUserId(String userId) {
        if (ToolsKit.isEmpty(userId)) {
            return new ArrayList<>();
        }
        QueryWrapper<Drafts> wrapper = new QueryWrapper<>();
        wrapper.eq("user_id", userId);
        wrapper.eq("status", "审核通过");
        return draftsDao.list(wrapper);
    }

    /**
     * 根据用户ID和类型获取草稿列表
     */
    public List<Drafts> getDraftsByUserIdAndType(String userId, int type) {
        if (ToolsKit.isEmpty(userId)) {
            return new ArrayList<>();
        }
        QueryWrapper<Drafts> wrapper = new QueryWrapper<>();
        wrapper.eq("user_id", userId).eq("type", type);
        wrapper.eq("status", "审核通过");
        return draftsDao.list(wrapper);
    }

    /**
     * 保存草稿并处理参数
     */
    public Drafts saveDraftsWithParam(Drafts drafts, DraftsParamVo paramVo, String userId) {
        if (drafts == null) {
            return null;
        }

        // 设置基本信息
        if (ToolsKit.isEmpty(drafts.getId())) {
            ToolsKit.setIdEntityData(drafts, userId);
        } else {
            ToolsKit.setIdEntityData(drafts, userId);
        }

        // 转换参数为JSON
        String paramJson = draftsParamToJson(paramVo);
        drafts.setDraftsParam(paramJson);

        // 保存到数据库
        draftsDao.saveOrUpdate(drafts);

        return drafts;
    }

    /**
     * 获取草稿参数
     */
    public DraftsParamVo getDraftsParam(String id) {
        Drafts drafts = getById(id);
        if (drafts == null) {
            return null;
        }
        return parseDraftsParamJson(drafts.getDraftsParam());
    }

    /**
     * 获取草稿箱ID列表
     */
    private List<String> getDraftsIdsList(String userId, int type, int subType, String materialColumnId, int length, int page, int pageSize, String lastId, String printerType, String paperType, String version, String keyword) {
        List<String> idsList = new ArrayList<>();
        List<Drafts> draftsList = new ArrayList<>();
        draftsList = draftsDao.findDraftsList(userId, type, subType, materialColumnId, length, page, pageSize, lastId, printerType, getPaperInfoListByPrinterType(printerType), paperType, keyword);
        if (ToolsKit.isNotEmpty(draftsList)) {
            for (Drafts drafts : draftsList) {
                idsList.add(drafts.getId());
            }
        }
        return idsList;
    }

    /**
     * 获取草稿箱列表
     */
    public List<DraftsDto> getDraftsList(String userId, String type, String subType, String materialColumnId, String length, int pageNo, int pageSize, String lastId, String printerType, String paperType, String version, String keyword) throws ServiceException {

        if (ToolsKit.isEmpty(userId)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("用户ID不能为空");
        }
        if (ToolsKit.isEmpty(type)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("类型不能为空");
        }
        List<DraftsDto> dtoList = new ArrayList<DraftsDto>();
        try {
            if (pageNo > 0) {
                pageNo--;
            }
            if (ToolsKit.isEmpty(subType)) {// 如果没有副类型，默认为编辑纸条
                subType = String.valueOf(DraftsSubTypeEnums.EDIT.getValue());
            }
            if (ToolsKit.isEmpty(length)) {
                length = PaperInfoEnums.LXZ1.getLength();
            }
            List<String> idsList = this.getDraftsIdsList(userId, Integer.parseInt(type), Integer.parseInt(subType), materialColumnId, Integer.parseInt(length), pageNo, pageSize, lastId, printerType, paperType, version, keyword);
            if (ToolsKit.isNotEmpty(idsList)) {
                for (String id : idsList) {
                    Drafts drafts = this.getDraftsById(id);
                    if (ToolsKit.isNotEmpty(drafts)) {
                        DraftsDto dto = new DraftsDto();
                        dto.setId(drafts.getId());
                        if (StrUtil.isNotBlank(drafts.getName())) {
                            dto.setName(drafts.getName());
                        }
                        dto.setTitle(drafts.getTitle());
                        dto.setIsMirror(drafts.getIsMirror());
                        dto.setPreviewPoint(drafts.getPreviewPoint());

                        ResourceData picRes = resourceDataBuService.getResourceDataById(drafts.getResPicId());
                        if (ToolsKit.isNotEmpty(picRes) && ToolsKit.isNotEmpty(picRes.getResUrl())) {
                            PicVo picVo = parsePicVoFromJson(picRes.getResUrl());
                            String picUrl = picVo.getPic();
                            if (ToolsKit.isNotEmpty(picUrl) && picUrl.contains(",")) {
                                picUrl = picUrl.replaceAll(",", "," + commonProperties.getFileDomain());
                            }
                            dto.setPic(ToolsKit.URL.getUrlByServer(commonProperties.getFileDomain(), picUrl));
                            picVo.setPic(dto.getPic());
                            dto.setPicVo(picVo);
                        } else {
                            dto.setPic(StringUtils.EMPTY);
                        }
                        ResourceData dataRes = resourceDataBuService.getResourceDataById(drafts.getResDataId());
                        //先判断新版本data是否存在数据，如果存在则返回data，不存在则返回resurl
//                        if(ToolsKit.isNotEmpty(dataRes)){
//                            if(ToolsKit.isNotEmpty(dataRes.getData())){
//                                dto.setData(dataRes.getData());
//                            }else if(ToolsKit.isNotEmpty(dataRes.getResUrl())){
//                                PicVo dataVo = parsePicVoFromJson(dataRes.getResUrl());
//                                dto.setData(ToolsKit.URL.getUrlByServer(commonProperties.getFileDomain(), dataVo.getPic()));
//                            } else {
//                                dto.setData(StringUtils.EMPTY);
//                            }
//                        }else {
//                            dto.setData(StringUtils.EMPTY);
//                        }
                        if (ToolsKit.isNotEmpty(dataRes) && ToolsKit.isNotEmpty(dataRes.getResUrl())) {
                            PicVo dataVo = parsePicVoFromJson(dataRes.getResUrl());
                            dto.setData(ToolsKit.URL.getUrlByServer(commonProperties.getFileDomain(), dataVo.getPic()));
                        } else {
                            dto.setData(StringUtils.EMPTY);
                        }
                        dto.setTempletData(dataRes.getData());
                        dto.setCreateTime(drafts.getCreatetime());
                        dto.setFmtTime(ToolsKit.Date.format(drafts.getCreatetime(), "yyyy-MM-dd HH:mm"));
                        dto.setParam(drafts.getDraftsParam());
                        dto.setType(drafts.getType());
                        dto.setPlaceType(drafts.getPlaceType());
                        dto.setPrinterType(drafts.getPrinterType());

                        PaperInfoDto paperInfoDto = new PaperInfoDto();
                        PaperInfoEnums enums = PaperInfoEnums.getMap().get(PaperInfoEnums.LXZ1.getLength());
                        if (PaperInfoEnums.getMap().get(drafts.getLength() + "") != null) {
                            enums = PaperInfoEnums.getMap().get(drafts.getLength() + "");
                        }
                        paperInfoDto.setHeight(enums.getHeight());
                        paperInfoDto.setWidth(enums.getWidth());
                        paperInfoDto.setName(enums.getName());
                        paperInfoDto.setLengthType(enums.getLength());

                        // 新增
                        paperInfoDto.setPaperLength(drafts.getPaperLength());
                        paperInfoDto.setPaperType(drafts.getPaperType());
                        paperInfoDto.setPaperWidth(drafts.getPaperWidth());
                        paperInfoDto.setPaperColor(drafts.getPaperColor());

                        dto.setPaperObj(paperInfoDto);
                        dto.setPicPrint(drafts.getPicPrintUrl());
                        dto.setMaterialColumnId(drafts.getMaterialColumnId());

                        dtoList.add(dto);
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return dtoList;
    }

    /**
     * 获取打印机类型对应的纸张信息列表
     */
    public List<PrintPaperInfo> getPaperInfoListByPrinterType(String printerType) {
        if (ToolsKit.isEmpty(printerType)) {
            return null;
        }

        List<PrintPaperInfo> dtoList = new ArrayList<>();
        List<PrintPaperInfo> list = printPaperInfoBuService.getList();
        for (PrintPaperInfo info : list) {
            if (ToolUtils.hasPrinterType(info.getPrinterType(), printerType)) {
                dtoList.add(info);
            }
        }
        return dtoList;
    }

    /**
     * 添加或更新草稿箱
     */
    public Map<String, String> addOrUpdateDrafts(String id, String name, String userId, String picUrl, String picPrintUrl, String dataUrl, String type, String subType, String materialColumnId, String length, String typeId, String placeType, String paperLength, String paperWidth, String paperType, String paperColor, String printerType, String title, String isMirror, String previewPoint) {
        if (ToolsKit.isEmpty(userId)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("用户ID不能为空");
        }
        if (ToolsKit.isEmpty(name)) {
            name = "";
        }
        if (ToolsKit.isEmpty(picUrl)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("图片地址不能为空");
        }
        if (ToolsKit.isEmpty(dataUrl)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("数据信息不能为空");
        }
        if (ToolsKit.isEmpty(type)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("类型不能为空");
        }
        if (ToolsKit.isEmpty(subType)) {// 如果没有副类型，默认为编辑纸条
            subType = String.valueOf(DraftsSubTypeEnums.EDIT.getValue());
        }
        if (ToolsKit.isEmpty(length)) {
            length = "-1";
        }

        if (ToolsKit.isEmpty(placeType)) {
            placeType = "0"; //默认竖方向
        }

        if (ToolsKit.isEmpty(paperLength)) {
            paperLength = "0";
        }
        if (ToolsKit.isEmpty(paperWidth)) {
            paperWidth = "0";
        }
        if (ToolsKit.isEmpty(paperType)) {
            paperType = "1";
        }
        if (ToolsKit.isEmpty(paperColor)) {
            paperColor = "1"; // 1 默认白色
        }
        if (ToolsKit.isEmpty(isMirror)) {
            isMirror = "0";
        }

        String pic = ToolsKit.URL.replaceUrl(commonProperties.getFileDomain(), picUrl);
        String data = ToolsKit.URL.replaceUrl(commonProperties.getFileDomain(), dataUrl);
        Drafts drafts = null;
        if (ToolsKit.isNotEmpty(id)) {
            drafts = this.getDraftsById(id);
            if (ToolsKit.isNotEmpty(drafts)) {
                resourceDataBuService.updateResource(drafts.getResPicId(), pic, ResourceTypeEnums.PIC.getValue());
                resourceDataBuService.updateResource(drafts.getResDataId(), data, ResourceTypeEnums.DATA.getValue());

                draftsCacheService.removeDraftsIdToList(userId, drafts.getType(), drafts.getSubType(), Integer.parseInt(length), drafts.getId());
                drafts.setCreatetime(new Date());
                drafts.setType(Integer.parseInt(type));
                drafts.setLength(Integer.parseInt(length));

                drafts.setPaperLength(Float.parseFloat(paperLength));
                drafts.setPaperWidth(Float.parseFloat(paperWidth));
                drafts.setPaperType(Integer.parseInt(paperType));
                drafts.setPaperColor(Integer.parseInt(paperColor));
                drafts.setPrinterType(printerType);
                drafts.setPicPrintUrl(picPrintUrl);
                drafts.setTitle(title);
                drafts.setIsMirror(Integer.parseInt(isMirror));
                drafts.setPreviewPoint(previewPoint);
                drafts.setName(name);

                this.save(drafts);
                draftsCacheService.addDraftsIdToList(userId, drafts.getType(), drafts.getSubType(), drafts.getLength(), drafts.getId(), drafts.getCreatetime().getTime());
            } else {
                drafts = this.newDrafts(userId, name, Integer.parseInt(type), pic, picPrintUrl, data, Integer.parseInt(subType), materialColumnId, Integer.parseInt(length), null, Integer.parseInt(placeType), Float.parseFloat(paperLength), Float.parseFloat(paperWidth), Integer.parseInt(paperType), Integer.parseInt(paperColor), printerType, title, isMirror, previewPoint);
            }
        } else {
            drafts = this.newDrafts(userId, name, Integer.parseInt(type), pic, picPrintUrl, data, Integer.parseInt(subType), materialColumnId, Integer.parseInt(length), typeId, Integer.parseInt(placeType), Float.parseFloat(paperLength), Float.parseFloat(paperWidth), Integer.parseInt(paperType), Integer.parseInt(paperColor), printerType, title, isMirror, previewPoint);
        }
        Map<String, String> map = new HashMap<String, String>();
        map.put("id", drafts.getId());
        return map;
    }

    /**
     * 添加或更新草稿箱-新版
     */
    public Map<String, String> addOrUpdateDraftsByNew(String id, String name, String userId, String picUrl, String picPrintUrl, String data, String type, String subType, String materialColumnId, String length, String typeId, String placeType, String paperLength, String paperWidth, String paperType, String paperColor, String printerType, String title, String isMirror, String previewPoint) {
        if (ToolsKit.isEmpty(userId)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("用户ID不能为空");
        }
        if (ToolsKit.isEmpty(name)) {
            name = "";
        }
        if (ToolsKit.isEmpty(picUrl)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("图片地址不能为空");
        }
        if (ToolsKit.isEmpty(data)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("数据信息不能为空");
        }
        if (ToolsKit.isEmpty(type)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("类型不能为空");
        }
        if (ToolsKit.isEmpty(subType)) {// 如果没有副类型，默认为编辑纸条
            subType = String.valueOf(DraftsSubTypeEnums.EDIT.getValue());
        }
        if (ToolsKit.isEmpty(length)) {
            length = "-1";
        }

        if (ToolsKit.isEmpty(placeType)) {
            placeType = "0"; //默认竖方向
        }

        if (ToolsKit.isEmpty(paperLength)) {
            paperLength = "0";
        }
        if (ToolsKit.isEmpty(paperWidth)) {
            paperWidth = "0";
        }
        if (ToolsKit.isEmpty(paperType)) {
            paperType = "1";
        }
        if (ToolsKit.isEmpty(paperColor)) {
            paperColor = "1"; // 1 默认白色
        }
        if (ToolsKit.isEmpty(isMirror)) {
            isMirror = "0";
        }

        String pic = ToolsKit.URL.replaceUrl(commonProperties.getFileDomain(), picUrl);
        Drafts drafts = null;
        if (ToolsKit.isNotEmpty(id)) {
            drafts = this.getDraftsById(id);
            if (ToolsKit.isNotEmpty(drafts)) {
                resourceDataBuService.updateResourceByNew(drafts.getResPicId(), pic, ResourceTypeEnums.PIC.getValue());
                resourceDataBuService.updateResourceByNew(drafts.getResDataId(), data, ResourceTypeEnums.DATA.getValue());

                draftsCacheService.removeDraftsIdToList(userId, drafts.getType(), drafts.getSubType(), Integer.parseInt(length), drafts.getId());
                drafts.setCreatetime(new Date());
                drafts.setType(Integer.parseInt(type));
                drafts.setLength(Integer.parseInt(length));

                drafts.setPaperLength(Float.parseFloat(paperLength));
                drafts.setPaperWidth(Float.parseFloat(paperWidth));
                drafts.setPaperType(Integer.parseInt(paperType));
                drafts.setPaperColor(Integer.parseInt(paperColor));
                drafts.setPrinterType(printerType);
                drafts.setPicPrintUrl(picPrintUrl);
                drafts.setTitle(title);
                drafts.setIsMirror(Integer.parseInt(isMirror));
                drafts.setPreviewPoint(previewPoint);
                drafts.setName(name);

                this.save(drafts);
                draftsCacheService.addDraftsIdToList(userId, drafts.getType(), drafts.getSubType(), drafts.getLength(), drafts.getId(), drafts.getCreatetime().getTime());
            } else {
                drafts = this.newDraftsByNew(userId, name, Integer.parseInt(type), pic, picPrintUrl, data, Integer.parseInt(subType), materialColumnId, Integer.parseInt(length), null, Integer.parseInt(placeType), Float.parseFloat(paperLength), Float.parseFloat(paperWidth), Integer.parseInt(paperType), Integer.parseInt(paperColor), printerType, title, isMirror, previewPoint);
            }
        } else {
            drafts = this.newDraftsByNew(userId, name, Integer.parseInt(type), pic, picPrintUrl, data, Integer.parseInt(subType), materialColumnId, Integer.parseInt(length), typeId, Integer.parseInt(placeType), Float.parseFloat(paperLength), Float.parseFloat(paperWidth), Integer.parseInt(paperType), Integer.parseInt(paperColor), printerType, title, isMirror, previewPoint);
        }
        Map<String, String> map = new HashMap<String, String>();
        map.put("id", drafts.getId());
        return map;
    }

    /**
     * 创建新草稿
     */
    private Drafts newDrafts(String userId, String name, int type, String pic, String picPrintUrl, String data, int subType, String materialColumnId, int length, String typeId, int placeType, float paperLength, float paperWidth, int paperType, int paperColor, String printerType, String title, String isMirror, String previewPoint) {
        Drafts drafts = new Drafts();
        ToolsKit.setIdEntityData(drafts, userId);
        drafts.setUserId(userId);
        ResourceDataDto picRes = resourceDataBuService.saveResource(userId, pic, ResourceTypeEnums.PIC.getValue(), null);
        ResourceDataDto dataRes = resourceDataBuService.saveResource(userId, data, ResourceTypeEnums.DATA.getValue(), null);
        drafts.setResPicId(picRes.getId());
        drafts.setResDataId(dataRes.getId());
        drafts.setType(type);
        drafts.setSubType(subType);
        drafts.setLength(length);
        DraftsParamVo draftsParamVo = new DraftsParamVo();
        draftsParamVo.setTypeId(typeId);
        drafts.setDraftsParam(draftsParamToJson(draftsParamVo));
        drafts.setCreatetime(new Date());
        drafts.setPlaceType(placeType);
        drafts.setPicPrintUrl(picPrintUrl);

        drafts.setPaperType(paperType);
        drafts.setPaperWidth(paperWidth);
        drafts.setPaperLength(paperLength);
        drafts.setPaperColor(paperColor);
        drafts.setPrinterType(printerType);

        drafts.setTitle(title);
        drafts.setIsMirror(Integer.parseInt(isMirror));
        drafts.setMaterialColumnId(materialColumnId);
        drafts.setPreviewPoint(previewPoint);
        drafts.setName(name);

        this.save(drafts);
        draftsCacheService.addDraftsIdToList(userId, type, subType, length, drafts.getId(), drafts.getCreatetime().getTime());
        return drafts;
    }

    /**
     * 创建新草稿
     */
    private Drafts newDraftsByNew(String userId, String name, int type, String pic, String picPrintUrl, String data, int subType, String materialColumnId, int length, String typeId, int placeType, float paperLength, float paperWidth, int paperType, int paperColor, String printerType, String title, String isMirror, String previewPoint) {
        Drafts drafts = new Drafts();
        ToolsKit.setIdEntityData(drafts, userId);
        drafts.setUserId(userId);
        ResourceDataDto picRes = resourceDataBuService.saveResourceByNew(userId, pic, ResourceTypeEnums.PIC.getValue(), null);
        ResourceDataDto dataRes = resourceDataBuService.saveResourceByNew(userId, data, ResourceTypeEnums.DATA.getValue(), null);
        drafts.setResPicId(picRes.getId());
        drafts.setResDataId(dataRes.getId());
        drafts.setType(type);
        drafts.setSubType(subType);
        drafts.setLength(length);
        DraftsParamVo draftsParamVo = new DraftsParamVo();
        draftsParamVo.setTypeId(typeId);
        drafts.setDraftsParam(draftsParamToJson(draftsParamVo));
        drafts.setCreatetime(new Date());
        drafts.setPlaceType(placeType);
        drafts.setPicPrintUrl(picPrintUrl);

        drafts.setPaperType(paperType);
        drafts.setPaperWidth(paperWidth);
        drafts.setPaperLength(paperLength);
        drafts.setPaperColor(paperColor);
        drafts.setPrinterType(printerType);

        drafts.setTitle(title);
        drafts.setIsMirror(Integer.parseInt(isMirror));
        drafts.setMaterialColumnId(materialColumnId);
        drafts.setPreviewPoint(previewPoint);
        drafts.setName(name);

        this.save(drafts);
        draftsCacheService.addDraftsIdToList(userId, type, subType, length, drafts.getId(), drafts.getCreatetime().getTime());
        return drafts;
    }

    /**
     * 删除草稿箱
     */
    public void delDrafts(String id) {
        if (ToolsKit.isEmpty(id)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("记录ID不能为空");
        }

        // 先检查记录是否存在（不经过逻辑删除过滤）
        Drafts drafts = this.getDraftsById(id);
        if (ToolsKit.isNotEmpty(drafts)) {
            System.out.println("删除草稿箱 - ID: " + id + ", 原状态: " + drafts.getStatus());

            try {
                // 使用MyBatis-Plus的逻辑删除方法，这会自动设置@TableLogic字段
//                drafts.setStatus(ToolsConst.DATA_DELETE_STATUS);
//                drafts.setUpdatetime(new Date());
                boolean delresult = draftsDao.removeById(drafts.getId());
                System.out.println("删除草稿箱 - 逻辑删除结果: " + delresult);
//                this.deleteById(drafts.getId());
                // 清理缓存
                draftsCacheService.removeDraftsById(drafts.getId());
                draftsCacheService.removeDraftsIdToList(drafts.getUserId(), drafts.getType(), drafts.getSubType(), drafts.getLength(), drafts.getId());
                System.out.println("删除草稿箱 - 缓存清理完成");

            } catch (Exception e) {
                System.err.println("删除草稿箱失败 - ID: " + id + ", 错误: " + e.getMessage());
                e.printStackTrace();
                throw new ServiceException("删除草稿箱失败: " + e.getMessage());
            }
        } else {
            System.out.println("删除草稿箱 - 记录不存在: " + id);
            throw new ServiceException("要删除的草稿箱不存在");
        }
    }

    /**
     * 验证草稿箱删除状态（调试用）
     */
    public String verifyDraftsDeleteStatus(String id) {
        if (ToolsKit.isEmpty(id)) {
            return "ID为空";
        }

        try {
            // 由于@TableLogic注解，getById会自动过滤已删除的记录
            // 我们需要使用原生SQL或者禁用逻辑删除来查询
            Drafts drafts = draftsDao.getById(id);
            if (drafts == null) {
                // 记录可能被逻辑删除了，尝试查询所有记录（包括已删除的）
                return "记录不存在（可能已被逻辑删除）";
            }

            return "记录存在，状态: " + drafts.getStatus() + ", 用户ID: " + drafts.getUserId() + ", 类型: " + drafts.getType() + ", 创建时间: " + drafts.getCreatetime();
        } catch (Exception e) {
            return "查询失败: " + e.getMessage();
        }
    }

    /**
     * 测试删除指定ID的草稿（用于调试）
     */
    public String testDeleteDrafts(String id) {
        if (ToolsKit.isEmpty(id)) {
            return "ID为空";
        }

        try {
            // 先查看删除前的状态
            String beforeStatus = verifyDraftsDeleteStatus(id);
            System.out.println("删除前状态: " + beforeStatus);

            // 执行删除
            delDrafts(id);

            // 查看删除后的状态
            String afterStatus = verifyDraftsDeleteStatus(id);
            System.out.println("删除后状态: " + afterStatus);

            return "删除前: " + beforeStatus + " | 删除后: " + afterStatus;
        } catch (Exception e) {
            return "删除失败: " + e.getMessage();
        }
    }

    /**
     * 添加最近使用的标签纸张信息
     */
    public RecentPaperInfoDto addRecentOperation(String userId, RecentPaperInfoDto paperInfoDto) {
        if (ToolsKit.isEmpty(userId)) {
            return paperInfoDto;
        }
        paperInfoDto.setCreatetime(new Date());
        String key = CACHE_KEY_USERID_DRAFF_RECENT_PAPERINFO_LIST + userId;
        List<JSONObject> listPaperInfo = CacheKit.cache().get(key, List.class);
        if (ToolsKit.isEmpty(listPaperInfo)) {
            List<RecentPaperInfoDto> dtoList = new ArrayList<>();
            dtoList.add(paperInfoDto);
            CacheKit.cache().set(CACHE_KEY_USERID_DRAFF_RECENT_PAPERINFO_LIST + userId, dtoList, ToolsConst.MONTH_SECOND * 12);
        } else {
            // 判断是否重复
            boolean isExist = Boolean.FALSE;

            // 把取出来的JSON对象转换成RecentPaperInfoDto
            List tempPaperInfoList = new ArrayList();
            for (int i = 0; i < listPaperInfo.size(); i++) {
                RecentPaperInfoDto infoDto = JSONObject.toJavaObject(listPaperInfo.get(i), RecentPaperInfoDto.class);
                if (infoDto.equals(paperInfoDto)) {
                    isExist = true;
                    infoDto.setCreatetime(new Date());
                    infoDto.setPrinterType(paperInfoDto.getPrinterType());
                }
                tempPaperInfoList.add(infoDto);
            }

            if (isExist) {
                tempPaperInfoList.sort(new Comparator<RecentPaperInfoDto>() {
                    @Override
                    public int compare(RecentPaperInfoDto o1, RecentPaperInfoDto o2) {
                        return o1.getCreatetime().getTime() > o2.getCreatetime().getTime() ? -1 : 1;
                    }
                });
            } else {
                tempPaperInfoList.add(0, paperInfoDto);
                if (tempPaperInfoList.size() > 5) {
                    tempPaperInfoList = tempPaperInfoList.subList(0, 5);
                }
            }

            CacheKit.cache().set(CACHE_KEY_USERID_DRAFF_RECENT_PAPERINFO_LIST + userId, tempPaperInfoList, ToolsConst.MONTH_SECOND * 12);
        }
        return paperInfoDto;
    }

    /**
     * 获取最近使用的标签纸张信息列表
     */
    public List<PrintPaperInfoDto> getRecentOperationList(String userId, String printerType, Locale locale) {
        if (ToolsKit.isEmpty(userId)) {
            return new ArrayList<>();
        }
        String key = CACHE_KEY_USERID_DRAFF_RECENT_PAPERINFO_LIST + userId;
        List<JSONObject> list = CacheKit.cache().get(key, List.class);

        List<RecentPaperInfoDto> resultTemp = new ArrayList();

        if (ToolsKit.isEmpty(list)) {
            return new ArrayList<>();
        } else {
            for (JSONObject obj : list) {
                if (ToolsKit.isNotEmpty(printerType)) {
                    if (printerType.equals(obj.getString("printerType"))) {
                        resultTemp.add(JSONObject.toJavaObject(obj, RecentPaperInfoDto.class));
                    }
                } else {
                    resultTemp.add(JSONObject.toJavaObject(obj, RecentPaperInfoDto.class));
                }
            }
        }

        List<PrintPaperInfoDto> result = new ArrayList<>();
        List<PrintPaperInfoDto> paperInfoDtoList = printPaperInfoBuService.getList(locale, null);
        if (ToolsKit.isNotEmpty(resultTemp)) {
            resultTemp.forEach(item -> {
                for (PrintPaperInfoDto paperInfo : paperInfoDtoList) {
                    if (paperInfo.getColorNum() == item.getPaperColor() && Integer.parseInt(paperInfo.getType() + "" + paperInfo.getMaterial()) == item.getPaperType() && paperInfo.getHeight() == item.getPaperWidth() && (paperInfo.getWidth() == item.getPaperLength() || paperInfo.getType() == 1)) {
                        result.add(paperInfo);
                        break;
                    }
                }
            });
        }
        return result;
    }

    /**
     * 清除最近使用的标签纸张信息
     */
    public boolean clearRecentOperation(String userId) {
        if (ToolsKit.isEmpty(userId)) {
            return true;
        }
        String key = CACHE_KEY_USERID_DRAFF_RECENT_PAPERINFO_LIST + userId;
        CacheKit.cache().del(key);
        return true;
    }

    /**
     * 获取草稿箱列表（分页）- 支撑平台使用
     */
    public IPage<DraftsDto> getDraftsPage(String userId, String type, String subType, String length, int pageNo, int pageSize, String lastId) throws ServiceException {
        if (ToolsKit.isEmpty(type)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("类型不能为空");
        }

        if (pageNo > 0) {
            pageNo--;
        }
        if (ToolsKit.isEmpty(subType)) {// 如果没有副类型，默认为编辑纸条
            subType = String.valueOf(DraftsSubTypeEnums.EDIT.getValue());
        }

        IPage<Drafts> page = draftsDao.findDraftsPage(userId, Integer.parseInt(type), Integer.parseInt(subType), ToolsKit.isEmpty(length) ? null : Integer.parseInt(length), pageNo, pageSize, lastId);

        List<DraftsDto> dtoList = new ArrayList<>();
        if (page != null && ToolsKit.isNotEmpty(page.getRecords())) {
            for (Drafts drafts : page.getRecords()) {
                if (ToolsKit.isNotEmpty(drafts)) {
                    DraftsDto dto = new DraftsDto();
                    dto.setPicPrint(drafts.getPicPrintUrl());
                    dto.setId(drafts.getId());

                    ResourceData picRes = resourceDataBuService.getResourceDataById(drafts.getResPicId());
                    if (ToolsKit.isNotEmpty(picRes) && ToolsKit.isNotEmpty(picRes.getResUrl())) {
                        PicVo picVo = parsePicVoFromJson(picRes.getResUrl());
                        String picUrl = picVo.getPic();
                        if (ToolsKit.isNotEmpty(picUrl) && picUrl.contains(",")) {
                            picUrl = picUrl.replaceAll(",", "," + commonProperties.getFileDomain());
                        }
                        dto.setPic(ToolsKit.URL.getUrlByServer(commonProperties.getFileDomain(), picUrl));
                        picVo.setPic(dto.getPic());
                        dto.setPicVo(picVo);
                    } else {
                        dto.setPic(StringUtils.EMPTY);
                    }

                    ResourceData dataRes = resourceDataBuService.getResourceDataById(drafts.getResDataId());
                    if (ToolsKit.isNotEmpty(dataRes) && ToolsKit.isNotEmpty(dataRes.getResUrl())) {
                        PicVo dataVo = parsePicVoFromJson(dataRes.getResUrl());
                        dto.setData(ToolsKit.URL.getUrlByServer(commonProperties.getFileDomain(), dataVo.getPic()));
                    } else {
                        dto.setData(StringUtils.EMPTY);
                    }

                    dto.setCreateTime(drafts.getCreatetime());
                    dto.setFmtTime(ToolsKit.Date.format(drafts.getCreatetime(), "yyyy-MM-dd HH:mm"));
                    dto.setParam(drafts.getDraftsParam());
                    dto.setType(drafts.getType());
                    dto.setPlaceType(drafts.getPlaceType());
                    dto.setPrinterType(drafts.getPrinterType());

                    PaperInfoDto paperInfoDto = new PaperInfoDto();
                    PaperInfoEnums enums = PaperInfoEnums.getMap().get(PaperInfoEnums.LXZ1.getLength());
                    if (PaperInfoEnums.getMap().get(drafts.getLength() + "") != null) {
                        enums = PaperInfoEnums.getMap().get(drafts.getLength() + "");
                    }
                    paperInfoDto.setHeight(enums.getHeight());
                    paperInfoDto.setWidth(enums.getWidth());
                    paperInfoDto.setName(enums.getName());
                    paperInfoDto.setLengthType(enums.getLength());

                    // 新增
                    paperInfoDto.setPaperLength(drafts.getPaperLength());
                    paperInfoDto.setPaperType(drafts.getPaperType());
                    paperInfoDto.setPaperWidth(drafts.getPaperWidth());
                    paperInfoDto.setPaperColor(drafts.getPaperColor() == 0 ? 1 : drafts.getPaperColor());

                    dto.setPaperObj(paperInfoDto);

                    // 获取用户的codeId
                    UserLoginDto userInfo = userAccountAndInfoBuServiced.getLoginInfo(drafts.getCreateuserid());
                    if (userInfo != null) {
                        dto.setCodeId(String.valueOf(userInfo.getUserInfoDto().getCodeId()));
                    }

                    dtoList.add(dto);
                }
            }
        }

        // 创建返回的分页对象
        com.baomidou.mybatisplus.extension.plugins.pagination.Page<DraftsDto> resultPage = new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>(pageNo + 1, pageSize);
        resultPage.setRecords(dtoList);
        resultPage.setTotal(page.getTotal());
        resultPage.setCurrent(pageNo + 1);
        resultPage.setSize(pageSize);

        return resultPage;
    }
}
