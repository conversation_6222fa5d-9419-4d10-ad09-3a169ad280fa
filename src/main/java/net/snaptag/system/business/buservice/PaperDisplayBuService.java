package net.snaptag.system.business.buservice;

import com.baomidou.mybatisplus.core.metadata.IPage;
import net.snaptag.system.business.cache.PubSubCacheService;
import net.snaptag.system.business.dao.PaperDisplayDao;
import net.snaptag.system.business.entity.PaperDisplay;
import net.snaptag.system.common.DataConst;
import net.snaptag.system.sadais.core.common.exception.ServiceException;
import net.snaptag.system.sadais.util.core.ToolsKit;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2024/3/28 17:58
 * @description：
 * @modified By：
 * @version: $
 */
@Service
public class PaperDisplayBuService {
    @Autowired
    private PaperDisplayDao paperDisplayDao;
    @Autowired
    private PubSubCacheService pubSubCacheService;

    private List<PaperDisplay> globlePaperDisplayList;

    public IPage<PaperDisplay> findPage(int pageNo, int pageSize, String name) {
        if (pageNo>0){
            pageNo--;
        }
        return paperDisplayDao.getPageByCond(pageNo, pageSize, name);
    }

    public List<PaperDisplay> findAll(){
        if (ToolsKit.isEmpty(this.globlePaperDisplayList)){
            this.globlePaperDisplayList = paperDisplayDao.findListByCond();
        }
        return this.globlePaperDisplayList;
    }

    // 增,改
    public String saveOrUpdate(PaperDisplay newPaperDisplay, String userId) {
        if (ToolsKit.isNotEmpty(newPaperDisplay.getId())){
            PaperDisplay paperDisplay = paperDisplayDao.getById(newPaperDisplay.getId());
            if (paperDisplay==null){
                throw new ServiceException("未能找到对应的id记录");
            }
            paperDisplay.setI18nKey(newPaperDisplay.getI18nKey());
            paperDisplay.setLanguageCodes(newPaperDisplay.getLanguageCodes());
            paperDisplay.setName(newPaperDisplay.getName());
            paperDisplay.setPrinterTypes(newPaperDisplay.getPrinterTypes());
            paperDisplay.setPaperIds(newPaperDisplay.getPaperIds());
            paperDisplay.setTypes(newPaperDisplay.getTypes());
            paperDisplay.setSortNum(newPaperDisplay.getSortNum());
            paperDisplayDao.saveOrUpdate(paperDisplay);
        } else {
            newPaperDisplay.setId(null);
            ToolsKit.setIdEntityData(newPaperDisplay, userId);
            paperDisplayDao.saveOrUpdate(newPaperDisplay);
        }
        pubSubCacheService.updatePaperDisplayCacheData();
        return null;
    }
    // 删aa

    public String delById(String id){
        if (ToolsKit.isEmpty(id)){
            return null;
        }
        paperDisplayDao.removeById(id);
        pubSubCacheService.updatePaperDisplayCacheData();
        return null;
    }

    public void init(){
        this.globlePaperDisplayList = null;
    }
}
