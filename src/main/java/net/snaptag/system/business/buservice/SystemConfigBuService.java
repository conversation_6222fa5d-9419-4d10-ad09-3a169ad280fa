package net.snaptag.system.business.buservice;

import net.snaptag.system.business.dto.PaperInfoDto;
import net.snaptag.system.business.enums.PaperInfoEnums;
import net.snaptag.system.business.enums.PrinterTypeEnums;
import net.snaptag.system.sadais.util.core.ToolsKit;
import org.springframework.stereotype.Service;
import java.util.*;

@Service
public class SystemConfigBuService {

    /**
     *
     * @param locale
     * @return
     */
    public List<PaperInfoDto> getPaperInfoList(Locale locale, String printerType) {
        List<PaperInfoDto> paperInfoItems = new ArrayList<>();

        if (ToolsKit.isEmpty(printerType)){
            printerType = PaperInfoEnums.LXZ1.getPrinterType();
        }

        for (PaperInfoEnums enumObj: PaperInfoEnums.values()) {
            PaperInfoDto paperInfoDto = new PaperInfoDto();
            paperInfoDto.setHeight(enumObj.getHeight());
            paperInfoDto.setWidth(enumObj.getWidth());
            paperInfoDto.setName(enumObj.getName());
            paperInfoDto.setLengthType(enumObj.getLength());

            if (!printerType.equals(enumObj.getPrinterType())){
                continue;
            }

            paperInfoItems.add(paperInfoDto);
        }

//        for(String key: PaperInfoEnums.getMap().keySet()){//keySet获取map集合key的集合  然后在遍历key即可
//            PaperInfoEnums enums = PaperInfoEnums.getMap().get(key);
//            PaperInfoDto paperInfoDto = new PaperInfoDto();
//            paperInfoDto.setHeight(enums.getHeight());
//            paperInfoDto.setWidth(enums.getWidth());
//            paperInfoDto.setName(enums.getName());
//            paperInfoDto.setLengthType(enums.getLength());
//
//            if (!printerType.equals(enums.getPrinterType())){
//                continue;
//            }
//
//            paperInfoItems.add(paperInfoDto);
//        }
        return paperInfoItems;
    }

    /***
     * 获取打印机类型的名称
     * @param locale
     * @return
     */
    public List<Map<String, Object>> getPrinterTypeList(Locale locale) {
        List<Map<String, Object>> pritnerList = new ArrayList<>();
        for (PrinterTypeEnums objEnums : PrinterTypeEnums.values()) {
            Map<String, Object> map = new HashMap<>();
            map.put("name", objEnums.getName());
            map.put("code", objEnums.getCode());
            map.put("icon", objEnums.getIcon());
            pritnerList.add(map);
        }
        return pritnerList;
    }
}
