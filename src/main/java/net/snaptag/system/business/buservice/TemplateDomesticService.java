package net.snaptag.system.business.buservice;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import net.snaptag.system.business.dao.TemplateDomesticDao;
import net.snaptag.system.business.dto.TemplateDomesticDto;
import net.snaptag.system.business.entity.TemplateDomestic;
import net.snaptag.system.common.DataConst;
import net.snaptag.system.sadais.util.core.ToolsKit;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 家用行业模板业务服务类
 */
@Service
public class TemplateDomesticService {

    @Autowired
    private TemplateDomesticDao templateDomesticDao;

    /**
     * 获取家用行业模板列表
     * 参考JFinal实现思路，使用MyBatis-Plus框架
     *
     * @param pageNumber 页码
     * @param pageSize   页大小
     * @param name       模板名称（支持模糊查询）
     * @param groupId    分组ID
     * @param indexRange 尺寸范围（格式：宽度开始,宽度结束）
     * @return 模板列表
     */
    public List<TemplateDomesticDto> getDomesticTemplateList(int pageNumber, int pageSize, String name, String groupId, String indexRange, Integer paperType) {
        List<TemplateDomesticDto> result = new ArrayList<>();


        // 处理名称模糊查询（参考JFinal的实现）
        String namePattern = null;
        if (ToolsKit.String.isNotBlank(name)) {
            namePattern = name; // DAO层会处理LIKE查询
        }

        // 处理尺寸范围查询（参考JFinal的indexRange处理）
        BigDecimal widthBegin = null;
        BigDecimal widthEnd = null;
        if (ToolsKit.String.isNotBlank(indexRange)) {
            String[] indexRangeArr = indexRange.split(",");
            if (indexRangeArr.length > 0 && ToolsKit.String.isNotBlank(indexRangeArr[0])) {
                try {
                    widthBegin = new BigDecimal(indexRangeArr[0]);
                } catch (NumberFormatException e) {
                    // 忽略无效的宽度开始值
                }
            }
            if (indexRangeArr.length > 1 && ToolsKit.String.isNotBlank(indexRangeArr[1])) {
                try {
                    widthEnd = new BigDecimal(indexRangeArr[1]);
                } catch (NumberFormatException e) {
                    // 忽略无效的宽度结束值
                }
            }
        }

        // 调用DAO查询数据
        List<TemplateDomestic> list = templateDomesticDao.findTemplateDomesticListWithRange(
                groupId, namePattern, widthBegin, widthEnd, pageNumber, pageSize, paperType
        );

        // 转换为DTO
        if (ToolsKit.isNotEmpty(list)) {
            for (TemplateDomestic templateDomestic : list) {
                TemplateDomesticDto dto = convertToDto(templateDomestic);
                result.add(dto);
            }
        }

        return result;
    }

    /**
     * 获取家用-行业模板分页数据
     *
     * @param pageNumber 页码
     * @param pageSize   页大小
     * @param name       模板名称
     * @param groupId    分组ID
     * @param indexRange 尺寸范围
     * @return 分页数据
     */
    public IPage<TemplateDomesticDto> getDomesticTemplatePageList(int pageNumber, int pageSize, String name, String groupId, String indexRange) {
        String namePattern = null;
        if (ToolsKit.String.isNotBlank(name)) {
            namePattern = name;
        }

        BigDecimal widthBegin = null;
        BigDecimal widthEnd = null;
        if (ToolsKit.String.isNotBlank(indexRange)) {
            String[] indexRangeArr = indexRange.split(",");
            if (indexRangeArr.length > 0 && ToolsKit.String.isNotBlank(indexRangeArr[0])) {
                try {
                    widthBegin = new BigDecimal(indexRangeArr[0]);
                } catch (NumberFormatException e) {
                    // 忽略无效的宽度开始值
                }
            }
            if (indexRangeArr.length > 1 && ToolsKit.String.isNotBlank(indexRangeArr[1])) {
                try {
                    widthEnd = new BigDecimal(indexRangeArr[1]);
                } catch (NumberFormatException e) {
                    // 忽略无效的宽度结束值
                }
            }
        }

        // 调用DAO分页查询
        IPage<TemplateDomestic> page = templateDomesticDao.findTemplateDomesticPageWithRange(
                groupId, namePattern, widthBegin, widthEnd, pageNumber, pageSize
        );

        // 转换分页结果
        IPage<TemplateDomesticDto> resultPage = page.convert(this::convertToDto);
        return resultPage;
    }

    /**
     * 根据条件查询模板列表（不分页）
     *
     * @param userId      用户ID
     * @param type        模板类型
     * @param machineType 机器类型
     * @param paperType   纸张类型
     * @param name        模板名称
     * @return 模板列表
     */
    public List<TemplateDomesticDto> findTemplateDomesticList(Integer userId, Integer type, Integer machineType,
                                                              Integer paperType, String name) {
        List<TemplateDomestic> list = templateDomesticDao.findTemplateDomesticList(
                userId, type, machineType, paperType, null, name, 0, 0
        );

        List<TemplateDomesticDto> result = new ArrayList<>();
        if (ToolsKit.isNotEmpty(list)) {
            for (TemplateDomestic templateDomestic : list) {
                TemplateDomesticDto dto = convertToDto(templateDomestic);
                result.add(dto);
            }
        }
        return result;
    }

    /**
     * 根据ID获取模板详情
     *
     * @param id 模板ID
     * @return 模板DTO
     */
    public TemplateDomesticDto getTemplateDomesticById(String id) {
        TemplateDomestic templateDomestic = templateDomesticDao.getById(id);
        if (templateDomestic != null) {
            return convertToDto(templateDomestic);
        }
        return null;
    }

    /**
     * 保存模板
     *
     * @param templateDomestic 模板实体
     * @return 是否成功
     */
    public boolean saveTemplateDomestic(TemplateDomestic templateDomestic) {
        return templateDomesticDao.save(templateDomestic);
    }

    /**
     * 更新模板
     *
     * @param templateDomestic 模板实体
     * @return 是否成功
     */
    public boolean updateTemplateDomestic(TemplateDomestic templateDomestic) {
        return templateDomesticDao.updateById(templateDomestic);
    }

    /**
     * 删除模板
     *
     * @param id 模板ID
     * @return 是否成功
     */
    public boolean deleteTemplateDomestic(String id) {
        return templateDomesticDao.removeById(id);
    }

    /**
     * 统计模板数量
     *
     * @param userId 用户ID
     * @param type   模板类型
     * @return 数量
     */
    public long countTemplateDomestic(Integer userId, Integer type) {
        return templateDomesticDao.countByConditions(userId, type, null, null);
    }

    /**
     * 将实体转换为DTO
     *
     * @param templateDomestic 实体对象
     * @return DTO对象
     */
    private TemplateDomesticDto convertToDto(TemplateDomestic templateDomestic) {
        TemplateDomesticDto dto = new TemplateDomesticDto();

        // 手动复制属性，避免类型转换问题
        dto.setId(templateDomestic.getId());
        dto.setUserId(templateDomestic.getUserId());
        dto.setGroupId(templateDomestic.getGroupId());
        dto.setName(templateDomestic.getName());
        dto.setCover(templateDomestic.getCover());
        dto.setGap(templateDomestic.getGap());
        dto.setHeight(templateDomestic.getHeight());
        dto.setWidth(templateDomestic.getWidth());
        dto.setPaperType(templateDomestic.getPaperType());
        dto.setPrintDirection(templateDomestic.getPrintDirection());
        dto.setData(templateDomestic.getData());
        dto.setBlackLabelGap(templateDomestic.getBlackLabelGap());
        dto.setBlackLabelOffset(templateDomestic.getBlackLabelOffset());
        dto.setType(templateDomestic.getType());
        dto.setNameEn(templateDomestic.getNameEn());
        dto.setNameKor(templateDomestic.getNameKor());
        dto.setUpdateTime(templateDomestic.getUpdatetime());
        dto.setMachineType(templateDomestic.getMachineType());
        dto.setCutAfterPrint(templateDomestic.getCutAfterPrint());
        dto.setLabelNum(templateDomestic.getLabelNum());
        dto.setLabelGap(templateDomestic.getLabelGap());
        dto.setNameJP(templateDomestic.getNameJP());
        dto.setNameHK(templateDomestic.getNameHK());
        dto.setMultiLabelType(templateDomestic.getMultiLabelType());
        dto.setPaperTearType(templateDomestic.getPaperTearType());
        dto.setShareUser(templateDomestic.getShareUser());
        dto.setLabelType(templateDomestic.getLabelType());
        dto.setTicketMachineType(templateDomestic.getTicketMachineType());
        dto.setPaperFeedCount(templateDomestic.getPaperFeedCount());
        dto.setMirrorImage(templateDomestic.getMirrorImage());
        dto.setNameRU(templateDomestic.getNameRU());
        dto.setPrintType(templateDomestic.getPrintType());
        dto.setCreateTime(templateDomestic.getCreatetime());
        dto.setStatus(templateDomestic.getStatus());

        return dto;
    }

    /**
     * 调试查询 - 检查数据库状态和查询条件
     */
    public Map<String, Object> debugQuery() {
        Map<String, Object> debugInfo = new HashMap<>();

        try {
            // 1. 检查数据库中的总记录数（不加任何条件）
            long totalCount = templateDomesticDao.count();
            debugInfo.put("totalRecordsInDB", totalCount);

            // 2. 检查status字段的值分布
            QueryWrapper<TemplateDomestic> statusWrapper = new QueryWrapper<>();
            statusWrapper.select("status", "COUNT(*) as count")
                    .groupBy("status");
            List<Map<String, Object>> statusDistribution = templateDomesticDao.listMaps(statusWrapper);
            debugInfo.put("statusDistribution", statusDistribution);

            // 3. 检查DataConst.DATA_SUCCESS_STATUS的值
            debugInfo.put("DATA_SUCCESS_STATUS", DataConst.DATA_SUCCESS_STATUS);

            // 4. 查询前5条记录的基本信息
            QueryWrapper<TemplateDomestic> sampleWrapper = new QueryWrapper<>();
            sampleWrapper.last("LIMIT 5");
            List<TemplateDomestic> sampleRecords = templateDomesticDao.list(sampleWrapper);

            List<Map<String, Object>> sampleInfo = new ArrayList<>();
            for (TemplateDomestic record : sampleRecords) {
                Map<String, Object> info = new HashMap<>();
                info.put("id", record.getId());
                info.put("name", record.getName());
                info.put("status", record.getStatus());
                info.put("groupId", record.getGroupId());
                info.put("userId", record.getUserId());
                sampleInfo.add(info);
            }
            debugInfo.put("sampleRecords", sampleInfo);

            // 5. 测试不同的status条件
            QueryWrapper<TemplateDomestic> testWrapper1 = new QueryWrapper<>();
            testWrapper1.eq("status", "审核通过");
            long count1 = templateDomesticDao.count(testWrapper1);
            debugInfo.put("countWithStatus审核通过", count1);

            QueryWrapper<TemplateDomestic> testWrapper2 = new QueryWrapper<>();
            testWrapper2.eq("status", DataConst.DATA_SUCCESS_STATUS);
            long count2 = templateDomesticDao.count(testWrapper2);
            debugInfo.put("countWithDataConstStatus", count2);

            // 6. 测试表名是否正确
            debugInfo.put("tableName", "v1_templet_domestic");

        } catch (Exception e) {
            debugInfo.put("error", e.getMessage());
            debugInfo.put("errorType", e.getClass().getSimpleName());
        }

        return debugInfo;
    }
}
