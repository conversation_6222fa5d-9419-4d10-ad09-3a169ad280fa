package net.snaptag.system.business.buservice;


import net.snaptag.system.account.buservice.UserAccountAndInfoBuService;
import net.snaptag.system.account.buservice.UserAccountBuService;
import net.snaptag.system.account.buservice.UserInfoBuService;
import net.snaptag.system.business.dao.DeviceConnectLogDao;
import net.snaptag.system.business.dto.ConnectLogDto;
import net.snaptag.system.business.entity.DeviceConnectLog;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import net.snaptag.system.sadais.util.core.ToolsKit;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service
public class DeviceConnectLogBuService {
    @Autowired
    private DeviceConnectLogDao deviceConnectLogDao;
    @Autowired
    private UserInfoBuService userInfoBuService;
    @Autowired
    private UserAccountAndInfoBuService userAccountAndInfoBuService;
    @Autowired
    private UserAccountBuService userAccountBuService;

    public void addOrUpdateEntity(ConnectLogDto connectLogDto){
        DeviceConnectLog deviceConnectLog = deviceConnectLogDao.findOneByUserIdSnMac(connectLogDto.getUserId(), connectLogDto.getDeviceSn(), connectLogDto.getMacAddress());
        if (ToolsKit.isNotEmpty(connectLogDto.getDeviceName())){
            // 统一把设备名称全大写
            connectLogDto.setDeviceName(connectLogDto.getDeviceName().toUpperCase());
        }
        if (deviceConnectLog!=null && ToolsKit.isNotEmpty(deviceConnectLog.getId())) {
            ToolsKit.Bean.copyProperties(connectLogDto, deviceConnectLog);
            deviceConnectLog.setLastestTime(new Date());
            deviceConnectLogDao.saveOrUpdate(deviceConnectLog);
        } else {
            deviceConnectLog= new DeviceConnectLog();
            ToolsKit.setIdEntityData(deviceConnectLog, connectLogDto.getUserId());
            ToolsKit.Bean.copyProperties(connectLogDto, deviceConnectLog);
            deviceConnectLog.setLastestTime(new Date());
            deviceConnectLogDao.saveOrUpdate(deviceConnectLog);
        }
    }

    public Page<ConnectLogDto> findPage(int pageNo, int pageSize, String userQueryStr, String deviceSn, String deviceName, String macAddress, String lastTimeBegin, String lastTimeEnd) {
        if (pageNo>0){
            pageNo--;
        }

        Date dateBegin = ToolsKit.isNotEmpty(lastTimeBegin)?ToolsKit.Date.parse(lastTimeBegin): null;
        Date dateEnd = ToolsKit.isNotEmpty(lastTimeEnd)?ToolsKit.Date.parse(lastTimeEnd): null;

        String userId = userQueryStr;
        if (ToolsKit.isNotEmpty(userQueryStr)) {
            String tmpUserId = "";
            // 根据codeId获取
            if (ToolsKit.Number.isInteger(userQueryStr)){
                tmpUserId = userInfoBuService.getUserId(Integer.parseInt(userQueryStr));
            }

            if (ToolsKit.isNotEmpty(tmpUserId)) {
                userId = tmpUserId;
            } else {
                tmpUserId = userAccountBuService.getUserIdByAccountLike(userQueryStr);
                if (ToolsKit.isNotEmpty(tmpUserId)) {
                    userId = tmpUserId;
                }
            }
        }

        Page<ConnectLogDto> result = new Page<ConnectLogDto>(pageNo + 1, pageSize);
        try {
            IPage<DeviceConnectLog> pageList = deviceConnectLogDao.findConnLogPage(pageNo, pageSize, userId, deviceSn, deviceName,macAddress, dateBegin, dateEnd);
            result.setCurrent(pageList.getCurrent());
            result.setSize(pageList.getSize());
            result.setTotal(pageList.getTotal());
            if (ToolsKit.isNotEmpty(pageList)) {
                List<ConnectLogDto> dtoList = new ArrayList<ConnectLogDto>();
                for (DeviceConnectLog deviceConnectLog : pageList.getRecords()) {
                    ConnectLogDto dto = new ConnectLogDto();
                    ToolsKit.Bean.copyProperties(deviceConnectLog, dto);
                    if (ToolsKit.isNotEmpty(deviceConnectLog.getUserId())){
                        dto.setUserInfoDto(userAccountAndInfoBuService.getLoginInfo(deviceConnectLog.getUserId()));
                    }
                    dtoList.add(dto);
                }
                result.setRecords(dtoList);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }
}