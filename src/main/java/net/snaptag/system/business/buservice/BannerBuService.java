package net.snaptag.system.business.buservice;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import net.snaptag.system.business.cache.BannerCacheBuService;
import net.snaptag.system.business.cache.PubSubCacheService;
import net.snaptag.system.business.dao.BannerDao;
import net.snaptag.system.business.dto.BannerDto;
import net.snaptag.system.business.dto.BannerInfoDto;
import net.snaptag.system.business.entity.Banner;
import net.snaptag.system.business.entity.BannerResEnums;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import net.snaptag.system.common.DataConst;
import net.snaptag.system.sadais.core.common.enums.ExceptionEnums;
import net.snaptag.system.sadais.core.common.exception.ServiceException;
import net.snaptag.system.sadais.util.core.ToolsKit;
import net.snaptag.system.sadais.web.config.CommonProperties;
import net.snaptag.system.sadais.web.dto.HeadInfoDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * banner 服务类
 * 
 * <AUTHOR> 2018年11月20日
 */
@Service
public class BannerBuService {
    @Autowired
    private BannerDao bannerDao;
    @Autowired
    private CommonProperties commonProperties;
    @Autowired
    private BannerCacheBuService bannerCacheService;
    @Autowired
    private PubSubCacheService pubSubCacheService;

    private static final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 将JSON字符串转换为Map<String, String>
     */
    private Map<String, String> parsePicsJson(String picsJson) {
        if (picsJson == null || picsJson.trim().isEmpty()) {
            return new HashMap<>();
        }
        try {
            return objectMapper.readValue(picsJson, new TypeReference<Map<String, String>>() {});
        } catch (Exception e) {
            // 如果解析失败，返回空Map
            return new HashMap<>();
        }
    }

    /**
     * 将Map<String, String>转换为JSON字符串
     */
    private String mapToPicsJson(Map<String, String> picsMap) {
        if (picsMap == null || picsMap.isEmpty()) {
            return null;
        }
        try {
            return objectMapper.writeValueAsString(picsMap);
        } catch (Exception e) {
            // 如果转换失败，返回null
            return null;
        }
    }

    /**
     * 获取列表
     * 
     * @return 标签列表
     * @throws Exception
     */
    public List<Banner> findBannerList() {
        QueryWrapper<Banner> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", DataConst.DATA_SUCCESS_STATUS);
        queryWrapper.orderByAsc("sort");
        return bannerDao.list(queryWrapper);
    }

    /**
     * 获取banner列表
     * 
     * @param type
     *            banner类型
     * @return
     * @throws ServiceException
     */
    public List<BannerDto> getBannerList(String type, HeadInfoDto headInfoDto) throws ServiceException {
        if (ToolsKit.isEmpty(type)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("类型不能为空");
        }
        List<Banner> bannerList = bannerCacheService.getBannerList(headInfoDto.getLanguage());
        List<BannerDto> dtoList = new ArrayList<BannerDto>();
        try {
            long time = System.currentTimeMillis();
            for (Banner banner : bannerList) {
                if (type.equals(banner.getColumn())) {

                    boolean isAdd = false;
                    if (ToolsKit.isNotEmpty(banner.getStartTime()) && ToolsKit.isNotEmpty(banner.getEndTime())) {
                        if (banner.getStartTime().getTime() <= time && time < banner.getEndTime().getTime()) {
                            isAdd = true;
                        }
                    } else {
                        isAdd = true;
                    }

                    if (isAdd) {
                        BannerDto dto = new BannerDto();
                        dto.setName(banner.getName());
                        Map<String, String> picsMap = parsePicsJson(banner.getPics());
                        String picUrl = picsMap.get(BannerResEnums.P686x280.getKey());
                        dto.setPics(ToolsKit.URL.getUrlByServer(commonProperties.getFileDomain(), picUrl));
                        dto.setJumpVal(banner.getJumpVal());
                        dto.setJumpType(banner.getJumpType());
                        dto.setShareTitle(banner.getShareTitle());
                        dto.setShareContent(banner.getShareContent());
                        dto.setShareFlag(banner.getShareFlag());
                        dto.setParamAndroid(banner.getParamAndroid());
                        dto.setParamIos(banner.getParamIos());
                        dtoList.add(dto);
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return dtoList;
    }

    /**
     * 获取banner信息
     * 
     * @param id
     *            记录
     * @throws ServiceException
     */
    public BannerInfoDto getBannerInfo(String id) throws ServiceException {
        if (ToolsKit.isEmpty(id)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("版本ID不能为空");
        }
        Banner banner = bannerDao.getById(id);
        if (ToolsKit.isNotEmpty(banner)) {
            BannerInfoDto bannerInfoDto = new BannerInfoDto();
            ToolsKit.Bean.copyProperties(banner, bannerInfoDto);
            Map<String, String> picsMap = parsePicsJson(banner.getPics());
            String picUrl = picsMap.get(BannerResEnums.P686x280.getKey());
            bannerInfoDto.setPic(ToolsKit.URL.getUrlByServer(commonProperties.getFileDomain(), picUrl));
            return bannerInfoDto;
        }
        return null;
    }

    /**
     * 保存或修改banner信息
     * 
     * @param bannerInfoDto
     *            记录ID
     * @throws ServiceException
     */
    public void saveOrUpdate(BannerInfoDto bannerInfoDto) throws ServiceException {
        if (ToolsKit.isEmpty(bannerInfoDto.getPic())) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("图片资源不能为空");
        }
        if (bannerInfoDto.getJumpType() < 100) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("跳转类型参数有误");
        }
        try {
            Banner banner = null;
            if (ToolsKit.isNotEmpty(bannerInfoDto.getId())) {
                banner = bannerDao.getById(bannerInfoDto.getId());
                if (ToolsKit.isEmpty(banner)) {
                    throw new ServiceException().setCode(ExceptionEnums.ERROR.getCode()).setMessage("查询不到banner信息");
                }
            } else {
                banner = new Banner();
                ToolsKit.setIdEntityData(banner, "SYSTEM_USER_ID");
            }
            ToolsKit.Bean.copyProperties(bannerInfoDto, banner);
            Map<String, String> pics = parsePicsJson(banner.getPics());
            if (ToolsKit.isEmpty(pics)) {
                pics = new HashMap<String, String>();
            }
            pics.put(BannerResEnums.P686x280.getKey(), ToolsKit.URL.replaceUrl(commonProperties.getFileDomain(), bannerInfoDto.getPic()));
            banner.setPics(mapToPicsJson(pics));

            if (ToolsKit.isEmpty(banner.getId())){
                banner.setId(null);
            }
            bannerDao.saveOrUpdate(banner);
            pubSubCacheService.updateBannerCacheData();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 根据ID删除banner信息
     * 
     * @param id
     * @throws ServiceException
     */
    public void del(String id) throws ServiceException {
        if (ToolsKit.isEmpty(id)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("记录ID不能为空");
        }
        bannerDao.removeById(id);
        pubSubCacheService.updateBannerCacheData();
    }

    /**
     * 获取banner分页数据
     * 
     *
     * @param column
     * @param pageNo
     *            当前页码
     * @param pageSize
     *            每页大小
     * @return
     * @throws ServiceException
     */
    public Page<BannerInfoDto> findBannerPage(String name, String column, String localeCode, int pageNo, int pageSize) throws ServiceException {
        if (pageNo > 0) {
            pageNo--;
        }
        Page<BannerInfoDto> result = new Page<BannerInfoDto>(pageNo + 1, pageSize);
        try {
            IPage<Banner> pageList = bannerDao.findBannerPage(name, column, localeCode, pageNo, pageSize);
            result.setCurrent(pageList.getCurrent());
            result.setSize(pageList.getSize());
            result.setTotal(pageList.getTotal());
            if (ToolsKit.isNotEmpty(pageList.getRecords())) {
                List<BannerInfoDto> dtoList = new ArrayList<BannerInfoDto>();
                for (Banner banner : pageList.getRecords()) {
                    BannerInfoDto bannerInfoDto = new BannerInfoDto();
                    ToolsKit.Bean.copyProperties(banner, bannerInfoDto);
                    if (ToolsKit.isNotEmpty(banner.getPics())) {
                        Map<String, String> picsMap = parsePicsJson(banner.getPics());
                        String picUrl = picsMap.get(BannerResEnums.P686x280.getKey());
                        bannerInfoDto.setPic(ToolsKit.URL.getUrlByServer(commonProperties.getFileDomain(), picUrl));
                    }
                    dtoList.add(bannerInfoDto);
                }
                result.setRecords(dtoList);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }

    public List<BannerInfoDto> getBannerListAll() {
        List<Banner> bannerList = bannerDao.findAllList();
        List<BannerInfoDto> dtoList = new ArrayList<BannerInfoDto>();
        try {
            long time = System.currentTimeMillis();
            for (Banner banner : bannerList) {
                BannerInfoDto bannerInfoDto = new BannerInfoDto();
                ToolsKit.Bean.copyProperties(banner, bannerInfoDto);
                if (ToolsKit.isNotEmpty(banner.getPics())) {
                    Map<String, String> picsMap = parsePicsJson(banner.getPics());
                    String picUrl = picsMap.get(BannerResEnums.P686x280.getKey());
                    bannerInfoDto.setPic(ToolsKit.URL.getUrlByServer(commonProperties.getFileDomain(), picUrl));
                }
                dtoList.add(bannerInfoDto);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return dtoList;
    }
}