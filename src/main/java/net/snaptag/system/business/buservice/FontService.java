package net.snaptag.system.business.buservice;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import net.snaptag.system.business.dao.FontDao;
import net.snaptag.system.business.dto.FontDTO;
import net.snaptag.system.business.dto.FontQueryDTO;
import net.snaptag.system.business.entity.Font;
import net.snaptag.system.business.utils.FileUtils;
import net.snaptag.system.business.vo.FontListVO;
import net.snaptag.system.business.vo.FontVO;
import net.snaptag.system.sadais.core.common.exception.ServiceException;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDateTime;
import java.io.File;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

/**
 * 字体服务接口
 *
 * <AUTHOR>
 * @since 2025-01-16
 */
@Service
public class FontService {

    @Autowired
    private FontDao fontDao;

    @Value("${file.upload.path:/data/uploads/fonts/}")
    private String uploadPath;

    @Value("${file.upload.max-size:104857600}")
    private Long maxFileSize;

    @Value("${file.upload.allowed-types:ttf,otf,woff,woff2}")
    private String allowedTypes;

    public boolean addFont(FontDTO fontDTO) {
        // 检查字体名称是否存在
        if (existsFontName(fontDTO.getFontName(), null)) {
            throw new ServiceException("字体名称已存在");
        }

        Font font = new Font();
        BeanUtils.copyProperties(fontDTO, font);
        font.setCreateTime(LocalDateTime.now());

        return fontDao.save(font);
    }

    public boolean deleteFont(String fontId) {
        Font font = fontDao.selectById(fontId);
        if (font == null) {
            throw new ServiceException("字体不存在");
        }

        // 删除文件
        try {
            FileUtils.deleteFile(font.getFontUrl());
            if (StrUtil.isNotBlank(font.getFontCover())) {
                FileUtils.deleteFile(font.getFontCover());
            }
        } catch (Exception e) {
            System.err.format("删除字体文件失败: %s%n", e.getMessage());
        }

        return fontDao.deleteById(fontId);
    }

    public boolean updateFont(FontDTO fontDTO) {
        Font existingFont = fontDao.selectById(fontDTO.getFontId());
        if (existingFont == null) {
            throw new ServiceException("字体不存在");
        }

        // 检查字体名称是否存在（排除当前字体）
        if (existsFontName(fontDTO.getFontName(), fontDTO.getFontId())) {
            throw new ServiceException("字体名称已存在");
        }

        Font font = new Font();
        BeanUtils.copyProperties(fontDTO, font);

        return fontDao.updateById(font);
    }

    public FontVO getFontDetail(String fontId, String locale) {
        FontVO fontVO = fontDao.selectFontDetail(fontId, locale);
        if (fontVO == null) {
            throw new ServiceException("字体不存在");
        }

        // 设置下载URL
        fontVO.setDownloadUrl("/api/v1/fonts/" + fontId + "/download");

        // 设置文件大小
        try {
            File file = new File(fontVO.getFontUrl());
            if (file.exists()) {
                fontVO.setFileSize(file.length());
                fontVO.setFileSizeFormatted(FileUtil.readableFileSize(file.length()));
            }
        } catch (Exception e) {
            System.err.format("获取文件大小失败: %s%n", e.getMessage());
        }

        return fontVO;
    }

    public IPage<Font> getFontPage(FontQueryDTO queryDTO) {
        IPage<Font> page = fontDao.selectFontPage(queryDTO);
        return page;
    }

    public List<FontListVO> getFontsByLocale(String locale) {
        List<FontListVO> fonts = fontDao.selectFontsByLocale(locale);

        // 设置下载URL
        fonts.forEach(font -> {
            font.setDownloadUrl("/api/v1/fonts/" + font.getFontId() + "/download");
        });

        return fonts;
    }

    public List<Font> getFontsByKind(String fontKind, String locale) {
        List<Font> fonts = fontDao.selectFontsByKind(fontKind, locale);

        // 设置locale字段和其他处理
        fonts.forEach(font -> {
            font.setLocale(locale);
        });

        return fonts;
    }

    public List<Font> getFontsDynamic(String fontKind) {
        List<Font> fonts = fontDao.selectFontsDynamic(fontKind);

        // locale字段已经通过SQL查询获取，不需要在这里设置
        return fonts;
    }

    public List<String> getFontKinds(String locale) {
        return fontDao.selectFontKinds(locale);
    }

    public String uploadFontFile(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            return ("文件不能为空");
        }

        // 检查文件大小
        if (file.getSize() > maxFileSize) {
            return ("文件大小不能超过" + FileUtil.readableFileSize(maxFileSize));
        }

        // 检查文件类型
        String originalFilename = file.getOriginalFilename();
        if (StrUtil.isBlank(originalFilename)) {
            return ("文件名不能为空");
        }

        String fileExtension = FileUtil.extName(originalFilename).toLowerCase();
        List<String> allowedTypeList = Arrays.asList(allowedTypes.split(","));
        if (!allowedTypeList.contains(fileExtension)) {
            return ("不支持的文件类型，支持的类型: " + allowedTypes);
        }

        try {
            // 生成文件名
            String fileName = UUID.randomUUID().toString() + "." + fileExtension;
            String filePath = uploadPath + fileName;

            // 确保目录存在
            File uploadDir = new File(uploadPath);
            if (!uploadDir.exists()) {
                uploadDir.mkdirs();
            }

            // 保存文件
            File destFile = new File(filePath);
            file.transferTo(destFile);

            return filePath;

        } catch (IOException e) {
            throw new ServiceException("文件上传失败: " + e.getMessage());
        }
    }

    public void downloadFont(String fontId, HttpServletResponse response) {
        Font font = fontDao.selectById(fontId);
        if (font == null) {
            throw new ServiceException("字体不存在");
        }

        try {
            FileUtils.downloadFile(font.getFontUrl(), font.getFontName(), response);
        } catch (Exception e) {
            System.err.println("字体文件下载失败:" + e.getMessage());
            throw new ServiceException("文件下载失败: " + e.getMessage());
        }
    }

    public boolean existsFontName(String fontName, String fontId) {
        return fontDao.existsFontName(fontName, fontId);
    }

    public Long countFonts() {
        return fontDao.countFonts();
    }

}
