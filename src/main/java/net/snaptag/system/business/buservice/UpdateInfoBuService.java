package net.snaptag.system.business.buservice;

import net.snaptag.system.business.cache.SystemMsgCacheBuService;
import net.snaptag.system.business.dao.UpdateInfoDao;
import net.snaptag.system.business.dto.UpdateInfoDto;
import net.snaptag.system.business.entity.UpdateInfo;
import net.snaptag.system.sadais.core.common.core.ToolsConst;
import net.snaptag.system.sadais.core.common.enums.ExceptionEnums;
import net.snaptag.system.sadais.core.common.exception.ServiceException;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import net.snaptag.system.sadais.util.core.ToolsKit;
import net.snaptag.system.sadais.web.config.CommonProperties;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class UpdateInfoBuService {
    @Autowired
    private UpdateInfoDao updateInfoDao;
    @Autowired
    private SystemMsgCacheBuService systemMsgCacheBuService;
    @Autowired
    private CommonProperties commonProperties;

    /**
     * 获取版本更新信息列表
     * 
     * @return 版本更新信息列表
     * @throws Exception
     */
    public List<UpdateInfo> findUpdateInfoList() {
        return updateInfoDao.findUpdateInfoList();
    }

    /**
     * 获取版本更新信息
     * 
     * @return
     * @throws ServiceException
     */
    public List<UpdateInfoDto> getUpdateInfoList() throws ServiceException {
        List<UpdateInfoDto> dtoList = new ArrayList<UpdateInfoDto>();
        List<UpdateInfo> updateInfoList = systemMsgCacheBuService.getUpdateInfoList();
        if (ToolsKit.isNotEmpty(updateInfoList)) {
            for (UpdateInfo updateInfo : updateInfoList) {
                UpdateInfoDto dto=new UpdateInfoDto();
                ToolsKit.Bean.copyProperties(updateInfo, dto);
                dtoList.add(dto);
            }
        }
        return dtoList;
    }


    /* ------------------------------------支撑平台------------------------------------- */
    /**
     * 获取版本更新信息
     *
     * @param id
     *            记录
     * @throws ServiceException
     */
    public UpdateInfoDto getUpInfo(String id) throws ServiceException {
        if (ToolsKit.isEmpty(id)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("版本ID不能为空");
        }
        UpdateInfo updateInfo = updateInfoDao.getById(id);
        UpdateInfoDto updateInfoDto = new UpdateInfoDto();
        ToolsKit.Bean.copyProperties(updateInfo, updateInfoDto);
        return updateInfoDto;
    }

    /**
     * 保存或修改版本更新信息
     *
     * @param id
     *            记录ID
     * @throws ServiceException
     */
    public void saveOrUpdate(String id, String channel, String version, String url, String param, String remark, String needForceUpdate, String title, String needIndexShow) throws ServiceException {
        if (ToolsKit.isEmpty(channel)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("渠道不能为空");
        }
        if (ToolsKit.isEmpty(version)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("版本不能为空");
        }
        if (ToolsKit.isEmpty(url)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("下载地址不能为空");
        }

        UpdateInfo updateInfo = null;
        if (ToolsKit.isNotEmpty(id)) {
            updateInfo = updateInfoDao.getById(id);
            if (ToolsKit.isEmpty(updateInfo)) {
                throw new ServiceException().setCode(ExceptionEnums.ERROR.getCode()).setMessage("查询不到版本更新信息");
            }
        } else {
            updateInfo = new UpdateInfo();
            ToolsKit.setIdEntityData(updateInfo, ToolsConst.SYSTEM_USER_ID);
        }

        if (ToolsKit.isNotEmpty(needForceUpdate)){
            updateInfo.setNeedForceUpdate(Integer.parseInt(needForceUpdate));
        }

        updateInfo.setChannel(channel);
        updateInfo.setVersion(version);
        updateInfo.setUrl(ToolsKit.URL.getUrlByServer(commonProperties.getFileDomain(), url));
        updateInfo.setParam(param);
        updateInfo.setRemark(remark);
        updateInfo.setTitle(title);


        if (ToolsKit.isNotEmpty(needIndexShow)){
            updateInfo.setNeedIndexShow(Integer.parseInt(needIndexShow));
        }

        updateInfoDao.saveOrUpdate(updateInfo);
        systemMsgCacheBuService.initUpdateInfo();
    }

    /**
     * 根据ID删除版本更新信息
     *
     * @param id
     * @throws ServiceException
     */
    public void del(String id) throws ServiceException {
        if (ToolsKit.isEmpty(id)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("记录ID不能为空");
        }
        updateInfoDao.removeById(id);
        systemMsgCacheBuService.initUpdateInfo();
    }

    /**
     * 获取版本更新分页数据
     *
     * @param pageNo
     *            当前页码
     * @param pageSize
     *            每页大小
     * @return
     * @throws ServiceException
     */
    public Page<UpdateInfoDto> findUpdateInfoPage(int pageNo, int pageSize) throws ServiceException {
        if (pageNo > 0) {
            pageNo--;
        }
        Page<UpdateInfoDto> result = new Page<UpdateInfoDto>(pageNo + 1, pageSize);
        try {
            IPage<UpdateInfo> pageList = updateInfoDao.findUpdateInfoPage(pageNo, pageSize);
            result.setCurrent(pageList.getCurrent());
            result.setSize(pageList.getSize());
            result.setTotal(pageList.getTotal());
            if (ToolsKit.isNotEmpty(pageList)) {
                List<UpdateInfoDto> dtoList = new ArrayList<UpdateInfoDto>();
                for (UpdateInfo updateInfo : pageList.getRecords()) {
                    UpdateInfoDto updateInfoDto = new UpdateInfoDto();
                    ToolsKit.Bean.copyProperties(updateInfo, updateInfoDto);
                    dtoList.add(updateInfoDto);
                }
                result.setRecords(dtoList);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }
}
