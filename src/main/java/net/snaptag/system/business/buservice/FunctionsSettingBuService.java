package net.snaptag.system.business.buservice;


import net.snaptag.system.business.cache.PubSubCacheService;
import net.snaptag.system.business.dao.FunctionsSettingDao;
import net.snaptag.system.business.dto.FunctionsSettingDto;
import net.snaptag.system.business.entity.FunctionsSetting;
import net.snaptag.system.business.utils.Constant;
import net.snaptag.system.business.utils.ToolUtils;
import net.snaptag.system.sadais.util.core.ToolsKit;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2024/1/11 10:51
 * @description：
 * @modified By：
 * @version: $
 */
@Service
public class FunctionsSettingBuService {
    @Autowired
    PubSubCacheService pubSubCacheService;

    @Autowired
    private FunctionsSettingDao functionsSettingDao;
    private List<FunctionsSetting> functionsSettingList = new ArrayList<>();

    public List<FunctionsSettingDto> findListByType(String printerType, String type, String languageCode) {
         if (ToolsKit.isEmpty(functionsSettingList)){
            functionsSettingList = functionsSettingDao.findListByCond(null, null, null);
        }
        List<FunctionsSettingDto> result = new ArrayList<>();
        if (ToolsKit.isNotEmpty(functionsSettingList)){
            for (FunctionsSetting entity: functionsSettingList) {
                if (ToolsKit.isNotEmpty(entity.getPrinterTypes())
                        && ToolUtils.hasPrinterType(entity.getPrinterTypes(), printerType)
                        && ToolsKit.isNotEmpty(entity.getLanguageCodes())
                        && entity.getLanguageCodes().contains(languageCode)) {
                    FunctionsSettingDto dto = new FunctionsSettingDto();
                    ToolsKit.Bean.copyProperties(entity, dto);
                    result.add(dto);
                }
            }
        }
        return result;
    }

    public List<FunctionsSetting> findList() {
        return functionsSettingDao.findListByCond(null,null, null);
    }

    public FunctionsSetting saveOrUpdate(FunctionsSetting functionsSetting) {
        if (ToolsKit.isEmpty(functionsSetting.getId())){
            functionsSetting.setId(null);
        }
        ToolsKit.setIdEntityData(functionsSetting, Constant.DEFAULT_USER_COURSE_ID);
        functionsSettingDao.saveOrUpdate(functionsSetting);
        pubSubCacheService.updateFunctionSetting();
        return functionsSetting;
    }

    public String deleteById(String id) {
        if (ToolsKit.isNotEmpty(id)){
            functionsSettingDao.removeById(id);
        }
        pubSubCacheService.updateFunctionSetting();
        return "SUCCESS";
    }

    public void init() {
        functionsSettingList.clear();
    }
}
