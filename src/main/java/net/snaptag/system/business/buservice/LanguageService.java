package net.snaptag.system.business.buservice;

import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Locale;

/**
 * Author：Chenjy
 * Date:2025/9/5
 * Description:
 */
@Service
public class LanguageService {

    /**
     * 语言信息封装类
     */
    public static class LanguageVO {
        private String langCode;      // 语言编码（如zhCN）
        private String displayName;   // 显示名称（如"简体"）
        private String fullName;      // 完整名称（如"简体中文"）
        private Locale locale;        // 对应的Locale对象

        public LanguageVO(String langCode, String displayName, String fullName, Locale locale) {
            this.langCode = langCode;
            this.displayName = displayName;
            this.fullName = fullName;
            this.locale = locale;
        }

        // Getters
        public String getLangCode() {
            return langCode;
        }

        public String getDisplayName() {
            return displayName;
        }

        public String getFullName() {
            return fullName;
        }

        public Locale getLocale() {
            return locale;
        }
    }

    /**
     * 获取系统支持的所有语言分类列表
     *
     * @return 语言分类列表
     */
    public List<LanguageVO> getSupportedLanguages() {
        List<LanguageVO> languages = new ArrayList<>();

        // 添加简体中文
        languages.add(new LanguageVO(
                "zhCN",
                "简体",
                "简体中文",
                Locale.CHINA
        ));

        // 添加繁体中文
        languages.add(new LanguageVO(
                "zhTW",
                "繁体",
                "繁体中文",
                Locale.TRADITIONAL_CHINESE
        ));

        // 添加英语
        languages.add(new LanguageVO(
                "enUS",
                "English",
                "英语",
                Locale.ENGLISH
        ));

        // 添加日语
        languages.add(new LanguageVO(
                "jaJP",
                "日本語",
                "日语",
                Locale.JAPANESE
        ));

        // 添加韩语
        languages.add(new LanguageVO(
                "koKR",
                "한국어",
                "韩语",
                Locale.KOREAN
        ));

        return languages;
    }

    /**
     * 根据语言编码获取对应的语言信息
     *
     * @param langCode 语言编码
     * @return 语言信息，如不存在则返回null
     */
    public LanguageVO getLanguageByCode(String langCode) {
        if (langCode == null || langCode.trim().isEmpty()) {
            return null;
        }

        return getSupportedLanguages().stream()
                .filter(lang -> langCode.equalsIgnoreCase(lang.getLangCode()))
                .findFirst()
                .orElse(null);
    }
}
