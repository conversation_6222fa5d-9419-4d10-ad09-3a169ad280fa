package net.snaptag.system.business.buservice;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import net.snaptag.system.business.dto.TempletBorderQueryDTO;
import net.snaptag.system.business.entity.TemplateBorder;
import net.snaptag.system.business.entity.TemplateBorderKind;
import net.snaptag.system.business.mapper.TemplateBorderKindMapper;
import net.snaptag.system.business.mapper.TemplateBorderMapper;
import net.snaptag.system.business.utils.LanguageUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * 模板边框服务类
 *
 * <AUTHOR>
 * @since 2025-08-20
 */
@Service
public class TemplateBorderService extends ServiceImpl<TemplateBorderMapper, TemplateBorder> {

    @Autowired
    private TemplateBorderKindMapper borderKindMapper;

    // 语言类型常量
    private static final int LANGUAGE_SYSTEM = 0;
    private static final int LANGUAGE_CHINESE = 1;
    private static final int LANGUAGE_ENGLISH = 2;
    private static final int LANGUAGE_TRADITION = 3;
    private static final int LANGUAGE_KOREAN = 4;
    private static final int LANGUAGE_RUSSIAN = 5;

    /**
     * 获取所有边框列表
     *
     * @return 边框列表
     */
    public List<TemplateBorder> getBorderList() {
        List<TemplateBorder> borders = baseMapper.selectAllBorders();
        
        // 处理返回数据，移除敏感字段
        borders.forEach(border -> {
            border.removeSensitiveFields();
        });
        
        return borders;
    }

    /**
     * 获取边框分类列表（支持多语言）
     *
     * @param language 语言类型（已废弃，保持兼容性）
     * @param lang 语言标识
     * @return 分类列表
     */
    public List<TemplateBorderKind> getBorderKindList(Integer language, String lang) {
        List<TemplateBorderKind> kinds;
        
        // 优先使用lang参数
        if (StringUtils.hasText(lang)) {
            // 验证语言字段是否存在
            String columnName = lang + "Name";
            if (!borderKindMapper.isColumnExist(columnName)) {
                throw new RuntimeException(String.format("参数lang:%s错误，语言不存在！", lang));
            }
            kinds = borderKindMapper.selectKindsByLang(lang);
        } else {
            // 使用language参数（兼容旧版本）
            if (language == null) {
                language = LANGUAGE_CHINESE;
            }
            kinds = borderKindMapper.selectKindsByLanguage(language);
        }
        
        return kinds;
    }

    /**
     * 分页查询边框列表
     *
     * @param pageNumber 页码
     * @param pageSize 页大小
     * @param borderKindId 分类ID（可选）
     * @param language 语言类型
     * @return 分页结果
     */
    public IPage<TemplateBorder> getBorderPage(int pageNumber, int pageSize, String borderKindId, Integer language) {
        Page<TemplateBorder> page = new Page<>(pageNumber, pageSize);

        IPage<TemplateBorder> result;
        if (StringUtils.hasText(borderKindId)) {
            // 按分类过滤
            result = baseMapper.selectBorderPageByKind(page, borderKindId);
        } else {
            // 查询所有
            result = baseMapper.selectBorderPage(page);
        }

        // 处理返回数据，包含多语言分类名称
        result.getRecords().forEach(border -> {
            // 根据language参数获取对应语言的分类名称
            if (border.getBorderKindId() != null && !border.getBorderKindId().trim().isEmpty()) {
                TemplateBorderKind kind = borderKindMapper.selectFullKindById(border.getBorderKindId());
                if (kind != null) {
                    String localizedName = getLocalizedKindName(kind, language);
                    border.setGroupName(localizedName);
                    border.setGroupDisplayName(localizedName);
                }
            }
            border.removeSensitiveFields();
        });

        return result;
    }

    /**
     * 动态分页查询边框列表（推荐使用）
     *
     * @param pageNumber 页码
     * @param pageSize 页大小
     * @param borderKindId 分类ID（可选）
     * @return 分页结果
     */
    public IPage<TemplateBorder> getBorderPageDynamic(int pageNumber, int pageSize, String borderKindId) {
        Page<TemplateBorder> page = new Page<>(pageNumber, pageSize);
        
        IPage<TemplateBorder> result = baseMapper.selectBorderPageDynamic(page, borderKindId);
        
        // 处理返回数据
        result.getRecords().forEach(border -> {
            border.removeSensitiveFields();
        });
        
        return result;
    }

    /**
     * 根据分类ID查询边框列表
     *
     * @param borderKindId 分类ID
     * @return 边框列表
     */
    public List<TemplateBorder> getBordersByKind(String borderKindId) {
        List<TemplateBorder> borders = baseMapper.selectBordersByKind(borderKindId);
        
        // 处理返回数据
        borders.forEach(border -> {
            border.removeSensitiveFields();
        });
        
        return borders;
    }

    /**
     * 根据ID查询边框详情
     *
     * @param borderId 边框ID
     * @return 边框详情
     */
    public TemplateBorder getBorderDetail(String borderId) {
        TemplateBorder border = baseMapper.selectBorderWithKind(borderId);
        if (border != null) {
            border.removeSensitiveFields();
        }
        return border;
    }

    /**
     * 根据ID查询边框详情（包含多语言分类信息）
     *
     * @param borderId 边框ID
     * @param language 语言类型
     * @return 边框详情
     */
    public TemplateBorder getBorderDetailWithLanguage(String borderId, Integer language) {
        TemplateBorder border = baseMapper.selectBorderWithKind(borderId);
        if (border != null) {
            // 设置多语言分类信息
            TemplateBorderKind kind = borderKindMapper.selectFullKindById(border.getBorderKindId());
            if (kind != null) {
                border.setBorderKindInfo(kind, language);
            }
            border.removeSensitiveFields();
        }
        return border;
    }

    /**
     * 根据ID查询边框详情（包含多语言分类信息）
     *
     * @param borderId 边框ID
     * @param lang 语言标识
     * @return 边框详情
     */
    public TemplateBorder getBorderDetailWithLang(String borderId, String lang) {
        TemplateBorder border = baseMapper.selectBorderWithKind(borderId);
        if (border != null) {
            // 设置多语言分类信息
            TemplateBorderKind kind = borderKindMapper.selectFullKindById(border.getBorderKindId());
            if (kind != null) {
                border.setBorderKindInfo(kind, lang);
            }
            border.removeSensitiveFields();
        }
        return border;
    }

    /**
     * 统计边框数量
     *
     * @return 边框数量
     */
    public Long getBorderCount() {
        return baseMapper.countBorders();
    }

    /**
     * 根据分类统计边框数量
     *
     * @param borderKindId 分类ID
     * @return 边框数量
     */
    public Long getBorderCountByKind(String borderKindId) {
        return baseMapper.countBordersByKind(borderKindId);
    }

    /**
     * 统计分类数量
     *
     * @return 分类数量
     */
    public Long getKindCount() {
        return borderKindMapper.countKinds();
    }

    /**
     * 检查边框ID是否存在
     *
     * @param borderId 边框ID
     * @return 是否存在
     */
    public boolean existsBorderId(String borderId) {
        return baseMapper.existsBorderId(borderId);
    }

    /**
     * 验证语言字段是否存在
     *
     * @param lang 语言标识
     * @return 是否存在
     */
    public boolean isValidLanguage(String lang) {
        if (!StringUtils.hasText(lang)) {
            return false;
        }
        String columnName = lang + "Name";
        return borderKindMapper.isColumnExist(columnName);
    }

    /**
     * 根据语言类型获取本地化的分类名称
     *
     * @param kind 分类对象
     * @param language 语言类型
     * @return 本地化名称
     */
    private String getLocalizedKindName(TemplateBorderKind kind, Integer language) {
        if (kind == null) {
            return "";
        }

        if (language == null) {
            return kind.getBorderKindName();
        }

        switch (language) {
            case LANGUAGE_ENGLISH: // 2
                return StringUtils.hasText(kind.getEnglishName()) ? kind.getEnglishName() : kind.getBorderKindName();
            case LANGUAGE_TRADITION: // 3
                return StringUtils.hasText(kind.getTraditionalName()) ? kind.getTraditionalName() : kind.getBorderKindName();
            case LANGUAGE_KOREAN: // 4
                return StringUtils.hasText(kind.getKoreanName()) ? kind.getKoreanName() : kind.getBorderKindName();
            case LANGUAGE_RUSSIAN: // 5
                return StringUtils.hasText(kind.getRussianName()) ? kind.getRussianName() : kind.getBorderKindName();
            case 6: // 法语
                return StringUtils.hasText(kind.getFrenchName()) ? kind.getFrenchName() : kind.getBorderKindName();
            case 7: // 西班牙语
                return StringUtils.hasText(kind.getSpanishName()) ? kind.getSpanishName() : kind.getBorderKindName();
            case 8: // 德语
                return StringUtils.hasText(kind.getGermanyName()) ? kind.getGermanyName() : kind.getBorderKindName();
            case 9: // 意大利语
                return StringUtils.hasText(kind.getItalyName()) ? kind.getItalyName() : kind.getBorderKindName();
            default:
                return kind.getBorderKindName();
        }
    }

    /**
     * 增强的分页查询（使用DTO参数）
     *
     * @param queryDTO 查询参数DTO
     * @return 分页结果
     */
    public IPage<TemplateBorder> getBorderPageEnhanced(TempletBorderQueryDTO queryDTO) {
        Page<TemplateBorder> page = new Page<>(queryDTO.getPageNumber(), queryDTO.getPageSize());

        IPage<TemplateBorder> result;
        if (queryDTO.hasKindFilter()) {
            // 按分类过滤
            result = baseMapper.selectBorderPageByKind(page, queryDTO.getBorderKindId());
        } else {
            // 查询所有
            result = baseMapper.selectBorderPage(page);
        }

        // 处理返回数据
        result.getRecords().forEach(border -> {
            // 如果需要多语言分类信息
            if (queryDTO.getIncludeMultiLang() && border.getBorderKindId() != null) {
                TemplateBorderKind kind = borderKindMapper.selectFullKindById(border.getBorderKindId());
                if (kind != null) {
                    if (queryDTO.hasLangParam()) {
                        border.setBorderKindInfo(kind, queryDTO.getLang());
                    } else {
                        border.setBorderKindInfo(kind, queryDTO.getLanguage());
                    }
                }
            }
            border.removeSensitiveFields();
        });

        return result;
    }

    /**
     * 高级分页查询（支持更多过滤条件）
     *
     * @param queryDTO 查询参数DTO
     * @return 分页结果
     */
    public IPage<TemplateBorder> getBorderPageAdvanced(TempletBorderQueryDTO queryDTO) {
        Page<TemplateBorder> page = new Page<>(queryDTO.getPageNumber(), queryDTO.getPageSize());

        // 使用动态查询
        IPage<TemplateBorder> result = baseMapper.selectBorderPageDynamic(page,
            queryDTO.hasKindFilter() ? queryDTO.getBorderKindId() : null);

        // 处理返回数据和多语言信息
        processPageResult(result, queryDTO);

        return result;
    }

    /**
     * 处理分页结果数据
     *
     * @param result 分页结果
     * @param queryDTO 查询参数
     */
    private void processPageResult(IPage<TemplateBorder> result, TempletBorderQueryDTO queryDTO) {
        if (result == null || result.getRecords().isEmpty()) {
            return;
        }

        result.getRecords().forEach(border -> {
            // 处理多语言分类信息
            if (queryDTO.getIncludeMultiLang() && border.getBorderKindId() != null) {
                TemplateBorderKind kind = borderKindMapper.selectFullKindById(border.getBorderKindId());
                if (kind != null) {
                    if (queryDTO.hasLangParam()) {
                        border.setBorderKindInfo(kind, LanguageUtils.normalizeLang(queryDTO.getLang()));
                    } else {
                        border.setBorderKindInfo(kind, LanguageUtils.normalizeLanguageType(queryDTO.getLanguage()));
                    }
                }
            }

            // 移除敏感字段
            border.removeSensitiveFields();
        });
    }

    /**
     * 获取分页查询统计信息
     *
     * @param queryDTO 查询参数DTO
     * @return 统计信息
     */
    public java.util.Map<String, Object> getBorderPageStatistics(TempletBorderQueryDTO queryDTO) {
        java.util.Map<String, Object> statistics = new java.util.HashMap<>();

        // 总数统计
        Long totalCount = queryDTO.hasKindFilter() ?
            getBorderCountByKind(queryDTO.getBorderKindId()) : getBorderCount();

        // 分类统计
        Long kindCount = getKindCount();

        statistics.put("totalBorders", totalCount);
        statistics.put("totalKinds", kindCount);
        statistics.put("hasFilter", queryDTO.hasKindFilter());
        statistics.put("filterKindId", queryDTO.getBorderKindId());

        return statistics;
    }
}
