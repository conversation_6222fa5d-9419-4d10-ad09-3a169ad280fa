package net.snaptag.system.business.buservice;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import net.snaptag.system.business.cache.SystemMsgCacheBuService;
import net.snaptag.system.business.dao.SystemMsgDao;
import net.snaptag.system.business.dto.MsgCenterDto;
import net.snaptag.system.business.dto.MsgCenterParamDto;
import net.snaptag.system.business.dto.SystemMsgDto;
import net.snaptag.system.business.entity.SystemMsg;
import net.snaptag.system.business.enums.MsgColumnTypeEnums;
import net.snaptag.system.business.enums.SysMsgPicRatioEnums;
import net.snaptag.system.business.utils.ToolUtils;
import net.snaptag.system.business.vo.ColumnVo;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import net.snaptag.system.sadais.core.common.enums.ExceptionEnums;
import net.snaptag.system.sadais.core.common.exception.ServiceException;
import net.snaptag.system.sadais.util.core.ToolsKit;
import net.snaptag.system.sadais.web.config.CommonProperties;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 系统消息服务类
 * 
 * <AUTHOR> 2018年7月10日
 */
@Service
public class SystemMsgBuService {
    @Autowired
    private SystemMsgDao systemMsgDao;
    @Autowired
    private SystemMsgCacheBuService systemMsgCacheBuService;
    @Autowired
    private CommonProperties commonProperties;

    private static final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 将JSON字符串转换为Map<String, String>
     */
    private Map<String, String> parsePicMapFromJson(String picMapJson) {
        if (picMapJson == null || picMapJson.trim().isEmpty()) {
            return new HashMap<>();
        }
        try {
            return objectMapper.readValue(picMapJson, new TypeReference<Map<String, String>>() {});
        } catch (Exception e) {
            // 如果解析失败，返回空Map
            return new HashMap<>();
        }
    }

    /**
     * 将Map<String, String>转换为JSON字符串
     */
    private String picMapToJson(Map<String, String> picMap) {
        if (picMap == null || picMap.isEmpty()) {
            return "{}";
        }
        try {
            return objectMapper.writeValueAsString(picMap);
        } catch (Exception e) {
            return "{}";
        }
    }

    /**
     * 获取系统消息列表
     * 
     * @return 系统消息列表
     * @throws Exception
     */
    public List<SystemMsg> findSystemMsgList() {
        return systemMsgDao.findSystemMsgList();
    }

    /**
     * 获取系统消息列表
     * 
     * @param userId
     * @param page
     * @param pageSize
     * @param dtoList
     * @param columnMap
     * @param isUpdate
     * @return
     * @throws ServiceException
     */
    public boolean getSystemMsgList(String userId, int page, int pageSize, List<MsgCenterDto> dtoList, Map<Integer, ColumnVo> columnMap, boolean isUpdate, String version)
            throws ServiceException {
        ColumnVo vo = columnMap.get(MsgColumnTypeEnums.SYSTEM.getType());
        List<SystemMsg> systemMsgList = systemMsgCacheBuService.getSystemMsgList();
        for (SystemMsg systemMsg : systemMsgList) {
            if (ToolsKit.isNotEmpty(systemMsg.getVersion())) {
                if (!ToolUtils.compareVersion(systemMsg.getVersion(), version)) {
                    continue;
                }
            }

            MsgCenterDto dto = new MsgCenterDto();
            dto.setMsgTitle(systemMsg.getMsgTitle());
            dto.setMsgContent(systemMsg.getMsgContent());
            dto.setMsgType(systemMsg.getMsgType());
            dto.setMsgSubType(systemMsg.getMsgSubType());
            dto.setMsgTime(systemMsg.getStartDate());
            // 修正SystemMsg.picMap JSON字段映射
            if (ToolsKit.isNotEmpty(systemMsg.getPicMap())) {
                Map<String, String> picMap = parsePicMapFromJson(systemMsg.getPicMap());
                String picUrl = picMap.get(SysMsgPicRatioEnums.P702x200.getValue());
                if (ToolsKit.isNotEmpty(picUrl)) {
                    dto.setPic(ToolsKit.URL.getUrlByServer(commonProperties.getFileDomain(), picUrl));
                }
            }
            MsgCenterParamDto paramDto = new MsgCenterParamDto();
            if (ToolsKit.isNotEmpty(systemMsg.getJumpVal()) && ToolsKit.URL.isUrl(systemMsg.getJumpVal())) {
                paramDto.setUrl(systemMsg.getJumpVal());
            } else {
                paramDto.setId(systemMsg.getJumpVal());
            }
            dto.setParam(paramDto);
            /*if (systemMsg.getCreatetime().getTime() > vo.getLastDate().getTime()) {// 消息时间大于最后阅读时间，说明未看过
                dto.setIsRead(ToolsConst.STATUS_1);
                isUpdate = true;
            }*/
            // V2.10.0 读取列表项时就标识为已读取状态
            dto.setIsRead(1);

            dto.setParamAndroid(systemMsg.getParamAndroid());
            dto.setParamIos(systemMsg.getParamIos());

            isUpdate = true;
            dtoList.add(dto);
        }
        boolean isOver = dtoList.size() < (page * pageSize + pageSize);
        int start = page * pageSize;
        int end = page * pageSize + pageSize;
        if (isOver) {
            end = dtoList.size();
        }
        if (start > end) {
            dtoList.clear();
        } else {
            dtoList = dtoList.subList(start, end);
        }
        return isUpdate;
    }

    /**
     * 查询系统消息
     *
     * @param startDate
     *            开始时间
     * @param endDate
     *            结束时间
     * @param pageNo
     *            当前页码
     * @param pageSize
     *            每页大小
     * @return
     * @throws ServiceException
     */
    public Page<SystemMsgDto> findSystemMsgPage(String startDate, String endDate, int pageNo, int pageSize) throws ServiceException {
        Date start = null;
        Date end = null;
        if (ToolsKit.isNotEmpty(startDate)) {
            start = ToolsKit.Date.parse(startDate + " 00:00:00", DatePattern.NORM_DATETIME_PATTERN);
        }
        if (ToolsKit.isNotEmpty(endDate)) {
            end = ToolsKit.Date.parse(endDate + " 23:59:59", DatePattern.NORM_DATETIME_PATTERN);
        }
        if (pageNo > 0) {
            pageNo--;
        }
        Page<SystemMsgDto> result = new Page<SystemMsgDto>(pageNo + 1, pageSize);
        try {
            IPage<SystemMsg> pageList = systemMsgDao.findSystemMsgPage(start, end, pageNo, pageSize);
            result.setCurrent(pageList.getCurrent());
            result.setSize(pageList.getSize());
            result.setTotal(pageList.getTotal());
            if (ToolsKit.isNotEmpty(pageList.getRecords())) {
                List<SystemMsgDto> dtoList = new ArrayList<SystemMsgDto>();
                for (SystemMsg systemMsg : pageList.getRecords()) {
                    SystemMsgDto dto = new SystemMsgDto();
                    ToolsKit.Bean.copyProperties(systemMsg, dto);
                    if (ToolsKit.isNotEmpty(systemMsg.getPicMap())) {
                        // 修正SystemMsg.picMap JSON字段映射
                        Map<String, String> picMap = parsePicMapFromJson(systemMsg.getPicMap());
                        String picUrl = picMap.get(SysMsgPicRatioEnums.P702x200.getValue());
                        if (ToolsKit.isNotEmpty(picUrl)) {
                            dto.setPic(ToolsKit.URL.getUrlByServer(commonProperties.getFileDomain(), picUrl));
                        }
                    }
                    dtoList.add(dto);
                }
                result.setRecords(dtoList);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }

    /**
     * 根据ID删除系统消息
     * 
     * @param id
     * @throws ServiceException
     */
    public void del(String id) throws ServiceException {
        if (ToolsKit.isEmpty(id)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("标签ID不能为空");
        }
        systemMsgDao.removeById(id);
        systemMsgCacheBuService.initSystemMsg();
    }

    /**
     * 添加或更新系统消息
     *
     * @throws ServiceException
     */
    public void saveOrUpdate(SystemMsgDto systemMsgDto) throws ServiceException {
        if (ToolsKit.isEmpty(systemMsgDto.getMsgContent())) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("内容不能为空");
        }
        SystemMsg systemMsg = null;
        if (ToolsKit.isNotEmpty(systemMsgDto.getId())) {
            systemMsg = systemMsgDao.getById(systemMsgDto.getId());
            if (ToolsKit.isEmpty(systemMsg)) {
                throw new ServiceException().setCode(ExceptionEnums.ERROR.getCode()).setMessage("查询不到系统消息信息");
            }
            systemMsgDto.setCreatetime(systemMsg.getCreatetime());
        } else {
            systemMsg = new SystemMsg();
            ToolsKit.setIdEntityData(systemMsg, "SYSTEM_USER_ID");
        }
        ToolsKit.Bean.copyProperties(systemMsgDto, systemMsg);
        // 修正SystemMsg.picMap JSON字段映射
        if (ToolsKit.isNotEmpty(systemMsgDto.getPic())) {
            Map<String, String> picMap = parsePicMapFromJson(systemMsg.getPicMap());
            if (ToolsKit.isEmpty(picMap)) {
                picMap = new HashMap<String, String>();
            }
            String pic = ToolsKit.URL.replaceUrl(commonProperties.getFileDomain(), systemMsgDto.getPic());
            picMap.put(SysMsgPicRatioEnums.P702x200.getValue(), pic);
            systemMsg.setPicMap(picMapToJson(picMap));
        }
        if (ToolsKit.isEmpty(systemMsgDto.getId())) {
            systemMsg.setId(null);
            systemMsg.setCreatetime(new Date());
        }
        systemMsgDao.saveOrUpdate(systemMsg);
        systemMsgCacheBuService.initSystemMsg();
    }
}
