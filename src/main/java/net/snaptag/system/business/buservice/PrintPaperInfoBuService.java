package net.snaptag.system.business.buservice;

import net.snaptag.system.business.cache.PubSubCacheService;
import net.snaptag.system.business.dao.PrintPaperInfoDao;
import net.snaptag.system.business.dto.PrintPaperInfoDto;
import net.snaptag.system.business.entity.Dict;
import net.snaptag.system.business.entity.PaperDisplay;
import net.snaptag.system.business.entity.PrintPaperInfo;
import net.snaptag.system.business.utils.Constant;
import net.snaptag.system.business.utils.ToolUtils;
import net.snaptag.system.sadais.core.common.core.ToolsConst;
import net.snaptag.system.sadais.util.core.ToolsKit;
import net.snaptag.system.sadais.web.core.I18nUtils;
import net.snaptag.system.sadais.web.enums.LanguageEnums;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;

/**
 * <AUTHOR>
 * @date ：Created in 2023/7/20 14:59
 * @description：
 * @modified By：
 * @version: $
 */
@Service
public class PrintPaperInfoBuService {
    private final static String NAME_STYLE = "%s%s-%s%s-%s-%s";
    @Autowired
    private PrintPaperInfoDao printPaperInfoDao;
    @Autowired
    private I18nUtils i18nUtils;
    @Autowired
    private DictBuService dictBuService;
    @Autowired
    private PaperDisplayBuService paperDisplayBuService;

    @Autowired
    private PubSubCacheService pubSubCacheService;

    private final static String DICT_PAPER_SIZE = "paper_size";

    private List<PrintPaperInfo> globelList = new ArrayList<>();

    public List<PrintPaperInfo> getGlobelList(){
        if (ToolsKit.isEmpty(globelList)){
            globelList = printPaperInfoDao.findListOrderBySortNum();
        }
        return globelList;
    }
    public void init(){
        this.globelList = null;
    }

    public List<PrintPaperInfo> getList(){
        List<PrintPaperInfo> list = this.getGlobelList();
        return list;
    }

    public List<PrintPaperInfoDto> getList(String printerType, String type, Locale locale) {
        if (ToolsKit.isEmpty(type)){
            type="-99";
        }
        List<PrintPaperInfoDto> list = this.getList(locale, null);
        List<PrintPaperInfoDto> result = new ArrayList<>();
        String finalType = type;


        for (PrintPaperInfoDto item:list) {
            if (ToolsKit.isNotEmpty(printerType) && (item.getPrinterType().contains(printerType.toLowerCase()+";" ) || item.getPrinterType().endsWith(printerType.toLowerCase()))){
                if (item.getType()==Integer.parseInt(finalType)){
                    result.add(item);
                } else if ("-99".equals(finalType)) {
                    result.add(item);
                }
            }
        }
        return result;
    }

    public List<PrintPaperInfoDto> getList(Locale locale, String version){
        List<PrintPaperInfo> list = this.getGlobelList();

        List<PrintPaperInfoDto> result = new ArrayList<>();
        list.forEach(item -> {
            if (item.getIsHide()==1) {
                return;
            }
            PrintPaperInfoDto dto = new PrintPaperInfoDto();
            ToolsKit.Bean.copyProperties(item, dto);
            if(!"zh_CN".equals(locale.toString())){
                String zhName = i18nUtils.getKey(item.getEnTitle(), locale);
                if (ToolsKit.isNotEmpty(zhName)){
                    dto.setZhTitle(zhName);
                }
            }

            StringBuffer name = new StringBuffer("");
            Float heightTemp = dto.getHeight();

            float kuandu = (heightTemp.intValue()==1 || heightTemp.intValue()==0) ? item.getWidth() : item.getHeight();
            String labelName = item.getDirection()==1? deleteZero(item.getHeight()) + "*" + deleteZero(item.getWidth()) : deleteZero(item.getWidth()) + "*" + deleteZero(item.getHeight());

            if (ToolsKit.isNotEmpty(item.getListName())){
                name.append(item.getListName() + " " + dto.getZhTitle());
            } else {
                name.append(item.getType()==1 ? deleteZero(kuandu) + "mm" : labelName);
                name.append(" "+dto.getZhTitle());
            }

            String nameLeftTop = i18nUtils.getKey("paper_type_kuan", locale);

            if (ToolsKit.isNotEmpty(item.getListName())){
                if (LanguageEnums.ZH_CN.getKey().equals(locale.toString())) {
                    if (item.getType()==1){
                        dto.setNameLeftTop(nameLeftTop + item.getListName() + " " + dto.getZhTitle());
                    } else {
                        dto.setNameLeftTop(item.getListName() + " " + dto.getZhTitle());
                    }
                } else {
                    if (item.getType()==1){
                        dto.setNameLeftTop(nameLeftTop + item.getListName() + " ");
                    } else {
                        dto.setNameLeftTop(item.getListName());
                    }
                }
            } else {
                if (LanguageEnums.ZH_CN.getKey().equals(locale.toString())) {
                    if (item.getType()==1){
                        dto.setNameLeftTop(nameLeftTop + deleteZero(kuandu) + "mm " +dto.getZhTitle());
                    } else {
                        dto.setNameLeftTop(labelName + "mm " + dto.getZhTitle());
                    }
                } else {
                    if (item.getType()==1){
                        dto.setNameLeftTop(nameLeftTop + deleteZero(kuandu) + "mm ");
                    } else {
                        dto.setNameLeftTop(labelName + "mm ");
                    }
                }
            }

            // 默认打印色值颜色
            if (ToolsKit.isEmpty(dto.getPrintColorCode())){
                dto.setPrintColorCode("#000000");
            }

            String [] paperIds = dto.getPaperId().split("-");
            dto.setColorNum(Integer.parseInt(paperIds[paperIds.length-1]));

            dto.setName(name.toString());

            // 比3.8.0版本小的话，判断是否小数位，有小数位的都不要
//            if (!ToolUtils.compareVersion(Constant.VERSION380, version)){
//                if (ToolsKit.Number.isInteger(deleteZero(dto.getHeight()))
//                    && ToolsKit.Number.isInteger(deleteZero(dto.getWidth()))){
//                    result.add(dto);
//                }
//            } else {
//                result.add(dto);
//            }
            result.add(dto);
        });
        return result;
    }

    public List<PrintPaperInfoDto> getList(String printerType, String type, Locale locale, String version) {
        if (ToolsKit.isEmpty(type)){
            type="-99";
        }
        List<PrintPaperInfoDto> list = this.getList(locale, null);
        List<PrintPaperInfoDto> result = new ArrayList<>();
        String finalType = type;


        for (PrintPaperInfoDto item:list) {

            // 比3.8.0版本小的话，判断是否小数位，有小数位的都不要
//            if (!ToolUtils.compareVersion(Constant.VERSION380, version)){
//                if (!ToolsKit.Number.isInteger(deleteZero(item.getHeight()))
//                        || !ToolsKit.Number.isInteger(deleteZero(item.getWidth()))){
//                    continue;
//                }
//            }

            if (ToolsKit.isNotEmpty(printerType)){
                if (ToolUtils.hasPrinterType(item.getPrinterType(), printerType)){
                    if (item.getType()==Integer.parseInt(finalType)) {
                        result.add(item);
                    } else if ("-99".equals(finalType)) {
                        result.add(item);
                    }
                }
            } else if (item.getType()==Integer.parseInt(finalType)){
                result.add(item);
            } else if ("-99".equals(finalType)){
                result.add(item);
            }
        }
        return result;
    }

    public Object getSizeList(String printerType, String type, String height, Locale locale, String version) {
        if(ToolsKit.isEmpty(type)){
            type = "1";// 默认连续纸
        }
        if (ToolsKit.isEmpty(printerType)){
            printerType = "mp3";
        }

        List<PrintPaperInfoDto> list = this.getList(printerType,type,locale, version);
        List<Map<String, Object>> result = new ArrayList<>();

        List<Dict> dictList = dictBuService.findListByType(DICT_PAPER_SIZE);

        // 连续纸处理
        if ("1".equals(type)){
            for (Dict dict: dictList) {
                List<PrintPaperInfoDto> dtoList = new ArrayList<>();
                for (PrintPaperInfoDto dto: list) {
                    Float heightTemp = dto.getHeight();
                    float kuandu = (heightTemp.intValue()==1 || heightTemp.intValue()==0) ? dto.getWidth() : dto.getHeight();
                    String key = deleteZero(kuandu) + "mm";
                    if (dict.getLabel().equals(key)){
                        dtoList.add(dto);
                    }
                }
                if (dtoList.isEmpty()) {
                    continue;
                }

                Map<String, Object> lxz = new HashMap<>();
                lxz.put("name", dict.getLabel());
                lxz.put("items", dtoList);
                result.add(lxz);
            }
        } else {
            // 处理间隙纸
            for (Dict dict: dictList) {
                List<PrintPaperInfoDto> dtoList = new ArrayList<>();
                for (PrintPaperInfoDto dto: list) {
                    String labelName = dto.getDirection()==1? deleteZero(dto.getHeight()) + "*" + deleteZero(dto.getWidth()) : deleteZero(dto.getWidth()) + "*" + deleteZero(dto.getHeight());
                    if (dict.getLabel().equals(labelName)){
                        dtoList.add(dto);
                    }
                }
                if (dtoList.isEmpty()){
                    continue;
                }
                Map<String, Object> lxz = new HashMap<>();
                lxz.put("name", dict.getLabel() );
                lxz.put("items", dtoList);
                result.add(lxz);
            }
        }
        return result;
    }

    public Object getSizeList2(String printerType, String type, String height, Locale locale, String version) {
        if(ToolsKit.isEmpty(type)){
            type = "1";// 默认连续纸
        }
        if (ToolsKit.isEmpty(printerType)){
            printerType = "mp3";
        }
//        List<PrintPaperInfoDto> list = this.getList(printerType,type,locale, version);
        List<Map<String, Object>> result = new ArrayList<>();
        List<PaperDisplay> displays = this.getPaperDisplayListByCond(printerType, type, locale);
        List<PrintPaperInfoDto> list = this.getList(locale, null);
        if (ToolsKit.isNotEmpty(displays)){
            for (int i = 0; i < displays.size(); i++) {
                PaperDisplay displayItem = displays.get(i);

                List<PrintPaperInfoDto> dtoList = new ArrayList<>();
                for (int j = 0; j <list.size() ; j++) {
                    if (displayItem.getPaperIds().contains(list.get(j).getId())){
                        dtoList.add(list.get(j));
                    }
                }

                String zhName = i18nUtils.getKey(displayItem.getI18nKey(), locale);
                if (ToolsKit.isEmpty(zhName)){
                    zhName = displayItem.getName();
                }
                Map<String, Object> lxz = new HashMap<>();
                lxz.put("name", zhName );
                lxz.put("items", dtoList);
                result.add(lxz);
            }
        }
        return result;
    }

    private List<PaperDisplay> getPaperDisplayListByCond(String printerType, String type, Locale locale) {
        List<PaperDisplay> reslut = new ArrayList<>();
        List<PaperDisplay> list = paperDisplayBuService.findAll();
        if (ToolsKit.isNotEmpty(list)){
            list.forEach(item -> {
                // ToolsKit.isNotEmpty(item.getPrinterTypes()) && item.getPrinterTypes().contains(printerType.toLowerCase())
                if (ToolUtils.hasPrinterType(item.getPrinterTypes(), printerType)
                        && ToolsKit.isNotEmpty(item.getLanguageCodes()) && item.getLanguageCodes().contains(locale.toString())
                        && ToolsKit.isNotEmpty(item.getTypes()) && item.getTypes().contains(type)){
                    reslut.add(item);
                }
            });
        }
        return reslut;
    }

    public PrintPaperInfo saveOrUpdate(PrintPaperInfo printPaperInfo) {
        if (ToolsKit.isEmpty(printPaperInfo.getId())){
            printPaperInfo.setId(null);
            ToolsKit.setIdEntityData(printPaperInfo, ToolsConst.SYSTEM_USER_ID);
        }
        printPaperInfoDao.saveOrUpdate(printPaperInfo);
        pubSubCacheService.updatePaperInfoCacheData();
        return printPaperInfo;
    }

    public String delete(String id) {
        printPaperInfoDao.removeById(id);
        pubSubCacheService.updatePaperInfoCacheData();
        return "success";
    }

    public String deleteZero(float f) {
        return new BigDecimal(Float.toString(f)).stripTrailingZeros().toPlainString();
    }



}
