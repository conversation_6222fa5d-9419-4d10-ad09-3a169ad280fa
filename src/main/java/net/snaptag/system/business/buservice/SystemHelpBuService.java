package net.snaptag.system.business.buservice;

import net.snaptag.system.business.dao.SystemHelpItemDao;
import net.snaptag.system.business.entity.SystemHelpItem;
import net.snaptag.system.business.vo.SystemHelpItemVo;
import net.snaptag.system.sadais.core.common.core.ToolsConst;
import net.snaptag.system.sadais.core.common.exception.ServiceException;
import com.baomidou.mybatisplus.core.metadata.IPage;
import net.snaptag.system.sadais.util.core.ToolsKit;
import net.snaptag.system.sadais.web.core.I18nUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class SystemHelpBuService {

    @Autowired
    private SystemHelpItemDao systemHelpItemDao;

    @Autowired
    private I18nUtils i18nUtils;

//    public List<SystemHelpItem> getHelpItemList(int page, int pageSize, String type, String printerType, String language) {
//        page = page - 1;
//        if (page < 0) {
//            page = 0;
//        }
//        Page<SystemHelpItem>  pages = systemHelpItemDao.findHelpItemPage(page, pageSize, type, printerType, language);
//        if (ToolsKit.isNotEmpty(pages) && ToolsKit.isNotEmpty(pages.getResult())){
//            return pages.getResult();
//        } else {
//            return new ArrayList<>();
//        }
//    }

    /***
     * 分页增加类型
     * @param page
     * @param pageSize
     * @param type
     * @return
     */
    public List<SystemHelpItemVo> getHelpItemList(int page, int pageSize, String type, String printerType, String language) {
        List<SystemHelpItemVo> itemList = new ArrayList<>();
        page = page - 1;
        if (page < 0) {
            page = 0;
        }

        IPage<SystemHelpItem>  pages = systemHelpItemDao.findHelpItemPage(page, pageSize, type, printerType, language);
        if (ToolsKit.isNotEmpty(pages) && ToolsKit.isNotEmpty(pages.getRecords())){
            pages.getRecords().forEach(item ->{
                itemList.add(new SystemHelpItemVo(item.getTitle(), item.getUrl(), item.getCoverUrl()));
            });
            return itemList;
        } else {
            return new ArrayList<>();
        }
    }

    private void initSystemHelpItems() {
//        addSystemHelpItem("1.为什么打印出来的是白纸，没有内容？", "");
//        addSystemHelpItem("2.为什么打印不全或者有深有浅？", "");
//        addSystemHelpItem("3.扫描二维码无法连接星星机？", "");
    }

    public void addSystemHelpItem(String title, String url, String coverUrl, String sortNum, String type, String printerType, String localLanguageCode) {
        SystemHelpItem systemHelpItem = new SystemHelpItem();
        systemHelpItem.setTitle(title);
        systemHelpItem.setUrl(url);
        systemHelpItem.setType(type);

        systemHelpItem.setCoverUrl(coverUrl);
        if (ToolsKit.isNotEmpty(printerType)){
            systemHelpItem.setPrinterType(printerType.toLowerCase());
        }
        systemHelpItem.setLocalLanguageCode(localLanguageCode);

        int sort = 0;
        if (ToolsKit.isNotEmpty(sortNum) && ToolsKit.Number.isInteger(sortNum)){
            sort = Integer.parseInt(sortNum);
        }
        systemHelpItem.setSortNum(sort);
        ToolsKit.setIdEntityData(systemHelpItem, ToolsConst.SYSTEM_USER_ID);
        systemHelpItemDao.saveOrUpdate(systemHelpItem);
    }

    /**
     * 删除
     * @param id
     */
    public void deleteSystemHelp(String id){
        systemHelpItemDao.removeById(id);
    }

    /***
     * 修改
     * @param item
     */
    public void update(SystemHelpItem item){
        if (ToolsKit.isEmpty(item.getId())){
            throw new ServiceException("id 不能为空");
        }
        systemHelpItemDao.saveOrUpdate(item);
    }

    /**
     * 查询分页
     * @param page
     * @param pageSize
     * @return
     */
    public IPage<SystemHelpItem> getHelpItemPage(int page, int pageSize, String type, String printerType, String language) {
        page = page - 1;
        if (page < 0) {
            page = 0;
        }
        return systemHelpItemDao.findHelpItemPage(page,pageSize,type, printerType, language);
    }
}