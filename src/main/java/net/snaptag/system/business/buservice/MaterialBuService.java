package net.snaptag.system.business.buservice;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import net.snaptag.system.business.cache.MaterialCacheService;
import net.snaptag.system.business.cache.PubSubCacheService;
import net.snaptag.system.business.dao.MaterialDao;
import net.snaptag.system.business.dto.*;
import net.snaptag.system.business.entity.Dict;
import net.snaptag.system.business.entity.Material;
import net.snaptag.system.business.entity.MaterialResource;
import net.snaptag.system.business.enums.MaterialPicEnums;
import net.snaptag.system.business.enums.MaterialResEnums;
import net.snaptag.system.business.enums.MaterialTypeEnums;
import net.snaptag.system.business.utils.Constant;
import net.snaptag.system.business.vo.PicVo;
import com.baomidou.mybatisplus.core.metadata.IPage;
import net.snaptag.system.common.DataConst;
import net.snaptag.system.sadais.core.common.enums.ExceptionEnums;
import net.snaptag.system.sadais.core.common.exception.ServiceException;
import net.snaptag.system.sadais.util.core.ToolsKit;
import net.snaptag.system.sadais.web.common.JsonKit;
import net.snaptag.system.sadais.web.common.WebKit;
import net.snaptag.system.sadais.web.config.CommonProperties;
import net.snaptag.system.sadais.web.core.I18nUtils;
import net.snaptag.system.sadais.web.dto.PicInfoDto;
import net.snaptag.system.sadais.web.enums.LanguageEnums;
import net.snaptag.system.sadais.web.utils.WebTools;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.io.File;
import java.io.IOException;
import java.net.URL;
import java.util.*;

@Service
public class MaterialBuService {
    @Autowired
    private MaterialDao materialDao;
    @Autowired
    private MaterialCacheService materialCacheService;
    @Autowired
    private CommonProperties commonProperties;
    @Autowired
    private I18nUtils i18nUtils;
    @Autowired
    private MaterialResourceBuService materialResourceBuService;
    @Autowired
    private DictBuService dictBuService;
    @Autowired
    private LanguageService languageService;

    private static final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 将JSON字符串转换为Map
     */
    private Map<String, String> parseIconJson(String iconJson) {
        if (iconJson == null || iconJson.trim().isEmpty()) {
            return new HashMap<>();
        }
        try {
            return objectMapper.readValue(iconJson, new TypeReference<Map<String, String>>() {
            });
        } catch (Exception e) {
            // 如果解析失败，返回空Map
            return new HashMap<>();
        }
    }

    /**
     * 将JSON字符串转换为Map<String, PicVo>
     */
    private Map<String, PicVo> parseResMapJson(String resMapJson) {
        if (resMapJson == null || resMapJson.trim().isEmpty()) {
            return new HashMap<>();
        }
        try {
            return objectMapper.readValue(resMapJson, new TypeReference<Map<String, PicVo>>() {
            });
        } catch (Exception e) {
            // 如果解析失败，返回空Map
            return new HashMap<>();
        }
    }

    /**
     * 将Map<String, PicVo>转换为JSON字符串
     */
    private String mapToResMapJson(Map<String, PicVo> resMap) {
        if (resMap == null || resMap.isEmpty()) {
            return null;
        }
        try {
            return objectMapper.writeValueAsString(resMap);
        } catch (Exception e) {
            // 如果转换失败，返回null
            return null;
        }
    }

    /**
     * 将JSON字符串转换为List<String>
     */
    private List<String> parsePositionJson(String positionJson) {
        if (positionJson == null || positionJson.trim().isEmpty()) {
            return new ArrayList<>();
        }
        try {
            return objectMapper.readValue(positionJson, new TypeReference<List<String>>() {
            });
        } catch (Exception e) {
            // 如果解析失败，返回空List
            return new ArrayList<>();
        }
    }

    /**
     * 将List<String>转换为JSON字符串
     */
    private String listToPositionJson(List<String> positionList) {
        if (positionList == null || positionList.isEmpty()) {
            return null;
        }
        try {
            return objectMapper.writeValueAsString(positionList);
        } catch (Exception e) {
            // 如果转换失败，返回null
            return null;
        }
    }

    @Autowired
    private PubSubCacheService pubSubCacheService;

    private final static String DICT_MATERIAL_SUB_TYPE_ZS = "material_sub_type_zs";
    private final static String DICT_MATERIAL_SUB_TYPE_BK = "material_sub_type_bk";

    /**
     * 获取素材库列表
     *
     * @return 素材库列表
     * @throws Exception
     */
    public List<Material> findMaterialList() {
        return materialDao.findMaterialList();
    }

    /**
     * 获取素材库栏目
     *
     * @return
     */
    public List<MaterialColumnDto> getMaterialColumn(String type, String subType, String version, Locale locale, Boolean overseas) throws ServiceException {
        if (ToolsKit.isEmpty(type)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("类型不能为空");
        }

        // 如果是字体，则不要subtype
        if (MaterialTypeEnums.FRONT.getValue() == Integer.parseInt(type)) {
            subType = null;
        }

        List<MaterialColumnDto> dtoList = new ArrayList<MaterialColumnDto>();
        List<String> rootIdList = materialCacheService.getMaterialRootIdList();

        for (String id : rootIdList) {
            Material material = materialCacheService.getMaterialById(id);

            if (Integer.parseInt(type) != material.getType()) {
                continue;
            }

            MaterialTypeEnums typeEnum = MaterialTypeEnums.getByValue(Integer.parseInt(type));
            if (typeEnum != null) {
                List<String> childMaterialList = materialCacheService.getMaterialChildIdList(id);
                // 检查childMaterialList是否为空
                if (ToolsKit.isEmpty(childMaterialList)) {
                    continue; // 如果没有子材料，跳过当前循环
                }
                Material childMaterial = new Material();
                List<Dict> dictList = new ArrayList<>();
                switch (typeEnum) {
                    case TIEZHI:  // 直接使用枚举实例，无需getValue()
                        // 处理贴纸逻辑

                        childMaterial = materialCacheService.getMaterialById(childMaterialList.get(0));
                        dictList = dictBuService.findListByType(DICT_MATERIAL_SUB_TYPE_ZS);
                        for (Dict dict : dictList) {
                            dtoList.add(getDictColumn(childMaterial, dict, locale));
                        }
                        break;
                    case BIAN_KUANG:
                        // 处理边框逻辑
                        childMaterial = materialCacheService.getMaterialById(childMaterialList.get(0));
                        dictList = dictBuService.findListByType(DICT_MATERIAL_SUB_TYPE_BK);
                        for (Dict dict : dictList) {

                            dtoList.add(getDictColumn(childMaterial, dict, locale));
                        }
                        break;
                    // 其他枚举值的case...
                    default:
                        // 处理默认情况
                        // 正常判断
                        List<String> childIdList = materialCacheService.getMaterialChildIdList(id);
                        for (String childId : childIdList) {
                            childMaterial = materialCacheService.getMaterialById(childId);
                            if (ToolsKit.isNotEmpty(childMaterial) && childMaterial.getType() == Integer.parseInt(type)) {
                                MaterialColumnDto dto = new MaterialColumnDto();
                                dto.setId(childMaterial.getId());
                                dto.setName(childMaterial.getName());

                                String name = i18nUtils.getKey(childMaterial.getName(), locale);
                                if (ToolsKit.isEmpty(name)) {
                                    name = childMaterial.getLabel();
                                }
                                dto.setName(name);


                                dto.setLabel(childMaterial.getZhTitle());
                                if (childMaterial.getType() == MaterialTypeEnums.TIEZHI.getValue()) {
                                    dto.setId(childId + "_" + childMaterial.getLabel());
                                } else {
                                    dto.setId(childId);
                                }
                                dto.setType(childMaterial.getType());
                                dto.setSubType(childMaterial.getSubType());
                                dto.setHasContent(childMaterial.getHasContent());
                                if (childMaterial.getIcon() != null) {
                                    Map<String, String> iconMap = parseIconJson(childMaterial.getIcon());
                                    dto.setIcon(ToolsKit.URL.getUrlByServer(commonProperties.getFileDomain(), iconMap.get("entityUrl")));
                                }
                                dtoList.add(dto);
                            }
                        }
                }
            }
//            if (Integer.parseInt(type) == MaterialTypeEnums.TIEZHI.getValue()) {
//
//            } else if (ToolsKit.isNotEmpty(material) && material.getType() == Integer.parseInt(type)) {
//
//            }
        }
        return dtoList;
    }

    //获取Dict中的子栏目
    private MaterialColumnDto getDictColumn(Material childMaterial, Dict dict, Locale locale) {
        MaterialColumnDto dto = new MaterialColumnDto();
        dto.setId(childMaterial.getId() + "_" + dict.getValue());

        // dto.setName(dict.getLocaleCode());
        String name = i18nUtils.getKey(dict.getLocaleCode(), locale);
        if (ToolsKit.isEmpty(name)) {
            name = dict.getLabel();
        }
        dto.setName(name);
        dto.setLabel(dict.getLabel());
        dto.setType(childMaterial.getType());
        dto.setSubType(childMaterial.getSubType());
        dto.setHasContent(childMaterial.getHasContent());
        if (childMaterial.getIcon() != null) {
            dto.setIcon(ToolsKit.URL.getUrlByServer(commonProperties.getFileDomain(), dict.getIcon()));
        }
        dto.setIcons(parseIconJson(childMaterial.getIcon()));
        return dto;
    }

    /**
     * 根据类型获取素材库资源
     *
     * @param id 类型
     * @return
     */
    public List<MaterialDto> getMaterialList(String id, String userId, String length, String placeType, Locale locale, Boolean overseas, String version, String label) throws ServiceException {
        if (ToolsKit.isEmpty(id)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("类型ID不能为空");
        }
        if (id.contains("_")) {
            label = id.split("_")[1];
            id = id.split("_")[0];
        }

        if (id.contains("##")) {
            String language = id.split("##")[1];
            id = id.split("##")[0];
            if (language.equals("zhCN")) {
                locale = WebTools.getLocaleByLanguage(LanguageEnums.ZH_CN.getKey());
            } else if (language.equals("zhTW")) {
                locale = WebTools.getLocaleByLanguage(LanguageEnums.ZH_TW.getKey());
            } else if (language.equals("enUS")) {
                locale = WebTools.getLocaleByLanguage(LanguageEnums.EN_US.getKey());
            } else if (language.equals("jaJP")) {
                locale = WebTools.getLocaleByLanguage(LanguageEnums.JA_JP.getKey());
            } else if (language.equals("koKR")) {
                locale = WebTools.getLocaleByLanguage(LanguageEnums.KO_KR.getKey());
            } else {
                locale = null;//WebTools.getLocaleByLanguage(LanguageEnums.ZH_CN.getKey());
            }
        }

//        List<MaterialDto> dtoList = new ArrayList<MaterialDto>();
//        Material material = materialCacheService.getMaterialById(id);
//        MaterialDto materialDto = new MaterialDto();
        MaterialDto dtoChild = this.getMaterialDto(id, userId, length, placeType, locale, overseas, version, label);
        if (dtoChild != null) {
            return dtoChild.getMaterialDto();
        }
        return new ArrayList<>();  // 返回空列表而不是null
    }

    private MaterialDto getMaterialDto(String childId, String userId, String length, String placeType, Locale locale, Boolean overseas, String version, String label) {
        System.out.println("-------getMaterialDto--------begin");
        Material materialChild = materialCacheService.getMaterialById(childId);// 子节点对象
        if (ToolsKit.isNotEmpty(materialChild)) {
            MaterialDto dtoChild = new MaterialDto();
            dtoChild.setId(materialChild.getId());

            String name = null;
            name = materialChild.getZhTitle();
            dtoChild.setName(name);
            dtoChild.setLabel(label);
            dtoChild.setType(materialChild.getType());
            dtoChild.setSubType(materialChild.getSubType());
            dtoChild.setIsChoose(materialChild.getId().equals(Constant.CHOOSE_MATERIAL_ID) ? 1 : 0);
            List<MaterialDto> materialChildDto = new ArrayList<MaterialDto>();
            {// 其他栏目数据
                List<String> idList = materialCacheService.getMaterialResourceIdList(materialChild.getId());
                if (ToolsKit.isNotEmpty(idList)) {
                    List<Long> sortKeys = new ArrayList<>();
                    Map<Long, MaterialDto> sortMap = new HashMap<>();
                    int idx = 0;
                    for (String ids : idList) {
                        idx++;
                        MaterialResource materialResource = materialCacheService.getMaterialResourceById(ids);
                        if (ToolsKit.isNotEmpty(materialResource)) {
                            if ("tz_front".equals(materialChild.getName()) && locale != null) {
                                // 如果是字体，需要分语种,除了简体、繁体、日语、韩语外，其他归类到英语上
                                String language = locale.toString();
                                if (ToolsKit.isNotEmpty(language)
                                        && !LanguageEnums.ZH_CN.getKey().equals(language)
                                        && !LanguageEnums.ZH_TW.getKey().equals(language)
                                        && !LanguageEnums.KO_KR.getKey().equals(language)
                                        && !LanguageEnums.JA_JP.getKey().equals(language)) {
                                    language = LanguageEnums.EN_US.getKey();
                                }
                                System.out.println("language=" + language + "      localeCode=" + materialResource.getLocaleCode());
                                if (!language.equals(materialResource.getLocaleCode())) {
                                    continue;
                                }
                            }
                            if ("tz_bk".equals(materialChild.getName())) {
                                if (ToolsKit.isNotEmpty(length) && materialResource.getLength() != Integer.parseInt(length) && materialResource.getLength() != 9999) {
                                    continue;
                                }

                                if (materialResource.getPlaceType() != -1 && ToolsKit.isNotEmpty(placeType) && materialResource.getPlaceType() != Integer.parseInt(placeType)) {
                                    continue;
                                }
                            }
                            if (materialResource.getMId().equals(Constant.BK_ID)
                                    && materialResource.getPlaceType() != Integer.parseInt(placeType)) {
                                continue;
                            }
                            if (ToolsKit.isNotEmpty(label) && !"tz_front".equals(materialChild.getName())) {
//                                System.out.println(materialResource.getId()+"="+materialResource.getLabel());
                                if (ToolsKit.isEmpty(materialResource.getLabel()) || !materialResource.getLabel().contains(label)) {
                                    //System.out.println("contains="+!materialChild.getLabel().contains(label));
                                    continue;
                                }
                            }

                            MaterialDto tempDto = new MaterialDto();
                            Map<String, PicVo> map = parseResMapJson(materialResource.getResMap());
                            ResDto resDto = new ResDto();
                            if (!ToolsKit.isEmpty(map)) {
                                for (Map.Entry<String, PicVo> entry : map.entrySet()) {
                                    PicVo picVo = entry.getValue();
                                    ToolsKit.Reflect.invoke(resDto, "set" + ToolsKit.String.toUpperCaseFirstOne(entry.getKey()),
                                            new Object[]{new PicDto(ToolsKit.URL.getUrlByServer(commonProperties.getFileDomain(), picVo.getPic()),
                                                    picVo.getHeight(), picVo.getWidth(), picVo.getSize(), picVo.getStartPoint(), picVo.getEndPoint())});
                                }
                            }
                            tempDto.setId(materialResource.getId());
                            tempDto.setType(materialChild.getType());
                            tempDto.setSubType(materialChild.getSubType());
                            tempDto.setLabel(label);
                            tempDto.setResDto(resDto);
                            if (materialResource.getType() > 0) {
                                tempDto.setType(materialResource.getType());
                            }

                            if (materialResource.getIsNew() > 0) {
                                // 显示isNew的期限，showNewTimeLimit为空时，默认30天
                                Date showNewTimeLimit = ToolsKit.isNotEmpty(materialResource.getShowNewTimeLimit()) ? ToolsKit.Date.parseDate(materialResource.getShowNewTimeLimit()) : ToolsKit.Date.offsetDay(materialResource.getCreatetime(), 30);
                                Date currentDate = new Date();
                                if (showNewTimeLimit.getTime() > currentDate.getTime()) {
                                    tempDto.setIsNew(materialResource.getIsNew());
                                } else {
                                    tempDto.setIsNew(0);
                                }
                            } else {
                                tempDto.setIsNew(materialResource.getIsNew());
                            }

                            if (materialChild.getType() == MaterialTypeEnums.TIEZHI.getValue()) {
                                tempDto.setMaterialColumnId(childId + "_" + label);
                            } else {
                                tempDto.setMaterialColumnId(childId);
                            }

                            tempDto.setSortNum(materialResource.getSortNum());
                            materialChildDto.add(tempDto);
                        }
                    }

                    // 对资源按时间排倒序
//                    materialChildDto.clear();
//                    Collections.sort(sortKeys);
//                    for (int i = sortKeys.size() - 1; i >= 0; i--) {
//                        materialChildDto.add(sortMap.get(sortKeys.get(i)));
//                    }
                }
            }
            dtoChild.setMaterialDto(materialChildDto);


            // 增加图案
            if (materialChild.getIcon() != null) {
                Map<String, String> iconMap = parseIconJson(materialChild.getIcon());
                if (ToolsKit.isNotEmpty(iconMap.get(MaterialPicEnums.entityUrl.getValue()))) {
                    dtoChild.setIconUrlEntity(ToolsKit.URL.getUrlByServer(commonProperties.getFileDomain(), iconMap.get(MaterialPicEnums.entityUrl.getValue())));
                }
                if (ToolsKit.isNotEmpty(iconMap.get(MaterialPicEnums.ashUrl.getValue()))) {
                    dtoChild.setIconUrlShadow(ToolsKit.URL.getUrlByServer(commonProperties.getFileDomain(), iconMap.get(MaterialPicEnums.ashUrl.getValue())));
                }
            }

            return dtoChild;
        }
        return null;
    }

    /**
     * 根据记录ID获取用户的反馈
     *
     * @param id
     */
    public Material getById(String id) {
        return materialDao.getById(id);
    }

    /**
     * 保存素材库
     *
     * @param material
     */
    public void save(Material material) {
        if (StringUtils.isBlank(material.getId())) {

            // 添加素材库
            material.setStatus(DataConst.DATA_SUCCESS_STATUS);
            material.setCreatetime(new Date());
            materialDao.saveOrUpdate(material);
        } else {
            // 更新素材库
            material.setUpdatetime(new Date());
            materialDao.saveOrUpdate(material);
        }

        pubSubCacheService.updateMaterialResCacheData();
//        materialCacheService.initMaterial();
    }

    /**
     * 删除素材库
     *
     * @param id
     */
    public void delById(String id) {
        materialDao.removeById(id);
        pubSubCacheService.updateMaterialResCacheData();
//        materialCacheService.initMaterial();
    }

    /**
     * 获取素材选择列表
     *
     * @return
     */
    public List<MaterialSelectDto> getMaterialList(Locale locale) throws ServiceException {
        List<MaterialSelectDto> dtoList = new ArrayList<MaterialSelectDto>();
        List<String> rootList = materialCacheService.getMaterialRootIdList();
        for (String rootId : rootList) {
            List<String> childList = materialCacheService.getMaterialChildIdList(rootId);
            // 检查childList是否为空
            if (ToolsKit.isEmpty(childList)) {
                continue; // 如果没有子材料，跳过当前循环
            }
            for (int i = 0; i < childList.size(); i++) {
                Material material = materialCacheService.getMaterialById(childList.get(i));
                MaterialSelectDto dto = new MaterialSelectDto();
                dto.setId(material.getId());
                dto.setName(material.getName());
                dto.setTitle(i18nUtils.getKey(material.getName(), locale));
                dto.setZhTitle(material.getZhTitle());
                dto.setEnTitle(material.getEnTitle());
                dto.setCom(material.getCom());
                dto.setType(material.getType());
                dtoList.add(dto);
            }

        }
        return dtoList;
    }

    /**
     * 获取素材列表
     *
     * @param pageNo   当前页码
     * @param pageSize 每页大小
     * @return
     * @throws ServiceException
     */
    public Page<PageListDto> getPageList(String id, String label, int pageNo, int pageSize, String localeCode) throws ServiceException {
        if (pageNo > 0) {
            pageNo--;
        }
        Page<PageListDto> result = new Page<PageListDto>(pageNo + 1, pageSize);
        try {
            IPage<MaterialResource> pageList = materialResourceBuService.findResourcePage(id, label, pageNo, pageSize, localeCode);
            result.setCurrent(pageList.getCurrent());
            result.setSize(pageList.getSize());
            result.setTotal(pageList.getTotal());
            if (ToolsKit.isNotEmpty(pageList)) {
                List<PageListDto> dtoList = new ArrayList<PageListDto>();
                for (MaterialResource materialResource : pageList.getRecords()) {
                    Material material = materialCacheService.getMaterialById(materialResource.getMId());
                    Map<String, PicVo> resMap = parseResMapJson(materialResource.getResMap());
                    PicVo picVo = resMap.get(MaterialResEnums.LIST.getKey());
                    if (ToolsKit.isEmpty(picVo) || ToolsKit.isEmpty(picVo.getPic())) {
                        picVo = resMap.get(MaterialResEnums.RES.getKey());
                    }
                    PageListDto dto = new PageListDto();
                    dto.setId(materialResource.getId());
                    dto.setName(material.getName());
                    dto.setPic(ToolsKit.URL.getUrlByServer(commonProperties.getFileDomain(), picVo.getPic()));
                    dto.setHeight(picVo.getHeight());
                    dto.setWidth(picVo.getWidth());
                    dto.setLength(materialResource.getLength());
                    dto.setCreateTime(materialResource.getCreatetime());
                    dto.setSortNum(materialResource.getSortNum());
                    dtoList.add(dto);
//                    System.out.println("materialResource=" + JSONObject.toJSON(materialResource));
                }
                result.setRecords(dtoList);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }

    /**
     * 获取素材选择列表
     *
     * @return
     */
    public void saveOrUpdate(String id, String mId, String length, String placeType, String picDto, String isNew, String label, String sortNum, String localeCode, String showNewTimeLimit) throws ServiceException {
        if (ToolsKit.isEmpty(mId)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("素材类型不能为空");
        }
        if (ToolsKit.isEmpty(picDto)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("资源信息不能为空");
        }
        if (ToolsKit.isEmpty(placeType)) {
            placeType = "0";
        }
        MaterialResource materialResource = materialResourceBuService.getById(id);
        if (ToolsKit.isEmpty(materialResource)) {
            materialResource = new MaterialResource();
            materialResource.setStatus(DataConst.DATA_SUCCESS_STATUS);
            materialResource.setCreatetime(new Date());
            materialResource.setCreateuserid("SYSTEM_USER_ID");
        }
        Map<String, PicVo> picMap = JsonKit.jsonParseObject(picDto, Map.class);
        Map<String, PicVo> resMap = parseResMapJson(materialResource.getResMap());
        if (ToolsKit.isEmpty(resMap)) {
            resMap = new HashMap<String, PicVo>();
        }
        if (ToolsKit.isNotEmpty(picMap)) {
            for (Map.Entry<String, PicVo> entry : picMap.entrySet()) {
                PicVo picVo = resMap.get(entry.getKey());
                if (ToolsKit.isEmpty(picVo)) {
                    picVo = new PicVo();
                }
                ToolsKit.Bean.copyProperties(entry.getValue(), picVo);
                if (ToolsKit.isNotEmpty(picVo.getPic()) && (picVo.getPic().endsWith(".png") || picVo.getPic().endsWith(".jpg")
                        || picVo.getPic().endsWith(".jpeg") || picVo.getPic().endsWith(".ttf") || picVo.getPic().endsWith(".otf"))) {
                    PicInfoDto picInfoDto = WebKit.getPicInfo(ToolsKit.URL.getUrlByServer(commonProperties.getFileDomain(), picVo.getPic()));
                    if (picInfoDto.getImageHeight() > 0) {
                        picVo.setHeight(picInfoDto.getImageHeight());
                    }
                    if (picInfoDto.getImageWidth() > 0) {
                        picVo.setWidth(picInfoDto.getImageWidth());
                    }
                    picVo.setPic(ToolsKit.URL.replaceUrl(commonProperties.getFileDomain(), picVo.getPic()));
                    resMap.put(entry.getKey(), picVo);
                }
            }
            materialResource.setMId(mId);
            if (ToolsKit.isEmpty(length)) {
                length = "-1";
            }
            if (ToolsKit.isEmpty(placeType)) {
                length = "1";
            }
            materialResource.setLength(Integer.parseInt(length));
            materialResource.setPlaceType(Integer.parseInt(placeType));
            materialResource.setResMap(mapToResMapJson(resMap));

            if (ToolsKit.isNotEmpty(isNew)) {
                materialResource.setIsNew(Integer.parseInt(isNew));
            }

            if (ToolsKit.isEmpty(sortNum)) {
                sortNum = "0";
            }
            materialResource.setSortNum(Integer.parseInt(sortNum));
            materialResource.setLabel(label);
            materialResource.setLocaleCode(localeCode);
            materialResource.setShowNewTimeLimit(showNewTimeLimit);
            materialResourceBuService.save(materialResource);
            pubSubCacheService.updateMaterialResCacheData();
//            materialCacheService.initMaterial();
//            materialCacheService.initMaterialResource();
        }
    }

    public void saveOrUpdateBatch(String id, String mId, String length, String placeType, String picstr, String isNew, String label, String localeCode, String showNewTimeLimit) throws ServiceException {
        if (ToolsKit.isEmpty(picstr)) {
            return;
        }
        String[] pics = picstr.split(",");
        for (String pic : pics) {
            Map<String, PicVo> picMap = new HashMap<>();
            PicVo picVo = new PicVo();
            picVo.setPic(pic);
            picMap.put("listUrl", picVo);
            picMap.put("resUrl", picVo);
            this.saveOrUpdate(id, mId, length, placeType, JSONObject.toJSONString(picMap), isNew, label, "0", localeCode, showNewTimeLimit);
        }
    }

    /**
     * 根据ID删除素材信息
     *
     * @param id
     * @throws ServiceException
     */
    public void del(String id) throws ServiceException {
        if (ToolsKit.isEmpty(id)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("素材ID不能为空");
        }
        if (id.indexOf(",") > -1) {
            String[] ids = id.split(",");
            for (String idTemp : ids) {
                if (ToolsKit.isNotEmpty(idTemp)) {
                    materialResourceBuService.delById(idTemp);
                }
            }
        } else {
            materialResourceBuService.delById(id);
        }

        materialCacheService.initMaterial();
        materialCacheService.initMaterialResource();
    }

    /**
     * 获取素材信息
     *
     * @param id
     * @return
     * @throws ServiceException
     */
    public MaterialResourceDto getMaterial(String id) throws ServiceException {
        if (ToolsKit.isEmpty(id)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("素材ID不能为空");
        }
        MaterialResourceDto dto = new MaterialResourceDto();
        MaterialResource materialResource = materialResourceBuService.getById(id);
        dto.setmId(materialResource.getMId());
        Map<String, PicDto> resMap = new HashMap<String, PicDto>();
        Map<String, PicVo> materialResMap = parseResMapJson(materialResource.getResMap());
        for (Map.Entry<String, PicVo> entry : materialResMap.entrySet()) {
            PicDto picDto = new PicDto();
            PicVo picVo = entry.getValue();
            ToolsKit.Bean.copyProperties(picVo, picDto);
            picDto.setPic(ToolsKit.URL.getUrlByServer(commonProperties.getFileDomain(), picVo.getPic()));
            resMap.put(entry.getKey(), picDto);
        }
        dto.setLocaleCode(materialResource.getLocaleCode());
        dto.setLabel(materialResource.getLabel());
        dto.setLength(materialResource.getLength());
        dto.setPlaceType(materialResource.getPlaceType());
        dto.setResMap(resMap);
        dto.setIsNew(materialResource.getIsNew());
        dto.setSortNum(materialResource.getSortNum());
        dto.setShowNewTimeLimit(materialResource.getShowNewTimeLimit());

        return dto;
    }

    public void saveMaterial(String name, String com, String zhTitle, String enTitle, String type, String subType) {
        if (ToolsKit.isEmpty(name)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("素材库名称不能为空");
        }
        if (ToolsKit.isEmpty(zhTitle)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("素材库中文标题不能为空");
        }
        if (ToolsKit.isEmpty(enTitle)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("素材库英文标题不能为空");
        }
        if (ToolsKit.isEmpty(com)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("素材库组件不能为空");
        }
        if (ToolsKit.isEmpty(type)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("素材库类型不能为空");
        }
        if (ToolsKit.isEmpty(subType)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("素材库子类型不能为空");
        }

        Integer iType = Integer.parseInt(type);
        Integer iSubType = Integer.parseInt(subType);
        String parentId = null;
        List<Material> lists = findMaterialList();
        if (lists != null) {
            for (Material material : lists) {
                if (material.getName().equals(name)) {
                    throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("素材库已存在");
                }
                if (material.getType() == iType && material.getSubType() == iSubType) {
                    parentId = material.getId();
                }
            }
        }
        if (ToolsKit.isEmpty(parentId)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("素材库父ID不能为空");
        }

        Material materialAdd = new Material();
        materialAdd.setName(name);
        materialAdd.setCom(com);
        materialAdd.setZhTitle(zhTitle);
        materialAdd.setEnTitle(enTitle);
        materialAdd.setCreatetime(new Date());
        materialAdd.setPId(parentId);
        materialAdd.setType(0);
        materialAdd.setSubType(0);
        save(materialAdd);
    }

    public void updateMaterial(String id, String zhTitle, String enTitle) {
        if (ToolsKit.isEmpty(id)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("素材库ID不能为空");
        }
        if (ToolsKit.isEmpty(zhTitle)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("素材库中文标题不能为空");
        }
        if (ToolsKit.isEmpty(enTitle)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("素材库英文标题不能为空");
        }

        Material material = getById(id);
        if (material == null) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("指定素材库不存在");
        }
        material.setZhTitle(zhTitle);
        material.setEnTitle(enTitle);
        save(material);
    }

    public void deleteMaterial(String id) {
        if (ToolsKit.isEmpty(id)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("素材库ID不能为空");
        }
        delById(id);
    }

    public MaterialResource getMaterialResourceById(String id) {
        return materialResourceBuService.getById(id);
    }

    public String getAllMaterialResource() throws IOException {
        List<MaterialResource> list = materialResourceBuService.findMaterialResourceList();
        // 循环它，如果里面的resMap 有值
        // 循环里面的map，如果里面的pic 有值，如果不是以http开头的，则按它的相对路径，在本地d盘下的tttt，建立相对路径，下载下来
        for (MaterialResource materialResource : list) {
            Map<String, PicVo> resMap = parseResMapJson(materialResource.getResMap());
            for (Map.Entry<String, PicVo> entry : resMap.entrySet()) {
                PicVo picVo = entry.getValue();
                if (ToolsKit.isEmpty(picVo.getPic())) {
                    continue;
                }
                if (picVo.getPic().startsWith("http")) {
                    continue;
                }
                String pic = ToolsKit.URL.getUrlByServer("https://m.snaptag.top", picVo.getPic());
                // 下载它到d盘tttt目录下，建立相对路径

                String path = ToolsKit.URL.getPath(pic);
                System.out.println(path);

                String dir = path.substring(0, path.lastIndexOf("/"));
                String filePath = "D:/tttt/" + path;
                FileUtils.copyURLToFile(new URL(pic), new File(filePath));
            }
        }
        return "SUCCESS";
    }

    public void importMaterialResource(MaterialResource materialResource) {
        materialResourceBuService.save(materialResource);
        pubSubCacheService.updateMaterialResCacheData();
//        materialCacheService.initMaterial();
//        materialCacheService.initMaterialResource();
    }

    public Object getFontMaterialList(Locale locale) {
        String type = String.valueOf(MaterialTypeEnums.FRONT.getValue());
        String subType = null;

        List<MaterialColumnDto> dtoList = new ArrayList<MaterialColumnDto>();
        List<String> rootIdList = materialCacheService.getMaterialRootIdList();

        for (String id : rootIdList) {
            Material material = materialCacheService.getMaterialById(id);
            if (material.getType() != Integer.parseInt(type)) {
                continue;
            }
            List<String> childIdList = materialCacheService.getMaterialChildIdList(id);
            // 检查childIdList是否为空
            if (ToolsKit.isEmpty(childIdList)) {
                continue; // 如果没有子材料，跳过当前循环
            }

            for (String childId : childIdList) {
                Material materialChild = materialCacheService.getMaterialById(childId);
                // 如果当前subType为空，则按照之前的获取所有类型的
                MaterialColumnDto dto = new MaterialColumnDto();
                dto.setId(materialChild.getId());
                // String name = i18nUtils.getKey(material.getName(), locale);
                dto.setName(materialChild.getZhTitle());
                dto.setType(materialChild.getType());
                dto.setSubType(materialChild.getSubType());
                dto.setHasContent(materialChild.getHasContent());
                if (materialChild.getIcon() != null) {
                    Map<String, String> iconMap = parseIconJson(materialChild.getIcon());
                    dto.setIcon(ToolsKit.URL.getUrlByServer(commonProperties.getFileDomain(), iconMap.get(MaterialPicEnums.P60x60.getValue())));
                }
                dtoList.add(dto);
            }
        }

        String id = dtoList.get(0).getId();
        String label = dtoList.get(0).getLabel();

//        List<MaterialDto> dtoList = new ArrayList<MaterialDto>();
//        Material material = materialCacheService.getMaterialById(id);
//        MaterialDto materialDto = new MaterialDto();
        MaterialDto dtoChild = this.getMaterialDto(id, null, null, null, locale, null, null, label);
        if (dtoChild != null) {
            return dtoChild.getMaterialDto();
        }
        return new ArrayList<>();  // 返回空列表而不是null
    }

    public String export(String mid) {
        String downloadUrl = commonProperties.getFileDomain() + "/tmp/imageurl" + mid + ".txt";//写死
        //5fb726675baa9f55802cf75c
        IPage<MaterialResource> pageList = materialResourceBuService.findResourcePage(mid, null, 0, 999, null);
        List<MaterialResource> list = pageList.getRecords();
        if (ToolsKit.isNotEmpty(list)) {
            try {
                File file = new File("/tmp/imageurl" + mid + ".txt");
                file.createNewFile();

                List<String> urlList = new ArrayList<>();
                list.forEach(item -> {
                    Map<String, PicVo> map = parseResMapJson(item.getResMap());
                    for (Map.Entry<String, PicVo> entry : map.entrySet()) {
                        PicVo picVo = entry.getValue();
                        urlList.add(ToolsKit.URL.getUrlByServer(commonProperties.getFileDomain(), picVo.getPic()));
                        //                    System.out.println(ToolsKit.URL.getUrlByServer(commonProperties.getFileDomain(), picVo.getPic()));
                    }
                });
                ToolsKit.File.appendLines(urlList, file, "utf-8");
                System.out.println(file.getAbsolutePath());
                System.out.println("文件生成------" + file.getAbsolutePath());
                file.setReadable(true, false);
                file.setWritable(true, false);
//                OSSUtils.getInstance().putObject("xplable", "/tmp/imageurl"+mid+".txt", file.getAbsolutePath());
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return null;
    }

    public List<MaterialDto> getMaterialColumnAndList(String type, String subtype, String version, Locale locale, Boolean overseas, String length, String placeType) {

        if (ToolsKit.isEmpty(placeType)) {
            placeType = "0"; //默认竖向
        }
        if (ToolsKit.isEmpty(length)) {
            length = "-1";// 默认纸张
        }

        List<MaterialDto> result = new ArrayList<>();

        List<MaterialColumnDto> list = this.getMaterialColumn(type, subtype, version, locale, overseas);
        for (MaterialColumnDto item : list) {
            String id = item.getId();
            String label = item.getLabel();
            if (id.contains("_")) {
                label = id.split("_")[1];
                id = id.split("_")[0];
            }
            if (MaterialTypeEnums.TIEZHI.getValue() != item.getType()) {
                label = null;
            }

            if (id.contains("##")) {
                String language = id.split("##")[1];
                id = id.split("##")[0];
                if (language.equals("zhCN")) {
                    locale = WebTools.getLocaleByLanguage(LanguageEnums.ZH_CN.getKey());
                } else if (language.equals("zhTW")) {
                    locale = WebTools.getLocaleByLanguage(LanguageEnums.ZH_TW.getKey());
                } else if (language.equals("enUS")) {
                    locale = WebTools.getLocaleByLanguage(LanguageEnums.EN_US.getKey());
                } else if (language.equals("jaJP")) {
                    locale = WebTools.getLocaleByLanguage(LanguageEnums.JA_JP.getKey());
                } else if (language.equals("koKR")) {
                    locale = WebTools.getLocaleByLanguage(LanguageEnums.KO_KR.getKey());
                } else {
                    locale = null;//WebTools.getLocaleByLanguage(LanguageEnums.ZH_CN.getKey());
                }
            }

            MaterialDto dtoChild = this.getMaterialDto(id, null, length, placeType, locale, overseas, version, label);
            if (dtoChild == null || ToolsKit.isEmpty(dtoChild.getMaterialDto())) {
                continue;
            }
//            String name = i18nUtils.getKey(item.getName(), locale);
//            if (ToolsKit.isEmpty(name)){
//                name = item.getLabel();
//            }
//            dtoChild.setName(name);
            dtoChild.setName(item.getName());


            if (MaterialTypeEnums.TIEZHI.getValue() == item.getType()) {
                dtoChild.setId(item.getId() + "_" + dtoChild.getLabel());

            } else {
                dtoChild.setLabel(item.getLabel());
            }

            dtoChild.setId(item.getId());

            if (item.getIcons() != null) {
                if (ToolsKit.isNotEmpty(item.getIcons().get(MaterialPicEnums.entityUrl.getValue()))) {
                    dtoChild.setIconUrlEntity(ToolsKit.URL.getUrlByServer(commonProperties.getFileDomain(), item.getIcons().get(MaterialPicEnums.entityUrl.getValue())));
                }
                if (ToolsKit.isNotEmpty(item.getIcons().get(MaterialPicEnums.ashUrl.getValue()))) {
                    dtoChild.setIconUrlShadow(ToolsKit.URL.getUrlByServer(commonProperties.getFileDomain(), item.getIcons().get(MaterialPicEnums.ashUrl.getValue())));
                }
            }
            if (ToolsKit.isEmpty(dtoChild.getIconUrlEntity())) {
                dtoChild.setIconUrlEntity(item.getIcon());
                dtoChild.setIconUrlShadow(item.getIcon());
            }

            result.add(dtoChild);
        }
        return result;
    }

    public Object getFontMaterialListWithLanguage(String targetLanguage) {
        // 如果是字体，将其变成5种语言
        String type = String.valueOf(MaterialTypeEnums.FRONT.getValue());
        String subType = null;

        List<MaterialColumnDto> dtoList = new ArrayList<MaterialColumnDto>();
        List<String> rootIdList = materialCacheService.getMaterialRootIdList();

        for (String id : rootIdList) {
            Material material = materialCacheService.getMaterialById(id);
            if (material.getType() != Integer.parseInt(type)) {
                continue;
            }
            List<String> childIdList = materialCacheService.getMaterialChildIdList(id);
            // 检查childIdList是否为空
            if (ToolsKit.isEmpty(childIdList)) {
                continue; // 如果没有子材料，跳过当前循环
            }

            for (String childId : childIdList) {
                Material materialChild = materialCacheService.getMaterialById(childId);
                // 如果当前subType为空，则按照之前的获取所有类型的
                MaterialColumnDto dto = new MaterialColumnDto();
                dto.setId(materialChild.getId());
                // String name = i18nUtils.getKey(material.getName(), locale);
                dto.setName(materialChild.getZhTitle());
                dto.setType(materialChild.getType());
                dto.setSubType(materialChild.getSubType());
                dto.setHasContent(materialChild.getHasContent());
                if (materialChild.getIcon() != null) {
                    Map<String, String> iconMap = parseIconJson(materialChild.getIcon());
                    dto.setIcon(ToolsKit.URL.getUrlByServer(commonProperties.getFileDomain(), iconMap.get(MaterialPicEnums.P60x60.getValue())));
                }
                dtoList.add(dto);
            }
        }

        // 特殊处理
        if (ToolsKit.isEmpty(dtoList)) {
            return new ArrayList<>(); // 避免空指针
        }
        MaterialColumnDto dto = dtoList.get(0);
        String id = dto.getId();

        List<MaterialDto> dtoListNew = new ArrayList<>();

        // 定义支持的语言映射关系
        Map<String, LanguageInfo> languageMap = new HashMap<>();
        languageMap.put("zhCN", new LanguageInfo("简体", Locale.CHINA));
        languageMap.put("zhTW", new LanguageInfo("繁体", Locale.TRADITIONAL_CHINESE));
        languageMap.put("enUS", new LanguageInfo("English", Locale.ENGLISH));
        languageMap.put("jaJP", new LanguageInfo("日本語", Locale.JAPANESE));
        languageMap.put("koKR", new LanguageInfo("한국어", Locale.KOREAN));
        // 可以在这里继续添加其他语言

        // 判断是否需要返回全部语言
        boolean returnAll = ToolsKit.isEmpty(targetLanguage) || "all".equalsIgnoreCase(targetLanguage);

        // 根据条件添加语言数据
        for (Map.Entry<String, LanguageInfo> entry : languageMap.entrySet()) {
            String langCode = entry.getKey();
            LanguageInfo info = entry.getValue();

            // 如果指定了显示名称且不匹配当前语言，则跳过
            if (!returnAll && !info.getDisplayName().equals(targetLanguage)) {
                continue;
            }

            // 添加当前语言的数据
            dto.setName(info.getDisplayName());
            dto.setId(id + "##" + langCode);
            MaterialDto langDto = new MaterialDto();
            ToolsKit.Bean.copyProperties(dto, langDto);
            langDto.setMaterialDto(getMaterialList(
                    dto.getId(), null, null, null,
                    info.getLocale(), false, null, dto.getName()
            ));
            dtoListNew.add(langDto);
        }

        return dtoListNew;
    }

    public List<LanguageService.LanguageVO> getSupportedLanguages() {
        return languageService.getSupportedLanguages();
    }

    // 内部辅助类用于封装语言信息
    private static class LanguageInfo {
        private String displayName;
        private Locale locale;

        public LanguageInfo(String displayName, Locale locale) {
            this.displayName = displayName;
            this.locale = locale;
        }

        public String getDisplayName() {
            return displayName;
        }

        public Locale getLocale() {
            return locale;
        }
    }

    public Object getMaterialListWithBiankuang(Locale locale) {
        // 如果是边框，BIAN_KUANG
        String type = String.valueOf(MaterialTypeEnums.BIAN_KUANG.getValue());
        String subType = null;

        List<MaterialColumnDto> dtoList = new ArrayList<MaterialColumnDto>();
        List<String> rootIdList = materialCacheService.getMaterialRootIdList();

        for (String id : rootIdList) {
            Material material = materialCacheService.getMaterialById(id);
            if (material.getType() != Integer.parseInt(type)) {
                continue;
            }
            List<String> childIdList = materialCacheService.getMaterialChildIdList(id);
            // 检查childIdList是否为空
            if (ToolsKit.isEmpty(childIdList)) {
                continue; // 如果没有子材料，跳过当前循环
            }

            for (String childId : childIdList) {
                Material materialChild = materialCacheService.getMaterialById(childId);
                // 如果当前subType为空，则按照之前的获取所有类型的
                MaterialColumnDto dto = new MaterialColumnDto();
                dto.setId(materialChild.getId());
                // String name = i18nUtils.getKey(material.getName(), locale);
                dto.setName(materialChild.getZhTitle());
                dto.setType(materialChild.getType());
                dto.setSubType(materialChild.getSubType());
                dto.setHasContent(materialChild.getHasContent());
                if (materialChild.getIcon() != null) {
                    Map<String, String> iconMap = parseIconJson(materialChild.getIcon());
                    dto.setIcon(ToolsKit.URL.getUrlByServer(commonProperties.getFileDomain(), iconMap.get(MaterialPicEnums.P60x60.getValue())));
                }
                dtoList.add(dto);
            }
        }

        // 特殊处理
        MaterialColumnDto dto = dtoList.get(0);
        String id = dto.getId();

        List<MaterialDto> dtoListNew = new ArrayList<>();

        // 获取字典项中关于边框的
        List<Dict> dictList = dictBuService.findListByType(DICT_MATERIAL_SUB_TYPE_BK);
        for (int i = 0; i < dictList.size(); i++) {
            Dict dict = dictList.get(i);


            dto.setName(dict.getLabel());


            dto.setId(id + "_" + dict.getValue());
            MaterialDto cn = new MaterialDto();
            ToolsKit.Bean.copyProperties(dto, cn);
            cn.setMaterialDto(getMaterialList(dto.getId(), null, null, null, locale, false, null, dto.getName()));

            String name = i18nUtils.getKey(dict.getLocaleCode(), locale);
            if (ToolsKit.isEmpty(name)) {
                name = dict.getLabel();
            }
            cn.setName(name);

            dtoListNew.add(cn);
        }
        return dtoListNew;
    }
}
