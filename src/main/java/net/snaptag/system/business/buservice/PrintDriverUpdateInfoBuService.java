package net.snaptag.system.business.buservice;

import net.snaptag.system.account.buservice.UserAccountAndInfoBuService;
import net.snaptag.system.account.buservice.UserInfoBuService;
import net.snaptag.system.account.dto.UserLoginDto;
import net.snaptag.system.business.cache.PrinterDriverUpgradeCacheService;
import net.snaptag.system.business.cache.PubSubCacheService;
import net.snaptag.system.business.dao.PrintDriverUpdateInfoDao;
import net.snaptag.system.business.dto.PrintDriverUpdateInfoDto;
import net.snaptag.system.business.entity.PrintDriverUpdateInfo;
import net.snaptag.system.business.enums.PrintDriverUpdateFollowTypeEnums;
import net.snaptag.system.business.utils.MD5Utils;
import net.snaptag.system.sadais.cache.kit.CacheKit;
import net.snaptag.system.sadais.core.common.core.ToolsConst;
import net.snaptag.system.sadais.core.common.exception.ServiceException;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import net.snaptag.system.sadais.util.core.ToolsKit;
import net.snaptag.system.sadais.web.core.I18nUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * <AUTHOR>
 * @date ：Created in 2023/4/6 9:10
 * @description：打印机驱动更新
 * @modified By：
 * @version: $
 */
@Service
public class PrintDriverUpdateInfoBuService {
    private final static String I18N_DESCRIBE_FORCE = "printer_driver_update_need_force";
    private final static String I18n_DESCRIBE_NOT_FORCE = "printer_driver_update_not_need_force";
    private final static String WHILE_USER_LIST_KEY = "printer_driver_update_while_user_list";

    @Autowired
    private I18nUtils i18nUtils;

    @Autowired
    private PrinterDriverUpgradeCacheService printerDriverUpgradeCacheService;

    @Autowired
    private UserInfoBuService userInfoBuService;

    @Autowired
    private PubSubCacheService pubSubCacheService;

    private final static Integer PRINT_DRIVER_SHOW = 1;
    @Autowired
    private PrintDriverUpdateInfoDao printDriverUpdateInfoDao;
    @Autowired
    private UserAccountAndInfoBuService userAccountAndInfoBuServiced;

    public List<PrintDriverUpdateInfoDto> findListByCond(String version, Locale locale) {
        List<PrintDriverUpdateInfo> infoList = printDriverUpdateInfoDao.findListByCond(version);
        if (ToolsKit.isEmpty(infoList)){
            return new ArrayList<>();
        }
        List<PrintDriverUpdateInfoDto> result = new ArrayList<>();
        for (PrintDriverUpdateInfo info: infoList
             ) {
            PrintDriverUpdateInfoDto temp = new PrintDriverUpdateInfoDto();
            ToolsKit.Bean.copyProperties(info, temp);
            settingI18nForDescribe(temp, locale);
            result.add(temp);
        }
        return result;
    }

    private void settingI18nForDescribe(PrintDriverUpdateInfoDto dto, Locale locale) {
        if(ToolsKit.isNotEmpty(dto.getNeedForceUpdate()) && dto.getNeedForceUpdate()==1){
            // 强制更新
            dto.setDescribe(i18nUtils.getKey(I18N_DESCRIBE_FORCE,locale));
        } else {
            dto.setDescribe(i18nUtils.getKey(I18n_DESCRIBE_NOT_FORCE,locale));
        }
    }

    public List<PrintDriverUpdateInfoDto> findListByCond(String version, Integer usedFlag, Locale locale) {
        List<PrintDriverUpdateInfo> infoList = printDriverUpdateInfoDao.findListByUsed(version, usedFlag);
        if (ToolsKit.isEmpty(infoList)){
            return new ArrayList<>();
        }
        List<PrintDriverUpdateInfoDto> result = new ArrayList<>();
        for (PrintDriverUpdateInfo info: infoList
        ) {
            PrintDriverUpdateInfoDto temp = new PrintDriverUpdateInfoDto();
            ToolsKit.Bean.copyProperties(info, temp);
            settingI18nForDescribe(temp, locale);
            result.add(temp);
        }
        return result;
    }

    public List<Map<String, Object>> findListMap(Locale locale){
        List<PrintDriverUpdateInfoDto> list = this.findListByCond(null, null, locale);
        if (ToolsKit.isEmpty(list)){
            return new ArrayList<>();
        }
        Map<String, Object> printDriverMap = new HashMap<>();
        for (PrintDriverUpdateInfoDto dto: list
             ) {
            List<PrintDriverUpdateInfoDto> tmpList = new ArrayList<>();
            if (ToolsKit.isNotEmpty(printDriverMap.get(dto.getVersionName()))){
                tmpList = (List<PrintDriverUpdateInfoDto>) printDriverMap.get(dto.getVersionName());
            }
            tmpList.add(dto);
            printDriverMap.put(dto.getVersionName(), tmpList);
        }

        List<Map<String, Object>> result = new ArrayList<>();
        for (String key: printDriverMap.keySet()) {
            Map<String, Object> map = new HashMap<>();
            map.put("name", key);
            map.put("list", printDriverMap.get(key));
            result.add(map);
        }
        return result;
    }

    public Page<PrintDriverUpdateInfo> findPageByCond(String printerModel, int pageNo, int pageSize) {
        IPage<PrintDriverUpdateInfo> page = printDriverUpdateInfoDao.findPageByCond(printerModel, pageNo, pageSize);
        Page<PrintDriverUpdateInfo> result = new Page<>(pageNo + 1, pageSize);
        result.setCurrent(page.getCurrent());
        result.setSize(page.getSize());
        result.setTotal(page.getTotal());
        result.setRecords(page.getRecords());
        return result;
    }

    public PrintDriverUpdateInfo addOrUpdate(PrintDriverUpdateInfo printDriverUpdateInfo, String autoCale){
        if (printDriverUpdateInfo==null){
            return new PrintDriverUpdateInfo();
        }

        if (ToolsKit.isEmpty(printDriverUpdateInfo.getId())){
            printDriverUpdateInfo.setId(null);
        }
        ToolsKit.setIdEntityData(printDriverUpdateInfo, ToolsConst.SYSTEM_USER_ID);

        if (ToolsKit.isNotEmpty(autoCale) && "1".equals(autoCale)){
            printDriverUpdateInfo.setMd5(MD5Utils.getMD5(printDriverUpdateInfo.getUrl()));
        }
        // 调这个接口的话，强制把followStatus改成预发布状态
        printDriverUpdateInfo.setFollowStatus(PrintDriverUpdateFollowTypeEnums.COMMON.getValue());
        printDriverUpdateInfoDao.saveOrUpdate(printDriverUpdateInfo);
        pubSubCacheService.updatePrinterDriverUpgrade();
        return printDriverUpdateInfo;
    }

    public String del(String id){
        if (ToolsKit.isEmpty(id)){
            throw new ServiceException("缺省参数id");
        }
        printDriverUpdateInfoDao.removeById(id);
        pubSubCacheService.updatePrinterDriverUpgrade();
        return "success";
    }

    public PrintDriverUpdateInfo findByid(String id) {
        if (ToolsKit.isEmpty(id)){
            throw new ServiceException("缺省参数id");
        }
        return printDriverUpdateInfoDao.getById(id);
    }

    public Object findTypeList() {
        Map<String, String> result = new HashMap<>();
        Page<PrintDriverUpdateInfo> page = this.findPageByCond(null,0,9999);
        if (page!=null && ToolsKit.isNotEmpty(page.getRecords())){
            page.getRecords().forEach(item -> {
                if (ToolsKit.isNotEmpty(item.getPrinterModel())){
                    result.put(item.getPrinterModel().trim(), item.getPrinterModel());
                }
            });
        }
        return result;
    }

    public PrintDriverUpdateInfoDto findByUpgradeInfoByTypeAndVersion(String userId, String printerType, String version, Locale locale) {
        PrintDriverUpdateInfoDto result = null;
        if (ToolsKit.isEmpty(printerType) || ToolsKit.isEmpty(version)){
            return result;
        }
        printerType = printerType.toUpperCase();
        // 根据printerType，获取所有打印机型号的所有固件版本
        List<PrintDriverUpdateInfo> list = printerDriverUpgradeCacheService.getMap().get(printerType);
        boolean isWhiteListUser = false;
        if (ToolsKit.isNotEmpty(userId) && CacheKit.cache().exists(WHILE_USER_LIST_KEY)){
            isWhiteListUser = CacheKit.cache().zrevrank(WHILE_USER_LIST_KEY).contains(userId);
        }
        if (ToolsKit.isNotEmpty(list)){
            // 如果是恒全的，p81，t81，直接根据version获取即可
            if ("P81".equals(printerType) || "T81".equals(printerType)){
                int index = 0;
                while (index<list.size()){
                    PrintDriverUpdateInfo tmp = list.get(index);
                    if (!isWhiteListUser && tmp.getFollowStatus()!= PrintDriverUpdateFollowTypeEnums.SUCCESS.getValue()){
                        continue;
                    }
                    if (tmp.getVersionCode().compareTo(version)>0) {
                        result = new PrintDriverUpdateInfoDto();
                        ToolsKit.Bean.copyProperties(tmp, result);
                        break;
                    }
                    index++;
                }
            } else {
                int index = 0;
                // 其他的，取大版本
                String[] targetVersions = version.split("\\.");
                while (index<list.size()){
                    PrintDriverUpdateInfo tmp = list.get(index);
                    String [] tmpVersions = tmp.getVersionCode().split("\\.");

                    // 相同大版本后，判断是否有大于目标版本
                    if (targetVersions[0].equals(tmpVersions[0]) && tmpVersions[1].compareTo(targetVersions[1])>0) {
                        // 是否有固件前置条件，没有的话，返回这个版本号的信息，否则再判断
                        if (ToolsKit.isEmpty(tmp.getPreDriverId())) {
                            result = new PrintDriverUpdateInfoDto();
                            ToolsKit.Bean.copyProperties(tmp, result);
                            break;
                        } else {
                            // 根据以下的找到它的前置，找不到就不处理
                            PrintDriverUpdateInfo preDriverObj = null;
                            for (int i = index; i < list.size(); i++) {
                                if (tmp.getPreDriverId().equals(list.get(i).getId())) {
                                    preDriverObj = list.get(i);
                                    break;
                                }
                            }
                            if (preDriverObj!=null){
                                String [] tmpSecVersions = preDriverObj.getVersionCode().split("\\.");
                                if (targetVersions[0].equals(tmpSecVersions[0]) && targetVersions[1].equals(tmpSecVersions[1])){
                                    result = new PrintDriverUpdateInfoDto();
                                    ToolsKit.Bean.copyProperties(tmp, result);
                                    break;
                                }
                            }
                        }
                    }
                    index++;
                }
            }
        }
        if (ToolsKit.isNotEmpty(result)){
            settingI18nForDescribe(result, locale);
        }
        return result;
    }

    public List<PrintDriverUpdateInfo> findAll(String printerType) {
        return printerDriverUpgradeCacheService.getMap().get(printerType);
    }

    /***
     * 给固件升级增加白名单
     * @return
     */
    public List<UserLoginDto> findWhileList() {
        List<UserLoginDto> result = new ArrayList<>();
        if (CacheKit.cache().exists(WHILE_USER_LIST_KEY)) {
            List<String> userIds = CacheKit.cache().zrevrank(WHILE_USER_LIST_KEY);
            for (String userId: userIds) {
                result.add(userAccountAndInfoBuServiced.getLoginInfo(userId));
            }
        }
        return result;
    }


    public String addWhileUser(String otherid) {
        if (ToolsKit.isNotEmpty(otherid)){
            CacheKit.cache().zadd(WHILE_USER_LIST_KEY, Double.parseDouble(String.valueOf(System.currentTimeMillis())), otherid, ToolsConst.MONTH_SECOND*12);
        }
        return "SUCCESS";
    }


    public String removeWhileUser(String otherid) {
        if (ToolsKit.isNotEmpty(otherid)){
            CacheKit.cache().zrem(WHILE_USER_LIST_KEY, otherid);
        }
        return "SUCCESS";
    }

    /***
     * 提交审核
     * @param id
     * @return
     */
    public String updateSubmit(String id) {
        PrintDriverUpdateInfo printDriverUpdateInfo = this.findByid(id);
        if (printDriverUpdateInfo!=null){
            printDriverUpdateInfo.setFollowStatus(PrintDriverUpdateFollowTypeEnums.AUDIT.getValue());
        } else {
            throw new ServiceException("记录已不存在");
        }
        printDriverUpdateInfoDao.saveOrUpdate(printDriverUpdateInfo);
        pubSubCacheService.updatePrinterDriverUpgrade();
        return "SUCCESS";
    }

    /***
     * 打回，使记录变成预发布状态
     * @param id
     * @return
     */
    public String updateReject(String id) {
        PrintDriverUpdateInfo printDriverUpdateInfo = this.findByid(id);
        if (printDriverUpdateInfo!=null){
            printDriverUpdateInfo.setFollowStatus(PrintDriverUpdateFollowTypeEnums.COMMON.getValue());
        } else {
            throw new ServiceException("记录已不存在");
        }
        printDriverUpdateInfoDao.saveOrUpdate(printDriverUpdateInfo);
        pubSubCacheService.updatePrinterDriverUpgrade();
        return "SUCCESS";
    }

    public Object updatePass(String id) {
        PrintDriverUpdateInfo printDriverUpdateInfo = this.findByid(id);
        if (printDriverUpdateInfo!=null){
            printDriverUpdateInfo.setFollowStatus(PrintDriverUpdateFollowTypeEnums.SUCCESS.getValue());
        } else {
            throw new ServiceException("记录已不存在");
        }
        printDriverUpdateInfoDao.saveOrUpdate(printDriverUpdateInfo);
        pubSubCacheService.updatePrinterDriverUpgrade();
        return "SUCCESS";
    }
}
