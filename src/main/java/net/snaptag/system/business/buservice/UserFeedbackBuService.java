package net.snaptag.system.business.buservice;

import cn.hutool.core.date.DatePattern;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import net.snaptag.system.business.dao.UserFeedbackDao;
import net.snaptag.system.business.dto.*;
import net.snaptag.system.business.entity.UserFeedback;
import net.snaptag.system.business.enums.FeedBackPrinterTypeEnums;
import net.snaptag.system.business.enums.FeedBackProblemTypeEnums;
import net.snaptag.system.business.enums.MsgColumnTypeEnums;
import net.snaptag.system.business.utils.ToolUtils;
import net.snaptag.system.sadais.core.common.core.ToolsConst;
import net.snaptag.system.sadais.core.common.enums.ExceptionEnums;
import net.snaptag.system.sadais.core.common.exception.ServiceException;
import net.snaptag.system.sadais.util.core.ToolsKit;
import net.snaptag.system.sadais.web.core.I18nUtils;
import net.snaptag.system.sadais.web.enums.MsgTypeEnums;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.*;

import java.util.ArrayList;
import java.util.List;

/**
 * 用户反馈业务服务类
 * 已修正JSON字段映射问题
 */
@Service
public class UserFeedbackBuService {

    @Autowired
    private UserFeedbackDao userFeedbackDao;

    @Autowired
    private MsgCenterBuService msgCenterBuService;
    @Autowired
    private I18nUtils i18nUtils;

    private static final ObjectMapper objectMapper = new ObjectMapper();
    private static final String SOFTWARE_TYPE = "software";
    private static final String HARDWARE_TYPE = "hardware";
    private static SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    /**
     * 将JSON字符串转换为List<String>
     */
    private List<String> parseImagesJson(String imagesJson) {
        if (imagesJson == null || imagesJson.trim().isEmpty()) {
            return new ArrayList<>();
        }
        try {
            return objectMapper.readValue(imagesJson, new TypeReference<List<String>>() {
            });
        } catch (Exception e) {
            // 如果解析失败，返回空List
            return new ArrayList<>();
        }
    }

    /**
     * 将List<String>转换为JSON字符串
     */
    private String imagesToJson(List<String> images) {
        if (images == null || images.isEmpty()) {
            return null;
        }
        try {
            return objectMapper.writeValueAsString(images);
        } catch (Exception e) {
            // 如果转换失败，返回null
            return null;
        }
    }

    /**
     * 保存用户反馈
     */
    public void save(UserFeedback userFeedback) {
        if (userFeedback != null) {
            userFeedbackDao.saveOrUpdate(userFeedback);
        }
    }

    /**
     * 根据记录ID获取用户的反馈
     *
     * @param id  - 反馈ID
     * @param mid - 消息ID
     * @return
     */
    public UserFeedbackItem getById(String id, String mid) {
        if (ToolsKit.isEmpty(id)) {
            throw new ServiceException("反馈ID不能为空");
        }
        if (ToolsKit.isEmpty(mid)) {
            throw new ServiceException("消息ID不能为空");
        }
        // 标识反馈已读状态
//        msgCenterService.read(mid);
        QueryWrapper<UserFeedback> wrapper = new QueryWrapper<>();
        wrapper.eq("id", id);
        wrapper.eq("status", ToolsConst.DATA_SUCCESS_STATUS);
        UserFeedback feedback = userFeedbackDao.getOne(wrapper);
        UserFeedbackItem feedbackVo = new UserFeedbackItem();
        if (feedback != null) {
            feedbackVo.setMobile(feedback.getMobile());
            feedbackVo.setContent(feedback.getContent());
            feedbackVo.setCreateTime(sdf.format(feedback.getCreatetime()));
            List<String> images = feedback.getImages().isEmpty() ? new ArrayList<>() : parseImagesJson(feedback.getImages());
            feedbackVo.setImages(images);
            feedbackVo.setQtype(feedback.getQtype());
            feedbackVo.setResult(feedback.getResult());
            feedbackVo.setType(feedback.getType());
            feedbackVo.setUserId(feedback.getUserId());
            feedbackVo.setPrinterType(feedback.getPrinterType());
            feedbackVo.setClientInfo(feedback.getClientInfo());
        }
        return feedbackVo;
    }

    /**
     * 根据ID获取用户反馈
     */
    public UserFeedback getById(String id) {
        if (ToolsKit.isEmpty(id)) {
            return null;
        }
        return userFeedbackDao.getById(id);
    }

    /**
     * 删除用户反馈
     */
    public void deleteById(String id) {
        if (ToolsKit.isNotEmpty(id)) {
            userFeedbackDao.removeById(id);
        }
    }

    /**
     * 根据用户ID获取反馈列表
     */
    public List<UserFeedback> getFeedbackByUserId(String userId) {
        if (ToolsKit.isEmpty(userId)) {
            return new ArrayList<>();
        }
        QueryWrapper<UserFeedback> wrapper = new QueryWrapper<>();
        wrapper.eq("user_id", userId);
        wrapper.eq("status", "审核通过");
        return userFeedbackDao.list(wrapper);
    }

    /**
     * 保存用户反馈并处理图片
     */
    public UserFeedback saveFeedbackWithImages(UserFeedback userFeedback, List<String> imageList, String userId) {
        if (userFeedback == null) {
            return null;
        }

        // 设置基本信息
        if (ToolsKit.isEmpty(userFeedback.getId())) {
            ToolsKit.setIdEntityData(userFeedback, userId);
        } else {
            ToolsKit.setIdEntityData(userFeedback, userId);
        }

        // 转换图片列表为JSON
        String imagesJson = imagesToJson(imageList);
        userFeedback.setImages(imagesJson);

        // 保存到数据库
        userFeedbackDao.saveOrUpdate(userFeedback);

        return userFeedback;
    }

    /**
     * 获取反馈图片列表
     */
    public List<String> getFeedbackImages(String id) {
        UserFeedback feedback = getById(id);
        if (feedback == null) {
            return new ArrayList<>();
        }
        return parseImagesJson(feedback.getImages());
    }

    /**
     * 根据类型获取反馈列表
     */
    public List<UserFeedback> getFeedbackByType(String type) {
        if (ToolsKit.isEmpty(type)) {
            return new ArrayList<>();
        }
        QueryWrapper<UserFeedback> wrapper = new QueryWrapper<>();
        wrapper.eq("type", type);
        wrapper.eq("status", "审核通过");
        return userFeedbackDao.list(wrapper);
    }

    // ==================== 兼容性方法 - 保持与旧版接口一致 ====================

    /**
     * 删除反馈 - 兼容旧版方法名
     */
    public void delById(String id) {
        this.deleteById(id);
    }

    /**
     * 保存实体 - 兼容旧版方法名
     */
    public void saveEntity(UserFeedback userFeedback) {
        this.save(userFeedback);
    }

    /**
     * 根据ID和状态获取反馈 - 兼容旧版重载方法
     */
    public UserFeedback getByIdAndStatus(String id, String status) {
        if (ToolsKit.isEmpty(id)) {
            return null;
        }
        QueryWrapper<UserFeedback> wrapper = new QueryWrapper<>();
        wrapper.eq("id", id);
        if (ToolsKit.isNotEmpty(status)) {
            wrapper.eq("status", status);
        }
        return userFeedbackDao.getOne(wrapper);
    }

    /**
     * 添加用户反馈 - 兼容旧版方法
     */
    public void addUserFeedback(String userid, String type, String qtype, String content,
                               List<String> images, String mobile, String clientInfo,
                               String printerType, Locale locale) {
        UserFeedback feedback = new UserFeedback();
        ToolsKit.setIdEntityData(feedback, userid);
        feedback.setUserId(userid);
        feedback.setType(type);
        feedback.setQtype(qtype);
        feedback.setContent(content);
        feedback.setMobile(mobile);
        feedback.setClientInfo(clientInfo);
        feedback.setPrinterType(printerType);

        // 处理图片列表 - 转换为JSON字符串
        String imagesJson = imagesToJson(images);
        feedback.setImages(imagesJson);

        // 保存到数据库
        this.save(feedback);

        // 发送消息通知（如果需要）
        try {
            sendFeedbackNotification(feedback, locale);
        } catch (Exception e) {
            // 消息发送失败不影响反馈保存
        }
    }

    /**
     * 发送反馈通知
     */
    private void sendFeedbackNotification(UserFeedback feedback, Locale locale) {
        // 这里可以实现消息通知逻辑
        // 暂时空实现，保持兼容性
    }





    /**
     * 更新反馈状态 - 兼容旧版方法
     */
    public void update(UserFeedback userFeedback) {
        if (userFeedback != null && ToolsKit.isNotEmpty(userFeedback.getId())) {
            userFeedbackDao.updateById(userFeedback);
        }
    }

    /**
     * 回复用户反馈 - 兼容旧版方法
     */
    public void replyUserFeedback(String feedbackId, String reply, String adminUserId, Locale locale) {
        UserFeedback feedback = this.getById(feedbackId);
        if (feedback == null) {
            throw new ServiceException("反馈不存在");
        }

        // 更新反馈状态（暂时只更新状态，reply相关字段需要在数据库中添加）
        feedback.setResult(reply); // 暂时使用result字段存储回复内容

        this.update(feedback);

        // 发送回复通知给用户
        try {
            sendReplyNotification(feedback, locale);
        } catch (Exception e) {
            // 消息发送失败不影响回复保存
        }
    }

    /**
     * 发送回复通知
     */
    private void sendReplyNotification(UserFeedback feedback, Locale locale) {
        // 这里可以实现回复通知逻辑
        // 暂时空实现，保持兼容性
    }

    /**
     * 获取用户的反馈分页列表
     *
     * @param pageNo   - 当前页码
     * @param pageSize - 每页大小
     * @return
     */
    public Page<UserFeedback> findUserFeedbackPage(String startDate, String endDate, int pageNo, int pageSize) {
        Date start = null;
        Date end = null;
        if (ToolsKit.isNotEmpty(startDate)) {
            start = ToolsKit.Date.parse(startDate + ToolsConst.START_TIME, DatePattern.NORM_DATETIME_PATTERN);
        }
        if (ToolsKit.isNotEmpty(endDate)) {
            end = ToolsKit.Date.parse(endDate + ToolsConst.END_TIME, DatePattern.NORM_DATETIME_PATTERN);
        }
        return userFeedbackDao.findUserFeedbackPage(start, end, pageNo, pageSize);
    }

    public UserFeedbackVo getUserFeedbackList(String userId, int page, int pageSize) {
        page = page - 1;
        if (page < 0) {
            page = 0;
        }
        List<UserFeedback> lists = userFeedbackDao.findUserFeedbackList(userId, page, pageSize);
        UserFeedbackVo feedbackVo = new UserFeedbackVo(new ArrayList<>(), new ArrayList<>());
        if (lists != null) {
            for (UserFeedback item : lists) {
                List<String> images = item.getImages().isEmpty() ? new ArrayList<>() : parseImagesJson(item.getImages());
                UserFeedbackItem feedbackItem = new UserFeedbackItem(item.getUserId(), item.getType(), item.getQtype(), item.getContent(), images, item.getResult());
                if (item.getCreatetime() != null) {
                    feedbackItem.setCreateTime(sdf.format(item.getCreatetime()));
                }
                feedbackItem.setMobile(item.getMobile());

                if (item.getType().equals(SOFTWARE_TYPE)) {
                    feedbackVo.getSoftwares().add(feedbackItem);
                } else if (item.getType().equals(HARDWARE_TYPE)) {
                    feedbackVo.getHardwares().add(feedbackItem);
                }
            }
        }
        return feedbackVo;
    }

    public List<UserFeedbackItem> getUserFeedbackList2(String userId, int page, int pageSize, Locale locale) {
        page = page - 1;
        if (page < 0) {
            page = 0;
        }
        List<UserFeedback> lists = userFeedbackDao.findUserFeedbackList(userId, page, pageSize);
        List<UserFeedbackItem> result = new ArrayList<>();


        // userId, msgType, Integer.parseInt(pageNo), Integer.parseInt(pageSize), lastId, locale, headInfoDto.getVersion())
        List<MsgCenterDto> msgList = msgCenterBuService.getMsgCenterDtoList(userId, String.valueOf(MsgColumnTypeEnums.FEEDBACK.getType()), 1, 99, null, null, "");
        // msgCenterBuService.getMsgListByType(userId, MsgColumnTypeEnums.FEEDBACK.getType(),1, 99);
        Map<String, MsgCenterDto> noReadSsgMap = new HashMap<>();
        if (ToolsKit.isNotEmpty(msgList)) {
            msgList.forEach(item -> {
                if (ToolsKit.isNotEmpty(item.getParam()) && ToolsKit.isNotEmpty(item.getParam().getId())) {
                    if (noReadSsgMap.get(item.getParam().getId()) == null) {
                        noReadSsgMap.put(item.getParam().getId(), item);
                    } else {
                        MsgCenterDto temp = noReadSsgMap.get(item.getParam().getId());
                        if (temp.getMsgTime().getTime() < item.getMsgTime().getTime()) {
                            noReadSsgMap.put(item.getParam().getId(), item);
                        }
                    }
                }
            });
        }

//        UserFeedbackVo feedbackVo = new UserFeedbackVo(new ArrayList<>(), new ArrayList<>());
        if (lists != null) {
            for (UserFeedback item : lists) {
                List<String> images = item.getImages().isEmpty() ? new ArrayList<>() : parseImagesJson(item.getImages());
                UserFeedbackItem feedbackItem = new UserFeedbackItem(item.getUserId(), item.getType(), item.getQtype(), item.getContent(), images, item.getResult());
                if (item.getCreatetime() != null) {
                    feedbackItem.setCreateTime(sdf.format(item.getCreatetime()));
                }
                feedbackItem.setMobile(item.getMobile());
                feedbackItem.setPrinterType(item.getPrinterType());
                feedbackItem.setQtypeName(getQtypeName(feedbackItem.getQtype(), locale));

                // 设置是否已读
                if (noReadSsgMap.get(item.getId()) != null) {
                    feedbackItem.setIsRead(noReadSsgMap.get(item.getId()).getIsRead());
                    feedbackItem.setMessage(noReadSsgMap.get(item.getId()).getMsgContent());
                    feedbackItem.setMid(noReadSsgMap.get(item.getId()).getId());
                    feedbackItem.setId(item.getId());
                } else {
                    feedbackItem.setIsRead(1);
                }

                result.add(feedbackItem);
            }
        }
        return result;
    }

    private String getQtypeName(String qtype, Locale locale) {
        if (ToolsKit.isEmpty(qtype)) {
            return "";
        }
        String[] array = qtype.split("/");
        String[] arrayQtname = new String[array.length];
        StringBuffer sb = new StringBuffer("");
        for (int i = 0; i < array.length; i++) {
            if (FeedBackProblemTypeEnums.getMap().get(array[i]) != null) {
                FeedBackProblemTypeEnums enums = FeedBackProblemTypeEnums.getMap().get(array[i]);
                String descValue = i18nUtils.getKey(enums.getLanguageCode(), locale);
                if (ToolsKit.isEmpty(descValue)) {
                    arrayQtname[i] = enums.getDesc();
                } else {
                    arrayQtname[i] = descValue;
                }
            }
        }
        return StringUtils.join(arrayQtname, ",");
    }

    /**
     * 评论用户的反馈
     *
     * @param id     - 反馈ID
     * @param remark - 评论信息
     * @throws ServiceException
     */
    public void update(String id, String remark) throws ServiceException {
        if (ToolsKit.isEmpty(id)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("反馈ID不能为空");
        }
        if (ToolsKit.isEmpty(remark)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("评论信息不能为空");
        }

        UserFeedback feedback = getById(id);
        if (feedback != null) {
            feedback.setResult(remark);
            feedback.setUpdatetime(new Date());
            save(feedback);

            // 发送反馈回复消息
            SendMsgDto sendMsgDto = new SendMsgDto();
            sendMsgDto.setMsgContent(ToolUtils.replaceHtml(remark));
            sendMsgDto.setMsgType(MsgColumnTypeEnums.FEEDBACK.getType());
            sendMsgDto.setMsgSubType(MsgTypeEnums.NO_JUMP.getType());
            sendMsgDto.setSenderUserId(feedback.getUserId());
            sendMsgDto.setReceiverUserId(feedback.getUserId());
            MsgCenterParamDto param = new MsgCenterParamDto();
            param.setId(feedback.getId());
            sendMsgDto.setParam(param);
            msgCenterBuService.sendMsg(sendMsgDto);
        }
    }

    public List<Map<String, String>> getPrinterType(Locale locale) {
        List<Map<String, String>> result = new ArrayList<>();
        for (FeedBackPrinterTypeEnums enums : FeedBackPrinterTypeEnums.values()) {
            Map<String, String> temp = new HashMap<>();
            temp.put("picUrl", enums.getPicUrl());
//            temp.put("name", enums.getKey());
            if ("其他".equals(enums.getKey())) {
                temp.put("name", i18nUtils.getKey("feedback_problem_other", locale));
            } else {
                temp.put("name", enums.getKey());
            }
            result.add(temp);
        }
        return result;
    }

    public List<Map<String, String>> getProblemType(Locale locale) {
        List<Map<String, String>> result = new ArrayList<>();
//        System.out.println("locale.toString()="+locale);
        for (FeedBackProblemTypeEnums enums : FeedBackProblemTypeEnums.values()) {
            Map<String, String> temp = new HashMap<>();
            temp.put("type", enums.getType());
            temp.put("qtype", enums.getCode());
//            temp.put("desc", enums.getDesc());
            String descValue = i18nUtils.getKey(enums.getLanguageCode(), locale);
            if (ToolsKit.isEmpty(descValue)) {
                temp.put("desc", enums.getDesc());
            } else {
                temp.put("desc", descValue);
            }
            result.add(temp);
        }
        return result;
    }
}
