package net.snaptag.system.business.buservice;

import net.snaptag.system.business.dao.GoodsDao;
import net.snaptag.system.business.dto.GoodsDto;
import net.snaptag.system.business.entity.Goods;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import net.snaptag.system.sadais.util.core.ToolsKit;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2021/10/22 10:15
 * @description：
 * @modified By：
 * @version: $
 */
@Service
public class GoodsBuService {
    @Autowired
    private GoodsDao goodsDao;

    private final static int GOODS_ONSALE_STATUS_OUT = 0;   // 商品上架状态：未上架
    private final static int GOODS_ONSALE_STATUS_ON = 1;    // 商品上架状态：上架

    /***
     * 根据分页，获取所有已上架的商品信息
     * @param pageNo
     * @param pageSize
     * @return
     */
    public List<GoodsDto> getPageList(int pageNo, int pageSize){
        List<GoodsDto> result = new ArrayList<>();
        List<Goods> list = goodsDao.findGoodsList(GOODS_ONSALE_STATUS_ON, pageNo, pageSize);
        if (ToolsKit.isNotEmpty(list)){
            list.forEach(goods->{
                GoodsDto goodsDto = new GoodsDto();
                ToolsKit.Bean.copyProperties(goods, goodsDto);
                result.add(goodsDto);
            });
        }
        return result;
    }

    /***
     * 根据分页，获取所有商品信息
     * @param onsaleStatus
     * @param pageNo
     * @param pageSize
     * @return
     */
    public Page<Goods> getPageList(Integer onsaleStatus, int pageNo, int pageSize) {
        IPage<Goods> page = goodsDao.findGoodsPage(onsaleStatus, pageNo, pageSize);
        Page<Goods> result = new Page<>(pageNo + 1, pageSize);
        result.setCurrent(page.getCurrent());
        result.setSize(page.getSize());
        result.setTotal(page.getTotal());
        result.setRecords(page.getRecords());
        return result;
    }

    /***
     * 新增或修改商品信息
     * @param goods
     * @param userId
     * @return
     */
    public Goods addOrUpdate(Goods goods, String userId){
        if (ToolsKit.isEmpty(goods.getId())){
            ToolsKit.setIdEntityData(goods, userId);
            goods.setId(null);
        }
        goodsDao.saveOrUpdate(goods);
        return goods;
    }

    /**
     * 根据id删除商品信息
     * @param id
     */
    public void deleteById(String id){
        goodsDao.removeById(id);
    }
}
