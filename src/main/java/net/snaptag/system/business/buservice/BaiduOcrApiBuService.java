package net.snaptag.system.business.buservice;

import com.baidu.aip.ocr.AipOcr;
import net.snaptag.system.sadais.core.common.exception.ServiceException;
import net.snaptag.system.sadais.util.core.ToolsKit;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2024/9/30 15:01
 * @description：百度ocr服务类
 * @modified By：
 * @version: $
 */
@Service
public class BaiduOcrApiBuService {
    public static final String APP_ID = "115745939";
    public static final String API_KEY = "P44UGCvzorclRF6Ddu6Wkf9u";
    public static final String SECRET_KEY = "xppZHvGhQiiEMRV238OIfb1CH0hqMzzo";
    private static AipOcr client;
    static {
        client = new AipOcr(APP_ID, API_KEY, SECRET_KEY);
        client.setConnectionTimeoutInMillis(2000);
        client.setSocketTimeoutInMillis(60000);
    }

    public AipOcr getClient(){
        return client;
    }

    public List<String> doRequest(String url){
        List<String> result = new ArrayList<>();
        try{
            url = url.replace("https://m.snaptag.top", "https://xplable.oss-cn-hangzhou.aliyuncs.com");
            HashMap<String, String> params = new HashMap<>();
            params.put("detect_direction", "true");
            JSONObject res = getClient().basicAccurateGeneralUrl(url, params);
            if (res == null || !res.has("words_result")){
                System.out.println("识别失败===");
                System.out.println(res);
                return result;
            } else {
                JSONArray words = res.getJSONArray("words_result");
                for (int i=0; i<words.length(); i++) {
                    JSONObject word = words.getJSONObject(i);
                    result.add(word.getString("words"));
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException("图片识别失败,请联系管理员");
        }
        return result;
    }

    public static void main(String[] args) {
        BaiduOcrApiBuService service = new BaiduOcrApiBuService();
        List<String> result = service.doRequest("https://yoyin.oss-cn-hangzhou.aliyuncs.com/tmp/111-1.png");
        if (ToolsKit.isNotEmpty(result)) {
            result.forEach(item -> {
                System.out.println(item);
            });
        }
    }

    public List<String> doRequestData(String imageData) {
        List<String> result = new ArrayList<>();
        try{
            JSONObject res = getClient().basicAccurateGeneral(imageData, new HashMap<String, String>());
            //client.basicGeneral(url, new HashMap<String, String>());
            JSONArray words = res.getJSONArray("words_result");
            for (int i=0; i<words.length(); i++) {
                JSONObject word = words.getJSONObject(i);
                result.add(word.getString("words"));
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException("图片识别失败,请联系管理员");
        }
        return result;
    }
}
