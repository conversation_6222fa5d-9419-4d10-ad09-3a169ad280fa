package net.snaptag.system.business.utils;

import java.util.HashMap;
import java.util.Locale;
import java.util.Map;

/**
 * 多语言工具类
 *
 * <AUTHOR>
 * @since 2025-08-20
 */
public class LanguageUtils {

    // 语言类型常量
    public static final int LANGUAGE_SYSTEM = 0;
    public static final int LANGUAGE_CHINESE = 1;
    public static final int LANGUAGE_ENGLISH = 2;
    public static final int LANGUAGE_TRADITION = 3;
    public static final int LANGUAGE_KOREAN = 4;
    public static final int LANGUAGE_RUSSIAN = 5;
    public static final int LANGUAGE_FRENCH = 6;
    public static final int LANGUAGE_SPANISH = 7;
    public static final int LANGUAGE_GERMANY = 8;
    public static final int LANGUAGE_ITALY = 9;

    // 语言标识常量
    public static final String LANG_CHINESE = "chinese";
    public static final String LANG_ENGLISH = "english";
    public static final String LANG_TRADITIONAL = "traditional";
    public static final String LANG_KOREAN = "korean";
    public static final String LANG_RUSSIAN = "russian";
    public static final String LANG_FRENCH = "french";
    public static final String LANG_SPANISH = "spanish";
    public static final String LANG_GERMANY = "germany";
    public static final String LANG_ITALY = "italy";

    /**
     * 国际化i18n
     */
    public static final String LOCALE_ZH_CN = "zh_CN";
    public static final String LOCALE_ZH_TW = "zh_TW";
    public static final String LOCALE_EN = "en_US";
    public static final String LOCALE_KOREA = "ko_KR";
    public static final String LOCALE_RUSSIAN = "ru_RU";
    public static final String LOCALE_JA = "ja_JP";
    public static final String LOCALE_ES = "es_ES";
    public static final String LOCALE_ROMAN = "ro_RO";
    public static final String LOCALE_DE = "de_DE";
    public static final String LOCALE_FRENCH = "fr_FR";
    public static final String LOCALE_ITALY = "it_IT";
    public static final String LOCALE_SPANISH = "es_LA";


    // 语言类型到语言标识的映射
    private static final Map<Integer, String> LANGUAGE_TYPE_TO_LANG = new HashMap<>();

    // 语言标识到语言类型的映射
    private static final Map<String, Integer> LANG_TO_LANGUAGE_TYPE = new HashMap<>();

    // 语言标识到数据库字段名的映射
    private static final Map<String, String> LANG_TO_COLUMN = new HashMap<>();

    // 语言标识到数据库字段名的映射
    private static final Map<String, String> LOCALE_TO_COLUMN = new HashMap<>();

    static {
        // 初始化语言类型到语言标识的映射
        LANGUAGE_TYPE_TO_LANG.put(LANGUAGE_CHINESE, LANG_CHINESE);
        LANGUAGE_TYPE_TO_LANG.put(LANGUAGE_ENGLISH, LANG_ENGLISH);
        LANGUAGE_TYPE_TO_LANG.put(LANGUAGE_TRADITION, LANG_TRADITIONAL);
        LANGUAGE_TYPE_TO_LANG.put(LANGUAGE_KOREAN, LANG_KOREAN);
        LANGUAGE_TYPE_TO_LANG.put(LANGUAGE_RUSSIAN, LANG_RUSSIAN);
        LANGUAGE_TYPE_TO_LANG.put(LANGUAGE_FRENCH, LANG_FRENCH);
        LANGUAGE_TYPE_TO_LANG.put(LANGUAGE_SPANISH, LANG_SPANISH);
        LANGUAGE_TYPE_TO_LANG.put(LANGUAGE_GERMANY, LANG_GERMANY);
        LANGUAGE_TYPE_TO_LANG.put(LANGUAGE_ITALY, LANG_ITALY);

        // 初始化语言标识到语言类型的映射
        LANG_TO_LANGUAGE_TYPE.put(LANG_CHINESE, LANGUAGE_CHINESE);
        LANG_TO_LANGUAGE_TYPE.put(LANG_ENGLISH, LANGUAGE_ENGLISH);
        LANG_TO_LANGUAGE_TYPE.put(LANG_TRADITIONAL, LANGUAGE_TRADITION);
        LANG_TO_LANGUAGE_TYPE.put(LANG_KOREAN, LANGUAGE_KOREAN);
        LANG_TO_LANGUAGE_TYPE.put(LANG_RUSSIAN, LANGUAGE_RUSSIAN);
        LANG_TO_LANGUAGE_TYPE.put(LANG_FRENCH, LANGUAGE_FRENCH);
        LANG_TO_LANGUAGE_TYPE.put(LANG_SPANISH, LANGUAGE_SPANISH);
        LANG_TO_LANGUAGE_TYPE.put(LANG_GERMANY, LANGUAGE_GERMANY);
        LANG_TO_LANGUAGE_TYPE.put(LANG_ITALY, LANGUAGE_ITALY);

        // 初始化语言标识到数据库字段名的映射
        LANG_TO_COLUMN.put(LANG_CHINESE, "borderKindName");
        LANG_TO_COLUMN.put(LANG_ENGLISH, "englishName");
        LANG_TO_COLUMN.put(LANG_TRADITIONAL, "traditionalName");
        LANG_TO_COLUMN.put(LANG_KOREAN, "koreanName");
        LANG_TO_COLUMN.put(LANG_RUSSIAN, "russianName");
        LANG_TO_COLUMN.put(LANG_FRENCH, "frenchName");
        LANG_TO_COLUMN.put(LANG_SPANISH, "spanishName");
        LANG_TO_COLUMN.put(LANG_GERMANY, "germanyName");
        LANG_TO_COLUMN.put(LANG_ITALY, "italyName");


        // 初始化语言标识到数据库字段名的映射
        LOCALE_TO_COLUMN.put(LOCALE_ZH_CN, "logoKindName");
        LOCALE_TO_COLUMN.put(LOCALE_EN, "englishName");
        LOCALE_TO_COLUMN.put(LOCALE_ZH_TW, "traditionalName");
        LOCALE_TO_COLUMN.put(LOCALE_KOREA, "koreanName");
        LOCALE_TO_COLUMN.put(LOCALE_RUSSIAN, "russianName");
        LOCALE_TO_COLUMN.put(LOCALE_FRENCH, "frenchName");
        LOCALE_TO_COLUMN.put(LANG_SPANISH, "spanishName");
        LOCALE_TO_COLUMN.put(LOCALE_DE, "germanyName");
        LOCALE_TO_COLUMN.put(LOCALE_ITALY, "italyName");
    }

    /**
     * 根据语言类型获取语言标识
     *
     * @param languageType 语言类型
     * @return 语言标识
     */
    public static String getLanguageByType(Integer languageType) {
        if (languageType == null) {
            return LANG_CHINESE;
        }
        return LANGUAGE_TYPE_TO_LANG.getOrDefault(languageType, LANG_CHINESE);
    }

    /**
     * 根据语言标识获取语言类型
     *
     * @param lang 语言标识
     * @return 语言类型
     */
    public static Integer getLanguageType(String lang) {
        if (lang == null || lang.trim().isEmpty()) {
            return LANGUAGE_CHINESE;
        }
        return LANG_TO_LANGUAGE_TYPE.getOrDefault(lang.toLowerCase(), LANGUAGE_CHINESE);
    }

    /**
     * 根据语言标识获取数据库字段名
     *
     * @param lang 语言标识
     * @return 数据库字段名
     */
    public static String getColumnByLang(String lang) {
        if (lang == null || lang.trim().isEmpty()) {
            return "borderKindName";
        }
        return LANG_TO_COLUMN.getOrDefault(lang.toLowerCase(), "borderKindName");
    }

    /**
     * 根据语言标识获取数据库字段名
     *
     * @param locale 语言标识
     * @return 数据库字段名
     */
    public static String getColumnByLocale(Locale locale, String defaultName) {
        if (locale == null) {
            return defaultName;
        }
        return LOCALE_TO_COLUMN.getOrDefault(locale.toString(), defaultName);
    }

    /**
     * 验证语言标识是否有效
     *
     * @param lang 语言标识
     * @return 是否有效
     */
    public static boolean isValidLang(String lang) {
        if (lang == null || lang.trim().isEmpty()) {
            return false;
        }
        return LANG_TO_LANGUAGE_TYPE.containsKey(lang.toLowerCase());
    }

    /**
     * 验证语言类型是否有效
     *
     * @param languageType 语言类型
     * @return 是否有效
     */
    public static boolean isValidLanguageType(Integer languageType) {
        if (languageType == null) {
            return false;
        }
        return LANGUAGE_TYPE_TO_LANG.containsKey(languageType);
    }

    /**
     * 获取默认语言类型
     *
     * @return 默认语言类型
     */
    public static Integer getDefaultLanguageType() {
        return LANGUAGE_CHINESE;
    }

    /**
     * 获取默认语言标识
     *
     * @return 默认语言标识
     */
    public static String getDefaultLang() {
        return LANG_CHINESE;
    }

    /**
     * 标准化语言标识（转为小写）
     *
     * @param lang 语言标识
     * @return 标准化后的语言标识
     */
    public static String normalizeLang(String lang) {
        if (lang == null || lang.trim().isEmpty()) {
            return getDefaultLang();
        }
        String normalized = lang.toLowerCase().trim();
        return isValidLang(normalized) ? normalized : getDefaultLang();
    }

    /**
     * 标准化语言类型
     *
     * @param languageType 语言类型
     * @return 标准化后的语言类型
     */
    public static Integer normalizeLanguageType(Integer languageType) {
        if (languageType == null) {
            return getDefaultLanguageType();
        }
        return isValidLanguageType(languageType) ? languageType : getDefaultLanguageType();
    }

    /**
     * 获取所有支持的语言标识
     *
     * @return 语言标识数组
     */
    public static String[] getSupportedLangs() {
        return LANG_TO_LANGUAGE_TYPE.keySet().toArray(new String[0]);
    }

    /**
     * 获取所有支持的语言类型
     *
     * @return 语言类型数组
     */
    public static Integer[] getSupportedLanguageTypes() {
        return LANGUAGE_TYPE_TO_LANG.keySet().toArray(new Integer[0]);
    }

    /**
     * 构建多语言CASE WHEN SQL片段
     *
     * @param paramName     参数名称
     * @param defaultColumn 默认字段名
     * @return SQL片段
     */
    public static String buildLanguageCaseWhen(String paramName, String defaultColumn) {
        StringBuilder sql = new StringBuilder();
        sql.append("CASE ");

        for (Map.Entry<String, String> entry : LANG_TO_COLUMN.entrySet()) {
            if (!entry.getValue().equals(defaultColumn)) {
                sql.append("WHEN #{").append(paramName).append("} = '").append(entry.getKey()).append("' ")
                        .append("THEN IFNULL(NULLIF(").append(entry.getValue()).append(", ''), ").append(defaultColumn).append(") ");
            }
        }

        sql.append("ELSE ").append(defaultColumn).append(" END");
        return sql.toString();
    }
}
