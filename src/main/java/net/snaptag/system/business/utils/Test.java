package net.snaptag.system.business.utils;

import net.snaptag.system.sadais.util.core.ToolsKit;
import net.snaptag.system.sadais.web.common.WebConst;
import net.snaptag.system.sadais.web.utils.UrlEncoderUtils;

/**
 * <AUTHOR>
 * @date ：Created in 2024/4/2 9:53
 * @description：
 * @modified By：
 * @version: $
 */
public class Test {
    public static void main(String[] args) {
        String values = "123_456";
        if (UrlEncoderUtils.hasUrlEncoded(values)) {
            System.out.println(ToolsKit.URL.decode(values, WebConst.ENCODING_FIELD));
        } else {
            System.out.println(values);
        }
    }
}
