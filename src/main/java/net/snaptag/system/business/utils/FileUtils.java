package net.snaptag.system.business.utils;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;

/**
 * 文件工具类
 * 
 * <AUTHOR>
 * @since 2025-01-16
 */
@Slf4j
public class FileUtils {

    /**
     * 下载文件
     * 
     * @param filePath 文件路径
     * @param fileName 文件名
     * @param response HTTP响应
     */
    public static void downloadFile(String filePath, String fileName, HttpServletResponse response) {
        if (StrUtil.isBlank(filePath)) {
            throw new RuntimeException("文件路径不能为空");
        }

        File file = new File(filePath);
        if (!file.exists()) {
            throw new RuntimeException("文件不存在");
        }

        try (InputStream inputStream = new FileInputStream(file);
             OutputStream outputStream = response.getOutputStream()) {

            // 设置响应头
            response.setContentType("application/octet-stream");
            response.setContentLengthLong(file.length());
            
            // 处理文件名编码
            String encodedFileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8.toString());
            response.setHeader("Content-Disposition", "attachment; filename=\"" + encodedFileName + "\"");

            // 复制文件内容到响应流
            byte[] buffer = new byte[8192];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }

            outputStream.flush();

        } catch (IOException e) {
            log.error("文件下载失败: {}", e.getMessage(), e);
            throw new RuntimeException("文件下载失败: " + e.getMessage());
        }
    }

    /**
     * 删除文件
     * 
     * @param filePath 文件路径
     * @return 是否成功
     */
    public static boolean deleteFile(String filePath) {
        if (StrUtil.isBlank(filePath)) {
            return false;
        }

        try {
            File file = new File(filePath);
            if (file.exists()) {
                return file.delete();
            }
            return true;
        } catch (Exception e) {
            log.error("删除文件失败: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 获取文件扩展名
     * 
     * @param fileName 文件名
     * @return 扩展名
     */
    public static String getFileExtension(String fileName) {
        if (StrUtil.isBlank(fileName)) {
            return "";
        }
        return FileUtil.extName(fileName);
    }

    /**
     * 格式化文件大小
     * 
     * @param size 文件大小（字节）
     * @return 格式化后的大小
     */
    public static String formatFileSize(long size) {
        return FileUtil.readableFileSize(size);
    }

    /**
     * 检查文件是否为字体文件
     * 
     * @param fileName 文件名
     * @return 是否为字体文件
     */
    public static boolean isFontFile(String fileName) {
        if (StrUtil.isBlank(fileName)) {
            return false;
        }
        
        String extension = getFileExtension(fileName).toLowerCase();
        return "ttf".equals(extension) || "otf".equals(extension) || 
               "woff".equals(extension) || "woff2".equals(extension);
    }

    /**
     * 创建目录
     * 
     * @param dirPath 目录路径
     * @return 是否成功
     */
    public static boolean createDirectory(String dirPath) {
        if (StrUtil.isBlank(dirPath)) {
            return false;
        }

        try {
            File dir = new File(dirPath);
            if (!dir.exists()) {
                return dir.mkdirs();
            }
            return true;
        } catch (Exception e) {
            log.error("创建目录失败: {}", e.getMessage(), e);
            return false;
        }
    }
}
