package net.snaptag.system.business.utils;

import net.snaptag.system.business.entity.TemplateBorder;
import net.snaptag.system.business.entity.TemplateBorderKind;
import net.snaptag.system.business.vo.TempletBorderKindVO;
import net.snaptag.system.business.vo.TempletBorderListVO;
import net.snaptag.system.business.vo.TempletBorderVO;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 模板边框数据转换工具类
 *
 * <AUTHOR>
 * @since 2025-08-20
 */
public class TempletBorderConverter {

    /**
     * 转换边框实体为列表VO
     *
     * @param border 边框实体
     * @return 列表VO
     */
    public static TempletBorderListVO toListVO(TemplateBorder border) {
        if (border == null) {
            return null;
        }

        TempletBorderListVO vo = new TempletBorderListVO();
        BeanUtils.copyProperties(border, vo);
        return vo;
    }

    /**
     * 批量转换边框实体为列表VO
     *
     * @param borders 边框实体列表
     * @return 列表VO列表
     */
    public static List<TempletBorderListVO> toListVOList(List<TemplateBorder> borders) {
        if (borders == null || borders.isEmpty()) {
            return new ArrayList<>();
        }

        List<TempletBorderListVO> voList = new ArrayList<>();
        for (TemplateBorder border : borders) {
            TempletBorderListVO vo = toListVO(border);
            if (vo != null) {
                voList.add(vo);
            }
        }
        return voList;
    }

    /**
     * 转换边框实体为详情VO
     *
     * @param border 边框实体
     * @return 详情VO
     */
    public static TempletBorderVO toVO(TemplateBorder border) {
        if (border == null) {
            return null;
        }

        TempletBorderVO vo = new TempletBorderVO();
        BeanUtils.copyProperties(border, vo);
        return vo;
    }

    /**
     * 批量转换边框实体为详情VO
     *
     * @param borders 边框实体列表
     * @return 详情VO列表
     */
    public static List<TempletBorderVO> toVOList(List<TemplateBorder> borders) {
        if (borders == null || borders.isEmpty()) {
            return new ArrayList<>();
        }

        List<TempletBorderVO> voList = new ArrayList<>();
        for (TemplateBorder border : borders) {
            TempletBorderVO vo = toVO(border);
            if (vo != null) {
                voList.add(vo);
            }
        }
        return voList;
    }

    /**
     * 转换边框分类实体为VO
     *
     * @param kind 分类实体
     * @return 分类VO
     */
    public static TempletBorderKindVO toKindVO(TemplateBorderKind kind) {
        if (kind == null) {
            return null;
        }

        TempletBorderKindVO vo = new TempletBorderKindVO();
        BeanUtils.copyProperties(kind, vo);
        return vo;
    }

    /**
     * 批量转换边框分类实体为VO
     *
     * @param kinds 分类实体列表
     * @return 分类VO列表
     */
    public static List<TempletBorderKindVO> toKindVOList(List<TemplateBorderKind> kinds) {
        if (kinds == null || kinds.isEmpty()) {
            return new ArrayList<>();
        }

        List<TempletBorderKindVO> voList = new ArrayList<>();
        for (TemplateBorderKind kind : kinds) {
            TempletBorderKindVO vo = toKindVO(kind);
            if (vo != null) {
                voList.add(vo);
            }
        }
        return voList;
    }

    /**
     * 转换边框分类实体为VO（带多语言处理）
     *
     * @param kind 分类实体
     * @param language 语言类型
     * @return 分类VO
     */
    public static TempletBorderKindVO toKindVOWithLanguage(TemplateBorderKind kind, Integer language) {
        if (kind == null) {
            return null;
        }

        TempletBorderKindVO vo = toKindVO(kind);
        if (vo != null) {
            vo.setDisplayName(kind.getNameByLanguage(language));
        }
        return vo;
    }

    /**
     * 转换边框分类实体为VO（带多语言处理）
     *
     * @param kind 分类实体
     * @param lang 语言标识
     * @return 分类VO
     */
    public static TempletBorderKindVO toKindVOWithLang(TemplateBorderKind kind, String lang) {
        if (kind == null) {
            return null;
        }

        TempletBorderKindVO vo = toKindVO(kind);
        if (vo != null) {
            vo.setDisplayName(kind.getNameByLang(lang));
        }
        return vo;
    }

    /**
     * 批量转换边框分类实体为VO（带多语言处理）
     *
     * @param kinds 分类实体列表
     * @param language 语言类型
     * @return 分类VO列表
     */
    public static List<TempletBorderKindVO> toKindVOListWithLanguage(List<TemplateBorderKind> kinds, Integer language) {
        if (kinds == null || kinds.isEmpty()) {
            return new ArrayList<>();
        }

        List<TempletBorderKindVO> voList = new ArrayList<>();
        for (TemplateBorderKind kind : kinds) {
            TempletBorderKindVO vo = toKindVOWithLanguage(kind, language);
            if (vo != null) {
                voList.add(vo);
            }
        }
        return voList;
    }

    /**
     * 批量转换边框分类实体为VO（带多语言处理）
     *
     * @param kinds 分类实体列表
     * @param lang 语言标识
     * @return 分类VO列表
     */
    public static List<TempletBorderKindVO> toKindVOListWithLang(List<TemplateBorderKind> kinds, String lang) {
        if (kinds == null || kinds.isEmpty()) {
            return new ArrayList<>();
        }

        List<TempletBorderKindVO> voList = new ArrayList<>();
        for (TemplateBorderKind kind : kinds) {
            TempletBorderKindVO vo = toKindVOWithLang(kind, lang);
            if (vo != null) {
                voList.add(vo);
            }
        }
        return voList;
    }
}
