package net.snaptag.system.business.utils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class IpUtils {
    private static Map<String, Object> ipMap = null;
    // public static final String IPURL = "http://ftp.apnic.net/apnic/stats/apnic/delegated-apnic-latest";

    /**
     * 获取IP Map数据
     */
    public static Map<String, Object> getIpList() {
        // 集合存放Ip第一段
        ipMap = new HashMap<>();
        try {
            InputStream input = Thread.currentThread().getContextClassLoader().getResourceAsStream("static/ipaddress.txt");
            List<String> lines = IOUtils.readLines(input, StandardCharsets.UTF_8);

//            String str = HttpClientUtil.doGet("http://ftp.apnic.net/apnic/stats/apnic/delegated-apnic-latest");
//            String []  lines = str.split("\n");

            // 读取文件内所有的中国IP
            for (String line : lines) {
                if (!line.startsWith("apnic|CN|ipv4")){
                    continue;
                }
                String [] ipStr = line.split("\\|");
                //apnic|CN|ipv4|**********|1048576转化成：**********/20
                String ipAddress = ipStr[3] + "/";
                int count = ((Double)(Math.log( Double.parseDouble(ipStr[4]) )/ Math.log(2))).intValue();
                ipAddress += count;
                    JSONObject parentObj = new JSONObject();
                    String[] ips = ipAddress.split("\\.");
                    // 得到一个ip地址段的起始范围 101
                    int ip1 = Integer.parseInt(ips[0]);
                    int ip2 = Integer.parseInt(ips[1]);
                    /*
                     * **********/20 等于  apnic|CN|ipv4|**********|1048576(2的20次方)
                     * **********/11 等于  apnic|CN|ipv4|**********|2048(2的11次方)
                     * 类似如此数据，IP网端每个地址数256也就是2的8次方，总共是2的32次方
                     * 所以如果最后一个数值超过 16，意味着后两个网络被占满，前面的网段需要递增
                     * **********/20 中 20 意味着后两个网段已满, 第二个网络端递增 2的(20-16)次方等于16
                     * **********/20 = 以下IP从 80 ~ 95 全网端都是中国IP
                     * **********
                     * **********
                     * **********
                     * ...
                     * **********
                     */
                    // 获取从当前IP段开始的总地址数
                    String[] strs = ipAddress.split("\\/");
                    long addressCount = Long.parseLong(strs[1]);
                    // 存储各个网络段
                    JSONObject object = new JSONObject();
                    if (ipMap.get(String.valueOf(ip1)) != null) {
                        object = (JSONObject) ipMap.get(String.valueOf(ip1));
                    }
                    // 判断是否后两个字段被占满
                    if (addressCount > 16) {
                        // 后两个字段被占满时，也就是地址数大于 256*256=65536=2的16次方
                        double pow = Math.pow(2, addressCount - 16);
                        for (int i = 0; i < pow; i++) {
                            object.put(String.valueOf(ip2 + i), "all");
                        }
                    } else {
                        /**
                         * apnic CN三个连续数据如下
                         * **********/11
                         * **********/10
                         * ***********/12
                         * ---------------------
                         * 如上在第二网段相同的情况
                         * **********/11 等于
                         * **********
                         * ...
                         * **********
                         * 共8个
                         *----------------------
                         * **********/10 等于
                         * **********
                         * ...
                         * ***********
                         * 共4个
                         *----------------------
                         * ***********/12 等于
                         * ***********
                         * ...
                         * ***********
                         * 共16个
                         * ---------------------
                         * 从上述数据中看到 *********** 到 *********** 出现了断层，中间内容不属于中国的IP
                         * 所以都需要被记录下来，多个IPRange 我们使用数组存储
                         */
                        // 转换IP为long
                        long start_ip = ipv4ToLong(strs[0]);
                        long ip_range = (long) Math.pow(2, addressCount);
                        long end_ip = start_ip + ip_range;
                        String ipRange = start_ip + "-" + end_ip;
                        // 判断是否已存在已有数据
                        JSONArray ipRangeExist = (JSONArray) object.get(String.valueOf(ip2));
                        if (ipRangeExist == null) {
                            ipRangeExist = new JSONArray();
                        }
                        ipRangeExist.add(ipRange);
                        object.put(String.valueOf(ip2), ipRangeExist);
                    }
                    ipMap.put(String.valueOf(ip1), object);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return ipMap;
    }

    // 求出 IPV4 IP地址所对应的整数，比如 ************ 对应整数 3232252422
    // 192*256*256*256 + 168*256*256 + 66*256 + 6 = 3232252422
    // IP转换十进制（a.b.c.d）= a*256^3+b*256^2+c*256+d
    public static long ipv4ToLong(String ip) {
        String[] ips = ip.split("\\.");
        long result = 0;
        for (int i = 0; i < ips.length; i++) {
            result += Long.parseLong(ips[i]) * Math.pow(256, 3 - i);
        }
        return result;
    }

    /**
     * 判断IP是不是在中国
     *
     * @param ip    传入的ip
     * @return true
     */
    public static boolean ipInChina(String ip) {
        if (ipMap == null) {
            getIpList();
        }
        // 判断 IP 是否存在
        if (StringUtils.isEmpty(ip)) {
            return false;
        }
        // 第一个IP端作为key
        String[] ipArr = ip.split("\\.");
        String key = ipArr[0];
        String childKey = ipArr[1];
        // 当前IP转换为整数
        long ip_long = ipv4ToLong(ip);

        // 判断第一个IP端存在
        if (ipMap.containsKey(key)) {
            JSONObject parentObj = (JSONObject) ipMap.get(key);
            // 判断第二个IP段是否存在
            if (parentObj.getString(childKey) != null) {
                String ipRange = parentObj.getString(childKey);
                if (ipRange.equals("all")) {
                    // 整个其余网段都是中国IP
                    return true;
                } else {
                    JSONArray ipRangeArray = JSONArray.parseArray(ipRange);
                    for (Object range : ipRangeArray) {
                        String[] ipRanges = String.valueOf(range).split("\\-");
                        if (ipRanges.length == 2) {
                            long ipRange_start = Long.parseLong(ipRanges[0]);
                            long ipRange_end = Long.parseLong(ipRanges[1]);
                            // 判断是否在范围内
                            return ip_long >= ipRange_start && ip_long <= ipRange_end;
                        }
                    }
                }
            }
        }
        return false;
    }

    public static void main(String[] args) {

//        boolean is = IpUtils.ipInChina("************");
        System.out.println(ipv4ToLong("***********"));
    }

}

