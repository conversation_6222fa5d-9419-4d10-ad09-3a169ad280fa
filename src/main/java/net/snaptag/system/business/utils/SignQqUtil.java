package net.snaptag.system.business.utils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import net.snaptag.system.sadais.http.kit.HttpKit;
import net.snaptag.system.sadais.util.core.ToolsKit;

public class SignQqUtil {
    /**
     * 获取QQaccess_token
     */
    @SuppressWarnings({ "unchecked", "deprecation"})
    public static String getAccessToken(String appid, String appSecret, String code, String redirectUri) {
        try {
            String url = "https://graph.qq.com/oauth2.0/token?grant_type=authorization_code&client_id=" + appid + "&client_secret=" + appSecret + "&code="
                    + code + "&redirect_uri=" + redirectUri;
            String result = HttpKit.http().url(url).get().asString();
            JSONObject object = JSON.parseObject(result);
            return object.getString("access_token");
        } catch (Exception e) {
            e.printStackTrace();
            return "";
        }
    }

    /**
     * 获取QQ openid
     */
    @SuppressWarnings({ "unchecked", "deprecation"})
    public static String getOpenId(String accessToken) {
        try {
            String url = "https://graph.qq.com/oauth2.0/me?access_token=" + accessToken;
            String result = HttpKit.http().url(url).get().asString();
            System.out.println(result);
            if (ToolsKit.isNotEmpty(result)) {
                JSONObject object = JSON.parseObject(result.substring(result.indexOf("{"), result.lastIndexOf("}") + 1));
                return object.getString("openid");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "";
    }

    /**
     * 获取QQ 用户信息
     */
    @SuppressWarnings({ "unchecked", "deprecation"})
    public static JSONObject getUserInfo(String appid, String accessToken, String openId) {
        try {
            String url = "https://graph.qq.com/user/get_user_info?oauth_consumer_key=" + appid + "&access_token=" + accessToken + "&openid=" + openId;
            String result = HttpKit.http().url(url).get().asString();
            System.out.println(result);
            return JSON.parseObject(result);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }
}
