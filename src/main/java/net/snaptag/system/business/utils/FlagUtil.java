package net.snaptag.system.business.utils;
/**
 * 标志位操作基类
 */
public class FlagUtil {
    /**
     * 判断某一标示位是否有值
     * 
     * @param flag
     *            状态位值
     * @param number
     *            标志位数
     * @return 某一标示位是否有值
     */
    public static boolean isFlag(int flag, int number) {
        Double powValue = Math.pow(2, (number - 1));
        return (flag & powValue.longValue()) != 0;
    }

    /**
     * 更新某一标志位数值
     * 
     * @param flag
     *            标志位值
     * @param number
     *            要进行操作的标志位
     * @param value
     *            要进行操作的标志位值
     * @return 更新后的标志位值
     */
    public static int updateFlag(int flag, int number, boolean value) {
        if (value && !isFlag(flag, number)) { // 如果想要对标志位设置为TRUE，并且当前标志位值为FALSE时
            Double powValue = Math.pow(2, (number - 1));
            flag = (int) (flag + powValue);
        } else if (!value && isFlag(flag, number)) { // 如果想要对标志位设置为FALSE，并且当前标志位值为TRUE时
            Double powValue = Math.pow(2, (number - 1));
            flag = (int) (flag - powValue);
        }
        return flag;
    }
}
