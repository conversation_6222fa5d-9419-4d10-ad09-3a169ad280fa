package net.snaptag.system.business.dao;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import net.snaptag.system.business.dto.FontQueryDTO;
import net.snaptag.system.business.entity.Font;
import net.snaptag.system.business.mapper.FontMapper;
import net.snaptag.system.business.vo.FontListVO;
import net.snaptag.system.business.vo.FontVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 字体数据访问接口
 *
 * <AUTHOR>
 * @since 2025-01-16
 */
@Repository
public class FontDao extends ServiceImpl<FontMapper, Font> {

    @Autowired
    private FontMapper fontMapper;

    public boolean save(Font font) {
        return fontMapper.insert(font) > 0;
    }

    public boolean deleteById(String fontId) {
        return fontMapper.deleteById(fontId) > 0;
    }

    public boolean updateById(Font font) {
        return fontMapper.updateById(font) > 0;
    }

    public Font selectById(String fontId) {
        return fontMapper.selectById(fontId);
    }

    public FontVO selectFontDetail(String fontId, String locale) {
        return fontMapper.selectFontDetail(fontId, locale);
    }

    public IPage<Font> selectFontPage(FontQueryDTO queryDTO) {
        Page<Font> page = new Page<>(queryDTO.getCurrent(), queryDTO.getSize());
        return fontMapper.selectFontPage(page, "审核通过");
    }

    public List<FontListVO> selectFontsByLocale(String locale) {
        return fontMapper.selectFontsByLocale(locale);
    }

    public List<Font> selectFontsByKind(String fontKind, String locale) {
        return fontMapper.selectFontsByKind(fontKind, locale);
    }

    public List<Font> selectFontsDynamic(String fontKind) {
        return fontMapper.selectFontsDynamic(fontKind);
    }

    public List<String> selectFontKinds(String locale) {
        return fontMapper.selectFontKinds(locale);
    }

    public Long countFonts() {
        return fontMapper.countFonts();
    }

    public boolean existsFontName(String fontName, String fontId) {
        return fontMapper.existsFontName(fontName, fontId);
    }
}
