package net.snaptag.system.business.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import net.snaptag.system.business.entity.ResourceData;
import net.snaptag.system.business.mapper.ResourceDataMapper;
import net.snaptag.system.common.DataConst;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

/**
 * 资源信息
 *
 * <AUTHOR> 2018年6月12日
 */
@Repository
public class ResourceDataDao extends ServiceImpl<ResourceDataMapper, ResourceData> {

    @Autowired
    private ResourceDataMapper resourceDataMapper;
    /**
     * 根据ID获取资源对象
     * 
     * @return 资源对象
     * @throws Exception
     */
    public ResourceData getResourceDataById(String id) {
        QueryWrapper<ResourceData> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("id", id);
        queryWrapper.eq("status", DataConst.DATA_SUCCESS_STATUS);
        return this.getOne(queryWrapper);
    }
}
