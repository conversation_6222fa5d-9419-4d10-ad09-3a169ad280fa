package net.snaptag.system.business.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import net.snaptag.system.business.entity.Goods;
import net.snaptag.system.business.mapper.GoodsMapper;
import net.snaptag.system.common.DataConst;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Goods DAO
 *
 * <AUTHOR>
 */
@Repository
public class GoodsDao extends ServiceImpl<GoodsMapper, Goods> {

    @Autowired
    private GoodsMapper goodsMapper;

    /**
     * 查询商品列表
     */
    public List<Goods> findGoodsList(Integer saleStatus, int pageNo, int pageSize) {
        QueryWrapper<Goods> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", DataConst.DATA_SUCCESS_STATUS);

        if (saleStatus != null) {
            queryWrapper.eq("sale_status", saleStatus);
        }

        queryWrapper.orderByAsc("sort_num");

        // 如果需要分页
        if (pageSize > 0) {
            Page<Goods> page = new Page<>(pageNo + 1, pageSize);
            IPage<Goods> result = this.page(page, queryWrapper);
            return result.getRecords();
        }

        return this.list(queryWrapper);
    }

    /**
     * 获取Goods信息分页数据
     */
    public IPage<Goods> findGoodsPage(Integer saleStatus, int pageNo, int pageSize) {
        QueryWrapper<Goods> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", DataConst.DATA_SUCCESS_STATUS);

        if (saleStatus != null) {
            queryWrapper.eq("sale_status", saleStatus);
        }

        queryWrapper.orderByAsc("sort_num");
        Page<Goods> page = new Page<>(pageNo + 1, pageSize);
        return this.page(page, queryWrapper);
    }
}
