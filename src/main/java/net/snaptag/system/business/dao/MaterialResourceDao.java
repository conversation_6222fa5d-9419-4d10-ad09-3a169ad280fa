package net.snaptag.system.business.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import net.snaptag.system.business.entity.MaterialResource;
import net.snaptag.system.business.mapper.MaterialResourceMapper;
import net.snaptag.system.common.DataConst;
import net.snaptag.system.sadais.util.core.ToolsKit;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 素材库资源
 *
 * <AUTHOR> 2018年6月12日
 */
@Repository
public class MaterialResourceDao extends ServiceImpl<MaterialResourceMapper, MaterialResource> {

    @Autowired
    private MaterialResourceMapper materialResourceMapper;
    /**
     * 获取素材库资源列表
     * 
     * @return 素材库资源列表
     * @throws Exception
     */
    public List<MaterialResource> findMaterialResourceList() {
        QueryWrapper<MaterialResource> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", DataConst.DATA_SUCCESS_STATUS);
        queryWrapper.orderByDesc("sort_num", "createtime");
        return this.list(queryWrapper);
    }

    /**
     * 获取素材列表
     * 
     * @param id
     *            素材ID
     * @param pageNo
     *            当前页码
     * @param pageSize
     *            每页大小
     * @return
     */
    public IPage<MaterialResource> findResourcePage(String id,String label, int pageNo, int pageSize, String localeCode) {
        QueryWrapper<MaterialResource> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", DataConst.DATA_SUCCESS_STATUS);

        if (ToolsKit.isNotEmpty(id)) {
            queryWrapper.eq("m_id", id);
        }

        if (ToolsKit.isNotEmpty(localeCode)) {
            queryWrapper.eq("locale_code", localeCode);
        }

        if (ToolsKit.isNotEmpty(label)){
            queryWrapper.like("label", label);
        }

        queryWrapper.orderByDesc("sort_num", "createtime");
        Page<MaterialResource> page = new Page<>(pageNo + 1, pageSize);
        return this.page(page, queryWrapper);
    }
}
