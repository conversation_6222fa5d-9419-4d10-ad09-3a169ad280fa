package net.snaptag.system.business.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import net.snaptag.system.business.entity.ShortLink;
import net.snaptag.system.business.mapper.ShortLinkMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @date ：Created in 2022/3/24 9:41
 * @description：短链接DAO
 * @modified By：
 * @version: $
 */
@Repository
public class ShortLinkDao extends ServiceImpl<ShortLinkMapper, ShortLink> {

    @Autowired
    private ShortLinkMapper shortLinkMapper;
    public Long getCount(){
        return (long) this.count();
    }
}
