package net.snaptag.system.business.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import net.snaptag.system.business.entity.MsgColumn;
import net.snaptag.system.business.mapper.MsgColumnMapper;
import net.snaptag.system.common.DataConst;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

@Repository
public class MsgColumnDao extends ServiceImpl<MsgColumnMapper, MsgColumn> {

    @Autowired
    private MsgColumnMapper msgColumnMapper;
    /**
     * 根据用户ID获取消息栏目
     * 
     * @return 消息栏目
     * @throws Exception
     */
    public MsgColumn getMsgColumnByUserId(String userId) {
        QueryWrapper<MsgColumn> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId);
        queryWrapper.eq("status", DataConst.DATA_SUCCESS_STATUS);
        return this.getOne(queryWrapper);
    }
}