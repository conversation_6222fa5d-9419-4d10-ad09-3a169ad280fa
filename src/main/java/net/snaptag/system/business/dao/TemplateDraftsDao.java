package net.snaptag.system.business.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import net.snaptag.system.business.entity.TemplateDrafts;
import net.snaptag.system.business.enums.PaperInfoEnums;
import net.snaptag.system.business.mapper.TemplateDraftsMapper;
import net.snaptag.system.common.DataConst;
import net.snaptag.system.sadais.util.core.ToolsKit;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2021/9/28 11:05
 * @description：模板草稿DAO
 * @modified By：
 * @version: $
 */
@Repository
public class TemplateDraftsDao extends ServiceImpl<TemplateDraftsMapper, TemplateDrafts> {

    @Autowired
    private TemplateDraftsMapper templateDraftsMapper;

    public List<TemplateDrafts> findList(int pageNo, int pageSize, String type, int isHot, String localeCode, String printerType, String paperType, String length) {
        QueryWrapper<TemplateDrafts> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", DataConst.DATA_SUCCESS_STATUS);

        if (ToolsKit.isNotEmpty(type)) {
            queryWrapper.eq("type", type);
        }
        if (ToolsKit.isNotEmpty(localeCode)) {
            queryWrapper.like("locale_code", localeCode);
        }
        if (isHot != 0) {
            queryWrapper.eq("is_hot", isHot);
        }

        if (ToolsKit.isNotEmpty(length)) {
            // 这里要过滤下，如果printerType有值，过滤掉，重复的
            String finalLength = length;
            if (ToolsKit.isNotEmpty(printerType)) {
                List<PaperInfoEnums> paperInfoEnums = PaperInfoEnums.getEnumsByPrinterTYpe(printerType);
                for (PaperInfoEnums enums : paperInfoEnums) {
                    if (enums.getLength().trim().equals(length)) {
                        finalLength = enums.getLength();
                    }
                }
            }

            // 精确匹配纸张长度，支持以length结尾或length+逗号的格式
            final String lengthToMatch = finalLength;
            queryWrapper.and(wrapper -> wrapper
                    .like("printer_type", lengthToMatch + ",")
                    .or().like("printer_type", "%" + lengthToMatch)
            );
        } else if (ToolsKit.isNotEmpty(printerType)) {
            List<PaperInfoEnums> paperInfoEnums = PaperInfoEnums.getEnumsByPrinterTYpe(printerType);
            if (ToolsKit.isNotEmpty(paperInfoEnums)) {
                queryWrapper.and(wrapper -> {
                    for (int i = 0; i < paperInfoEnums.size(); i++) {
                        String paperLength = paperInfoEnums.get(i).getLength();
                        if (i == 0) {
                            wrapper.like("printer_type", paperLength + ",")
                                    .or().like("printer_type", "%" + paperLength);
                        } else {
                            wrapper.or().like("printer_type", paperLength + ",")
                                    .or().like("printer_type", "%" + paperLength);
                        }
                    }
                });
            }
        }

        if (ToolsKit.isNotEmpty(paperType)) {
            // 尝试不同的查询方式
            queryWrapper.and(wrapper -> {
                // 方式1：直接匹配paperType值
                wrapper.eq("paper_type", paperType)
                        // 方式2：LIKE查询包含paperType
                        .or().like("paper_type", paperType)
                        // 方式3：原来的范围查询（如果paper_type是数字类型）
                        .or().apply("CAST(paper_type AS SIGNED) >= {0} AND CAST(paper_type AS SIGNED) < {1}",
                                Integer.parseInt(paperType) * 10,
                                (Integer.parseInt(paperType) + 1) * 10);
            });
        }

        queryWrapper.orderByDesc("sort_num");
        queryWrapper.last("LIMIT " + pageSize + " OFFSET " + (pageNo > 0 ? (pageNo - 1) * pageSize : 0));
        return this.list(queryWrapper);
    }

    public List<TemplateDrafts> findList(int pageNo, int pageSize, String type, int isHot, String localeCode, List<String> lens, String paperType, Integer widthBegin, Integer widthEnd, String keyword) {
        QueryWrapper<TemplateDrafts> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", DataConst.DATA_SUCCESS_STATUS);

        if (ToolsKit.isNotEmpty(type)) {
            queryWrapper.eq("type", type);
        }
        if (ToolsKit.isNotEmpty(localeCode)) {
            queryWrapper.like("locale_code", localeCode);
        }
        if (isHot != 0) {
            queryWrapper.eq("is_hot", isHot);
        }

        if (ToolsKit.isNotEmpty(keyword)) {
            queryWrapper.and(wrapper -> wrapper
                    .like("name", keyword)
                    .or().like("recommend", keyword)
                    .or().like("content", keyword)
                    .or().like("paper_width", keyword)
                    .or().like("paper_height", keyword)
            );
        }

//        if (ToolsKit.isNotEmpty(length)){
//            // 精确匹配纸张尺寸，支持以length结尾或length+分号的格式
//            queryWrapper.and(wrapper -> wrapper
//                .like("paper_size", "%" + length)
//                .or().like("paper_size", length + ";%")
//            );
        if (widthBegin != null || widthEnd != null) {
            // 尺寸范围查询（参考JFinal的indexRange处理）
            if (widthBegin != null) {
                queryWrapper.ge("paper_width", widthBegin);
            }
            if (widthEnd != null) {
                queryWrapper.le("paper_width", widthEnd);
            }
        } else if (ToolsKit.isNotEmpty(lens)) {
            queryWrapper.and(wrapper -> {
                for (int i = 0; i < lens.size(); i++) {
                    String escapedLength = lens.get(i).replaceAll("\\*", "\\\\*");
                    if (i == 0) {
                        wrapper.like("paper_size", "%" + escapedLength + "%")
                                .or().like("paper_size", escapedLength + ";%")
                                .or().like("paper_size", "%" + escapedLength);
                    } else {
                        wrapper.or().like("paper_size", "%" + escapedLength + "%")
                                .or().like("paper_size", escapedLength + ";%")
                                .or().like("paper_size", "%" + escapedLength);
                    }
                }
            });
        }

        if (ToolsKit.isNotEmpty(paperType)) {
            // 尝试不同的查询方式
            queryWrapper.and(wrapper -> {
                // 方式1：直接匹配paperType值
                wrapper.eq("paper_type", paperType)
                        // 方式2：LIKE查询包含paperType
                        .or().like("paper_type", paperType)
                        // 方式3：原来的范围查询（如果paper_type是数字类型）
                        .or().apply("CAST(paper_type AS SIGNED) >= {0} AND CAST(paper_type AS SIGNED) < {1}",
                                Integer.parseInt(paperType) * 10,
                                (Integer.parseInt(paperType) + 1) * 10);
            });
        }

        queryWrapper.orderByDesc("sort_num");
        queryWrapper.last("LIMIT " + pageSize + " OFFSET " + (pageNo > 0 ? (pageNo - 1) * pageSize : 0));
        return this.list(queryWrapper);
    }

    public IPage<TemplateDrafts> findPage(int pageNo, int pageSize, String type, int isHot, String localeCode, String printerType, String paperType, String length) {
        QueryWrapper<TemplateDrafts> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", DataConst.DATA_SUCCESS_STATUS);

        if (ToolsKit.isNotEmpty(type)) {
            queryWrapper.eq("type", type);
        }
        if (ToolsKit.isNotEmpty(localeCode)) {
            queryWrapper.like("locale_code", localeCode);
        }
        if (isHot != 0) {
            queryWrapper.eq("is_hot", isHot);
        }

        if (ToolsKit.isNotEmpty(length)) {
            // 精确匹配纸张长度
            queryWrapper.like("printer_type", "%" + length + "%");
        } else if (ToolsKit.isNotEmpty(printerType)) {
            List<PaperInfoEnums> paperInfoEnums = PaperInfoEnums.getEnumsByPrinterTYpe(printerType);
            if (ToolsKit.isNotEmpty(paperInfoEnums)) {
                queryWrapper.and(wrapper -> {
                    for (int i = 0; i < paperInfoEnums.size(); i++) {
                        String paperLength = paperInfoEnums.get(i).getLength();
                        if (i == 0) {
                            wrapper.like("printer_type", paperLength + ",")
                                    .or().like("printer_type", "%" + paperLength);
                        } else {
                            wrapper.or().like("printer_type", paperLength + ",")
                                    .or().like("printer_type", "%" + paperLength);
                        }
                    }
                });
            }
        }

        if (ToolsKit.isNotEmpty(paperType)) {
            // 尝试不同的查询方式
            queryWrapper.and(wrapper -> {
                // 方式1：直接匹配paperType值
                wrapper.eq("paper_type", paperType)
                        // 方式2：LIKE查询包含paperType
                        .or().like("paper_type", paperType)
                        // 方式3：原来的范围查询（如果paper_type是数字类型）
                        .or().apply("CAST(paper_type AS SIGNED) >= {0} AND CAST(paper_type AS SIGNED) < {1}",
                                Integer.parseInt(paperType) * 10,
                                (Integer.parseInt(paperType) + 1) * 10);
            });
        }

        queryWrapper.orderByDesc("sort_num");
        Page<TemplateDrafts> page = new Page<>(pageNo + 1, pageSize);
        return this.page(page, queryWrapper);
    }

    public IPage<TemplateDrafts> findPage(int pageNo, int pageSize, String keyword, String localeCode, String printerType, String paperType) {
        QueryWrapper<TemplateDrafts> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", DataConst.DATA_SUCCESS_STATUS);

        if (ToolsKit.isNotEmpty(localeCode)) {
            queryWrapper.like("locale_code", localeCode);
        }

        if (ToolsKit.isNotEmpty(printerType)) {
            queryWrapper.like("printer_type", printerType);
        }

        if (ToolsKit.isNotEmpty(keyword)) {
            queryWrapper.and(wrapper -> wrapper
                    .like("name", keyword)
                    .or().like("recommend", keyword)
                    .or().like("content", keyword)
            );
        }

        if (ToolsKit.isNotEmpty(paperType)) {
            queryWrapper.like("paper_type", paperType);
        }

        queryWrapper.orderByDesc("sort_num");
        Page<TemplateDrafts> page = new Page<>(pageNo + 1, pageSize);
        return this.page(page, queryWrapper);
    }
}
