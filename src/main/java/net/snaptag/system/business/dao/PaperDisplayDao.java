package net.snaptag.system.business.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import net.snaptag.system.business.entity.PaperDisplay;
import net.snaptag.system.business.mapper.PaperDisplayMapper;
import net.snaptag.system.common.DataConst;
import net.snaptag.system.sadais.util.core.ToolsKit;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2024/3/28 16:59
 * @description：纸张显示DAO
 * @modified By：
 * @version: $
 */
@Repository
public class PaperDisplayDao extends ServiceImpl<PaperDisplayMapper, PaperDisplay> {

    @Autowired
    private PaperDisplayMapper paperDisplayMapper;
    public IPage<PaperDisplay> getPageByCond(int pageNo, int pageSize, String name) {
        QueryWrapper<PaperDisplay> queryWrapper = new QueryWrapper<>();

        if (ToolsKit.isNotEmpty(name)) {
            queryWrapper.like("name", name);
        }

        queryWrapper.eq("status", DataConst.DATA_SUCCESS_STATUS);
        queryWrapper.orderByDesc("sort_num");
        Page<PaperDisplay> page = new Page<>(pageNo + 1, pageSize);
        return this.page(page, queryWrapper);
    }

    public List<PaperDisplay> findListByCond() {
        QueryWrapper<PaperDisplay> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", DataConst.DATA_SUCCESS_STATUS);
        queryWrapper.orderByDesc("sort_num");
        return this.list(queryWrapper);
    }
}
