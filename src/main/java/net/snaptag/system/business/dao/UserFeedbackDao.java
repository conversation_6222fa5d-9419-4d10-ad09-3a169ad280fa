package net.snaptag.system.business.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import net.snaptag.system.business.entity.UserFeedback;
import net.snaptag.system.business.mapper.UserFeedbackMapper;
import net.snaptag.system.common.DataConst;
import net.snaptag.system.sadais.util.core.ToolsKit;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * 用户反馈信息
 *
 * <AUTHOR> 2019年10月08日
 */
@Repository
public class UserFeedbackDao extends ServiceImpl<UserFeedbackMapper, UserFeedback> {

    @Autowired
    private UserFeedbackMapper userFeedbackMapper;

    /**
     * 获取用户的反馈列表
     * @param userId
     * @param pageNo
     * @param pageSize
     * @return
     */
    public List<UserFeedback> findUserFeedbackList(String userId, int pageNo, int pageSize) {
        QueryWrapper<UserFeedback> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId);
        queryWrapper.orderByDesc("createtime");
        queryWrapper.last("LIMIT " + pageSize + " OFFSET " + (pageNo * pageSize));
        return this.list(queryWrapper);
    }

    /**
     * 获取用户的反馈分页列表
     *
     * @param pageNo - 当前页码
     * @param pageSize - 每页大小
     * @return
     */
    public Page<UserFeedback> findUserFeedbackPage(Date startDate, Date endDate, int pageNo, int pageSize) {
        QueryWrapper<UserFeedback> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", DataConst.DATA_SUCCESS_STATUS);

        if (ToolsKit.isNotEmpty(startDate) && ToolsKit.isNotEmpty(endDate)) {
            queryWrapper.between("createtime", startDate, endDate);
        }

        queryWrapper.orderByDesc("createtime");
        Page<UserFeedback> page = new Page<>(pageNo + 1, pageSize);
        return this.page(page, queryWrapper);
    }

}
