package net.snaptag.system.business.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import net.snaptag.system.business.entity.PrintDriverUpdateInfo;
import net.snaptag.system.business.mapper.PrintDriverUpdateInfoMapper;
import net.snaptag.system.common.DataConst;
import net.snaptag.system.sadais.util.core.ToolsKit;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2023/4/6 8:59
 * @description：打印机驱动更新DAO
 * @modified By：
 * @version: $ 打印机驱动更新
 */
@Repository
public class PrintDriverUpdateInfoDao extends ServiceImpl<PrintDriverUpdateInfoMapper, PrintDriverUpdateInfo> {

    @Autowired
    private PrintDriverUpdateInfoMapper printDriverUpdateInfoMapper;
    public List<PrintDriverUpdateInfo> findListByCond(String printerModel){
        QueryWrapper<PrintDriverUpdateInfo> queryWrapper = new QueryWrapper<>();
        if (ToolsKit.isNotEmpty(printerModel)){
            queryWrapper.eq("printer_model", printerModel);
        }
        queryWrapper.eq("status", DataConst.DATA_SUCCESS_STATUS);
        queryWrapper.orderByDesc("createtime");
        return this.list(queryWrapper);
    }

    public List<PrintDriverUpdateInfo> findListByUsed(String printerModel, Integer usedFlag){
        QueryWrapper<PrintDriverUpdateInfo> queryWrapper = new QueryWrapper<>();
        if (ToolsKit.isNotEmpty(printerModel)){
            queryWrapper.eq("printer_model", printerModel);
        }
        if (usedFlag!=null){
            queryWrapper.eq("used_flag", usedFlag.intValue());
        }
        queryWrapper.eq("status", DataConst.DATA_SUCCESS_STATUS);
        queryWrapper.orderByDesc("version_code");
        return this.list(queryWrapper);
    }

    public IPage<PrintDriverUpdateInfo> findPageByCond(String printerModel, int pageNo, int pageSize){
        QueryWrapper<PrintDriverUpdateInfo> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", DataConst.DATA_SUCCESS_STATUS);

        if (ToolsKit.isNotEmpty(printerModel)){
            queryWrapper.eq("printer_model", printerModel);
            queryWrapper.orderByDesc("version_code");
        } else {
            queryWrapper.orderByDesc("createtime");
        }

        Page<PrintDriverUpdateInfo> page = new Page<>(pageNo, pageSize);
        return this.page(page, queryWrapper);
    }
}
