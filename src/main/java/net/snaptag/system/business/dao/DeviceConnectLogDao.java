package net.snaptag.system.business.dao;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import net.snaptag.system.business.entity.DeviceConnectLog;
import net.snaptag.system.business.mapper.DeviceConnectLogMapper;
import net.snaptag.system.common.DataConst;
import net.snaptag.system.sadais.util.core.ToolsKit;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.Date;

/**
 * 设备连接日志
 *
 * <AUTHOR> 2018年6月12日
 */
@Repository
public class DeviceConnectLogDao extends ServiceImpl<DeviceConnectLogMapper, DeviceConnectLog> {

    @Autowired
    private DeviceConnectLogMapper deviceConnectLogMapper;
    /**
     * 查询设备连接日志记录
     * 
     * @param userId
     *            用户ID
     * @param startDate
     *            开始时间
     * @param endDate
     *            结束时间
     * @param pageNo
     *            当前页码
     * @param pageSize
     *            每页大小
     * @return
     */
    public IPage<DeviceConnectLog> findConnLogPage(int pageNo, int pageSize, String userId, String deviceSn, String deviceName, String macAddress, Date startDate, Date endDate) {
        QueryWrapper<DeviceConnectLog> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", DataConst.DATA_SUCCESS_STATUS);

        if (ToolsKit.isNotEmpty(startDate) && ToolsKit.isNotEmpty(endDate)) {
            queryWrapper.between("createtime", startDate, endDate);
        }
        if (ToolsKit.isNotEmpty(userId)) {
            queryWrapper.eq("user_id", userId);
        }
        if (ToolsKit.isNotEmpty(deviceSn)) {
            queryWrapper.eq("device_sn", deviceSn);
        }
        if (ToolsKit.isNotEmpty(deviceName)) {
            queryWrapper.eq("device_name", deviceName.toUpperCase());
        }
        if (ToolsKit.isNotEmpty(macAddress)) {
            queryWrapper.eq("mac_address", macAddress);
        }

        queryWrapper.orderByDesc("device_lastest_time");
        Page<DeviceConnectLog> page = new Page<>(pageNo + 1, pageSize);
        return this.page(page, queryWrapper);
    }

    public DeviceConnectLog findOneByUserIdSnMac(String userId, String deviceSn, String deviceMac) {
        QueryWrapper<DeviceConnectLog> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", DataConst.DATA_SUCCESS_STATUS);

        if (ToolsKit.isNotEmpty(userId)) {
            queryWrapper.eq("user_id", userId);
        }
        if (ToolsKit.isNotEmpty(deviceMac)){
            queryWrapper.eq("mac_address", deviceMac);
        } else if (ToolsKit.isNotEmpty(deviceSn)){
            queryWrapper.eq("device_sn", deviceSn);
        }

        return this.getOne(queryWrapper);
    }
}
