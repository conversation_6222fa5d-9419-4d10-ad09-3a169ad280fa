package net.snaptag.system.business.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import net.snaptag.system.business.entity.WebPrint;
import net.snaptag.system.business.mapper.WebPrintMapper;
import net.snaptag.system.common.DataConst;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 网页打印数据操作
 *
 * <AUTHOR> 2019年11月05日
 */
@Repository
public class WebPrintDao extends ServiceImpl<WebPrintMapper, WebPrint> {

    @Autowired
    private WebPrintMapper webPrintMapper;

    /**
     * 获取默认分组列表
     * @return
     */
    public List<WebPrint> findDefaultGroupList() {
        QueryWrapper<WebPrint> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("is_default", 1);
        queryWrapper.orderByAsc("createtime");
        return this.list(queryWrapper);
    }

    /**
     * 获取指定用户的分组列表
     * @param userId - 用户标识
     * @return
     */
    public List<WebPrint> findUserGroupList(String userId) {
        QueryWrapper<WebPrint> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId);
        queryWrapper.orderByAsc("createtime");
        return this.list(queryWrapper);
    }

    /***
     * 分组名字叫“阅读”,且被删除的
     * @return
     */
    public List<WebPrint> findUserGroupListForDeleteYuedu(String userId){
        QueryWrapper<WebPrint> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId);
        queryWrapper.orderByAsc("createtime");
        return this.list(queryWrapper);
    }

    /**
     * 获取指定用户的分组
     * @param userId - 用户标识
     * @param groupId - 分组标识
     * @return
     */
    public WebPrint findUserGroup(String userId, String groupId) {
        QueryWrapper<WebPrint> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId);
        queryWrapper.eq("id", groupId);
        return this.getOne(queryWrapper);
    }

}
