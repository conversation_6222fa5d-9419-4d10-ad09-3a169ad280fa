package net.snaptag.system.business.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import net.snaptag.system.business.entity.PrintPaperInfo;
import net.snaptag.system.business.mapper.PrintPaperInfoMapper;
import net.snaptag.system.common.DataConst;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2023/7/20 10:07
 * @description：打印纸张信息DAO
 * @modified By：
 * @version: $
 */
@Repository
public class PrintPaperInfoDao extends ServiceImpl<PrintPaperInfoMapper, PrintPaperInfo> {

    @Autowired
    private PrintPaperInfoMapper printPaperInfoMapper;

    public List<PrintPaperInfo> findListOrderBySortNum() {
        QueryWrapper<PrintPaperInfo> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", DataConst.DATA_SUCCESS_STATUS);
        queryWrapper.orderByAsc("sort_num", "paper_id");
        return this.list(queryWrapper);
    }
}
