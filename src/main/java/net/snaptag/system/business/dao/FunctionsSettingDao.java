package net.snaptag.system.business.dao;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import net.snaptag.system.business.entity.FunctionsSetting;
import net.snaptag.system.business.mapper.FunctionsSettingMapper;
import net.snaptag.system.common.DataConst;
import net.snaptag.system.sadais.util.core.ToolsKit;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2024/1/11 10:46
 * @description：功能设置DAO
 * @modified By：
 * @version: $
 */
@Repository
public class FunctionsSettingDao extends ServiceImpl<FunctionsSettingMapper, FunctionsSetting> {

    @Autowired
    private FunctionsSettingMapper functionsSettingMapper;

    public List<FunctionsSetting> findListByCond(String printerType, String type, String languageCodes){
        QueryWrapper<FunctionsSetting> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", DataConst.DATA_SUCCESS_STATUS);

        if (ToolsKit.isNotEmpty(printerType)){
            queryWrapper.like("printer_types", printerType.toLowerCase());
        }
        if (ToolsKit.isNotEmpty(languageCodes)){
            queryWrapper.like("language_codes", languageCodes);
        }
        if (ToolsKit.isNotEmpty(type)){
            queryWrapper.eq("type", type);
        }

        queryWrapper.orderByAsc("sort_num");
        return this.list(queryWrapper);
    }
}
