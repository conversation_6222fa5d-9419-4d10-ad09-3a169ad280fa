package net.snaptag.system.business.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import net.snaptag.system.business.entity.UpdateInfo;
import net.snaptag.system.business.mapper.UpdateInfoMapper;
import net.snaptag.system.common.DataConst;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 版本更新信息
 *
 * <AUTHOR> 2018年6月12日
 */
@Repository
public class UpdateInfoDao extends ServiceImpl<UpdateInfoMapper, UpdateInfo> {

    @Autowired
    private UpdateInfoMapper updateInfoMapper;
    /**
     * 获取版本更新信息列表
     * 
     * @return 版本更新信息列表
     * @throws Exception
     */
    public List<UpdateInfo> findUpdateInfoList() {
        QueryWrapper<UpdateInfo> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", DataConst.DATA_SUCCESS_STATUS);
        queryWrapper.orderByDesc("createtime");
        return this.list(queryWrapper);
    }

    /**
     * 获取版本更新信息分页数据
     * 
     * @param pageNo
     *            当前页码
     * @param pageSize
     *            每页大小
     * @return
     */
    public IPage<UpdateInfo> findUpdateInfoPage(int pageNo, int pageSize) {
        QueryWrapper<UpdateInfo> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", DataConst.DATA_SUCCESS_STATUS);
        queryWrapper.orderByDesc("createtime");
        Page<UpdateInfo> page = new Page<>(pageNo + 1, pageSize);
        return this.page(page, queryWrapper);
    }
}
