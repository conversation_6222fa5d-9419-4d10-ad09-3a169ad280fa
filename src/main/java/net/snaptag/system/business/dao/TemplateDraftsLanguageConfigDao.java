package net.snaptag.system.business.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import net.snaptag.system.business.entity.TemplateDraftsLanguageConfig;
import net.snaptag.system.business.mapper.TemplateDraftsLanguageConfigMapper;
import net.snaptag.system.common.DataConst;
import net.snaptag.system.sadais.util.core.ToolsKit;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @date ：Created in 2022/3/24 9:41
 * @description：模板草稿语言配置DAO
 * @modified By：
 * @version: $
 */
@Repository
public class TemplateDraftsLanguageConfigDao extends ServiceImpl<TemplateDraftsLanguageConfigMapper, TemplateDraftsLanguageConfig> {

    @Autowired
    private TemplateDraftsLanguageConfigMapper templateDraftsLanguageConfigMapper;
    public TemplateDraftsLanguageConfig getConfigByLanguage(String language){
        if (ToolsKit.isEmpty(language)){
            return null;
        }
        QueryWrapper<TemplateDraftsLanguageConfig> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("language", language);
        queryWrapper.eq("status", DataConst.DATA_SUCCESS_STATUS);
        return this.getOne(queryWrapper);
    }
}
