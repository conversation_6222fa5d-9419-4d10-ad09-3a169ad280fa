package net.snaptag.system.business.dao;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import net.snaptag.system.business.entity.Dict;
import net.snaptag.system.business.mapper.DictMapper;
import net.snaptag.system.common.DataConst;
import net.snaptag.system.sadais.util.core.ToolsKit;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.LinkedHashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2023/8/24 9:23
 * @description：Dict DAO
 * @modified By：
 * @version: $
 */
@Repository
public class DictDao extends ServiceImpl<DictMapper, Dict> {

    @Autowired
    private DictMapper dictMapper;

    /**
     * 查找不同的类型
     */
    public LinkedHashMap<String, String> findDistinctType() {
        List<Dict> list = dictMapper.findAllList(DataConst.DATA_SUCCESS_STATUS);
        if (ToolsKit.isNotEmpty(list)) {
            LinkedHashMap<String, String> result = new LinkedHashMap<>();
            list.forEach(item -> {
                if (result.get(item.getType()) == null) {
                    result.put(item.getType(), item.getName());
                }
            });
            return result;
        }
        return new LinkedHashMap<>();
    }

    /**
     * 根据类型分页查询
     */
    public IPage<Dict> findPageByType(int pageNo, int pageSize, String type) {
        Page<Dict> page = new Page<>(pageNo + 1, pageSize);
        return dictMapper.findDictPage(page, null, type, null, DataConst.DATA_SUCCESS_STATUS);
    }

    /**
     * 根据类型查询列表
     */
    public List<Dict> findListByType(String type) {
        return dictMapper.findListByType(type, DataConst.DATA_SUCCESS_STATUS);
    }

    /**
     * 根据类型和语言代码查询列表
     */
    public List<Dict> findListByTypeAndLocale(String type, String localeCode) {
        return dictMapper.findListByTypeAndLocale(type, localeCode, DataConst.DATA_SUCCESS_STATUS);
    }
}
