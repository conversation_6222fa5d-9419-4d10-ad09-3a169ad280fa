package net.snaptag.system.business.dao;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import net.snaptag.system.business.entity.Material;
import net.snaptag.system.business.mapper.MaterialMapper;
import net.snaptag.system.common.DataConst;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 素材库 DAO
 *
 * <AUTHOR> 2018年6月12日
 */
@Repository
public class MaterialDao extends ServiceImpl<MaterialMapper, Material> {

    @Autowired
    private MaterialMapper materialMapper;

    /**
     * 获取素材库列表
     *
     * @return 素材库列表
     */
    public List<Material> findMaterialList() {
        return materialMapper.findAllList(DataConst.DATA_SUCCESS_STATUS);
    }

    /**
     * 分页查询素材库列表
     */
    public IPage<Material> findMaterialPage(String name, Integer type, Integer subType, String pId, int pageNo, int pageSize) {
        Page<Material> page = new Page<>(pageNo + 1, pageSize);
        return materialMapper.findMaterialPage(page, name, type, subType, pId, DataConst.DATA_SUCCESS_STATUS);
    }

    /**
     * 根据类型查询素材库列表
     */
    public List<Material> findListByType(int type) {
        return materialMapper.findListByType(type, DataConst.DATA_SUCCESS_STATUS);
    }

    /**
     * 根据父ID查询子素材库列表
     */
    public List<Material> findListByPId(String pId) {
        return materialMapper.findListByPId(pId, DataConst.DATA_SUCCESS_STATUS);
    }
}
