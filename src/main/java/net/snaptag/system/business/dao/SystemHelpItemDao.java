package net.snaptag.system.business.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import net.snaptag.system.business.entity.SystemHelpItem;
import net.snaptag.system.business.mapper.SystemHelpItemMapper;
import net.snaptag.system.common.DataConst;
import net.snaptag.system.sadais.util.core.ToolsKit;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;

/**
 * 系统帮助项
 *
 * <AUTHOR> 2019年10月08日
 */
@Repository
public class SystemHelpItemDao extends ServiceImpl<SystemHelpItemMapper, SystemHelpItem> {

    @Autowired
    private SystemHelpItemMapper systemHelpItemMapper;

    /**
     * 获取系统帮助项列表
     * @param pageNo
     * @param pageSize
     * @return
     */
    public List<SystemHelpItem> findHelpItemList(int pageNo, int pageSize) {
        QueryWrapper<SystemHelpItem> queryWrapper = new QueryWrapper<>();
        queryWrapper.orderByAsc("createtime");
        queryWrapper.last("LIMIT " + pageSize + " OFFSET " + (pageNo * pageSize));
        return this.list(queryWrapper);
    }

    /**
     * 获取系统帮助项列表
     * @param pageNo
     * @param pageSize
     * @return
     */
    public IPage<SystemHelpItem> findHelpItemPage(int pageNo, int pageSize, String type, String printerType, String language) {
        QueryWrapper<SystemHelpItem> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", DataConst.DATA_SUCCESS_STATUS);

        if (ToolsKit.isNotEmpty(type)){
            queryWrapper.eq("type", type);
        }

        if (ToolsKit.isNotEmpty(printerType)){
            queryWrapper.like("printer_type", printerType.toLowerCase());
        }

        if (ToolsKit.isNotEmpty(language)){
            queryWrapper.like("local_language_code", language);
        }

        queryWrapper.orderByAsc("sort_num");
        Page<SystemHelpItem> page = new Page<>(pageNo + 1, pageSize);
        return this.page(page, queryWrapper);
    }
}
