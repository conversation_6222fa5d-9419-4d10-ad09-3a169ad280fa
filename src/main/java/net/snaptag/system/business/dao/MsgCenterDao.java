package net.snaptag.system.business.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import net.snaptag.system.business.entity.MsgCenter;
import net.snaptag.system.business.mapper.MsgCenterMapper;
import net.snaptag.system.common.DataConst;
import net.snaptag.system.sadais.util.core.ToolsKit;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class MsgCenterDao extends ServiceImpl<MsgCenterMapper, MsgCenter> {

    @Autowired
    private MsgCenterMapper msgCenterMapper;
    /**
     * 获取消息列表
     * 
     * @return 消息列表
     * @throws Exception
     */
    public List<MsgCenter> findMsgCenterList(String userId, int msgType, int pageNo, int pageSize, String lastId) {
        QueryWrapper<MsgCenter> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId);
        queryWrapper.eq("msg_type", msgType);
        queryWrapper.eq("status", DataConst.DATA_SUCCESS_STATUS);
        if (ToolsKit.isNotEmpty(lastId)) {
            queryWrapper.lt("id", lastId);
        }
        queryWrapper.orderByDesc("id");
        queryWrapper.select("id", "createtime");
        queryWrapper.last("LIMIT " + pageSize + " OFFSET " + (pageNo * pageSize));
        return this.list(queryWrapper);
    }

    /**
     * 获取消息列表（未读的）
     *
     * @return 消息列表
     * @throws Exception
     */
    public List<MsgCenter> findUnReadMsgCenterList(String userId, int msgType) {
        QueryWrapper<MsgCenter> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId);
        queryWrapper.eq("msg_type", msgType);
        queryWrapper.eq("status", DataConst.DATA_SUCCESS_STATUS);
        queryWrapper.eq("read_status", 0);
        queryWrapper.orderByDesc("id");
        queryWrapper.select("id", "createtime");
        return this.list(queryWrapper);
    }

    /**
     * 获取消息列表
     *
     * @return 消息列表
     * @throws Exception
     */
    public long countMsgCenter(String userId, int msgType, int readStatus) {
        QueryWrapper<MsgCenter> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId);
        queryWrapper.eq("msg_type", msgType);
        queryWrapper.eq("read_status", readStatus);
        queryWrapper.eq("status", DataConst.DATA_SUCCESS_STATUS);
        return this.count(queryWrapper);
    }

    /**
     * 根据ID获取消息对象
     * 
     * @return 消息对象
     * @throws Exception
     */
    public MsgCenter getMsgCenterById(String id) {
        QueryWrapper<MsgCenter> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("id", id);
        queryWrapper.eq("status", DataConst.DATA_SUCCESS_STATUS);
        return this.getOne(queryWrapper);
    }
}