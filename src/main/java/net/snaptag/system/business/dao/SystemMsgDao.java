package net.snaptag.system.business.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import net.snaptag.system.business.entity.SystemMsg;
import net.snaptag.system.business.mapper.SystemMsgMapper;
import net.snaptag.system.common.DataConst;
import net.snaptag.system.sadais.util.core.ToolsKit;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Repository
public class SystemMsgDao extends ServiceImpl<SystemMsgMapper, SystemMsg> {

    @Autowired
    private SystemMsgMapper systemMsgMapper;
    /**
     * 获取系统消息列表
     * 
     * @return 系统消息列表
     * @throws Exception
     */
    public List<SystemMsg> findSystemMsgList() {
        QueryWrapper<SystemMsg> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", DataConst.DATA_SUCCESS_STATUS);
        queryWrapper.orderByDesc("createtime");
        return this.list(queryWrapper);
    }

    /**
     * 查询系统消息
     * 
     * @param startDate
     *            开始时间
     * @param endDate
     *            结束时间
     * @param pageNo
     *            当前页码
     * @param pageSize
     *            每页大小
     * @return
     */
    public IPage<SystemMsg> findSystemMsgPage(Date startDate, Date endDate, int pageNo, int pageSize) {
        QueryWrapper<SystemMsg> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", DataConst.DATA_SUCCESS_STATUS);
        if (ToolsKit.isNotEmpty(startDate) && ToolsKit.isNotEmpty(endDate)) {
            queryWrapper.between("start_date", startDate, endDate);
        }
        queryWrapper.orderByDesc("createtime");
        Page<SystemMsg> page = new Page<>(pageNo + 1, pageSize);
        return this.page(page, queryWrapper);
    }
}