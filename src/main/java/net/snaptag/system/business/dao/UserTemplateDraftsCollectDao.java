package net.snaptag.system.business.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import net.snaptag.system.business.entity.TemplateDrafts;
import net.snaptag.system.business.entity.UserTemplateDraftsCollect;
import net.snaptag.system.business.mapper.UserTemplateDraftsCollectMapper;
import net.snaptag.system.common.DataConst;
import net.snaptag.system.sadais.util.core.ToolsKit;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2021/9/29 9:00
 * @description：用户模板草稿收藏DAO
 * @modified By：
 * @version: $
 */
@Repository
public class UserTemplateDraftsCollectDao extends ServiceImpl<UserTemplateDraftsCollectMapper, UserTemplateDraftsCollect> {

    @Autowired
    private UserTemplateDraftsCollectMapper userTemplateDraftsCollectMapper;
    public List<UserTemplateDraftsCollect> findByUserIdAndTemplateId(String userId, String templateDraftsId) {
        QueryWrapper<UserTemplateDraftsCollect> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", DataConst.DATA_SUCCESS_STATUS);
        if (ToolsKit.isNotEmpty(userId)){
            queryWrapper.eq("user_id", userId);
        }
        if (ToolsKit.isNotEmpty(templateDraftsId)){
            queryWrapper.eq("template_drafts_id", templateDraftsId);
        }
        return this.list(queryWrapper);
    }

    public List<UserTemplateDraftsCollect> findByUserIdForPage(String userId, int pageno, int pagesize) {
        QueryWrapper<UserTemplateDraftsCollect> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", DataConst.DATA_SUCCESS_STATUS);
        if (ToolsKit.isNotEmpty(userId)){
            queryWrapper.eq("user_id", userId);
        }
        queryWrapper.last("LIMIT " + pagesize + " OFFSET " + (pageno > 0 ? (pageno - 1) * pagesize : 0));
        return this.list(queryWrapper);
    }
}
