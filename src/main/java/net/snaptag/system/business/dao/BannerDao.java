package net.snaptag.system.business.dao;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import net.snaptag.system.business.entity.Banner;
import net.snaptag.system.business.mapper.BannerMapper;
import net.snaptag.system.common.DataConst;
import net.snaptag.system.sadais.util.core.ToolsKit;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Banner DAO
 *
 * <AUTHOR> 2018年6月12日
 */
@Repository
public class BannerDao extends ServiceImpl<BannerMapper, Banner> {

    @Autowired
    private BannerMapper bannerMapper;

    /**
     * 获取banner信息分页数据
     *
     * @param pageNo 当前页码
     * @param pageSize 每页大小
     * @return
     */
    public IPage<Banner> findBannerPage(String name, String column, String localeCode, int pageNo, int pageSize) {
        Page<Banner> page = new Page<>(pageNo + 1, pageSize); // MyBatis-Plus页码从1开始
        return bannerMapper.findBannerPage(page, name, column, localeCode, DataConst.DATA_SUCCESS_STATUS);
    }

    /**
     * 查询所有Banner列表
     */
    public List<Banner> findAllList() {
        return bannerMapper.findAllList(DataConst.DATA_SUCCESS_STATUS);
    }

    /**
     * 按排序字段查询Banner列表
     */
    public List<Banner> findListSortBySort() {
        return bannerMapper.findListSortBySort(DataConst.DATA_SUCCESS_STATUS);
    }
}
