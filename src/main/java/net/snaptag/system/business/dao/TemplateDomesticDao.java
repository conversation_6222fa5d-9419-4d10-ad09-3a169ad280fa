package net.snaptag.system.business.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import net.snaptag.system.business.entity.TemplateDomestic;
import net.snaptag.system.business.mapper.TemplateDomesticMapper;
import net.snaptag.system.common.DataConst;
import net.snaptag.system.sadais.util.core.ToolsKit;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;

/**
 * TemplateDomestic DAO
 *
 * <AUTHOR>
 */
@Repository
public class TemplateDomesticDao extends ServiceImpl<TemplateDomesticMapper, TemplateDomestic> {

    @Autowired
    private TemplateDomesticMapper templateDomesticMapper;

    /**
     * 查询家用行业模板列表
     */
    public List<TemplateDomestic> findTemplateDomesticList(Integer userId, Integer type, Integer machineType,
                                                           Integer paperType, String groupId, String name,
                                                           int pageNo, int pageSize) {
        QueryWrapper<TemplateDomestic> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", DataConst.DATA_SUCCESS_STATUS);

        if (userId != null) {
            queryWrapper.eq("userId", userId);
        }

        if (type != null) {
            queryWrapper.eq("type", type);
        }

        if (machineType != null) {
            queryWrapper.eq("machineType", machineType);
        }

        if (paperType != null) {
            queryWrapper.eq("paperType", paperType);
        }

        if (groupId != null) {
            queryWrapper.eq("groupId", groupId);
        }

        if (ToolsKit.isNotEmpty(name)) {
            queryWrapper.like("name", name);
        }

        queryWrapper.orderByDesc("createtime");

        // 如果需要分页
        if (pageSize > 0) {
            Page<TemplateDomestic> page = new Page<>(pageNo, pageSize);
            IPage<TemplateDomestic> result = this.page(page, queryWrapper);
            return result.getRecords();
        }

        return this.list(queryWrapper);
    }

    /**
     * 获取家用行业模板分页数据
     */
    public IPage<TemplateDomestic> findTemplateDomesticPage(Integer userId, Integer type, Integer machineType,
                                                            Integer paperType, String groupId, String name,
                                                            int pageNo, int pageSize) {
        QueryWrapper<TemplateDomestic> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", DataConst.DATA_SUCCESS_STATUS);

        if (userId != null) {
            queryWrapper.eq("userId", userId);
        }

        if (type != null) {
            queryWrapper.eq("type", type);
        }

        if (machineType != null) {
            queryWrapper.eq("machineType", machineType);
        }

        if (paperType != null) {
            queryWrapper.eq("paperType", paperType);
        }

        if (groupId != null) {
            queryWrapper.eq("groupId", groupId);
        }

        if (ToolsKit.isNotEmpty(name)) {
            queryWrapper.like("name", name);
        }

        queryWrapper.orderByDesc("createtime");
        Page<TemplateDomestic> page = new Page<>(pageNo, pageSize);
        return this.page(page, queryWrapper);
    }

    /**
     * 根据用户ID查询模板列表
     */
    public List<TemplateDomestic> findByUserId(Integer userId) {
        QueryWrapper<TemplateDomestic> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", DataConst.DATA_SUCCESS_STATUS);
        queryWrapper.eq("userId", userId);
        queryWrapper.orderByDesc("createtime");
        return this.list(queryWrapper);
    }

    /**
     * 根据模板类型查询列表
     */
    public List<TemplateDomestic> findByType(Integer type) {
        QueryWrapper<TemplateDomestic> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", DataConst.DATA_SUCCESS_STATUS);
        queryWrapper.eq("type", type);
        queryWrapper.orderByDesc("createtime");
        return this.list(queryWrapper);
    }

    /**
     * 根据机器类型查询模板列表
     */
    public List<TemplateDomestic> findByMachineType(Integer machineType) {
        QueryWrapper<TemplateDomestic> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", DataConst.DATA_SUCCESS_STATUS);
        queryWrapper.eq("machineType", machineType);
        queryWrapper.orderByDesc("createtime");
        return this.list(queryWrapper);
    }

    /**
     * 根据纸张类型查询模板列表
     */
    public List<TemplateDomestic> findByPaperType(Integer paperType) {
        QueryWrapper<TemplateDomestic> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", DataConst.DATA_SUCCESS_STATUS);
        queryWrapper.eq("paperType", paperType);
        queryWrapper.orderByDesc("createtime");
        return this.list(queryWrapper);
    }

    /**
     * 统计用户模板数量
     */
    public long countByUserId(Integer userId) {
        QueryWrapper<TemplateDomestic> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", DataConst.DATA_SUCCESS_STATUS);
        queryWrapper.eq("userId", userId);
        return this.count(queryWrapper);
    }

    /**
     * 统计指定类型模板数量
     */
    public long countByType(Integer type) {
        QueryWrapper<TemplateDomestic> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", DataConst.DATA_SUCCESS_STATUS);
        queryWrapper.eq("type", type);
        return this.count(queryWrapper);
    }

    /**
     * 根据多个条件统计模板数量
     */
    public long countByConditions(Integer userId, Integer type, Integer machineType, Integer paperType) {
        QueryWrapper<TemplateDomestic> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", DataConst.DATA_SUCCESS_STATUS);

        if (userId != null) {
            queryWrapper.eq("userId", userId);
        }
        if (type != null) {
            queryWrapper.eq("type", type);
        }
        if (machineType != null) {
            queryWrapper.eq("machineType", machineType);
        }
        if (paperType != null) {
            queryWrapper.eq("paperType", paperType);
        }

        return this.count(queryWrapper);
    }

    /**
     * 根据条件查询模板列表（支持尺寸范围查询）
     * 参考JFinal的实现思路
     */
    public List<TemplateDomestic> findTemplateDomesticListWithRange(String groupId, String name,
                                                                    BigDecimal widthBegin, BigDecimal widthEnd,
                                                                    int pageNo, int pageSize, Integer paperType) {
        QueryWrapper<TemplateDomestic> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", DataConst.DATA_SUCCESS_STATUS);

        // 分组ID条件
        if (ToolsKit.isNotEmpty(groupId)) {
            queryWrapper.eq("groupId", groupId);
        }

        // 名称模糊查询（参考JFinal的name处理）
        if (ToolsKit.isNotEmpty(name)) {
            queryWrapper.like("name", name);
        }

        // 尺寸范围查询（参考JFinal的indexRange处理）
        if (widthBegin != null) {
            queryWrapper.ge("width", widthBegin);
        }
        if (widthEnd != null) {
            queryWrapper.le("width", widthEnd);
        }

        if (paperType != null) {
            queryWrapper.eq("paperType", paperType);
        }

        queryWrapper.orderByDesc("createtime");

        // 如果需要分页
        if (pageSize > 0) {
            Page<TemplateDomestic> page = new Page<>(pageNo, pageSize);
            IPage<TemplateDomestic> result = this.page(page, queryWrapper);
            return result.getRecords();
        }

        return this.list(queryWrapper);
    }

    /**
     * 分页查询模板列表（支持尺寸范围查询）
     */
    public IPage<TemplateDomestic> findTemplateDomesticPageWithRange(String groupId, String name,
                                                                     BigDecimal widthBegin, BigDecimal widthEnd,
                                                                     int pageNo, int pageSize) {
        QueryWrapper<TemplateDomestic> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", DataConst.DATA_SUCCESS_STATUS);

        // 分组ID条件
        if (groupId != null) {
            queryWrapper.eq("groupId", groupId);
        }

        // 名称模糊查询
        if (ToolsKit.isNotEmpty(name)) {
            queryWrapper.like("name", name);
        }

        // 尺寸范围查询
        if (widthBegin != null) {
            queryWrapper.ge("width", widthBegin);
        }
        if (widthEnd != null) {
            queryWrapper.le("width", widthEnd);
        }

        queryWrapper.orderByDesc("createtime");
        Page<TemplateDomestic> page = new Page<>(pageNo, pageSize);
        return this.page(page, queryWrapper);
    }
}
