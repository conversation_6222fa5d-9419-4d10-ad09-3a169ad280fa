package net.snaptag.system.business.cache;

import net.snaptag.system.business.buservice.DictBuService;
import net.snaptag.system.business.buservice.FunctionsSettingBuService;
import net.snaptag.system.business.buservice.PaperDisplayBuService;
import net.snaptag.system.business.buservice.PrintPaperInfoBuService;
import net.snaptag.system.business.enums.SystemPublishEnums;
import net.snaptag.system.sadais.cache.common.RedisListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class PubSubService extends RedisListener {
    @Autowired
    private MaterialCacheService materialCacheService;

    @Autowired
    private DictBuService dictBuService;

    @Autowired
    private BannerCacheBuService bannerCacheBuService;

    @Autowired
    private PrintPaperInfoBuService paperInfoBuService;

    @Autowired
    private PaperDisplayBuService paperDisplayBuService;

    @Autowired
    private FunctionsSettingBuService functionsSettingBuService;

    @Autowired
    private PrinterDriverUpgradeCacheService printerDriverUpgradeCacheService;

    @Override
    public void onMessage(String channel, String message) {
        System.out.println("onMessage: channel[" + channel + "], message[" + message + "]");

        if (SystemPublishEnums.BANNER_UPDATE_PUB_SUB.getKey().equals(channel)) {
            bannerCacheBuService.initBanner();
        } else if (SystemPublishEnums.MATERIAL_UPDATE_PUB_SUB.getKey().equals(channel)) {
            materialCacheService.initMaterial();
            materialCacheService.initMaterialResource();
        } else if (SystemPublishEnums.MATERIAL_RES_UPDATE_PUB_SUB.getKey().equals(channel)) {
            materialCacheService.initMaterial();
            materialCacheService.initMaterialResource();
        } else if (SystemPublishEnums.PAPER_DISPLAY_UPDATE_PUB_SUB.getKey().equals(channel)) {
            paperDisplayBuService.init();
        } else if (SystemPublishEnums.PAPER_INFO_UPDATE_PUB_SUB.getKey().equals(channel)) {
            paperInfoBuService.init();
        } else if (SystemPublishEnums.DICT_UPDATE_PUB_SUB.getKey().equals(channel)) {
            dictBuService.init();
        } else if (SystemPublishEnums.FUNCTION_SETTING_UPDATE_PUB_SUB.getKey().equals(channel)){
            functionsSettingBuService.init();
        } else if (SystemPublishEnums.PRINTER_DRIVER_UPDATE_PUB_SUB.getKey().equals(channel)) {
            printerDriverUpgradeCacheService.init();
        }
    }

    @Override
    public void onPMessage(String pattern, String channel, String message) {
        System.out.println("onPMessage: channel[" + channel + "], message[" + message + "]");
    }

    @Override
    public void onSubscribe(String channel, int subscribedChannels) {
        System.out.println("onSubscribe: channel[" + channel + "]," + "subscribedChannels[" + subscribedChannels + "]");
    }

    @Override
    public void onUnsubscribe(String channel, int subscribedChannels) {
        System.out.println("onUnsubscribe: channel[" + channel + "], " + "subscribedChannels[" + subscribedChannels + "]");
    }

    @Override
    public void onPUnsubscribe(String pattern, int subscribedChannels) {
        System.out.println("onPUnsubscribe: pattern[" + pattern + "]," + "subscribedChannels[" + subscribedChannels + "]");
    }

    @Override
    public void onPSubscribe(String pattern, int subscribedChannels) {
        System.out.println("onPSubscribe: pattern[" + pattern + "], " + "subscribedChannels[" + subscribedChannels + "]");
    }
}