package net.snaptag.system.business.cache;

import net.snaptag.system.business.enums.SystemPublishEnums;
import net.snaptag.system.sadais.cache.kit.CacheKit;
import net.snaptag.system.sadais.cache.model.RedisMessage;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date ：Created in 2024/6/28 14:28
 * @description：
 * @modified By：
 * @version: $
 */
@Service
public class PubSubCacheService {
    /**
     * 广播更新本地缓存数据
     */
    public void updateMaterialResCacheData() {
        pushRedisListen(SystemPublishEnums.MATERIAL_RES_UPDATE_PUB_SUB.getKey(), "MaterialResUpdatePublish");
    }

    public void updateDictCacheData(){
        pushRedisListen(SystemPublishEnums.DICT_UPDATE_PUB_SUB.getKey(), "DictUpdatePublish");
    }

    public void updatePaperInfoCacheData(){
        pushRedisListen(SystemPublishEnums.PAPER_INFO_UPDATE_PUB_SUB.getKey(), "PaperInfoUpdatePublish");
    }

    public void updatePaperDisplayCacheData(){
        pushRedisListen(SystemPublishEnums.PAPER_DISPLAY_UPDATE_PUB_SUB.getKey(), "PaperDisplayUpdatePublish");
    }

    public void updateBannerCacheData(){
        pushRedisListen(SystemPublishEnums.BANNER_UPDATE_PUB_SUB.getKey(), "BannerUpdatePublish");
    }

    public void updateFunctionSetting() {
        pushRedisListen(SystemPublishEnums.FUNCTION_SETTING_UPDATE_PUB_SUB.getKey(), "FunctionSettingUpdatePublish");
    }

    public void updatePrinterDriverUpgrade() {
        pushRedisListen(SystemPublishEnums.PRINTER_DRIVER_UPDATE_PUB_SUB.getKey(), "PrinterDriverUpdatePublish");
    }

    private void pushRedisListen(String channel, String body) {
        RedisMessage redisMessage = new RedisMessage();
        redisMessage.setChannel(channel);
        redisMessage.setBody(body);
        CacheKit.cache().publish(redisMessage);
    }


}
