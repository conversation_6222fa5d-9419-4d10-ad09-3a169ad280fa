package net.snaptag.system.business.cache;

import net.snaptag.system.business.entity.MsgColumn;
import net.snaptag.system.business.enums.MsgColumnCacheEnums;
import net.snaptag.system.sadais.cache.kit.CacheKit;
import net.snaptag.system.sadais.core.common.core.ToolsConst;
import org.springframework.stereotype.Service;

/**
 * 消息栏目缓存类
 * 
 * <AUTHOR> 2018年7月10日
 */
@Service
public class MsgColumnCacheService {
    /**
     * 保存消息数据
     * 
     * @param msgColumn
     */
    public void saveMsgColumn(MsgColumn msgColumn) {
        String key = MsgColumnCacheEnums.MSG_COLUMN_BY_USER_ID.getKey() + msgColumn.getUserId();
        CacheKit.cache().set(key, msgColumn, ToolsConst.DAY_SECOND * 3);
    }

    /**
     * 获取消息数据
     * 
     * @param userId
     *            用户ID
     * @return
     */
    public MsgColumn getMsgColumn(String userId) {
        String key = MsgColumnCacheEnums.MSG_COLUMN_BY_USER_ID.getKey() + userId;
        return CacheKit.cache().get(key, MsgColumn.class);
    }

    /**
     * 删除消息数据
     * 
     * @param userId
     *            用户ID
     */
    public void removeMsgColumn(String userId) {
        String key = MsgColumnCacheEnums.MSG_COLUMN_BY_USER_ID.getKey() + userId;
        CacheKit.cache().del(key);
    }
}
