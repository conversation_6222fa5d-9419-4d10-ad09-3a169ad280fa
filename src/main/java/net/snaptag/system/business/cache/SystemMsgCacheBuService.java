package net.snaptag.system.business.cache;

import net.snaptag.system.business.buservice.SystemMsgBuService;
import net.snaptag.system.business.buservice.UpdateInfoBuService;
import net.snaptag.system.business.dao.SystemMsgDao;
import net.snaptag.system.business.dao.UpdateInfoDao;
import net.snaptag.system.business.entity.SystemMsg;
import net.snaptag.system.business.entity.UpdateInfo;
import net.snaptag.system.sadais.util.core.ToolsKit;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * JVM缓存类
 * 
 * <AUTHOR> 2018年7月10日
 */
@Service
public class SystemMsgCacheBuService implements Runnable {
    @Autowired
    private SystemMsgDao systemMsgDao;

    @Autowired
    private UpdateInfoDao updateInfoDao;
    // 系统消息集合 key为id value为对象
    private Map<String, SystemMsg> systemMsgMap = new LinkedHashMap<String, SystemMsg>();

    // 版本信息集合 key为id value为对象
    private Map<String, UpdateInfo>       updateInfoMap             = new LinkedHashMap<String, UpdateInfo>();

    public SystemMsgCacheBuService() {
    }

    /**
     * 启动服务
     */
    public void start() {
        Thread t = new Thread(this);
        t.setName("JvmCacheService-Thread");
        t.start();
    }

    @Override
    public void run() {
        while (true) {
            try {
                Thread.sleep(10 * 60 * 1000L);
                initSystemMsg();
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        }
    }

    /**
     * 初始化系统消息
     */
    public void initSystemMsg() {
        try {
            List<SystemMsg> systemMsgList = systemMsgDao.findSystemMsgList();
            if (ToolsKit.isNotEmpty(systemMsgList) && systemMsgList.size() > 0) {
                systemMsgMap.clear();
                long now = System.currentTimeMillis();
                for (SystemMsg systemMsg : systemMsgList) {
//                    if (ToolsKit.isNotEmpty(systemMsg.getStartDate()) && ToolsKit.isNotEmpty(systemMsg.getEndDate())) {
//                        if (systemMsg.getStartDate().getTime() > now || systemMsg.getEndDate().getTime() < now) {
//                            continue;
//                        }
//                    }
                    if (ToolsKit.isNotEmpty(systemMsg.getStartDate()) && ToolsKit.isNotEmpty(systemMsg.getEndDate())) {
                        if (systemMsg.getEndDate().getTime() < now) {
                            // 只过去
                            continue;
                        }
                    }
                    systemMsgMap.put(systemMsg.getId(), systemMsg);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 获取系统消息列表
     * 
     * @return
     */
    public List<SystemMsg> getSystemMsgList() {
        List<SystemMsg> list = new ArrayList<SystemMsg>();
        Iterator<Map.Entry<String, SystemMsg>> it = systemMsgMap.entrySet().iterator();
        Date now = new Date();
        while (it.hasNext()) {
            Map.Entry<String, SystemMsg> entry = it.next();
            SystemMsg systemMsg = entry.getValue();
            if (ToolsKit.isNotEmpty(systemMsg.getStartDate()) && ToolsKit.isNotEmpty(systemMsg.getEndDate())) {
                if (systemMsg.getStartDate().getTime() > now.getTime() || systemMsg.getEndDate().getTime() < now.getTime()) {
                    continue;
                }
            }
            list.add(entry.getValue());
        }
        return list;
    }

    /**
     * 获取用户系统提示信息记录数
     * 
     * @param date
     *            用户最后一次更新时间
     * @return
     */
    public int getSystemMsgCount(Date date) {
        List<SystemMsg> msgList = this.getSystemMsgList();
        int count = 0;
        for (SystemMsg systemMsg : msgList) {
            if (ToolsKit.isEmpty(date) || systemMsg.getCreatetime().getTime() > date.getTime()) {
                count++;
            }
        }
        return count;
    }

    /**
     * 获取版本更新信息
     *
     * @return
     */
    public List<UpdateInfo> getUpdateInfoList() {
        List<UpdateInfo> list = new ArrayList<UpdateInfo>();
        for (Map.Entry<String, UpdateInfo> entry : updateInfoMap.entrySet()) {
            list.add(entry.getValue());
        }
        return list;
    }

    /**
     * 初始化版本更新信息
     */
    public void initUpdateInfo() {
        try {
            List<UpdateInfo> updateInfoList = updateInfoDao.findUpdateInfoList();
            if (ToolsKit.isNotEmpty(updateInfoList) && updateInfoList.size() > 0) {
                updateInfoMap.clear();
                for (UpdateInfo updateInfo : updateInfoList) {
                    updateInfoMap.put(updateInfo.getId(), updateInfo);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}