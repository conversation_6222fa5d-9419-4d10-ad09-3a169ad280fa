package net.snaptag.system.business.cache;

import net.snaptag.system.business.entity.ResourceData;
import net.snaptag.system.business.enums.ResourceDataCacheEnums;
import net.snaptag.system.sadais.cache.kit.CacheKit;
import net.snaptag.system.sadais.core.common.core.ToolsConst;
import org.springframework.stereotype.Service;

/**
 * 资源缓存服务类
 * 
 * <AUTHOR> 2018年7月25日
 */
@Service
public class ResourceDataCacheService {
    /**
     * 保存资源
     * 
     * @param resourceData
     */
    public void saveResourceData(ResourceData resourceData) {
        String key = ResourceDataCacheEnums.RESOURCE_DATA_BY_ID.getKey() + resourceData.getId();
        CacheKit.cache().set(key, resourceData, ToolsConst.DAY_SECOND * 3);
    }

    /**
     * 获取资源
     * 
     * @param id
     *            记录ID
     * @return
     */
    public ResourceData getResourceDataById(String id) {
        String key = ResourceDataCacheEnums.RESOURCE_DATA_BY_ID.getKey() + id;
        return CacheKit.cache().get(key, ResourceData.class);
    }

    /**
     * 删除资源
     * 
     * @param id
     *            记录ID
     * @return
     */
    public void removeResourceDataById(String id) {
        String key = ResourceDataCacheEnums.RESOURCE_DATA_BY_ID.getKey() + id;
        CacheKit.cache().del(key);
    }
}
