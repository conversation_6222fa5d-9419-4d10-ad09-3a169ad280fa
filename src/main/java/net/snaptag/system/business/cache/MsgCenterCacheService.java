package net.snaptag.system.business.cache;

import net.snaptag.system.business.entity.MsgCenter;
import net.snaptag.system.business.enums.MsgCenterCacheEnums;
import net.snaptag.system.sadais.cache.kit.CacheKit;
import net.snaptag.system.sadais.core.common.core.ToolsConst;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 消息中心缓存类
 * 
 * <AUTHOR> 2018年7月10日
 */
@Service
public class MsgCenterCacheService {
    /**
     * 保存消息数据
     * 
     * @param msgCenter
     */
    public void saveMsgCenter(MsgCenter msgCenter) {
        String key = MsgCenterCacheEnums.MSG_CENTER_BY_ID.getKey() + msgCenter.getId();
        CacheKit.cache().set(key, msgCenter, ToolsConst.DAY_SECOND * 3);
    }

    /**
     * 获取消息数据
     * 
     * @param id
     *            记录ID
     * @return
     */
    public MsgCenter getMsgCenter(String id) {
        String key = MsgCenterCacheEnums.MSG_CENTER_BY_ID.getKey() + id;
        return CacheKit.cache().get(key, MsgCenter.class);
    }

    /**
     * 删除消息数据
     * 
     * @param id
     */
    public void removeMsgCenter(String id) {
        String key = MsgCenterCacheEnums.MSG_CENTER_BY_ID.getKey() + id;
        CacheKit.cache().del(key);
    }

    /**
     * 获取消息列表
     * 
     * @param userId
     *            用户ID
     * @param msgType
     *            消息类型
     * @param page
     *            当前页码
     * @param pageSize
     *            每页大小
     * @return
     */
    public List<String> getMsgCenterIdsList(String userId, int msgType, int page, int pageSize) {
        String key = MsgCenterCacheEnums.MSG_CENTER_ID_LIST.getKey() + msgType + ":" + userId;
        return CacheKit.cache().zrevrange(key, page, pageSize);
    }

    /**
     * 保存消息ID列表
     * 
     * @param userId
     *            用户ID
     * @param msgType
     *            消息类型
     * @param id
     * @param time
     */
    public void addMsgCenterIdToList(String userId, int msgType, String id, long time) {
        String key = MsgCenterCacheEnums.MSG_CENTER_ID_LIST.getKey() + msgType + ":" + userId;
        CacheKit.cache().zadd(key, time, id, ToolsConst.DAY_SECOND * 3);
        CacheKit.cache().zremrangebyrank(key, 0, -1000);
    }

    /**
     * 从消息列表ID集合中删除ID
     * 
     * @param userId
     *            用户ID
     * @param msgType
     *            消息类型
     * @param id
     *            id
     */
    public void removeMsgCenterIdToList(String userId, int msgType, String id) {
        String key = MsgCenterCacheEnums.MSG_CENTER_ID_LIST.getKey() + msgType + ":" + userId;
        CacheKit.cache().zrem(key, id);
    }

    /**
     * 判断消息列表是否存在
     * 
     * @param userId
     *            用户ID
     * @param msgType
     *            消息类型
     * @return
     */
    public boolean existsList(String userId, int msgType) {
        String key = MsgCenterCacheEnums.MSG_CENTER_ID_LIST.getKey() + msgType + ":" + userId;
        return CacheKit.cache().exists(key);
    }

    /**
     * 获取ID所在集合列表中的位置
     * 
     * @param userId
     *            用户ID
     * @param msgType
     *            消息类型
     * @param id
     *            id
     * @return
     */
    public long zrevrank(String userId, int msgType, String id) {
        String key = MsgCenterCacheEnums.MSG_CENTER_ID_LIST.getKey() + msgType + ":" + userId;
        return CacheKit.cache().zrevrank(key, id);
    }
}
