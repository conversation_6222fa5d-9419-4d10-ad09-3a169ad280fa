package net.snaptag.system.business.cache;

import net.snaptag.system.business.dao.PrintDriverUpdateInfoDao;
import net.snaptag.system.business.entity.PrintDriverUpdateInfo;
import net.snaptag.system.sadais.util.core.ToolsKit;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * <AUTHOR>
 * @date ：Created in 2024/7/5 8:56
 * @description：
 * @modified By：
 * @version: $
 */
@Service
public class PrinterDriverUpgradeCacheService {
    private final static Integer PRINT_DRIVER_SHOW = 1;
    @Autowired
    private PrintDriverUpdateInfoDao printDriverUpdateInfoDao;

    private List<PrintDriverUpdateInfo> list;
    private Map<String, List<PrintDriverUpdateInfo>> map;

    public List<PrintDriverUpdateInfo> getList(){
        return list;
    }

    public Map<String, List<PrintDriverUpdateInfo>> getMap(){
        return map;
    }

    public void init(){
        list = new ArrayList<>();
        map = new HashMap<>();
        list = printDriverUpdateInfoDao.findListByUsed(null, null);
        if (list!=null){
            for (PrintDriverUpdateInfo driver: list ) {
                List<PrintDriverUpdateInfo> tempList = new ArrayList<>();
                if (ToolsKit.isNotEmpty(map.get(driver.getPrinterModel()))){
                    tempList = map.get(driver.getPrinterModel());
                }
                tempList.add(driver);
                // 对tempList的版本号，从高到低排序
                Collections.sort(tempList, new Comparator<PrintDriverUpdateInfo>() {
                    @Override
                    public int compare(PrintDriverUpdateInfo o1, PrintDriverUpdateInfo o2) {
                        if (ToolsKit.isEmpty(o1.getVersionCode())){
                            return 1;
                        }
                        if (ToolsKit.isEmpty(o2.getVersionCode())){
                            return 1;
                        }
                        return o2.getVersionCode().compareTo(o1.getVersionCode());
                    }
                });
                map.put(driver.getPrinterModel(), tempList);
            }
        }
    }

}
