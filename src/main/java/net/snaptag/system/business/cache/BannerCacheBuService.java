package net.snaptag.system.business.cache;

import net.snaptag.system.business.buservice.BannerBuService;
import net.snaptag.system.business.dao.BannerDao;
import net.snaptag.system.business.entity.Banner;
import net.snaptag.system.sadais.util.core.ToolsKit;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> 2018年11月20日
 */
@Service
public class BannerCacheBuService {
    @Autowired
    private BannerDao bannerDao;
    // banner集合
    private List<Banner>    bannerList = new ArrayList<Banner>();

    public BannerCacheBuService() {
    }

    /**
     * 初始化banner
     */
    public void initBanner() {
        try {
            List<Banner> bannerList = bannerDao.findListSortBySort();
            if (ToolsKit.isNotEmpty(bannerList) && bannerList.size() > 0) {
                this.bannerList.clear();
                long time = System.currentTimeMillis();
                for (Banner banner : bannerList) {
                    if (ToolsKit.isNotEmpty(banner.getStartTime()) && ToolsKit.isNotEmpty(banner.getEndTime())) {
                        if (banner.getStartTime().getTime() <= time && time < banner.getEndTime().getTime()) {
                            this.bannerList.add(banner);
                        }
                    } else {
                        this.bannerList.add(banner);
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 获取banner集合
     * 
     * @return
     */
    public List<Banner> getBannerList(String language) {
        return bannerList.stream().filter(banner -> ToolsKit.isNotEmpty(banner.getLocaleCode()) && banner.getLocaleCode().contains(language)).collect(Collectors.toList());
        // return bannerList;
    }
}