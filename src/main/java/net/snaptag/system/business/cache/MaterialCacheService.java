package net.snaptag.system.business.cache;

import net.snaptag.system.business.buservice.MaterialBuService;
import net.snaptag.system.business.buservice.MaterialResourceBuService;
import net.snaptag.system.business.dao.MaterialDao;
import net.snaptag.system.business.dao.MaterialResourceDao;
import net.snaptag.system.business.entity.Material;
import net.snaptag.system.business.entity.MaterialResource;
import net.snaptag.system.sadais.util.core.ToolsKit;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 素材缓存服务类
 * 
 * <AUTHOR> 2018年7月25日
 */
@Service
public class MaterialCacheService {
    @Autowired
    private MaterialDao materialDao;
    @Autowired
    private MaterialResourceDao materialResourceDao;
    @Autowired
    private MaterialResourceBuService materialResourceBuService;

    // 素材库集合 key为id value为对象
    private Map<String, Material> materialMap               = new LinkedHashMap<String, Material>();
    // 素材库根节点集合
    private List<String> materialRootIdList        = new ArrayList<String>();
    // 素材库子节点集合
    private Map<String, List<String>>     materialChildIdMap        = new HashMap<String, List<String>>();
    // 素材库资源集合 key为id value为对象
    private Map<String, MaterialResource> materialResourceMap       = new LinkedHashMap<String, MaterialResource>();
    // 素材库资源集合 key为素材库id value为素材资源id集合
    private Map<String, List<String>>     materialResourceIdListMap = new LinkedHashMap<String, List<String>>();

    /**
     * 初始化素材库
     */
    public void initMaterial() {
        try {
            List<Material> materialList = materialDao.findMaterialList();;
            if (ToolsKit.isNotEmpty(materialList) && materialList.size() > 0) {
                materialMap.clear();
                materialRootIdList.clear();
                materialChildIdMap.clear();
                for (Material material : materialList) {
                    if (ToolsKit.isEmpty(material.getPId())) {
                        materialRootIdList.add(material.getId());
                    }
//                    materialRootIdList.add(material.getId());
                    materialMap.put(material.getId(), material);
                }
                for (String rootId : materialRootIdList) {
                    List<String> list = Optional.ofNullable(materialChildIdMap.get(rootId)).orElse(new ArrayList<String>());
                    for (Material material : materialList) {
                        if (rootId.equals(material.getPId())) {
                            list.add(material.getId());
                        }
                    }
                    materialChildIdMap.put(rootId, list);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 初始化素材库资源
     */
    public void initMaterialResource() {
        try {
            List<MaterialResource> materialResourceList = materialResourceDao.findMaterialResourceList();;
            if (ToolsKit.isNotEmpty(materialResourceList) && materialResourceList.size() > 0) {
                materialResourceMap.clear();
                materialResourceIdListMap.clear();
                for (MaterialResource materialResource : materialResourceList) {
                    materialResourceMap.put(materialResource.getId(), materialResource);
//                    System.out.println(materialResource.getId()+"="+materialResource.getLabel());

                    List<String> list = Optional.ofNullable(materialResourceIdListMap.get(materialResource.getMId())).orElse(new ArrayList<String>());
                    list.add(materialResource.getId());
//                    if ("5fb7265e5baa9f55802cf75b".equals(materialResource.getmId())) {
//                        System.out.println(list.size());
//                    }
                    materialResourceIdListMap.put(materialResource.getMId(), list);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 获取素材库根节点ID集合
     *
     * @return
     */
    public List<String> getMaterialRootIdList() {
        return materialRootIdList;
    }

    /**
     * 根据素材库ID获取素材库对象
     *
     * @param id
     *            素材库ID
     * @return
     */
    public Material getMaterialById(String id) {
        return materialMap.get(id);
    }

    /**
     * 根据ID获取素材库资源对象
     *
     * @param id
     *            素材库资源ID
     * @return
     */
    public MaterialResource getMaterialResourceById(String id) {
        return materialResourceMap.get(id);
    }

    /**
     * 根据素材库获取资源ID列表
     *
     * @param mId
     * @return
     */
    public List<String> getMaterialResourceIdList(String mId) {
        return materialResourceIdListMap.get(mId);
    }

    /**
     * 获取素材库子节点ID集合
     *
     * @param rootId
     *            根节点ID
     * @return
     */
    public List<String> getMaterialChildIdList(String rootId) {
        Iterator<Map.Entry<String, List<String>>> iterator = materialChildIdMap.entrySet().iterator();
        while (iterator.hasNext()) {
            Map.Entry<String, List<String>> entry = iterator.next();
            if (entry.getKey().equals(rootId)) {
                return entry.getValue();
            }
        }
        return null;
    }
}
