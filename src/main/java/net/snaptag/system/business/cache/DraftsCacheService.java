package net.snaptag.system.business.cache;

import net.snaptag.system.business.entity.Drafts;
import net.snaptag.system.business.enums.DraftsCacheEnums;
import net.snaptag.system.sadais.cache.kit.CacheKit;
import net.snaptag.system.sadais.core.common.core.ToolsConst;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 草稿箱缓存服务类
 * 
 * <AUTHOR> 2018年7月25日
 */
@Service
public class DraftsCacheService {
    /**
     * 保存草稿箱
     * 
     * @param drafts
     */
    public void saveDrafts(Drafts drafts) {
        String key = DraftsCacheEnums.DRAFTS_BY_ID.getKey() + drafts.getId();
        CacheKit.cache().set(key, drafts, ToolsConst.DAY_SECOND * 3);
    }

    /**
     * 获取草稿箱
     * 
     * @param id
     *            记录ID
     * @return
     */
    public Drafts getDraftsById(String id) {
        String key = DraftsCacheEnums.DRAFTS_BY_ID.getKey() + id;
        return CacheKit.cache().get(key, Drafts.class);
    }

    /**
     * 删除草稿箱
     * 
     * @param id
     *            记录ID
     * @return
     */
    public void removeDraftsById(String id) {
        String key = DraftsCacheEnums.DRAFTS_BY_ID.getKey() + id;
        CacheKit.cache().del(key);
    }

    /**
     * 获取草稿箱列表
     * 
     * @param userId
     *            用户ID
     * @param page
     *            当前页码
     * @param pageSize
     *            每页大小
     * @return
     */
    public List<String> getDraftsIdsList(String userId, int type, int subType, int length, int page, int pageSize) {
        String key = DraftsCacheEnums.DRAFTS_ID_LIST.getKey() + type + ":" + subType + ":" + length + ":" + userId;
        return CacheKit.cache().zrevrange(key, page, pageSize);
    }

    /**
     * 保存草稿箱列表
     * 
     * @param userId
     *            用户ID
     * @param id
     *            动态ID
     * @param time
     */
    public void addDraftsIdToList(String userId, int type, int subType, int length, String id, long time) {
        String key = DraftsCacheEnums.DRAFTS_ID_LIST.getKey() + type + ":" + subType + ":" + length + ":" + userId;
        CacheKit.cache().zadd(key, time, id, ToolsConst.THIRTY_MINUTES * 3);
    }

    /**
     * 从草稿箱列表ID集合中删除ID
     * 
     * @param userId
     *            用户ID
     * @param id
     *            动态ID
     */
    public void removeDraftsIdToList(String userId, int type, int subType, int length, String id) {
        String key = DraftsCacheEnums.DRAFTS_ID_LIST.getKey() + type + ":" + subType + ":" + length + ":" + userId;
        CacheKit.cache().zrem(key, id);
    }

    /**
     * 判断草稿箱列表是否存在
     * 
     * @param userId
     *            用户ID
     * @return
     */
    public boolean existsList(String userId, int type, int subType, int length) {
        String key = DraftsCacheEnums.DRAFTS_ID_LIST.getKey() + type + ":" + subType + ":" + length + ":" + userId;
        return CacheKit.cache().exists(key);
    }

    /**
     * 获取ID所在集合列表中的位置
     * 
     * @param userId
     *            用户ID
     * @param id
     *            动态ID
     * @return
     */
    public Long zrevrank(String userId, int type, int subType, int length, String id) {
        String key = DraftsCacheEnums.DRAFTS_ID_LIST.getKey() + type + ":" + subType + ":" + length + ":" + userId;
        return CacheKit.cache().zrevrank(key, id);
    }
}
