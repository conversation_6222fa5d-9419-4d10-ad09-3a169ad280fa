package net.snaptag.system.storage.enums;
public enum STSParamEnum {
    ENDPOINT("endPoint", ""),
    // OSS
    OSS_BUCKET("bucket", ""),
    OSS_OBJECT_NAME("objectName", "api/img/gam/snapTag/"),;
    // SLS
    //SLS_PROJECT("project", "fitness"), 
    //SLS_LOGSTORE("logStore", "phone_log");
    private STSParamEnum(String name, String value) {
        this.name = name;
        this.value = value;
    }

    public String getValue() {
        return value;
    }

    public String getName() {
        return name;
    }
    private final String value;
    private final String name;
}
