package net.snaptag.system.storage.plugin;

import net.snaptag.system.sadais.core.common.core.IPlugin;
import net.snaptag.system.storage.aliyun.config.OssConfig;
import net.snaptag.system.storage.aliyun.kit.OSSKit;

public class OssPlugin implements IPlugin {
    public OssPlugin(String ossAccesskey, String ossAccesskeySecret, String ossEndPoint) {
        OssConfig.getInstance().setOssAccesskey(ossAccesskey);
        OssConfig.getInstance().setOssAccesskeySecret(ossAccesskeySecret);
        OssConfig.getInstance().setOssEndPoint(ossEndPoint);
    }

    @Override
    public void init() throws Exception {
    }

    @Override
    public void start() throws Exception {
        String endPoint = OssConfig.getInstance().getOssEndPoint();
        if (endPoint.indexOf("aliyun") > -1) {
            OSSKit.getInstance().init();
        }
    }

    @Override
    public void stop() throws Exception {
    }
}
