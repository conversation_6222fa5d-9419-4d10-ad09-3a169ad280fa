package net.snaptag.system.storage.plugin;

import net.snaptag.system.sadais.core.common.core.IPlugin;
import net.snaptag.system.storage.aliyun.config.StsConfig;
import net.snaptag.system.storage.aliyun.kit.STSKit;

public class StsPlugin implements IPlugin {
    public StsPlugin(String stsAccesskey, String stsAccesskeySecret, String stsRegion, String stsEndPoint, String stsRoleArn, String stsRoleSessionName,
            long stsDurationSeconds, String charEncode, String stsEndPoints) {
        StsConfig.getInstance().setStsAccesskey(stsAccesskey);
        StsConfig.getInstance().setStsAccesskeySecret(stsAccesskeySecret);
        StsConfig.getInstance().setStsRegion(stsRegion);
        StsConfig.getInstance().setStsEndPoint(stsEndPoint);
        StsConfig.getInstance().setStsRoleArn(stsRoleArn);
        StsConfig.getInstance().setStsRoleSessionName(stsRoleSessionName);
        StsConfig.getInstance().setStsDurationSeconds(stsDurationSeconds);
        StsConfig.getInstance().setCharEncode(charEncode);
        StsConfig.getInstance().setStsEndPoints(stsEndPoints);
    }

    @Override
    public void init() throws Exception {
    }

    @Override
    public void start() throws Exception {
        STSKit.getInstance().init();
    }

    @Override
    public void stop() throws Exception {
    }
}
