package net.snaptag.system.storage.aliyun.config;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class OssConfig {
    private static final Logger logger = LoggerFactory.getLogger(OssConfig.class);
    private String              ossAccesskey;
    private String              ossAccesskeySecret;
    private String              ossEndPoint;
    private static OssConfig ossConfig;

    public static OssConfig getInstance() {
        try {
            if (null == ossConfig) {
                ossConfig = new OssConfig();
            }
        } catch (Exception e) {
            logger.warn(e.getMessage(), e);
        }
        return ossConfig;
    }

    private OssConfig() {
    }

    public String getOssAccesskey() {
        return ossAccesskey;
    }

    public void setOssAccesskey(String ossAccesskey) {
        this.ossAccesskey = ossAccesskey;
    }

    public String getOssAccesskeySecret() {
        return ossAccesskeySecret;
    }

    public void setOssAccesskeySecret(String ossAccesskeySecret) {
        this.ossAccesskeySecret = ossAccesskeySecret;
    }

    public String getOssEndPoint() {
        return ossEndPoint;
    }

    public void setOssEndPoint(String ossEndPoint) {
        this.ossEndPoint = ossEndPoint;
    }
}
