package net.snaptag.system.storage.aliyun.utils;

import org.apache.commons.validator.routines.InetAddressValidator;

import java.io.File;
import java.io.IOException;
import java.net.InetAddress;
import java.net.NetworkInterface;
import java.net.SocketException;
import java.net.URISyntaxException;
import java.util.Enumeration;

public class Tools {
    public static String getWebRootPath() {
        try {
            return new File(Tools.class.getResource("/").toURI().getPath()).getParentFile().getParentFile().getCanonicalPath();
        } catch (URISyntaxException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return "";
    }

    public static String getLocalHostIP() {
        return getLocalHostIP(true);
    }

    public static String getLocalHostIP(boolean isPublicIp) {
        try {
            return getLocalMachineIp(isPublicIp);
        } catch (Exception e) {
            e.printStackTrace();
            return "";
        }
    }

    /**
     * 阿里云的ECS内网IP都是以10开头
     * 
     * @param isPublicIp
     * @return
     */
    private static String getLocalMachineIp(boolean isPublicIp) {
        InetAddressValidator validator = new InetAddressValidator();
        String candidate = new String();
        try {
            Enumeration<NetworkInterface> ifaces = NetworkInterface.getNetworkInterfaces();
            while (ifaces.hasMoreElements()) {
                NetworkInterface iface = (NetworkInterface) ifaces.nextElement();
                if (iface.isUp()) {
                    Enumeration<InetAddress> addresses = iface.getInetAddresses();
                    while (addresses.hasMoreElements()) {
                        InetAddress address = (InetAddress) addresses.nextElement();
                        if ((!address.isLinkLocalAddress()) && (address.getHostAddress() != null)) {
                            String ipAddress = address.getHostAddress();
                            if (isPublicIp) {
                                if (!ipAddress.equals("127.0.0.1") && !ipAddress.startsWith("10") && !ipAddress.startsWith("0")) {
                                    if (validator.isValidInet4Address(ipAddress)) {
                                        return ipAddress;
                                    }
                                }
                            } else {
                                if (!ipAddress.equals("127.0.0.1") && ipAddress.startsWith("10")) {
                                    if (validator.isValidInet4Address(ipAddress)) {
                                        return ipAddress;
                                    }
                                }
                            }
                            if (validator.isValid(ipAddress)) {
                                candidate = ipAddress;
                            }
                        }
                    }
                }
            }
        } catch (SocketException localSocketException) {
        }
        return candidate;
    }

    /**
     * MD5(ip)
     * 
     * @return
     */
    public static String getLogShardHash() {
        return MD5.MD5Encode(getLocalHostIP());
    }
}
