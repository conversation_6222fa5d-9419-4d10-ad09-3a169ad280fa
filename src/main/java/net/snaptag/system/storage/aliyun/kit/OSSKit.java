package net.snaptag.system.storage.aliyun.kit;

import net.snaptag.system.storage.aliyun.core.OSSUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class OSSKit {
    private static Logger   logger = LoggerFactory.getLogger(OSSKit.class);
    private static OSSUtils ossUtils;

    public static OSSUtils getInstance() {
        try {
            if (ossUtils == null) {
                ossUtils = OSSUtils.getInstance();
            }
        } catch (Exception e) {
            logger.warn(e.getMessage(), e);
        }
        return ossUtils;
    }
}
