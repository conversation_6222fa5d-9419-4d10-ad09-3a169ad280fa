package net.snaptag.system.storage.aliyun.kit;

import net.snaptag.system.storage.aliyun.core.STSUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 阿里获取 token工具类
 */
public class STSKit {
    private static Logger   logger = LoggerFactory.getLogger(STSKit.class);
    private static STSUtils stsUtils;

    public static STSUtils getInstance() {
        try {
            if (stsUtils == null) {
                stsUtils = STSUtils.getInstance();
            }
        } catch (Exception e) {
            logger.warn(e.getMessage(), e);
        }
        return stsUtils;
    }
}
