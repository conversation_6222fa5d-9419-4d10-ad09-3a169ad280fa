package net.snaptag.system.business.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import net.snaptag.system.business.buservice.TemplateBorderService;
import net.snaptag.system.business.dto.TempletBorderQueryDTO;
import net.snaptag.system.business.entity.TemplateBorder;
import net.snaptag.system.business.entity.TemplateBorderKind;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 模板边框服务测试类
 *
 * <AUTHOR>
 * @since 2025-08-20
 */
@SpringBootTest
@ActiveProfiles("test")
public class TemplateBorderServiceTest {

    @Autowired
    private TemplateBorderService templateBorderService;

    @Test
    public void testGetBorderList() {
        // 测试获取所有边框列表
        List<TemplateBorder> borders = templateBorderService.getBorderList();
        
        assertNotNull(borders, "边框列表不应为空");
        assertTrue(borders.size() > 0, "应该有边框数据");
        
        // 验证数据结构
        TemplateBorder firstBorder = borders.get(0);
        assertNotNull(firstBorder.getBorderId(), "边框ID不应为空");
        assertNotNull(firstBorder.getThumbUrl(), "缩略图URL不应为空");
        
        // 验证敏感字段已移除
        assertNull(firstBorder.getSysUserId(), "敏感字段sysUserId应被移除");
    }

    @Test
    public void testGetBorderKindListWithLanguage() {
        // 测试中文分类列表
        List<TemplateBorderKind> chineseKinds = templateBorderService.getBorderKindList(1, null);
        assertNotNull(chineseKinds, "中文分类列表不应为空");
        assertTrue(chineseKinds.size() > 0, "应该有分类数据");

        // 测试英文分类列表
        List<TemplateBorderKind> englishKinds = templateBorderService.getBorderKindList(2, null);
        assertNotNull(englishKinds, "英文分类列表不应为空");
        assertTrue(englishKinds.size() > 0, "应该有分类数据");
    }

    @Test
    public void testGetBorderKindListWithLang() {
        // 测试使用lang参数
        List<TemplateBorderKind> englishKinds = templateBorderService.getBorderKindList(null, "english");
        assertNotNull(englishKinds, "英文分类列表不应为空");
        assertTrue(englishKinds.size() > 0, "应该有分类数据");

        // 测试繁体中文
        List<TemplateBorderKind> traditionalKinds = templateBorderService.getBorderKindList(null, "traditional");
        assertNotNull(traditionalKinds, "繁体中文分类列表不应为空");
    }

    @Test
    public void testGetBorderPage() {
        // 测试基础分页查询
        IPage<TemplateBorder> page = templateBorderService.getBorderPage(1, 5, null, 1);
        
        assertNotNull(page, "分页结果不应为空");
        assertTrue(page.getTotal() > 0, "总数应大于0");
        assertTrue(page.getRecords().size() <= 5, "每页记录数不应超过5");
        assertEquals(1, page.getCurrent(), "当前页应为1");
    }

    @Test
    public void testGetBorderPageWithKindFilter() {
        // 测试按分类过滤的分页查询
        IPage<TemplateBorder> page = templateBorderService.getBorderPage(1, 10, "kind_001", 1);
        
        assertNotNull(page, "分页结果不应为空");
        
        // 验证过滤结果
        for (TemplateBorder border : page.getRecords()) {
            assertEquals("kind_001", border.getBorderKindId(), "所有边框应属于指定分类");
        }
    }

    @Test
    public void testGetBorderPageEnhanced() {
        // 测试增强分页查询
        TempletBorderQueryDTO queryDTO = new TempletBorderQueryDTO();
        queryDTO.setPageNumber(1);
        queryDTO.setPageSize(5);
        queryDTO.setLanguage(2); // 英文
        queryDTO.setIncludeMultiLang(true);
        
        IPage<TemplateBorder> page = templateBorderService.getBorderPageEnhanced(queryDTO);
        
        assertNotNull(page, "分页结果不应为空");
        assertTrue(page.getRecords().size() <= 5, "每页记录数不应超过5");
    }

    @Test
    public void testGetBordersByKind() {
        // 测试根据分类查询边框
        List<TemplateBorder> borders = templateBorderService.getBordersByKind("kind_001");
        
        assertNotNull(borders, "边框列表不应为空");
        
        // 验证所有边框都属于指定分类
        for (TemplateBorder border : borders) {
            assertEquals("kind_001", border.getBorderKindId(), "所有边框应属于指定分类");
        }
    }

    @Test
    public void testGetBorderDetail() {
        // 首先获取一个边框ID
        List<TemplateBorder> borders = templateBorderService.getBorderList();
        assertFalse(borders.isEmpty(), "应该有边框数据用于测试");
        
        String borderId = borders.get(0).getBorderId();
        
        // 测试获取边框详情
        TemplateBorder border = templateBorderService.getBorderDetail(borderId);
        
        assertNotNull(border, "边框详情不应为空");
        assertEquals(borderId, border.getBorderId(), "边框ID应匹配");
        assertNotNull(border.getHeaderImgUrl(), "左图URL不应为空");
        assertNotNull(border.getFillImgUrl(), "中间图URL不应为空");
        assertNotNull(border.getFooterImgUrl(), "右图URL不应为空");
    }

    @Test
    public void testGetBorderDetailWithLanguage() {
        // 获取一个边框ID
        List<TemplateBorder> borders = templateBorderService.getBorderList();
        assertFalse(borders.isEmpty(), "应该有边框数据用于测试");
        
        String borderId = borders.get(0).getBorderId();
        
        // 测试包含多语言信息的详情查询
        TemplateBorder border = templateBorderService.getBorderDetailWithLanguage(borderId, 2);
        
        assertNotNull(border, "边框详情不应为空");
        assertEquals(borderId, border.getBorderId(), "边框ID应匹配");
    }

    @Test
    public void testStatistics() {
        // 测试统计功能
        Long borderCount = templateBorderService.getBorderCount();
        Long kindCount = templateBorderService.getKindCount();
        
        assertNotNull(borderCount, "边框数量不应为空");
        assertNotNull(kindCount, "分类数量不应为空");
        assertTrue(borderCount > 0, "边框数量应大于0");
        assertTrue(kindCount > 0, "分类数量应大于0");
    }

    @Test
    public void testExistsBorderId() {
        // 获取一个存在的边框ID
        List<TemplateBorder> borders = templateBorderService.getBorderList();
        assertFalse(borders.isEmpty(), "应该有边框数据用于测试");
        
        String existingId = borders.get(0).getBorderId();
        String nonExistingId = "non_existing_id";
        
        // 测试存在的ID
        assertTrue(templateBorderService.existsBorderId(existingId), "存在的边框ID应返回true");
        
        // 测试不存在的ID
        assertFalse(templateBorderService.existsBorderId(nonExistingId), "不存在的边框ID应返回false");
    }

    @Test
    public void testLanguageValidation() {
        // 测试有效语言
        assertTrue(templateBorderService.isValidLanguage("english"), "english应该是有效语言");
        assertTrue(templateBorderService.isValidLanguage("traditional"), "traditional应该是有效语言");
        
        // 测试无效语言
        assertFalse(templateBorderService.isValidLanguage("invalid"), "invalid应该是无效语言");
        assertFalse(templateBorderService.isValidLanguage(""), "空字符串应该是无效语言");
        assertFalse(templateBorderService.isValidLanguage(null), "null应该是无效语言");
    }

    @Test
    public void testQueryDTOValidation() {
        // 测试查询DTO的参数验证
        TempletBorderQueryDTO queryDTO = new TempletBorderQueryDTO();
        
        // 测试默认值
        assertEquals(1, queryDTO.getPageNumber(), "默认页码应为1");
        assertEquals(10, queryDTO.getPageSize(), "默认页大小应为10");
        assertEquals(1, queryDTO.getLanguage(), "默认语言应为1");
        
        // 测试边界值处理
        queryDTO.setPageNumber(0);
        queryDTO.setPageSize(-1);
        assertEquals(1, queryDTO.getPageNumber(), "页码小于1时应修正为1");
        assertEquals(10, queryDTO.getPageSize(), "页大小小于1时应修正为10");
        
        // 测试最大值限制
        queryDTO.setPageSize(200);
        assertEquals(100, queryDTO.getPageSize(), "页大小应限制为最大100");
    }
}
