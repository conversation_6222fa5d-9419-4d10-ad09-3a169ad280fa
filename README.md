# SnapTag 微服务项目

基于Spring Boot 2.7.18的微服务应用，集成Nacos配置中心、MyBatis-Plus、SLS日志服务等。

## 🚀 快速开始

### 环境要求

- **Java**: JDK 17+
- **Maven**: 3.6+
- **MySQL**: 5.7+
- **Nacos**: 1.1.4+ (推荐使用localhost:8848)

### 克隆项目

```bash
git clone <repository-url>
cd snapTag-sever
```

### 依赖检查

运行依赖检查脚本（Windows）：
```bash
check-dependencies.bat
```

或手动检查本地依赖包是否存在：
```bash
ls libs/vida-sls-starter-1.0.3-SNAPSHOT.jar
```

### 配置Nacos

1. 启动Nacos服务端（localhost:8848）
2. 在Nacos控制台中创建配置：
   - **Data ID**: `SnapTag`
   - **Group**: `DEFAULT_GROUP`
   - **Namespace**: `93d46b33-48c9-4d59-8204-147c093992b0`
   - **配置格式**: `YAML`

### 启动应用

```bash
mvn spring-boot:run
```

应用将在 http://localhost:30001 启动

## 📁 项目结构

```
snapTag-sever/
├── libs/                          # 本地依赖包
│   ├── vida-sls-starter-1.0.3-SNAPSHOT.jar
│   └── README.md
├── src/main/java/
│   └── net/snaptag/system/
│       ├── SnapTagApplication.java # 主启动类
│       ├── config/                # 配置类
│       ├── business/              # 业务模块
│       ├── test/                  # 测试控制器
│       └── ...
├── src/main/resources/
│   ├── application.yml            # 主配置文件
│   └── mapper/                    # MyBatis映射文件
├── check-dependencies.bat         # 依赖检查脚本
└── README.md
```

## 🔧 核心功能

### Nacos配置中心
- ✅ 配置集中管理
- ✅ 配置动态刷新
- ✅ 配置覆盖本地配置
- ✅ 多环境配置支持

### SLS日志服务
- ✅ 阿里云日志服务集成
- ✅ AOP日志拦截
- ✅ 异步日志发送
- ✅ 多项目多日志库支持

### 数据库
- ✅ MyBatis-Plus集成
- ✅ MySQL数据库支持
- ✅ 连接池配置（HikariCP）
- ✅ 事务管理

### 缓存
- ✅ Redis集成
- ✅ 缓存服务
- ✅ 发布订阅功能

## 🧪 测试API

### Nacos配置测试
```bash
# 获取所有Nacos配置
GET http://localhost:30001/test/nacos/config

# 获取SLS配置
GET http://localhost:30001/test/nacos/sls

# 获取测试配置
GET http://localhost:30001/test/nacos/test
```

### 健康检查
```bash
# 应用健康状态
GET http://localhost:30001/actuator/health
```

## 📝 本地依赖说明

项目使用了自定义的`vida-sls-starter`依赖包，该包已包含在`libs/`目录中：

- **文件**: `libs/vida-sls-starter-1.0.3-SNAPSHOT.jar`
- **作用域**: `system`
- **自动包含**: 其他开发者无需额外安装

详细说明请查看：[libs/README.md](libs/README.md)

## 🔧 开发指南

### 添加新的配置
1. 在Nacos控制台中添加配置项
2. 在代码中使用`@NacosValue`注解获取配置
3. 配置支持自动刷新

### 添加新的日志
1. 使用`@SLSLog`注解标记方法
2. 或直接使用SLS客户端发送日志

### 数据库操作
1. 创建Entity类
2. 创建Mapper接口
3. 创建Service和Controller

## 🚨 注意事项

1. **本地依赖**: 确保`libs/vida-sls-starter-1.0.3-SNAPSHOT.jar`文件存在
2. **Nacos配置**: 必须在Nacos中创建对应的配置文件
3. **数据库连接**: 确保MySQL服务正常运行
4. **Redis连接**: 确保Redis服务正常运行

## 📞 联系方式

如有问题请联系项目维护者。
